<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Partners_Certification extends Google_Model
{
  public $achieved;
  public $certificationType;
  public $expiration;
  public $lastAchieved;
  public $warning;

  public function setAchieved($achieved)
  {
    $this->achieved = $achieved;
  }
  public function getAchieved()
  {
    return $this->achieved;
  }
  public function setCertificationType($certificationType)
  {
    $this->certificationType = $certificationType;
  }
  public function getCertificationType()
  {
    return $this->certificationType;
  }
  public function setExpiration($expiration)
  {
    $this->expiration = $expiration;
  }
  public function getExpiration()
  {
    return $this->expiration;
  }
  public function setLastAchieved($lastAchieved)
  {
    $this->lastAchieved = $lastAchieved;
  }
  public function getLastAchieved()
  {
    return $this->lastAchieved;
  }
  public function setWarning($warning)
  {
    $this->warning = $warning;
  }
  public function getWarning()
  {
    return $this->warning;
  }
}
