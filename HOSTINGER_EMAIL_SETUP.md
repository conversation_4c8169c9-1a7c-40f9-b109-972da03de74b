# 📧 Hostinger Email Account Setup - Critical Fix

## 🚨 MOST LIKELY CAUSE OF EMAIL DELIVERY FAILURE

**The #1 reason emails aren't being delivered:** The sender email address `<EMAIL>` doesn't exist as an actual email account in your Hostinger hosting!

---

## 🔍 Why This Happens

**Common Mistake:**
- You configured the system to send emails FROM `<EMAIL>`
- But this email account was never created in Hostinger
- <PERSON><PERSON> rejects emails from non-existent sender addresses
- Result: No emails are delivered, no error messages shown

**The Fix:**
Create the actual email account in Hostinger, then configure the system to use it.

---

## 🛠️ Step-by-Step Email Account Creation

### **Step 1: Login to Hostinger Control Panel**
1. Go to: https://hpanel.hostinger.com/
2. Login with your Hostinger credentials
3. Select your hosting plan for `passdrc.com`

### **Step 2: Access Email Management**
1. In the Hostinger control panel, look for **"Email"** section
2. Click **"Email Accounts"** or **"Manage Email"**
3. You should see your domain: `passdrc.com`

### **Step 3: Create Email Account**
1. Click **"Create Email Account"** or **"Add Email"**
2. Fill in the details:
   ```
   Email Address: <EMAIL>
   Password: [Create a strong password - save this!]
   Mailbox Size: 1GB (minimum)
   ```
3. Click **"Create"** or **"Add Account"**

### **Step 4: Verify Email Account**
1. The new email should appear in your email accounts list
2. Note down the password you created
3. Test login to webmail (optional but recommended)

---

## ⚙️ Update System Configuration

After creating the email account, update your system configuration:

### **Option 1: Use Simple Email Setup (Recommended)**
1. Go to: `https://passdrc.com/public_html/school/simple_email_setup.php`
2. Update the configuration with:
   ```
   System Email: <EMAIL>
   Protocol: smtp
   SMTP Host: mail.passdrc.com
   SMTP Port: 587
   SMTP Encryption: TLS
   SMTP Username: <EMAIL>
   SMTP Password: [The password you just created]
   ```
3. Save the configuration

### **Option 2: Use PHP Mail (Alternative)**
If SMTP still doesn't work, try PHP mail:
1. Go to: `https://passdrc.com/public_html/school/simple_email_setup.php`
2. Change configuration to:
   ```
   System Email: <EMAIL>
   Protocol: mail
   ```
3. Save the configuration

---

## 🧪 Test Email Delivery

### **Step 1: Run Advanced Diagnostics**
1. Go to: `https://passdrc.com/public_html/school/email_delivery_troubleshoot.php`
2. Enter your personal email address
3. Click "Run Complete Email Test"
4. Check results for each test method

### **Step 2: Test Password Recovery**
1. Go to: `https://passdrc.com/public_html/school/authentication/forgot`
2. Enter a valid username or email
3. Submit the form
4. Check your email (including spam folder)

---

## 🔧 Alternative Solutions if Email Account Creation Fails

### **Solution 1: Use Existing Email Account**
If you already have an email account like `<EMAIL>`:
1. Use that email address in the system configuration
2. Update both the sender email and SMTP username
3. Use the existing email's password

### **Solution 2: Use Hostinger's SMTP Server**
Sometimes using Hostinger's central SMTP works better:
```
SMTP Host: smtp.hostinger.com
SMTP Port: 587
SMTP Encryption: TLS
SMTP Username: <EMAIL>
SMTP Password: [your email password]
```

### **Solution 3: Switch to PHP Mail**
If SMTP continues to fail:
1. Change protocol to `mail` instead of `smtp`
2. This uses PHP's built-in mail function
3. Still requires the sender email to exist

---

## 📋 Hostinger Email Settings Reference

### **Incoming Mail Settings (IMAP/POP3)**
```
IMAP Server: mail.passdrc.com
IMAP Port: 993 (SSL) or 143 (TLS)
POP3 Server: mail.passdrc.com  
POP3 Port: 995 (SSL) or 110 (TLS)
```

### **Outgoing Mail Settings (SMTP)**
```
SMTP Server: mail.passdrc.com
SMTP Port: 587 (TLS) or 465 (SSL)
Authentication: Required
Username: Full email address
Password: Email account password
```

### **Alternative Hostinger SMTP**
```
SMTP Server: smtp.hostinger.com
SMTP Port: 587
Encryption: TLS
Authentication: Required
```

---

## 🚨 Common Hostinger Email Issues & Solutions

### **Issue 1: "Authentication Failed"**
**Cause:** Wrong username or password
**Solution:** 
- Use full email address as username
- Verify password is correct
- Try resetting email password in Hostinger panel

### **Issue 2: "Connection Timeout"**
**Cause:** Firewall or port blocking
**Solution:**
- Try different ports (587, 465, 25)
- Switch between TLS and SSL
- Use `smtp.hostinger.com` instead of `mail.passdrc.com`

### **Issue 3: "Sender Address Rejected"**
**Cause:** Email account doesn't exist
**Solution:**
- Create the email account in Hostinger
- Verify the account is active
- Use exact email address in configuration

### **Issue 4: "Emails Go to Spam"**
**Cause:** Missing email authentication
**Solution:**
- Add SPF record: `v=spf1 include:_spf.hostinger.com ~all`
- Enable DKIM in Hostinger DNS settings
- Use proper sender name and address

---

## 🎯 Quick Troubleshooting Checklist

**Before Testing Email:**
- [ ] Email account `<EMAIL>` exists in Hostinger
- [ ] Email account password is correct and saved
- [ ] System configuration uses the correct email and password
- [ ] SMTP settings match Hostinger requirements
- [ ] Email template is enabled in database

**During Testing:**
- [ ] Test with multiple email providers (Gmail, Yahoo, Outlook)
- [ ] Check spam/junk folders thoroughly
- [ ] Wait 5-10 minutes for delivery
- [ ] Try both username and email in password recovery
- [ ] Check Hostinger email logs if available

**If Still Not Working:**
- [ ] Try PHP mail instead of SMTP
- [ ] Use different SMTP server (smtp.hostinger.com)
- [ ] Contact Hostinger support about email restrictions
- [ ] Verify domain DNS settings are correct

---

## 📞 Hostinger Support

If email still doesn't work after following this guide:

1. **Contact Hostinger Support:**
   - Live chat or support ticket
   - Mention: "Email sending issues with SMTP authentication"
   - Provide: Your domain name and email account details

2. **Ask Hostinger About:**
   - Email sending restrictions on your plan
   - SMTP server settings for your domain
   - Any blocks on outgoing email
   - Email authentication requirements

3. **Alternative Hosting Email Services:**
   - Some shared hosting plans have email limitations
   - Consider upgrading plan if needed
   - Or use external email service (SendGrid, Mailgun)

---

## ✅ Success Indicators

**When email is working correctly:**
- ✅ Email account exists and is accessible
- ✅ SMTP authentication succeeds
- ✅ Test emails are delivered within 5 minutes
- ✅ Password recovery emails arrive in inbox
- ✅ Email links work correctly
- ✅ No error messages in system logs

**Most email delivery issues on Hostinger are resolved by simply creating the sender email account!**
