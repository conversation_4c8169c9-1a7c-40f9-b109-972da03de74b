<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_CloudResourceManager_FolderOperation extends Google_Model
{
  public $destinationParent;
  public $displayName;
  public $operationType;
  public $sourceParent;

  public function setDestinationParent($destinationParent)
  {
    $this->destinationParent = $destinationParent;
  }
  public function getDestinationParent()
  {
    return $this->destinationParent;
  }
  public function setDisplayName($displayName)
  {
    $this->displayName = $displayName;
  }
  public function getDisplayName()
  {
    return $this->displayName;
  }
  public function setOperationType($operationType)
  {
    $this->operationType = $operationType;
  }
  public function getOperationType()
  {
    return $this->operationType;
  }
  public function setSourceParent($sourceParent)
  {
    $this->sourceParent = $sourceParent;
  }
  public function getSourceParent()
  {
    return $this->sourceParent;
  }
}
