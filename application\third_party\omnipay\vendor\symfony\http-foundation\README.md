HttpFoundation Component
========================

The HttpFoundation component defines an object-oriented layer for the HTTP
specification.

Sponsor
-------

The HttpFoundation component for Symfony 5.4/6.0 is [backed][1] by [<PERSON><PERSON>][2].

Laravel is a PHP web development framework that is passionate about maximum developer
happiness. Laravel is built using a variety of bespoke and Symfony based components.

Help Symfony by [sponsoring][3] its development!

Resources
---------

 * [Documentation](https://symfony.com/doc/current/components/http_foundation.html)
 * [Contributing](https://symfony.com/doc/current/contributing/index.html)
 * [Report issues](https://github.com/symfony/symfony/issues) and
   [send Pull Requests](https://github.com/symfony/symfony/pulls)
   in the [main Symfony repository](https://github.com/symfony/symfony)

[1]: https://symfony.com/backers
[2]: https://laravel.com/
[3]: https://symfony.com/sponsor
