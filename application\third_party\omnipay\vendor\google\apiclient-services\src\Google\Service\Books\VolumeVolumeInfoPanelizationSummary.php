<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Books_VolumeVolumeInfoPanelizationSummary extends Google_Model
{
  public $containsEpubBubbles;
  public $containsImageBubbles;
  public $epubBubbleVersion;
  public $imageBubbleVersion;

  public function setContainsEpubBubbles($containsEpubBubbles)
  {
    $this->containsEpubBubbles = $containsEpubBubbles;
  }
  public function getContainsEpubBubbles()
  {
    return $this->containsEpubBubbles;
  }
  public function setContainsImageBubbles($containsImageBubbles)
  {
    $this->containsImageBubbles = $containsImageBubbles;
  }
  public function getContainsImageBubbles()
  {
    return $this->containsImageBubbles;
  }
  public function setEpubBubbleVersion($epubBubbleVersion)
  {
    $this->epubBubbleVersion = $epubBubbleVersion;
  }
  public function getEpubBubbleVersion()
  {
    return $this->epubBubbleVersion;
  }
  public function setImageBubbleVersion($imageBubbleVersion)
  {
    $this->imageBubbleVersion = $imageBubbleVersion;
  }
  public function getImageBubbleVersion()
  {
    return $this->imageBubbleVersion;
  }
}
