<?php
/**
 * Database Connection Test for Hostinger
 * Upload this to: /domains/passdrc.com/public_html/school/
 * Access via: https://school.passdrc.com/test_database.php
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🗄️ Database Connection Test</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";

// Load database configuration
if (file_exists('application/config/database.php')) {
    echo "<p>✅ <strong>Database config file found</strong></p>";
    
    // Include the config file
    include 'application/config/database.php';
    
    $hostname = $db['default']['hostname'];
    $username = $db['default']['username'];
    $password = $db['default']['password'];
    $database = $db['default']['database'];
    
    echo "<h2>📋 Database Configuration</h2>";
    echo "<p><strong>Host:</strong> $hostname</p>";
    echo "<p><strong>Database:</strong> $database</p>";
    echo "<p><strong>Username:</strong> $username</p>";
    echo "<p><strong>Password:</strong> " . str_repeat('*', strlen($password)) . "</p>";
    
    echo "<h2>🔌 Connection Test</h2>";
    
    try {
        // Test connection
        $connection = new mysqli($hostname, $username, $password, $database);
        
        if ($connection->connect_error) {
            echo "<p>❌ <strong>Connection Failed:</strong> " . $connection->connect_error . "</p>";
            echo "<p><strong>Error Number:</strong> " . $connection->connect_errno . "</p>";
            
            // Common solutions
            echo "<h3>💡 Possible Solutions:</h3>";
            echo "<ul>";
            echo "<li>Check database credentials in Hostinger control panel</li>";
            echo "<li>Ensure database user has proper permissions</li>";
            echo "<li>Try using '127.0.0.1' instead of 'localhost'</li>";
            echo "<li>Check if database is active and not suspended</li>";
            echo "</ul>";
            
        } else {
            echo "<p>✅ <strong>Database Connection Successful!</strong></p>";
            echo "<p><strong>MySQL Version:</strong> " . $connection->server_info . "</p>";
            
            // Test critical tables
            $tables_to_check = ['global_settings', 'login_credential', 'branch'];
            echo "<h3>📊 Table Check</h3>";
            
            foreach ($tables_to_check as $table) {
                $result = $connection->query("SHOW TABLES LIKE '$table'");
                if ($result && $result->num_rows > 0) {
                    echo "<p>✅ <strong>$table:</strong> Exists</p>";
                    
                    // For global_settings, try to read data
                    if ($table === 'global_settings') {
                        $data = $connection->query("SELECT id, institute_name FROM $table WHERE id = 1");
                        if ($data && $data->num_rows > 0) {
                            $row = $data->fetch_assoc();
                            echo "<p>&nbsp;&nbsp;&nbsp;📝 Institute: " . htmlspecialchars($row['institute_name']) . "</p>";
                        } else {
                            echo "<p>&nbsp;&nbsp;&nbsp;⚠️ No data in global_settings table</p>";
                        }
                    }
                } else {
                    echo "<p>❌ <strong>$table:</strong> Missing</p>";
                }
            }
            
            $connection->close();
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Exception:</strong> " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p>❌ <strong>Database config file not found!</strong></p>";
    echo "<p>Expected location: application/config/database.php</p>";
}

echo "<hr>";
echo "<p><strong>🔍 Next Steps:</strong></p>";
echo "<ul>";
echo "<li>If database connection fails, check credentials in Hostinger control panel</li>";
echo "<li>If connection works, the issue is likely in CodeIgniter bootstrap</li>";
echo "<li>Check Hostinger error logs for more details</li>";
echo "</ul>";
?>
