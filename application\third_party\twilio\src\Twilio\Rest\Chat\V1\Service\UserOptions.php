<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Chat
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Chat\V1\Service;

use Twilio\Options;
use Twilio\Values;

abstract class UserOptions
{
    /**
     * @param string $roleSid The SID of the [Role](https://www.twilio.com/docs/api/chat/rest/roles) assigned to the new User.
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @param string $friendlyName A descriptive string that you create to describe the new resource. This value is often used for display purposes.
     * @return CreateUserOptions Options builder
     */
    public static function create(
        
        string $roleSid = Values::NONE,
        string $attributes = Values::NONE,
        string $friendlyName = Values::NONE

    ): CreateUserOptions
    {
        return new CreateUserOptions(
            $roleSid,
            $attributes,
            $friendlyName
        );
    }




    /**
     * @param string $roleSid The SID of the [Role](https://www.twilio.com/docs/api/chat/rest/roles) assigned to this user.
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @param string $friendlyName A descriptive string that you create to describe the resource. It is often used for display purposes.
     * @return UpdateUserOptions Options builder
     */
    public static function update(
        
        string $roleSid = Values::NONE,
        string $attributes = Values::NONE,
        string $friendlyName = Values::NONE

    ): UpdateUserOptions
    {
        return new UpdateUserOptions(
            $roleSid,
            $attributes,
            $friendlyName
        );
    }

}

class CreateUserOptions extends Options
    {
    /**
     * @param string $roleSid The SID of the [Role](https://www.twilio.com/docs/api/chat/rest/roles) assigned to the new User.
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @param string $friendlyName A descriptive string that you create to describe the new resource. This value is often used for display purposes.
     */
    public function __construct(
        
        string $roleSid = Values::NONE,
        string $attributes = Values::NONE,
        string $friendlyName = Values::NONE

    ) {
        $this->options['roleSid'] = $roleSid;
        $this->options['attributes'] = $attributes;
        $this->options['friendlyName'] = $friendlyName;
    }

    /**
     * The SID of the [Role](https://www.twilio.com/docs/api/chat/rest/roles) assigned to the new User.
     *
     * @param string $roleSid The SID of the [Role](https://www.twilio.com/docs/api/chat/rest/roles) assigned to the new User.
     * @return $this Fluent Builder
     */
    public function setRoleSid(string $roleSid): self
    {
        $this->options['roleSid'] = $roleSid;
        return $this;
    }

    /**
     * A valid JSON string that contains application-specific data.
     *
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * A descriptive string that you create to describe the new resource. This value is often used for display purposes.
     *
     * @param string $friendlyName A descriptive string that you create to describe the new resource. This value is often used for display purposes.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Chat.V1.CreateUserOptions ' . $options . ']';
    }
}




class UpdateUserOptions extends Options
    {
    /**
     * @param string $roleSid The SID of the [Role](https://www.twilio.com/docs/api/chat/rest/roles) assigned to this user.
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @param string $friendlyName A descriptive string that you create to describe the resource. It is often used for display purposes.
     */
    public function __construct(
        
        string $roleSid = Values::NONE,
        string $attributes = Values::NONE,
        string $friendlyName = Values::NONE

    ) {
        $this->options['roleSid'] = $roleSid;
        $this->options['attributes'] = $attributes;
        $this->options['friendlyName'] = $friendlyName;
    }

    /**
     * The SID of the [Role](https://www.twilio.com/docs/api/chat/rest/roles) assigned to this user.
     *
     * @param string $roleSid The SID of the [Role](https://www.twilio.com/docs/api/chat/rest/roles) assigned to this user.
     * @return $this Fluent Builder
     */
    public function setRoleSid(string $roleSid): self
    {
        $this->options['roleSid'] = $roleSid;
        return $this;
    }

    /**
     * A valid JSON string that contains application-specific data.
     *
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * A descriptive string that you create to describe the resource. It is often used for display purposes.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It is often used for display purposes.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Chat.V1.UpdateUserOptions ' . $options . ']';
    }
}

