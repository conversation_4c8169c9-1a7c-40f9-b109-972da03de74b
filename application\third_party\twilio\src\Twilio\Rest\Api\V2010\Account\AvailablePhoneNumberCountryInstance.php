<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Api\V2010\Account;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Rest\Api\V2010\Account\AvailablePhoneNumberCountry\VoipList;
use Twilio\Rest\Api\V2010\Account\AvailablePhoneNumberCountry\NationalList;
use Twilio\Rest\Api\V2010\Account\AvailablePhoneNumberCountry\MobileList;
use Twilio\Rest\Api\V2010\Account\AvailablePhoneNumberCountry\MachineToMachineList;
use Twilio\Rest\Api\V2010\Account\AvailablePhoneNumberCountry\TollFreeList;
use Twilio\Rest\Api\V2010\Account\AvailablePhoneNumberCountry\SharedCostList;
use Twilio\Rest\Api\V2010\Account\AvailablePhoneNumberCountry\LocalList;


/**
 * @property string|null $countryCode
 * @property string|null $country
 * @property string|null $uri
 * @property bool|null $beta
 * @property array|null $subresourceUris
 */
class AvailablePhoneNumberCountryInstance extends InstanceResource
{
    protected $_voip;
    protected $_national;
    protected $_mobile;
    protected $_machineToMachine;
    protected $_tollFree;
    protected $_sharedCost;
    protected $_local;

    /**
     * Initialize the AvailablePhoneNumberCountryInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $accountSid The SID of the [Account](https://www.twilio.com/docs/iam/api/account) requesting the available phone number Country resource.
     * @param string $countryCode The [ISO-3166-1](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) country code of the country to fetch available phone number information about.
     */
    public function __construct(Version $version, array $payload, string $accountSid, string $countryCode = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'countryCode' => Values::array_get($payload, 'country_code'),
            'country' => Values::array_get($payload, 'country'),
            'uri' => Values::array_get($payload, 'uri'),
            'beta' => Values::array_get($payload, 'beta'),
            'subresourceUris' => Values::array_get($payload, 'subresource_uris'),
        ];

        $this->solution = ['accountSid' => $accountSid, 'countryCode' => $countryCode ?: $this->properties['countryCode'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return AvailablePhoneNumberCountryContext Context for this AvailablePhoneNumberCountryInstance
     */
    protected function proxy(): AvailablePhoneNumberCountryContext
    {
        if (!$this->context) {
            $this->context = new AvailablePhoneNumberCountryContext(
                $this->version,
                $this->solution['accountSid'],
                $this->solution['countryCode']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the AvailablePhoneNumberCountryInstance
     *
     * @return AvailablePhoneNumberCountryInstance Fetched AvailablePhoneNumberCountryInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): AvailablePhoneNumberCountryInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Access the voip
     */
    protected function getVoip(): VoipList
    {
        return $this->proxy()->voip;
    }

    /**
     * Access the national
     */
    protected function getNational(): NationalList
    {
        return $this->proxy()->national;
    }

    /**
     * Access the mobile
     */
    protected function getMobile(): MobileList
    {
        return $this->proxy()->mobile;
    }

    /**
     * Access the machineToMachine
     */
    protected function getMachineToMachine(): MachineToMachineList
    {
        return $this->proxy()->machineToMachine;
    }

    /**
     * Access the tollFree
     */
    protected function getTollFree(): TollFreeList
    {
        return $this->proxy()->tollFree;
    }

    /**
     * Access the sharedCost
     */
    protected function getSharedCost(): SharedCostList
    {
        return $this->proxy()->sharedCost;
    }

    /**
     * Access the local
     */
    protected function getLocal(): LocalList
    {
        return $this->proxy()->local;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.AvailablePhoneNumberCountryInstance ' . \implode(' ', $context) . ']';
    }
}

