<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Directory_Domains extends Google_Collection
{
  protected $collection_key = 'domainAliases';
  public $creationTime;
  protected $domainAliasesType = 'Google_Service_Directory_DomainAlias';
  protected $domainAliasesDataType = 'array';
  public $domainName;
  public $etag;
  public $isPrimary;
  public $kind;
  public $verified;

  public function setCreationTime($creationTime)
  {
    $this->creationTime = $creationTime;
  }
  public function getCreationTime()
  {
    return $this->creationTime;
  }
  public function setDomainAliases($domainAliases)
  {
    $this->domainAliases = $domainAliases;
  }
  public function getDomainAliases()
  {
    return $this->domainAliases;
  }
  public function setDomainName($domainName)
  {
    $this->domainName = $domainName;
  }
  public function getDomainName()
  {
    return $this->domainName;
  }
  public function setEtag($etag)
  {
    $this->etag = $etag;
  }
  public function getEtag()
  {
    return $this->etag;
  }
  public function setIsPrimary($isPrimary)
  {
    $this->isPrimary = $isPrimary;
  }
  public function getIsPrimary()
  {
    return $this->isPrimary;
  }
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  public function getKind()
  {
    return $this->kind;
  }
  public function setVerified($verified)
  {
    $this->verified = $verified;
  }
  public function getVerified()
  {
    return $this->verified;
  }
}
