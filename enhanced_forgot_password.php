<?php
/**
 * Enhanced Forgot Password Page
 * Provides both email and security question recovery options
 * 
 * This replaces the existing forgot password functionality
 * with dual recovery methods
 */

// Include CodeIgniter bootstrap
require_once('index.php');

// Get CodeIgniter instance
$CI =& get_instance();

// Load necessary models and libraries
$CI->load->model('authentication_model');
$CI->load->library('session');

$message = '';
$message_type = '';

// Handle email-based password recovery (existing functionality)
if ($_POST && isset($_POST['email_recovery'])) {
    $username_or_email = trim($_POST['username_or_email']);
    
    if (empty($username_or_email)) {
        $message = "Please enter your username or email address.";
        $message_type = "error";
    } else {
        // Use existing authentication model method
        $result = $CI->authentication_model->lose_password($username_or_email);
        
        if ($result) {
            $message = "If an account with that username or email exists, a password reset email has been sent.";
            $message_type = "success";
        } else {
            $message = "Username or email not found, or email could not be sent.";
            $message_type = "error";
        }
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width,initial-scale=1" name="viewport">
    <title>Password Recovery - <?php echo get_global_setting('institute_name'); ?></title>
    <link rel="shortcut icon" href="<?php echo base_url('assets/images/favicon.png');?>">
    
    <!-- Web Fonts -->
    <link href="<?php echo is_secure('fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');?>" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo base_url('assets/vendor/font-awesome/css/all.min.css'); ?>">
    
    <!-- Scripts -->
    <script src="<?php echo base_url('assets/vendor/jquery/jquery.js');?>"></script>
    <link rel="stylesheet" href="<?php echo base_url('assets/vendor/sweetalert/sweetalert-custom.css');?>">
    <script src="<?php echo base_url('assets/vendor/sweetalert/sweetalert.min.js');?>"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            position: relative;
            overflow-x: hidden;
        }

        /* Gradient Orbs Background */
        .gradient-orb-1 {
            position: absolute;
            top: 0;
            left: 0;
            width: 24rem;
            height: 24rem;
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(20, 184, 166, 0.2) 100%);
            border-radius: 50%;
            filter: blur(3rem);
            transform: translate(-50%, -50%);
        }

        .gradient-orb-2 {
            position: absolute;
            top: 50%;
            right: 0;
            width: 20rem;
            height: 20rem;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.2) 100%);
            border-radius: 50%;
            filter: blur(3rem);
            transform: translate(50%, -50%);
        }

        .container {
            position: relative;
            z-index: 10;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .recovery-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1.5rem;
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo-section img {
            max-height: 4rem;
            margin-bottom: 1rem;
        }

        .logo-section h1 {
            color: white;
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .logo-section p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.95rem;
        }

        .recovery-options {
            margin-bottom: 2rem;
        }

        .option-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .option-card:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(16, 185, 129, 0.5);
            transform: translateY(-2px);
        }

        .option-card.active {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.5);
        }

        .option-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .option-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.5rem;
            background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-size: 1.1rem;
        }

        .option-title {
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .option-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .recovery-form {
            display: none;
            margin-top: 1.5rem;
        }

        .recovery-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            color: white;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .form-control {
            width: 100%;
            padding: 0.875rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.75rem;
            color: white;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: rgba(16, 185, 129, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .btn {
            width: 100%;
            padding: 0.875rem 1.5rem;
            background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%);
            border: none;
            border-radius: 0.75rem;
            color: white;
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
        }

        .alert {
            padding: 1rem;
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }

        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .back-link {
            text-align: center;
            margin-top: 2rem;
        }

        .back-link a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .back-link a:hover {
            color: white;
        }

        @media (max-width: 640px) {
            .container {
                padding: 1rem;
            }
            
            .recovery-card {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="gradient-orb-1"></div>
    <div class="gradient-orb-2"></div>
    
    <div class="container">
        <div class="recovery-card">
            <div class="logo-section">
                <?php if (file_exists('assets/images/logo.png')): ?>
                    <img src="<?php echo base_url('assets/images/logo.png'); ?>" alt="Logo">
                <?php endif; ?>
                <h1>Password Recovery</h1>
                <p>Choose your preferred recovery method</p>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type; ?>">
                    <i class="fa fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <div class="recovery-options">
                <!-- Security Questions Option -->
                <div class="option-card" onclick="selectOption('security')">
                    <div class="option-header">
                        <div class="option-icon">
                            <i class="fa fa-shield-alt"></i>
                        </div>
                        <div class="option-title">Security Questions</div>
                    </div>
                    <div class="option-description">
                        Answer your security questions for immediate password reset. No email required.
                    </div>
                </div>
                
                <!-- Email Recovery Option -->
                <div class="option-card" onclick="selectOption('email')">
                    <div class="option-header">
                        <div class="option-icon">
                            <i class="fa fa-envelope"></i>
                        </div>
                        <div class="option-title">Email Recovery</div>
                    </div>
                    <div class="option-description">
                        Receive a password reset link via email. Requires email delivery to work.
                    </div>
                </div>
            </div>
            
            <!-- Security Questions Form -->
            <div id="security-form" class="recovery-form">
                <div class="form-group">
                    <a href="security_question_reset.php" class="btn">
                        <i class="fa fa-shield-alt"></i> Start Security Question Recovery
                    </a>
                </div>
            </div>
            
            <!-- Email Recovery Form -->
            <div id="email-form" class="recovery-form">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="username_or_email">Username or Email Address</label>
                        <input type="text" name="username_or_email" id="username_or_email" 
                               class="form-control" placeholder="Enter your username or email" required>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" name="email_recovery" class="btn">
                            <i class="fa fa-envelope"></i> Send Reset Email
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="back-link">
                <a href="<?php echo base_url('authentication'); ?>">
                    <i class="fa fa-arrow-left"></i> Back to Login
                </a>
            </div>
        </div>
    </div>
    
    <script>
        function selectOption(type) {
            // Remove active class from all option cards
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // Hide all forms
            document.querySelectorAll('.recovery-form').forEach(form => {
                form.classList.remove('active');
            });
            
            // Activate selected option
            event.currentTarget.classList.add('active');
            document.getElementById(type + '-form').classList.add('active');
        }
        
        // Auto-select security questions option (recommended)
        document.addEventListener('DOMContentLoaded', function() {
            selectOption('security');
        });
        
        <?php if ($message): ?>
            // Show message with SweetAlert if available
            <?php if ($message_type == 'success'): ?>
                swal({
                    title: "Success!",
                    text: "<?php echo addslashes($message); ?>",
                    type: "success",
                    confirmButtonText: "OK"
                });
            <?php else: ?>
                swal({
                    title: "Error",
                    text: "<?php echo addslashes($message); ?>",
                    type: "error",
                    confirmButtonText: "OK"
                });
            <?php endif; ?>
        <?php endif; ?>
    </script>
</body>
</html>
