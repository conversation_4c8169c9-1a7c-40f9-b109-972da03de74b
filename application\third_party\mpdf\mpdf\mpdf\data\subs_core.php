<?php
$aarr = array(
160 => 160,
161 => 161,
162 => 162,
163 => 163,
164 => 164,
165 => 165,
166 => 166,
167 => 167,
168 => 168,
169 => 169,
170 => 170,
171 => 171,
172 => 172,
173 => 173,
174 => 174,
175 => 175,
176 => 176,
177 => 177,
178 => 178,
179 => 179,
180 => 180,
181 => 181,
182 => 182,
183 => 183,
184 => 184,
185 => 185,
186 => 186,
187 => 187,
188 => 188,
189 => 189,
190 => 190,
191 => 191,
192 => 192,
193 => 193,
194 => 194,
195 => 195,
196 => 196,
197 => 197,
198 => 198,
199 => 199,
200 => 200,
201 => 201,
202 => 202,
203 => 203,
204 => 204,
205 => 205,
206 => 206,
207 => 207,
208 => 208,
209 => 209,
210 => 210,
211 => 211,
212 => 212,
213 => 213,
214 => 214,
215 => 215,
216 => 216,
217 => 217,
218 => 218,
219 => 219,
220 => 220,
221 => 221,
222 => 222,
223 => 223,
224 => 224,
225 => 225,
226 => 226,
227 => 227,
228 => 228,
229 => 229,
230 => 230,
231 => 231,
232 => 232,
233 => 233,
234 => 234,
235 => 235,
236 => 236,
237 => 237,
238 => 238,
239 => 239,
240 => 240,
241 => 241,
242 => 242,
243 => 243,
244 => 244,
245 => 245,
246 => 246,
247 => 247,
248 => 248,
249 => 249,
250 => 250,
251 => 251,
252 => 252,
253 => 253,
254 => 254,
255 => 255,
338 => 140,
339 => 156,
352 => 138,
353 => 154,
376 => 159,
381 => 142,
382 => 158,
402 => 131,
710 => 136,
732 => 152,
8211 => 150,
8212 => 151,
8216 => 145,
8217 => 146,
8218 => 130,
8220 => 147,
8221 => 148,
8222 => 132,
8224 => 134,
8225 => 135,
8226 => 149,
8230 => 133,
8240 => 137,
8249 => 139,
8250 => 155,
8364 => 128,
8482 => 153
);

$zarr = array(
8594 => 213,
8596 => 214,
8597 => 215,
9312 => 172,
9313 => 173,
9314 => 174,
9315 => 175,
9316 => 176,
9317 => 177,
9318 => 178,
9319 => 179,
9320 => 180,
9321 => 181,
9632 => 110,
9650 => 115,
9660 => 116,
9670 => 117,
9679 => 108,
9687 => 119,
9733 => 72,
9742 => 37,
9755 => 42,
9758 => 43,
9824 => 171,
9827 => 168,
9829 => 170,
9830 => 169,
9985 => 33,
9986 => 34,
9987 => 35,
9988 => 36,
9990 => 38,
9991 => 39,
9992 => 40,
9993 => 41,
9996 => 44,
9997 => 45,
9998 => 46,
9999 => 47,
10000 => 48,
10001 => 49,
10002 => 50,
10003 => 51,
10004 => 52,
10005 => 53,
10006 => 54,
10007 => 55,
10008 => 56,
10009 => 57,
10010 => 58,
10011 => 59,
10012 => 60,
10013 => 61,
10014 => 62,
10015 => 63,
10016 => 64,
10017 => 65,
10018 => 66,
10019 => 67,
10020 => 68,
10021 => 69,
10022 => 70,
10023 => 71,
10025 => 73,
10026 => 74,
10027 => 75,
10028 => 76,
10029 => 77,
10030 => 78,
10031 => 79,
10032 => 80,
10033 => 81,
10034 => 82,
10035 => 83,
10036 => 84,
10037 => 85,
10038 => 86,
10039 => 87,
10040 => 88,
10041 => 89,
10042 => 90,
10043 => 91,
10044 => 92,
10045 => 93,
10046 => 94,
10047 => 95,
10048 => 96,
10049 => 97,
10050 => 98,
10051 => 99,
10052 => 100,
10053 => 101,
10054 => 102,
10055 => 103,
10056 => 104,
10057 => 105,
10058 => 106,
10059 => 107,
10061 => 109,
10063 => 111,
10064 => 112,
10065 => 113,
10066 => 114,
10070 => 118,
10072 => 120,
10073 => 121,
10074 => 122,
10075 => 123,
10076 => 124,
10077 => 125,
10078 => 126,
10081 => 161,
10082 => 162,
10083 => 163,
10084 => 164,
10085 => 165,
10086 => 166,
10087 => 167,
10102 => 182,
10103 => 183,
10104 => 184,
10105 => 185,
10106 => 186,
10107 => 187,
10108 => 188,
10109 => 189,
10110 => 190,
10111 => 191,
10112 => 192,
10113 => 193,
10114 => 194,
10115 => 195,
10116 => 196,
10117 => 197,
10118 => 198,
10119 => 199,
10120 => 200,
10121 => 201,
10122 => 202,
10123 => 203,
10124 => 204,
10125 => 205,
10126 => 206,
10127 => 207,
10128 => 208,
10129 => 209,
10130 => 210,
10131 => 211,
10132 => 212,
10136 => 216,
10137 => 217,
10138 => 218,
10139 => 219,
10140 => 220,
10141 => 221,
10142 => 222,
10143 => 223,
10144 => 224,
10145 => 225,
10146 => 226,
10147 => 227,
10148 => 228,
10149 => 229,
10150 => 230,
10151 => 231,
10152 => 232,
10153 => 233,
10154 => 234,
10155 => 235,
10156 => 236,
10157 => 237,
10158 => 238,
10159 => 239,
10161 => 241,
10162 => 242,
10163 => 243,
10164 => 244,
10165 => 245,
10166 => 246,
10167 => 247,
10168 => 248,
10169 => 249,
10170 => 250,
10171 => 251,
10172 => 252,
10173 => 253,
10174 => 254
);

$sarr = array(
169 => 227,
172 => 216,
174 => 226,
176 => 176,
177 => 177,
181 => 109,
215 => 180,
247 => 184,
402 => 166,
913 => 65,
914 => 66,
915 => 71,
916 => 68,
917 => 69,
918 => 90,
919 => 72,
920 => 81,
921 => 73,
922 => 75,
923 => 76,
924 => 77,
925 => 78,
926 => 88,
927 => 79,
928 => 80,
929 => 82,
931 => 83,
932 => 84,
933 => 85,
934 => 70,
935 => 67,
936 => 89,
937 => 87,
945 => 97,
946 => 98,
947 => 103,
948 => 100,
949 => 101,
950 => 122,
951 => 104,
952 => 113,
953 => 105,
954 => 107,
955 => 108,
956 => 109,
957 => 110,
958 => 120,
959 => 111,
960 => 112,
961 => 114,
962 => 86,
963 => 115,
964 => 116,
965 => 117,
966 => 102,
967 => 99,
968 => 121,
969 => 119,
977 => 74,
978 => 161,
981 => 106,
982 => 118,
8226 => 183,
8230 => 188,
8242 => 162,
8243 => 178,
8260 => 164,
8465 => 193,
8472 => 195,
8476 => 194,
8482 => 228,
8486 => 87,
8501 => 192,
8592 => 172,
8593 => 173,
8594 => 174,
8595 => 175,
8596 => 171,
8629 => 191,
8656 => 220,
8657 => 221,
8658 => 222,
8659 => 223,
8660 => 219,
8704 => 34,
8706 => 182,
8707 => 36,
8709 => 198,
8710 => 68,
8711 => 209,
8712 => 206,
8713 => 207,
8715 => 39,
8719 => 213,
8721 => 229,
8722 => 45,
8725 => 164,
8727 => 42,
8730 => 214,
8733 => 181,
8734 => 165,
8736 => 208,
8743 => 217,
8744 => 218,
8745 => 199,
8746 => 200,
8747 => 242,
8756 => 92,
8764 => 126,
8773 => 64,
8776 => 187,
8800 => 185,
8801 => 186,
8804 => 163,
8805 => 179,
8834 => 204,
8835 => 201,
8836 => 203,
8838 => 205,
8839 => 202,
8853 => 197,
8855 => 196,
8869 => 94,
8901 => 215,
8992 => 243,
8993 => 245,
9001 => 225,
9002 => 241,
9674 => 224,
9824 => 170,
9827 => 167,
9829 => 169,
9830 => 168
);
