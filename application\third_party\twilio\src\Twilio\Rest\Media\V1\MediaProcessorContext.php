<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Media
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Media\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class MediaProcessorContext extends InstanceContext
    {
    /**
     * Initialize the MediaProcessorContext
     *
     * @param Version $version Version that contains the resource
     * @param string $sid The SID of the MediaProcessor resource to fetch.
     */
    public function __construct(
        Version $version,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'sid' =>
            $sid,
        ];

        $this->uri = '/MediaProcessors/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Fetch the MediaProcessorInstance
     *
     * @return MediaProcessorInstance Fetched MediaProcessorInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): MediaProcessorInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new MediaProcessorInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Update the MediaProcessorInstance
     *
     * @param string $status
     * @return MediaProcessorInstance Updated MediaProcessorInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(string $status): MediaProcessorInstance
    {

        $data = Values::of([
            'Status' =>
                $status,
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new MediaProcessorInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Media.V1.MediaProcessorContext ' . \implode(' ', $context) . ']';
    }
}
