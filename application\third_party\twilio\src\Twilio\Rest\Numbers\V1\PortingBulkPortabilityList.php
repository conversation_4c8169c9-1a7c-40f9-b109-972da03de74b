<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Numbers\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Serialize;


class PortingBulkPortabilityList extends ListResource
    {
    /**
     * Construct the PortingBulkPortabilityList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/Porting/Portability';
    }

    /**
     * Create the PortingBulkPortabilityInstance
     *
     * @param string[] $phoneNumbers The phone numbers which portability is to be checked. This should be a list of strings. Phone numbers are in E.164 format (e.g. +16175551212). .
     * @return PortingBulkPortabilityInstance Created PortingBulkPortabilityInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(array $phoneNumbers): PortingBulkPortabilityInstance
    {

        $data = Values::of([
            'PhoneNumbers' =>
                Serialize::map($phoneNumbers,function ($e) { return $e; }),
        ]);

        $payload = $this->version->create('POST', $this->uri, [], $data);

        return new PortingBulkPortabilityInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Constructs a PortingBulkPortabilityContext
     *
     * @param string $sid A 34 character string that uniquely identifies the Portability check.
     */
    public function getContext(
        string $sid
        
    ): PortingBulkPortabilityContext
    {
        return new PortingBulkPortabilityContext(
            $this->version,
            $sid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Numbers.V1.PortingBulkPortabilityList]';
    }
}
