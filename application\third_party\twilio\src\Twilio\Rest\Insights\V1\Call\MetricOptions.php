<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Insights\V1\Call;

use Twilio\Options;
use Twilio\Values;

abstract class MetricOptions
{
    /**
     * @param string $edge The Edge of this Metric. One of `unknown_edge`, `carrier_edge`, `sip_edge`, `sdk_edge` or `client_edge`.
     * @param string $direction The Direction of this Metric. One of `unknown`, `inbound`, `outbound` or `both`.
     * @return ReadMetricOptions Options builder
     */
    public static function read(
        
        string $edge = Values::NONE,
        string $direction = Values::NONE

    ): ReadMetricOptions
    {
        return new ReadMetricOptions(
            $edge,
            $direction
        );
    }

}

class ReadMetricOptions extends Options
    {
    /**
     * @param string $edge The Edge of this Metric. One of `unknown_edge`, `carrier_edge`, `sip_edge`, `sdk_edge` or `client_edge`.
     * @param string $direction The Direction of this Metric. One of `unknown`, `inbound`, `outbound` or `both`.
     */
    public function __construct(
        
        string $edge = Values::NONE,
        string $direction = Values::NONE

    ) {
        $this->options['edge'] = $edge;
        $this->options['direction'] = $direction;
    }

    /**
     * The Edge of this Metric. One of `unknown_edge`, `carrier_edge`, `sip_edge`, `sdk_edge` or `client_edge`.
     *
     * @param string $edge The Edge of this Metric. One of `unknown_edge`, `carrier_edge`, `sip_edge`, `sdk_edge` or `client_edge`.
     * @return $this Fluent Builder
     */
    public function setEdge(string $edge): self
    {
        $this->options['edge'] = $edge;
        return $this;
    }

    /**
     * The Direction of this Metric. One of `unknown`, `inbound`, `outbound` or `both`.
     *
     * @param string $direction The Direction of this Metric. One of `unknown`, `inbound`, `outbound` or `both`.
     * @return $this Fluent Builder
     */
    public function setDirection(string $direction): self
    {
        $this->options['direction'] = $direction;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Insights.V1.ReadMetricOptions ' . $options . ']';
    }
}

