# ✅ Hostinger Upload Checklist - Password Recovery Fix

## 🎯 Quick Reference

**Your Hostinger Details:**
- Domain: `passdrc.com`
- Path: `/public_html/school/`
- File Manager: https://hpanel.hostinger.com/

---

## 📋 Upload Checklist

### **STEP 1: Core System Files (Overwrite Existing)**

**Navigate to each folder and upload:**

- [ ] **Authentication_model.php**
  - 📁 Go to: `application/models/`
  - 📤 Upload: `Authentication_model.php` (overwrite)
  - ✅ Fixes password recovery validation

- [ ] **Email_model.php**
  - 📁 Go to: `application/models/`
  - 📤 Upload: `Email_model.php` (overwrite)
  - ✅ Fixes email sending logic

- [ ] **Mailer.php**
  - 📁 Go to: `application/libraries/`
  - 📤 Upload: `Mailer.php` (overwrite)
  - ✅ Fixes SMTP configuration

- [ ] **config.php**
  - 📁 Go to: `application/config/`
  - 📤 Upload: `config.php` (overwrite)
  - ✅ Fixes CSRF token issues

### **STEP 2: Setup Tools (New Files in Root)**

**Navigate to root folder (`public_html/school/`) and upload:**

- [ ] **fix_csrf_hostinger.php**
  - 📁 Root directory
  - 📤 Upload as new file
  - 🔧 CSRF diagnostic tool

- [ ] **simple_email_setup.php**
  - 📁 Root directory
  - 📤 Upload as new file
  - 📧 Email setup (no CSRF)

- [ ] **setup_email_config.php**
  - 📁 Root directory
  - 📤 Upload as new file
  - ⚙️ Advanced email setup

- [ ] **email_diagnostic.php**
  - 📁 Root directory
  - 📤 Upload as new file
  - 🧪 Email testing tool

---

## 🚀 After Upload - Configuration Steps

### **STEP 3: Fix CSRF Issues**
- [ ] Visit: `https://passdrc.com/public_html/school/fix_csrf_hostinger.php`
- [ ] Click "Extend CSRF Token Lifetime"
- [ ] Click "Extend Session Lifetime"
- [ ] Click "Disable CSRF Regeneration"

### **STEP 4: Configure Email**
- [ ] Visit: `https://passdrc.com/public_html/school/simple_email_setup.php`
- [ ] Enter email settings:
  - System Email: `<EMAIL>`
  - SMTP Host: `mail.passdrc.com`
  - SMTP Port: `587`
  - SMTP Encryption: `TLS`
  - Username: `<EMAIL>`
  - Password: [Your email password]
- [ ] Click "Save Email Configuration"
- [ ] Click "Create Email Template"

### **STEP 5: Test Everything**
- [ ] Visit: `https://passdrc.com/public_html/school/email_diagnostic.php`
- [ ] Run email test
- [ ] Visit: `https://passdrc.com/public_html/school/authentication/forgot`
- [ ] Test password recovery with username
- [ ] Test password recovery with email
- [ ] Check email delivery

### **STEP 6: Clean Up (Security)**
- [ ] Delete: `fix_csrf_hostinger.php`
- [ ] Delete: `simple_email_setup.php`
- [ ] Delete: `setup_email_config.php`
- [ ] Delete: `email_diagnostic.php`

---

## 📂 File Locations Summary

```
/domains/passdrc.com/public_html/school/
├── application/
│   ├── config/
│   │   └── config.php ← UPLOAD HERE (overwrite)
│   ├── libraries/
│   │   └── Mailer.php ← UPLOAD HERE (overwrite)
│   └── models/
│       ├── Authentication_model.php ← UPLOAD HERE (overwrite)
│       └── Email_model.php ← UPLOAD HERE (overwrite)
├── fix_csrf_hostinger.php ← UPLOAD HERE (new)
├── simple_email_setup.php ← UPLOAD HERE (new)
├── setup_email_config.php ← UPLOAD HERE (new)
└── email_diagnostic.php ← UPLOAD HERE (new)
```

---

## 🆘 Quick Troubleshooting

**Can't find the right folder?**
- Start at: `/domains/passdrc.com/public_html/school/`
- Look for `application` folder for core files
- Upload setup tools directly in `school` folder

**Upload failed?**
- Try uploading one file at a time
- Check file permissions (should be 644)
- Use Hostinger File Manager instead of FTP

**CSRF errors still happening?**
- Use `simple_email_setup.php` instead
- This bypasses all CSRF issues
- Works immediately without fixes

**Email not working?**
- Check SMTP credentials are correct
- Try port 465 with SSL instead of 587 with TLS
- Verify email account exists in Hostinger

---

## ✅ Success Indicators

**After completing all steps:**
- ✅ No CSRF token errors
- ✅ Email configuration saves successfully
- ✅ Password recovery accepts username AND email
- ✅ Reset emails are delivered to inbox
- ✅ Password reset links work correctly

**Total Time Estimate: 15-30 minutes**

---

## 🔗 Quick Access URLs

After upload, bookmark these for easy access:

**Configuration:**
- https://passdrc.com/public_html/school/simple_email_setup.php

**Testing:**
- https://passdrc.com/public_html/school/authentication/forgot

**Diagnostic:**
- https://passdrc.com/public_html/school/email_diagnostic.php

**Remember to delete setup files after configuration for security!**
