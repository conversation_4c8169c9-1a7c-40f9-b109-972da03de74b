<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Oauth
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Oauth;

use Twilio\Domain;
use Twilio\Exceptions\TwilioException;
use Twilio\InstanceContext;
use Twilio\Rest\Oauth\V1\DeviceCodeList;
use Twilio\Rest\Oauth\V1\OauthList;
use Twilio\Rest\Oauth\V1\OpenidDiscoveryList;
use Twilio\Rest\Oauth\V1\TokenList;
use Twilio\Rest\Oauth\V1\UserInfoList;
use Twilio\Version;

/**
 * @property DeviceCodeList $deviceCode
 * @property OauthList $oauth
 * @property OpenidDiscoveryList $openidDiscovery
 * @property TokenList $token
 * @property UserInfoList $userInfo
 */
class V1 extends Version
{
    protected $_deviceCode;
    protected $_oauth;
    protected $_openidDiscovery;
    protected $_token;
    protected $_userInfo;

    /**
     * Construct the V1 version of Oauth
     *
     * @param Domain $domain Domain that contains the version
     */
    public function __construct(Domain $domain)
    {
        parent::__construct($domain);
        $this->version = 'v1';
    }

    protected function getDeviceCode(): DeviceCodeList
    {
        if (!$this->_deviceCode) {
            $this->_deviceCode = new DeviceCodeList($this);
        }
        return $this->_deviceCode;
    }

    protected function getOauth(): OauthList
    {
        if (!$this->_oauth) {
            $this->_oauth = new OauthList($this);
        }
        return $this->_oauth;
    }

    protected function getOpenidDiscovery(): OpenidDiscoveryList
    {
        if (!$this->_openidDiscovery) {
            $this->_openidDiscovery = new OpenidDiscoveryList($this);
        }
        return $this->_openidDiscovery;
    }

    protected function getToken(): TokenList
    {
        if (!$this->_token) {
            $this->_token = new TokenList($this);
        }
        return $this->_token;
    }

    protected function getUserInfo(): UserInfoList
    {
        if (!$this->_userInfo) {
            $this->_userInfo = new UserInfoList($this);
        }
        return $this->_userInfo;
    }

    /**
     * Magic getter to lazy load root resources
     *
     * @param string $name Resource to return
     * @return \Twilio\ListResource The requested resource
     * @throws TwilioException For unknown resource
     */
    public function __get(string $name)
    {
        $method = 'get' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return $this->$method();
        }

        throw new TwilioException('Unknown resource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Oauth.V1]';
    }
}
