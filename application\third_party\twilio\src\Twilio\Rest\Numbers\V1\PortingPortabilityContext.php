<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Numbers\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\Version;
use Twilio\InstanceContext;


class PortingPortabilityContext extends InstanceContext
    {
    /**
     * Initialize the PortingPortabilityContext
     *
     * @param Version $version Version that contains the resource
     * @param string $phoneNumber The phone number which portability is to be checked. Phone numbers are in E.164 format (e.g. +16175551212).
     */
    public function __construct(
        Version $version,
        $phoneNumber
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'phoneNumber' =>
            $phoneNumber,
        ];

        $this->uri = '/Porting/Portability/PhoneNumber/' . \rawurlencode($phoneNumber)
        .'';
    }

    /**
     * Fetch the PortingPortabilityInstance
     *
     * @return PortingPortabilityInstance Fetched PortingPortabilityInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): PortingPortabilityInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new PortingPortabilityInstance(
            $this->version,
            $payload,
            $this->solution['phoneNumber']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Numbers.V1.PortingPortabilityContext ' . \implode(' ', $context) . ']';
    }
}
