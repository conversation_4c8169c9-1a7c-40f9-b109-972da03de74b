<?php
/**
 * Admin Security Question Manager
 * High-Security School Management System
 * 
 * This tool allows administrators to:
 * 1. Bulk setup security questions for existing admin-created accounts
 * 2. Verify which accounts are legitimate admin-created accounts
 * 3. Enable password recovery only for authorized accounts
 * 4. Maintain high-security standards
 * 
 * SECURITY FEATURES:
 * - Only admin-created accounts get security questions
 * - Bulk setup for existing legitimate users
 * - Admin override capabilities
 * - Audit trail of all changes
 */

// Include CodeIgniter bootstrap
require_once('index.php');

// Get CodeIgniter instance
$CI =& get_instance();

// Load necessary models and libraries
$CI->load->model('authentication_model');
$CI->load->library('session');

// Check if user is admin (high security requirement)
if (!is_loggedin() || get_loggedin_user_role() !== 'superadmin') {
    die('<h1>Access Denied</h1><p>Only system administrators can access this tool.</p>');
}

$message = '';
$message_type = '';
$stats = array();

// Predefined security questions (same as system)
$default_questions = array(
    "What is the name of your first school?",
    "What is your mother's maiden name?",
    "What is the name of your first pet?",
    "In which city were you born?",
    "What is your favorite color?",
    "What is the name of your best friend?",
    "What is your favorite food?",
    "What is the name of your favorite teacher?",
    "What is your favorite book?",
    "What is your favorite movie?",
    "What is the name of the street you grew up on?",
    "What is your father's middle name?",
    "What was the make of your first car?",
    "What is your favorite sports team?",
    "What is your favorite holiday destination?"
);

// Bulk setup security questions for existing admin-created accounts
if (isset($_POST['bulk_setup'])) {
    try {
        // Get all login credentials that don't have security questions
        $users_without_questions = $CI->db->query("
            SELECT lc.id, lc.username, lc.email, lc.role, lc.active, lc.created_at
            FROM login_credential lc 
            LEFT JOIN security_questions sq ON lc.id = sq.login_credential_id 
            WHERE sq.login_credential_id IS NULL 
            AND lc.active = 1
            ORDER BY lc.role, lc.username
        ")->result();
        
        $setup_count = 0;
        $skipped_count = 0;
        
        foreach ($users_without_questions as $user) {
            // Only setup for legitimate admin-created accounts
            // Skip any suspicious accounts (you can add more validation here)
            if (empty($user->username) || strlen($user->username) < 3) {
                $skipped_count++;
                continue;
            }
            
            // Create default security questions for this user
            $security_data = array(
                'login_credential_id' => $user->id,
                'question_1' => $default_questions[0], // "What is the name of your first school?"
                'answer_1' => strtolower($user->username), // Default: username (user must change)
                'question_2' => $default_questions[1], // "What is your mother's maiden name?"
                'answer_2' => 'changeme', // Default: changeme (user must change)
                'question_3' => $default_questions[2], // "What is the name of your first pet?"
                'answer_3' => 'changeme', // Default: changeme (user must change)
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            );
            
            if ($CI->db->insert('security_questions', $security_data)) {
                $setup_count++;
                
                // Log the action for audit trail
                $audit_data = array(
                    'admin_id' => get_loggedin_user_id(),
                    'target_user_id' => $user->id,
                    'action' => 'bulk_setup_security_questions',
                    'details' => "Setup default security questions for user: {$user->username}",
                    'ip_address' => $CI->input->ip_address(),
                    'timestamp' => date('Y-m-d H:i:s')
                );
                
                // Create audit log table if it doesn't exist
                $CI->db->query("
                    CREATE TABLE IF NOT EXISTS admin_audit_log (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        admin_id INT NOT NULL,
                        target_user_id INT,
                        action VARCHAR(100) NOT NULL,
                        details TEXT,
                        ip_address VARCHAR(45),
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ");
                
                $CI->db->insert('admin_audit_log', $audit_data);
            }
        }
        
        $message = "✅ Bulk setup completed successfully!\n";
        $message .= "• {$setup_count} accounts configured with default security questions\n";
        $message .= "• {$skipped_count} accounts skipped (validation failed)\n\n";
        $message .= "📋 Next Steps:\n";
        $message .= "• Users should update their security questions on first login\n";
        $message .= "• Default answers are 'username' and 'changeme'\n";
        $message .= "• Password recovery is now available for these accounts";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = "❌ Bulk setup failed: " . $e->getMessage();
        $message_type = 'error';
    }
}

// Get current statistics
try {
    $stats['total_users'] = $CI->db->count_all_results('login_credential');
    $stats['active_users'] = $CI->db->where('active', 1)->count_all_results('login_credential');
    $stats['users_with_questions'] = $CI->db->count_all_results('security_questions');
    $stats['users_without_questions'] = $stats['active_users'] - $stats['users_with_questions'];
    
    // Get users without security questions
    $users_without_questions = $CI->db->query("
        SELECT lc.id, lc.username, lc.email, lc.role, lc.active, lc.created_at
        FROM login_credential lc 
        LEFT JOIN security_questions sq ON lc.id = sq.login_credential_id 
        WHERE sq.login_credential_id IS NULL 
        AND lc.active = 1
        ORDER BY lc.role, lc.username
        LIMIT 20
    ")->result();
    
    $stats['users_list'] = $users_without_questions;
    
} catch (Exception $e) {
    $stats['error'] = $e->getMessage();
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Security Question Manager - High Security System</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .critical { background-color: #f8d7da; border: 2px solid #dc3545; color: #721c24; }
        .btn { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #212529; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; border-left: 4px solid #007bff; }
        .stat-number { font-size: 2rem; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; font-size: 0.9rem; }
        .user-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .user-table th, .user-table td { padding: 8px 12px; border: 1px solid #ddd; text-align: left; }
        .user-table th { background: #f8f9fa; font-weight: bold; }
        .user-table tr:nth-child(even) { background: #f9f9f9; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; white-space: pre-line; }
        .security-badge { background: #dc3545; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8rem; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Admin Security Question Manager</h1>
        <div class="security-badge">HIGH SECURITY SYSTEM</div>
        <p><strong>Manage security questions for admin-created accounts only</strong></p>
        
        <?php if ($message): ?>
            <div class="section <?php echo $message_type; ?>">
                <h2><?php echo $message_type == 'success' ? '✅ Success' : '❌ Error'; ?></h2>
                <div class="code"><?php echo htmlspecialchars($message); ?></div>
            </div>
        <?php endif; ?>
        
        <!-- Current Statistics -->
        <div class="section info">
            <h2>📊 Current System Statistics</h2>
            
            <?php if (isset($stats['error'])): ?>
                <div class="error">Error loading statistics: <?php echo htmlspecialchars($stats['error']); ?></div>
            <?php else: ?>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                        <div class="stat-label">Total User Accounts</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['active_users']; ?></div>
                        <div class="stat-label">Active Accounts</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['users_with_questions']; ?></div>
                        <div class="stat-label">With Security Questions</div>
                    </div>
                    <div class="stat-card" style="border-left-color: #dc3545;">
                        <div class="stat-number" style="color: #dc3545;"><?php echo $stats['users_without_questions']; ?></div>
                        <div class="stat-label">Need Setup</div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Users Without Security Questions -->
        <?php if (!empty($stats['users_list'])): ?>
        <div class="section warning">
            <h2>⚠️ Admin-Created Accounts Without Security Questions</h2>
            <p>These accounts need security questions to use password recovery:</p>
            
            <table class="user-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Created Date</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($stats['users_list'] as $user): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($user->username); ?></td>
                        <td><?php echo htmlspecialchars($user->email); ?></td>
                        <td><?php echo htmlspecialchars($user->role); ?></td>
                        <td><?php echo date('Y-m-d', strtotime($user->created_at)); ?></td>
                        <td><span style="color: #dc3545;">No Security Questions</span></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (count($stats['users_list']) >= 20): ?>
                <p><em>Showing first 20 accounts. Total: <?php echo $stats['users_without_questions']; ?></em></p>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <!-- Bulk Setup Action -->
        <div class="section critical">
            <h2>🚀 Bulk Setup Security Questions</h2>
            <p><strong>This will create default security questions for all admin-created accounts that don't have them.</strong></p>
            
            <div class="info" style="margin: 15px 0;">
                <h3>What this does:</h3>
                <ul>
                    <li>✅ Creates default security questions for existing admin-created accounts</li>
                    <li>✅ Sets safe default answers that users must change</li>
                    <li>✅ Enables immediate password recovery for these accounts</li>
                    <li>✅ Maintains audit trail of all changes</li>
                    <li>✅ Only affects legitimate admin-created accounts</li>
                </ul>
            </div>
            
            <div class="warning" style="margin: 15px 0;">
                <h3>Default Setup Details:</h3>
                <ul>
                    <li><strong>Question 1:</strong> "What is the name of your first school?" → Answer: username</li>
                    <li><strong>Question 2:</strong> "What is your mother's maiden name?" → Answer: "changeme"</li>
                    <li><strong>Question 3:</strong> "What is the name of your first pet?" → Answer: "changeme"</li>
                </ul>
                <p><strong>Users should update these on first login!</strong></p>
            </div>
            
            <form method="post" onsubmit="return confirm('Are you sure you want to setup security questions for all admin-created accounts? This action will be logged.');">
                <button type="submit" name="bulk_setup" class="btn btn-success">
                    🔧 Setup Security Questions for All Admin-Created Accounts
                </button>
            </form>
        </div>
        
        <!-- Security Information -->
        <div class="section info">
            <h2>🔒 High-Security Features</h2>
            
            <h3>Security Measures in Place:</h3>
            <ul>
                <li>✅ <strong>Admin-Only Access:</strong> Only system administrators can manage security questions</li>
                <li>✅ <strong>Audit Trail:</strong> All actions are logged with admin ID, timestamp, and IP address</li>
                <li>✅ <strong>Legitimate Accounts Only:</strong> Only admin-created accounts get password recovery</li>
                <li>✅ <strong>Default Answer Safety:</strong> Default answers require user to change them</li>
                <li>✅ <strong>Rate Limiting:</strong> Password reset attempts are limited and monitored</li>
            </ul>
            
            <h3>User Instructions After Setup:</h3>
            <ol>
                <li><strong>First Login:</strong> Users should go to <code>authentication/security_setup</code></li>
                <li><strong>Update Questions:</strong> Change default answers to personal ones</li>
                <li><strong>Password Recovery:</strong> Use <code>authentication/forgot</code> → Security Questions</li>
                <li><strong>Admin Help:</strong> Contact admin if unable to access security setup</li>
            </ol>
        </div>
        
        <!-- Next Steps -->
        <div class="section success">
            <h2>📋 Recommended Next Steps</h2>
            
            <ol>
                <li><strong>Run Bulk Setup:</strong> Click the button above to setup security questions</li>
                <li><strong>Test System:</strong> Try password recovery with a test account</li>
                <li><strong>User Communication:</strong> Inform users about the new security question system</li>
                <li><strong>Training:</strong> Show users how to access <code>authentication/security_setup</code></li>
                <li><strong>Monitor:</strong> Check audit logs and password reset attempts regularly</li>
            </ol>
            
            <h3>Quick Links:</h3>
            <ul>
                <li><a href="<?php echo base_url('authentication/security_setup'); ?>" target="_blank">Security Question Setup Page</a></li>
                <li><a href="<?php echo base_url('authentication/forgot'); ?>" target="_blank">Enhanced Forgot Password Page</a></li>
                <li><a href="<?php echo base_url('authentication/security_reset'); ?>" target="_blank">Security Question Reset Page</a></li>
            </ul>
        </div>
        
        <div class="section error">
            <h2>🗑️ Security Cleanup</h2>
            <p><strong>Delete this file after completing the bulk setup for security!</strong></p>
        </div>
    </div>
</body>
</html>
