<?php
/**
 * CSRF & Session Fix Script for <PERSON><PERSON>
 * This script diagnoses and fixes CSRF token expiration issues
 * 
 * Instructions:
 * 1. Place this file in the root directory of your school management system
 * 2. Access it via browser: http://yourdomain.com/fix_csrf_hostinger.php
 * 3. Run the diagnostics and apply fixes as needed
 * 4. Remove this file after fixing the issues
 */

// Include CodeIgniter bootstrap
require_once('index.php');

// Get CodeIgniter instance
$CI =& get_instance();

// Handle fix actions
$action_performed = false;
$action_message = '';

if ($_POST && isset($_POST['action'])) {
    $action_performed = true;
    
    switch ($_POST['action']) {
        case 'extend_csrf':
            // Extend CSRF token lifetime
            $config_content = file_get_contents(APPPATH . 'config/config.php');
            $config_content = preg_replace(
                '/\$config\[\'csrf_expire\'\]\s*=\s*\d+;/',
                '$config[\'csrf_expire\'] = 14400;', // 4 hours
                $config_content
            );
            file_put_contents(APPPATH . 'config/config.php', $config_content);
            $action_message = 'CSRF token lifetime extended to 4 hours';
            break;
            
        case 'extend_session':
            // Extend session lifetime
            $config_content = file_get_contents(APPPATH . 'config/config.php');
            $config_content = preg_replace(
                '/\$config\[\'sess_expiration\'\]\s*=\s*\d+;/',
                '$config[\'sess_expiration\'] = 14400;', // 4 hours
                $config_content
            );
            file_put_contents(APPPATH . 'config/config.php', $config_content);
            $action_message = 'Session lifetime extended to 4 hours';
            break;
            
        case 'disable_csrf_regen':
            // Disable CSRF regeneration
            $config_content = file_get_contents(APPPATH . 'config/config.php');
            $config_content = preg_replace(
                '/\$config\[\'csrf_regenerate\'\]\s*=\s*(TRUE|FALSE);/',
                '$config[\'csrf_regenerate\'] = FALSE;',
                $config_content
            );
            file_put_contents(APPPATH . 'config/config.php', $config_content);
            $action_message = 'CSRF regeneration disabled for stability';
            break;
            
        case 'fix_session_path':
            // Fix session save path for Hostinger
            $user_ini_content = file_get_contents('.user.ini');
            if (strpos($user_ini_content, 'session.save_path') !== false) {
                $user_ini_content = preg_replace(
                    '/session\.save_path\s*=\s*"[^"]*"/',
                    'session.save_path = "' . sys_get_temp_dir() . '"',
                    $user_ini_content
                );
            } else {
                $user_ini_content .= "\n; Session path fix for Hostinger\n";
                $user_ini_content .= 'session.save_path = "' . sys_get_temp_dir() . "\"\n";
            }
            file_put_contents('.user.ini', $user_ini_content);
            $action_message = 'Session save path fixed for Hostinger environment';
            break;
            
        case 'create_sessions_table':
            // Create sessions table if it doesn't exist
            try {
                $CI->db->query("
                    CREATE TABLE IF NOT EXISTS `rm_sessions` (
                        `id` varchar(128) NOT NULL,
                        `ip_address` varchar(45) NOT NULL,
                        `timestamp` int(10) unsigned DEFAULT 0 NOT NULL,
                        `data` blob NOT NULL,
                        KEY `rm_sessions_timestamp` (`timestamp`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
                ");
                $action_message = 'Sessions table created successfully';
            } catch (Exception $e) {
                $action_message = 'Error creating sessions table: ' . $e->getMessage();
            }
            break;
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>CSRF & Session Fix - Hostinger</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .diagnostic-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .btn { padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CSRF & Session Fix for Hostinger</h1>
        <p><strong>Note:</strong> This script diagnoses and fixes CSRF token expiration issues on Hostinger hosting.</p>
        
        <?php if ($action_performed): ?>
            <div class="diagnostic-section success">
                <strong>✓ Action Completed:</strong> <?php echo htmlspecialchars($action_message); ?>
                <br><small>You may need to refresh the page or clear browser cache to see changes.</small>
            </div>
        <?php endif; ?>
        
        <!-- Current Configuration -->
        <div class="diagnostic-section">
            <h2>📋 Current CSRF & Session Configuration</h2>
            <table>
                <tr><th>Setting</th><th>Current Value</th><th>Recommended</th><th>Status</th></tr>
                <tr>
                    <td>CSRF Protection</td>
                    <td><?php echo $CI->config->item('csrf_protection') ? 'Enabled' : 'Disabled'; ?></td>
                    <td>Enabled</td>
                    <td><?php echo $CI->config->item('csrf_protection') ? '✓' : '❌'; ?></td>
                </tr>
                <tr>
                    <td>CSRF Expire Time</td>
                    <td><?php echo $CI->config->item('csrf_expire'); ?> seconds (<?php echo round($CI->config->item('csrf_expire')/3600, 1); ?> hours)</td>
                    <td>14400 seconds (4 hours)</td>
                    <td><?php echo ($CI->config->item('csrf_expire') >= 14400) ? '✓' : '⚠️'; ?></td>
                </tr>
                <tr>
                    <td>CSRF Regenerate</td>
                    <td><?php echo $CI->config->item('csrf_regenerate') ? 'TRUE' : 'FALSE'; ?></td>
                    <td>FALSE</td>
                    <td><?php echo (!$CI->config->item('csrf_regenerate')) ? '✓' : '⚠️'; ?></td>
                </tr>
                <tr>
                    <td>Session Expiration</td>
                    <td><?php echo $CI->config->item('sess_expiration'); ?> seconds (<?php echo round($CI->config->item('sess_expiration')/3600, 1); ?> hours)</td>
                    <td>14400 seconds (4 hours)</td>
                    <td><?php echo ($CI->config->item('sess_expiration') >= 14400) ? '✓' : '⚠️'; ?></td>
                </tr>
                <tr>
                    <td>Session Driver</td>
                    <td><?php echo $CI->config->item('sess_driver'); ?></td>
                    <td>database</td>
                    <td><?php echo ($CI->config->item('sess_driver') == 'database') ? '✓' : '⚠️'; ?></td>
                </tr>
            </table>
        </div>
        
        <!-- Session Table Check -->
        <div class="diagnostic-section">
            <h2>🗄️ Session Table Status</h2>
            <?php
            try {
                $table_exists = $CI->db->table_exists('rm_sessions');
                if ($table_exists) {
                    $session_count = $CI->db->count_all('rm_sessions');
                    echo '<div class="success"><strong>✓ Sessions table exists</strong><br>Current sessions: ' . $session_count . '</div>';
                } else {
                    echo '<div class="error"><strong>❌ Sessions table missing</strong><br>The rm_sessions table is required for database session storage.</div>';
                    echo '<form method="post" style="margin-top: 10px;"><input type="hidden" name="action" value="create_sessions_table"><button type="submit" class="btn btn-success">Create Sessions Table</button></form>';
                }
            } catch (Exception $e) {
                echo '<div class="error"><strong>❌ Database Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>
        
        <!-- PHP Session Configuration -->
        <div class="diagnostic-section">
            <h2>🐘 PHP Session Configuration</h2>
            <table>
                <tr><th>PHP Setting</th><th>Current Value</th><th>Status</th></tr>
                <tr>
                    <td>session.gc_maxlifetime</td>
                    <td><?php echo ini_get('session.gc_maxlifetime'); ?> seconds</td>
                    <td><?php echo (ini_get('session.gc_maxlifetime') >= 1440) ? '✓' : '⚠️'; ?></td>
                </tr>
                <tr>
                    <td>session.cookie_lifetime</td>
                    <td><?php echo ini_get('session.cookie_lifetime'); ?> seconds</td>
                    <td><?php echo (ini_get('session.cookie_lifetime') == 0) ? '✓' : '⚠️'; ?></td>
                </tr>
                <tr>
                    <td>session.save_path</td>
                    <td><?php echo ini_get('session.save_path') ?: 'Default'; ?></td>
                    <td><?php echo is_writable(ini_get('session.save_path') ?: sys_get_temp_dir()) ? '✓' : '❌'; ?></td>
                </tr>
                <tr>
                    <td>session.cookie_secure</td>
                    <td><?php echo ini_get('session.cookie_secure') ? 'On' : 'Off'; ?></td>
                    <td><?php echo (is_https() && ini_get('session.cookie_secure')) || (!is_https() && !ini_get('session.cookie_secure')) ? '✓' : '⚠️'; ?></td>
                </tr>
            </table>
        </div>
        
        <!-- Quick Fixes -->
        <div class="diagnostic-section">
            <h2>🔧 Quick Fixes</h2>
            <p>Apply these fixes to resolve CSRF token expiration issues:</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
                <form method="post">
                    <input type="hidden" name="action" value="extend_csrf">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        Extend CSRF Token Lifetime<br>
                        <small>(2 hours → 4 hours)</small>
                    </button>
                </form>
                
                <form method="post">
                    <input type="hidden" name="action" value="extend_session">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        Extend Session Lifetime<br>
                        <small>(2 hours → 4 hours)</small>
                    </button>
                </form>
                
                <form method="post">
                    <input type="hidden" name="action" value="disable_csrf_regen">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        Disable CSRF Regeneration<br>
                        <small>(Prevents token changes)</small>
                    </button>
                </form>
                
                <form method="post">
                    <input type="hidden" name="action" value="fix_session_path">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        Fix Session Path<br>
                        <small>(Hostinger compatibility)</small>
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Manual Configuration -->
        <div class="diagnostic-section info">
            <h2>📝 Manual Configuration (Alternative)</h2>
            <p>If quick fixes don't work, manually edit <code>application/config/config.php</code>:</p>
            <div class="code">
// Extend CSRF token lifetime (4 hours)
$config['csrf_expire'] = 14400;

// Extend session lifetime (4 hours)  
$config['sess_expiration'] = 14400;

// Disable CSRF regeneration for stability
$config['csrf_regenerate'] = FALSE;

// Exclude setup scripts from CSRF protection
$config['csrf_exclude_uris'] = array(
    'setup_email_config',
    'email_diagnostic',
    'test_password_recovery'
);
            </div>
        </div>
        
        <!-- Hostinger Specific Issues -->
        <div class="diagnostic-section warning">
            <h2>⚠️ Hostinger Specific Issues</h2>
            <ul>
                <li><strong>Shared Hosting Limitations:</strong> Session storage may be limited on shared hosting</li>
                <li><strong>PHP Configuration:</strong> Some PHP settings may be restricted</li>
                <li><strong>File Permissions:</strong> Ensure session directories are writable</li>
                <li><strong>Memory Limits:</strong> Check if memory limits are sufficient</li>
                <li><strong>Time Zones:</strong> Server time zone differences can affect token expiration</li>
            </ul>
            
            <h3>Recommended Actions:</h3>
            <ol>
                <li>Apply the quick fixes above</li>
                <li>Test the email configuration setup again</li>
                <li>If issues persist, contact Hostinger support about session storage</li>
                <li>Consider using file-based sessions instead of database sessions</li>
            </ol>
        </div>
        
        <!-- Test Links -->
        <div class="diagnostic-section">
            <h2>🧪 Test Configuration</h2>
            <p>After applying fixes, test these pages:</p>
            <a href="setup_email_config.php" class="btn" target="_blank">Email Configuration Setup</a>
            <a href="email_diagnostic.php" class="btn" target="_blank">Email Diagnostic</a>
            <a href="<?php echo base_url('authentication/forgot'); ?>" class="btn" target="_blank">Password Recovery</a>
        </div>
        
        <div class="diagnostic-section error">
            <h2>🗑️ Cleanup</h2>
            <p><strong>Important:</strong> Delete this file after fixing the CSRF issues to prevent security risks.</p>
            <p>Files to remove after configuration:</p>
            <ul>
                <li><code>fix_csrf_hostinger.php</code> (this file)</li>
                <li><code>setup_email_config.php</code> (after email setup)</li>
                <li><code>email_diagnostic.php</code> (after testing)</li>
            </ul>
        </div>
    </div>
</body>
</html>
