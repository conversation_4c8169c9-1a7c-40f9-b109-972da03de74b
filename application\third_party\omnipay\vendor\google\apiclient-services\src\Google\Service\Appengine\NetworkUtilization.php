<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Appengine_NetworkUtilization extends Google_Model
{
  public $targetReceivedBytesPerSecond;
  public $targetReceivedPacketsPerSecond;
  public $targetSentBytesPerSecond;
  public $targetSentPacketsPerSecond;

  public function setTargetReceivedBytesPerSecond($targetReceivedBytesPerSecond)
  {
    $this->targetReceivedBytesPerSecond = $targetReceivedBytesPerSecond;
  }
  public function getTargetReceivedBytesPerSecond()
  {
    return $this->targetReceivedBytesPerSecond;
  }
  public function setTargetReceivedPacketsPerSecond($targetReceivedPacketsPerSecond)
  {
    $this->targetReceivedPacketsPerSecond = $targetReceivedPacketsPerSecond;
  }
  public function getTargetReceivedPacketsPerSecond()
  {
    return $this->targetReceivedPacketsPerSecond;
  }
  public function setTargetSentBytesPerSecond($targetSentBytesPerSecond)
  {
    $this->targetSentBytesPerSecond = $targetSentBytesPerSecond;
  }
  public function getTargetSentBytesPerSecond()
  {
    return $this->targetSentBytesPerSecond;
  }
  public function setTargetSentPacketsPerSecond($targetSentPacketsPerSecond)
  {
    $this->targetSentPacketsPerSecond = $targetSentPacketsPerSecond;
  }
  public function getTargetSentPacketsPerSecond()
  {
    return $this->targetSentPacketsPerSecond;
  }
}
