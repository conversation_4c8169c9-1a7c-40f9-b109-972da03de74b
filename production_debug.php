<?php
/**
 * Production Debug Script for Pie Chart Issues
 * Upload this file to your website root and access it via browser
 * This will help identify why the pie charts are not rendering on production
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Production Debug - Pie Chart Issues</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.debug-section { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.success { color: #10b981; font-weight: bold; }
.error { color: #ef4444; font-weight: bold; }
.warning { color: #f59e0b; font-weight: bold; }
.code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
h2 { color: #1f2937; border-bottom: 2px solid #10b981; padding-bottom: 5px; }
</style></head><body>";

echo "<h1>🔍 Production Debug Report - Pie Chart Issues</h1>";

// 1. Check if files exist and get their modification times
echo "<div class='debug-section'>";
echo "<h2>1. File Upload Verification</h2>";

$files_to_check = [
    'application/views/dashboard/index.php',
    'application/models/Dashboard_model.php',
    'assets/vendor/echarts/echarts.common.min.js'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $mod_time = filemtime($file);
        $readable_time = date('Y-m-d H:i:s', $mod_time);
        echo "<div class='success'>✅ {$file} - EXISTS (Modified: {$readable_time})</div>";
        
        // Check if file was modified recently (within last 24 hours)
        if (time() - $mod_time < 86400) {
            echo "<div class='success'>   → Recently modified (within 24 hours)</div>";
        } else {
            echo "<div class='warning'>   → Last modified more than 24 hours ago</div>";
        }
    } else {
        echo "<div class='error'>❌ {$file} - NOT FOUND</div>";
    }
}
echo "</div>";

// 2. Check dashboard view file content
echo "<div class='debug-section'>";
echo "<h2>2. Dashboard View File Analysis</h2>";

$dashboard_file = 'application/views/dashboard/index.php';
if (file_exists($dashboard_file)) {
    $content = file_get_contents($dashboard_file);
    
    // Check for our specific modifications
    $checks = [
        'Enhanced for production deployment' => strpos($content, 'Enhanced for production deployment') !== false,
        'Modern emerald-blue gradient colors' => strpos($content, 'Modern emerald-blue gradient colors') !== false,
        'Handle empty or zero data scenario' => strpos($content, 'Handle empty or zero data scenario') !== false,
        'incomeExpenseData' => strpos($content, 'incomeExpenseData') !== false,
        'strength_data' => strpos($content, 'strength_data') !== false,
        '#10b981' => strpos($content, '#10b981') !== false,
        '#3b82f6' => strpos($content, '#3b82f6') !== false
    ];
    
    foreach ($checks as $check => $found) {
        if ($found) {
            echo "<div class='success'>✅ Found: {$check}</div>";
        } else {
            echo "<div class='error'>❌ Missing: {$check}</div>";
        }
    }
    
    // Show file size
    $file_size = filesize($dashboard_file);
    echo "<div>📊 File size: " . number_format($file_size) . " bytes</div>";
    
} else {
    echo "<div class='error'>❌ Dashboard file not found!</div>";
}
echo "</div>";

// 3. Check model file content
echo "<div class='debug-section'>";
echo "<h2>3. Dashboard Model File Analysis</h2>";

$model_file = 'application/models/Dashboard_model.php';
if (file_exists($model_file)) {
    $content = file_get_contents($model_file);
    
    // Check for our specific modifications
    $checks = [
        'Enhanced for production deployment' => strpos($content, 'Enhanced for production deployment') !== false,
        'floatval($r[\'dr\'] ?? 0)' => strpos($content, 'floatval($r[\'dr\'] ?? 0)') !== false,
        'floatval($r[\'cr\'] ?? 0)' => strpos($content, 'floatval($r[\'cr\'] ?? 0)') !== false,
        'intval($row->total_student)' => strpos($content, 'intval($row->total_student)') !== false
    ];
    
    foreach ($checks as $check => $found) {
        if ($found) {
            echo "<div class='success'>✅ Found: {$check}</div>";
        } else {
            echo "<div class='error'>❌ Missing: {$check}</div>";
        }
    }
    
    // Show file size
    $file_size = filesize($model_file);
    echo "<div>📊 File size: " . number_format($file_size) . " bytes</div>";
    
} else {
    echo "<div class='error'>❌ Model file not found!</div>";
}
echo "</div>";

// 4. Check ECharts library
echo "<div class='debug-section'>";
echo "<h2>4. ECharts Library Check</h2>";

$echarts_file = 'assets/vendor/echarts/echarts.common.min.js';
if (file_exists($echarts_file)) {
    $file_size = filesize($echarts_file);
    echo "<div class='success'>✅ ECharts library found</div>";
    echo "<div>📊 File size: " . number_format($file_size) . " bytes</div>";
    
    if ($file_size < 100000) {
        echo "<div class='warning'>⚠️ File seems too small - might be corrupted</div>";
    }
} else {
    echo "<div class='error'>❌ ECharts library not found!</div>";
}
echo "</div>";

// 5. Check PHP version and extensions
echo "<div class='debug-section'>";
echo "<h2>5. Server Environment</h2>";
echo "<div>🐘 PHP Version: " . PHP_VERSION . "</div>";
echo "<div>🌐 Server: " . $_SERVER['SERVER_SOFTWARE'] . "</div>";
echo "<div>📁 Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</div>";
echo "<div>🔗 Current Script: " . $_SERVER['SCRIPT_NAME'] . "</div>";
echo "</div>";

// 6. Test data generation
echo "<div class='debug-section'>";
echo "<h2>6. Test Data Generation</h2>";

// Simulate the data that would be generated
echo "<div>Testing data generation similar to your dashboard...</div>";

// Test income vs expense data
$test_income_expense = [
    ['name' => 'expense', 'value' => 0],
    ['name' => 'income', 'value' => 0]
];

echo "<div class='code'>Income vs Expense Test Data:<br>";
echo "JSON: " . json_encode($test_income_expense) . "</div>";

// Test student by class data
$test_student_data = [
    ['value' => 0, 'name' => 'not_found_anything']
];

echo "<div class='code'>Student Quantity Test Data:<br>";
echo "JSON: " . json_encode($test_student_data) . "</div>";

echo "</div>";

// 7. Generate a simple test page
echo "<div class='debug-section'>";
echo "<h2>7. Quick Fix Test</h2>";
echo "<p>If the files are uploaded correctly but charts still don't work, try this:</p>";
echo "<div class='code'>";
echo "1. Clear browser cache completely (Ctrl+Shift+Delete)<br>";
echo "2. Try accessing your dashboard in an incognito/private window<br>";
echo "3. Check browser console for JavaScript errors (F12 → Console)<br>";
echo "4. Verify the ECharts library loads by checking Network tab in browser dev tools";
echo "</div>";
echo "</div>";

echo "<div class='debug-section'>";
echo "<h2>8. Next Steps</h2>";
echo "<div>Based on the results above:</div>";
echo "<ul>";
echo "<li>If files show as 'NOT FOUND' or 'Missing' - re-upload the files</li>";
echo "<li>If files are found but modifications are missing - the upload may have failed</li>";
echo "<li>If everything looks correct - there might be a server caching issue</li>";
echo "<li>Check your browser's developer console for JavaScript errors</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
