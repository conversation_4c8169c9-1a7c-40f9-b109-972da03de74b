; Configuration PHP pour Hostinger - Optimisée pour CodeIgniter
; Système de Gestion Scolaire eLima
; Mise à jour: 2025-07-12

; Limites de mémoire et d'exécution (augmentées pour Hostinger)
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
default_socket_timeout = 60

; Limites d'upload (optimisées)
upload_max_filesize = 50M
post_max_size = 50M
max_file_uploads = 50

; Variables et paramètres
max_input_vars = 5000
max_input_nesting_level = 64

; Configuration des sessions
session.gc_maxlifetime = 1440
session.cookie_lifetime = 0
session.use_cookies = 1
session.use_only_cookies = 1
session.cookie_httponly = 1
session.cookie_secure = 1
session.save_path = "/tmp"

; Gestion des erreurs
display_errors = Off
display_startup_errors = Off
log_errors = On
error_reporting = E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT & ~E_USER_NOTICE & ~E_USER_DEPRECATED
log_errors_max_len = 1024
ignore_repeated_errors = On

; Timezone
date.timezone = Africa/Kinshasa

; Optimisations pour CodeIgniter
allow_url_fopen = On
allow_url_include = Off
auto_globals_jit = On
register_globals = Off
magic_quotes_gpc = Off

; Optimisations de performance
realpath_cache_size = 4096K
realpath_cache_ttl = 600
opcache.enable = 1
opcache.memory_consumption = 128
opcache.max_accelerated_files = 4000

; Sécurité
expose_php = Off
allow_url_include = Off
sql.safe_mode = Off
