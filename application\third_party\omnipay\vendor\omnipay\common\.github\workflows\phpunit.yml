name: "PHPUnit tests"

on:
  pull_request:
  push:
    branches:
      - "master"

jobs:
  phpunit:
    name: "PHPUnit tests"

    runs-on: ${{ matrix.operating-system }}

    strategy:
      matrix:
        dependencies:
          - "lowest"
          - "highest"
        php-version:
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
        operating-system:
          - "ubuntu-latest"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "pcov"
          php-version: "${{ matrix.php-version }}"
          ini-values: memory_limit=-1
          tools: composer:v2

      - name: "Install lowest dependencies"
        if: ${{ matrix.dependencies == 'lowest' }}
        run: "composer update --prefer-lowest --no-interaction --no-progress --no-suggest"

      - name: "Install highest dependencies"
        if: ${{ matrix.dependencies == 'highest' }}
        run: "composer update --no-interaction --no-progress --no-suggest"

      - name: "Tests"
        run: "vendor/bin/phpunit"
