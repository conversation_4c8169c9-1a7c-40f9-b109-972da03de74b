<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pie Charts Test - School Management System</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #10b981;
            margin-bottom: 10px;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #e2e8f0;
            text-align: center;
        }
        
        .chart {
            width: 100%;
            height: 300px;
        }
        
        .test-controls {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .test-controls h3 {
            color: #3b82f6;
            margin-bottom: 15px;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #10b981;
            color: white;
        }
        
        .btn-secondary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            color: #10b981;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Pie Charts Test Suite</h1>
            <p>Testing the enhanced pie chart implementation for production deployment</p>
        </div>
        
        <div class="test-controls">
            <h3>Test Scenarios</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testEmptyData()">Test Empty Data</button>
                <button class="btn btn-secondary" onclick="testZeroData()">Test Zero Values</button>
                <button class="btn btn-warning" onclick="testNormalData()">Test Normal Data</button>
                <button class="btn btn-primary" onclick="testMixedData()">Test Mixed Data</button>
            </div>
            <div id="status" class="status" style="display: none;"></div>
        </div>
        
        <div class="charts-grid">
            <div class="chart-container">
                <div class="chart-title">Income Vs Expense Of July</div>
                <div id="income_expense_chart" class="chart"></div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">Student Quantity</div>
                <div id="student_quantity_chart" class="chart"></div>
            </div>
        </div>
    </div>

    <script>
        let incomeExpenseChart;
        let studentQuantityChart;
        
        // Initialize charts
        function initCharts() {
            const incomeExpenseEl = document.getElementById('income_expense_chart');
            const studentQuantityEl = document.getElementById('student_quantity_chart');
            
            if (incomeExpenseEl) {
                incomeExpenseChart = echarts.init(incomeExpenseEl);
            }
            
            if (studentQuantityEl) {
                studentQuantityChart = echarts.init(studentQuantityEl);
            }
            
            // Test with normal data initially
            testNormalData();
        }
        
        function updateIncomeExpenseChart(data) {
            if (!incomeExpenseChart) return;
            
            // Handle empty or zero data scenario
            if (!data || data.length === 0 || 
                (data.length === 2 && data[0].value === 0 && data[1].value === 0)) {
                data = [
                    {name: 'Income', value: 0.01, itemStyle: {color: '#10b981'}},
                    {name: 'Expense', value: 0.01, itemStyle: {color: '#3b82f6'}}
                ];
            } else {
                // Apply colors to existing data
                if (data.length >= 2) {
                    data[0].itemStyle = {color: '#3b82f6'}; // Blue for income
                    data[1].itemStyle = {color: '#10b981'}; // Emerald for expense
                }
            }
            
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.value <= 0.01) {
                            return params.seriesName + '<br/>' + params.name + ' : $0.00 (0%)';
                        }
                        return params.seriesName + '<br/>' + params.name + ' : $' + params.value + ' (' + params.percent + '%)';
                    }
                },
                legend: {
                    show: false
                },
                color: ["#3b82f6", "#10b981"],
                series: [{
                    name: 'Transaction',
                    type: 'pie',
                    radius: ['75%', '90%'],
                    center: ['50%', '50%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 4,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: false
                        },
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: data,
                    animationType: 'scale',
                    animationEasing: 'elasticOut',
                    animationDelay: function (idx) {
                        return Math.random() * 200;
                    }
                }]
            };
            
            incomeExpenseChart.setOption(option);
        }
        
        function updateStudentQuantityChart(data) {
            if (!studentQuantityChart) return;
            
            const modernColors = [
                '#10b981', '#3b82f6', '#06b6d4', '#8b5cf6', '#f59e0b', 
                '#ef4444', '#84cc16', '#f97316', '#ec4899', '#6366f1',
                '#14b8a6', '#a855f7'
            ];
            
            // Handle empty or zero data scenario
            if (!data || data.length === 0 || 
                (data.length === 1 && data[0].value === 0)) {
                data = [
                    {
                        name: 'No Data Available', 
                        value: 1, 
                        itemStyle: {
                            color: '#64748b',
                            opacity: 0.6
                        }
                    }
                ];
            }
            
            // Apply modern colors to data items
            for (let i = 0; i < data.length; i++) {
                if (!data[i].itemStyle) {
                    data[i].itemStyle = {};
                }
                data[i].itemStyle.color = modernColors[i % modernColors.length];
            }
            
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.name === 'No Data Available' && params.value === 1) {
                            return params.seriesName + '<br/>' + params.name + ' : 0 (0%)';
                        }
                        return params.seriesName + '<br/>' + params.name + ' : ' + params.value + ' (' + params.percent + '%)';
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    x: 'center',
                    y: 'bottom',
                    itemWidth: 14,
                    itemHeight: 14,
                    itemGap: 8,
                    textStyle: {
                        fontSize: 12,
                        color: '#94a3b8'
                    },
                    inactiveColor: '#4b5563'
                },
                series: [{
                    name: 'Strength',
                    type: 'pie',
                    radius: ['70%', '85%'],
                    center: ['50%', '46%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 4,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: false
                        },
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: data,
                    animationType: 'scale',
                    animationEasing: 'elasticOut',
                    animationDelay: function (idx) {
                        return Math.random() * 200;
                    }
                }]
            };
            
            studentQuantityChart.setOption(option);
        }
        
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.style.display = 'block';
            status.textContent = message;
            status.className = 'status';
            
            if (type === 'error') {
                status.style.background = 'rgba(239, 68, 68, 0.1)';
                status.style.borderColor = '#ef4444';
                status.style.color = '#ef4444';
            } else {
                status.style.background = 'rgba(16, 185, 129, 0.1)';
                status.style.borderColor = '#10b981';
                status.style.color = '#10b981';
            }
        }
        
        // Test scenarios
        function testEmptyData() {
            updateIncomeExpenseChart([]);
            updateStudentQuantityChart([]);
            showStatus('✅ Empty data test completed - Charts should display fallback data');
        }
        
        function testZeroData() {
            updateIncomeExpenseChart([
                {name: 'Income', value: 0},
                {name: 'Expense', value: 0}
            ]);
            updateStudentQuantityChart([
                {name: 'No Students', value: 0}
            ]);
            showStatus('✅ Zero data test completed - Charts should display with minimal values');
        }
        
        function testNormalData() {
            updateIncomeExpenseChart([
                {name: 'Income', value: 15000},
                {name: 'Expense', value: 8500}
            ]);
            updateStudentQuantityChart([
                {name: 'Grade 1', value: 25},
                {name: 'Grade 2', value: 30},
                {name: 'Grade 3', value: 28},
                {name: 'Grade 4', value: 22},
                {name: 'Grade 5', value: 35}
            ]);
            showStatus('✅ Normal data test completed - Charts should display with full data');
        }
        
        function testMixedData() {
            updateIncomeExpenseChart([
                {name: 'Income', value: 5000},
                {name: 'Expense', value: 0}
            ]);
            updateStudentQuantityChart([
                {name: 'Grade 1', value: 15},
                {name: 'Grade 2', value: 0},
                {name: 'Grade 3', value: 8}
            ]);
            showStatus('✅ Mixed data test completed - Charts should handle partial zero values');
        }
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (incomeExpenseChart) {
                incomeExpenseChart.resize();
            }
            if (studentQuantityChart) {
                studentQuantityChart.resize();
            }
        });
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof echarts !== 'undefined') {
                initCharts();
                showStatus('🎉 Charts initialized successfully! Try different test scenarios above.');
            } else {
                showStatus('❌ ECharts library not loaded. Please check your internet connection.', 'error');
            }
        });
    </script>
</body>
</html>
