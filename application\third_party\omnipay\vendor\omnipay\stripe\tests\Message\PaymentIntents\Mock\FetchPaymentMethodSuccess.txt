HTTP/1.1 200 OK
server: nginx
date: Fri, 09 Aug 2019 00:14:56 GMT
content-type: application/json
content-length: 906
access-control-allow-credentials: true
access-control-allow-methods: GET, POST, HEAD, OPTIONS, DELETE
access-control-allow-origin: *
access-control-expose-headers: Request-Id, Stripe-Manage-Version, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required
access-control-max-age: 300
cache-control: no-cache, no-store
request-id: req_uy4QJE6oEIHUFv
stripe-version: 2018-05-21
strict-transport-security: max-age=31556926; includeSubDomains; preload

{
  "id": "pm_1F55vt2okp6n5dXo2WxJfirJ",
  "object": "payment_method",
  "billing_details": {
    "address": {
      "city": "New Alessia",
      "country": "AU",
      "line1": "679 Berge Course",
      "line2": null,
      "postal_code": "50762-1465",
      "state": "Alabama"
    },
    "email": "<EMAIL>",
    "name": "Jack Beanstalk",
    "phone": null
  },
  "card": {
    "brand": "visa",
    "checks": {
      "address_line1_check": "pass",
      "address_postal_code_check": "pass",
      "cvc_check": "pass"
    },
    "country": "AU",
    "exp_month": 2,
    "exp_year": 2022,
    "fingerprint": "bTL1Ce0N4U41rp8s",
    "funding": "credit",
    "generated_from": null,
    "last4": "0006",
    "three_d_secure_usage": {
      "supported": true
    },
    "wallet": null
  },
  "created": 1565248870,
  "customer": "cus_FaCqpKDSJvFSsC",
  "livemode": false,
  "metadata": {
  },
  "type": "card"
}
