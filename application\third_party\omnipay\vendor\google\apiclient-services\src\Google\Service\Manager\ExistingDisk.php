<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Manager_ExistingDisk extends Google_Model
{
  protected $attachmentType = 'Google_Service_Manager_DiskAttachment';
  protected $attachmentDataType = '';
  public $source;

  public function setAttachment(Google_Service_Manager_DiskAttachment $attachment)
  {
    $this->attachment = $attachment;
  }
  public function getAttachment()
  {
    return $this->attachment;
  }
  public function setSource($source)
  {
    $this->source = $source;
  }
  public function getSource()
  {
    return $this->source;
  }
}
