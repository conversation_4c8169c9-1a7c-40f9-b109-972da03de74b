<?php
/**
 * Test HTTP 500 Error Fixes for Hostinger
 * Upload this to: /domains/passdrc.com/public_html/school/
 * Access via: https://school.passdrc.com/test_fixes.php
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Testing HTTP 500 Error Fixes</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";

echo "<h2>✅ Fix #1: Database Cache Disabled</h2>";

// Test database config
if (file_exists('application/config/database.php')) {
    $content = file_get_contents('application/config/database.php');
    if (strpos($content, "'cache_on' => FALSE") !== false) {
        echo "<p>✅ <strong>Database cache is now DISABLED</strong></p>";
    } else {
        echo "<p>❌ <strong>Database cache is still ENABLED</strong></p>";
    }
} else {
    echo "<p>❌ <strong>Database config file not found</strong></p>";
}

echo "<h2>✅ Fix #2: VIEWPATH Constant Added</h2>";

// Test index.php for VIEWPATH fix
if (file_exists('index.php')) {
    $content = file_get_contents('index.php');
    if (strpos($content, "define('VIEWPATH', APPPATH.'views'.DIRECTORY_SEPARATOR);") !== false) {
        echo "<p>✅ <strong>VIEWPATH constant fallback added to index.php</strong></p>";
    } else {
        echo "<p>❌ <strong>VIEWPATH constant fallback not found</strong></p>";
    }
} else {
    echo "<p>❌ <strong>index.php file not found</strong></p>";
}

echo "<h2>🚀 Testing CodeIgniter Bootstrap</h2>";

try {
    // Set up constants like index.php does
    $system_path = 'system';
    $application_folder = 'application';
    
    if (($_temp = realpath($system_path)) !== FALSE) {
        $system_path = $_temp . DIRECTORY_SEPARATOR;
    }
    
    define('BASEPATH', $system_path);
    define('FCPATH', dirname(__FILE__) . DIRECTORY_SEPARATOR);
    define('APPPATH', $application_folder . DIRECTORY_SEPARATOR);
    define('ENVIRONMENT', 'development');
    
    // Define VIEWPATH early (our fix)
    if (!defined('VIEWPATH')) {
        define('VIEWPATH', APPPATH.'views'.DIRECTORY_SEPARATOR);
    }
    
    echo "<p>✅ <strong>Constants defined successfully</strong></p>";
    echo "<p><strong>VIEWPATH:</strong> " . VIEWPATH . "</p>";
    
    // Test database connection with cache disabled
    include APPPATH . 'config/database.php';
    if (isset($db['default']['cache_on']) && $db['default']['cache_on'] === FALSE) {
        echo "<p>✅ <strong>Database cache confirmed DISABLED in config</strong></p>";
        
        // Try database connection
        $hostname = $db['default']['hostname'];
        $username = $db['default']['username'];
        $password = $db['default']['password'];
        $database = $db['default']['database'];
        
        $connection = new mysqli($hostname, $username, $password, $database);
        if (!$connection->connect_error) {
            echo "<p>✅ <strong>Database connection successful</strong></p>";
            $connection->close();
        } else {
            echo "<p>❌ <strong>Database connection failed:</strong> " . $connection->connect_error . "</p>";
        }
    } else {
        echo "<p>❌ <strong>Database cache is still enabled!</strong></p>";
    }
    
    // Test if we can now load CodeIgniter without errors
    echo "<p>🔄 <strong>Testing CodeIgniter bootstrap...</strong></p>";
    
    ob_start();
    $bootstrap_success = true;
    
    try {
        // This should now work without the cache error
        include_once BASEPATH . 'core/CodeIgniter.php';
        echo "<p>🎉 <strong>SUCCESS! CodeIgniter loaded without errors!</strong></p>";
    } catch (Exception $e) {
        $bootstrap_success = false;
        echo "<p>❌ <strong>CodeIgniter Error:</strong> " . $e->getMessage() . "</p>";
    } catch (Error $e) {
        $bootstrap_success = false;
        echo "<p>❌ <strong>CodeIgniter Fatal Error:</strong> " . $e->getMessage() . "</p>";
    }
    
    $output = ob_get_clean();
    if (!empty($output) && !$bootstrap_success) {
        echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
        echo "<strong>Bootstrap Output:</strong><br>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Test Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>🎯 Final Test</h2>";
echo "<p>If the tests above show success, try accessing your main application:</p>";
echo "<p><a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Test Main Application</a></p>";

echo "<h3>📋 Summary of Fixes Applied:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Disabled database cache</strong> in application/config/database.php</li>";
echo "<li>✅ <strong>Added VIEWPATH constant fallback</strong> in index.php</li>";
echo "<li>✅ <strong>Both fixes target the exact errors</strong> found in the diagnostic</li>";
echo "</ul>";

echo "<p><em>If you still see errors, please share the output above for further troubleshooting.</em></p>";
?>
