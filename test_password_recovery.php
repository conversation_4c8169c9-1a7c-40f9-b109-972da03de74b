<?php
/**
 * Test script for password recovery functionality
 * This script helps verify that the enhanced lose_password method works correctly
 * 
 * Instructions:
 * 1. Place this file in the root directory of your school management system
 * 2. Access it via browser: http://yourdomain.com/test_password_recovery.php
 * 3. Test with both username and email inputs
 * 4. Remove this file after testing is complete
 */

// Include CodeIgniter bootstrap
require_once('index.php');

// Get CodeIgniter instance
$CI =& get_instance();

// Load necessary models
$CI->load->model('authentication_model');

// Test data - replace with actual test data from your database
$test_cases = array(
    array(
        'input' => 'admin',  // Replace with actual username
        'type' => 'username',
        'description' => 'Test with username'
    ),
    array(
        'input' => '<EMAIL>',  // Replace with actual email
        'type' => 'email', 
        'description' => 'Test with email address'
    ),
    array(
        'input' => '<EMAIL>',
        'type' => 'invalid',
        'description' => 'Test with non-existent email (should fail)'
    )
);

?>
<!DOCTYPE html>
<html>
<head>
    <title>Password Recovery Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body>
    <h1>Password Recovery Functionality Test</h1>
    <p><strong>Note:</strong> This is a test script. Remove it after testing is complete.</p>
    
    <?php foreach ($test_cases as $test): ?>
        <div class="test-case">
            <h3><?php echo htmlspecialchars($test['description']); ?></h3>
            <p><strong>Input:</strong> <?php echo htmlspecialchars($test['input']); ?></p>
            <p><strong>Type:</strong> <?php echo htmlspecialchars($test['type']); ?></p>
            
            <?php
            try {
                // Test the lose_password method
                $result = $CI->authentication_model->lose_password($test['input']);
                
                if ($result === true) {
                    echo '<div class="success"><strong>✓ SUCCESS:</strong> Password recovery initiated successfully. Check email for reset instructions.</div>';
                } else {
                    if ($test['type'] === 'invalid') {
                        echo '<div class="info"><strong>ℹ EXPECTED:</strong> No user found with this input (this is expected for invalid test case).</div>';
                    } else {
                        echo '<div class="error"><strong>✗ FAILED:</strong> Password recovery failed. User not found or email not configured.</div>';
                    }
                }
            } catch (Exception $e) {
                echo '<div class="error"><strong>✗ ERROR:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>
    <?php endforeach; ?>
    
    <div class="test-case warning">
        <h3>Manual Testing Instructions</h3>
        <ol>
            <li>Go to the forgot password page: <a href="<?php echo base_url('authentication/forgot'); ?>" target="_blank"><?php echo base_url('authentication/forgot'); ?></a></li>
            <li>Try entering a valid username - should work</li>
            <li>Try entering a valid email address - should now work (this was the bug)</li>
            <li>Try entering an invalid username/email - should show error message</li>
            <li>Check that email notifications are sent correctly</li>
        </ol>
    </div>
    
    <div class="test-case info">
        <h3>What Was Fixed</h3>
        <ul>
            <li><strong>Before:</strong> Password recovery only worked with usernames stored in login_credential table</li>
            <li><strong>After:</strong> Password recovery now works with both usernames AND email addresses</li>
            <li><strong>Enhancement:</strong> Added findUserByEmail() method to search across staff, student, and parent tables</li>
            <li><strong>Improvement:</strong> Better error handling and validation</li>
        </ul>
    </div>
    
    <p><strong>Remember to delete this test file after testing!</strong></p>
</body>
</html>
