{"name": "SumoSelect", "version": "3.0.3", "title": "j<PERSON><PERSON>y <PERSON>", "description": "SumoSelect is a jquery plugin which beautifully renders a single or multiple HTML select element. it can be used for any device e.g for a android device the select will open the default android select popup and vice versa for other devices, and also if it fails to identify the device then it works according to screen resolution which results a select to open in a popup fashion ( like it happens on chrome on android ). its fully customizable and have many other features.  ", "keywords": ["j<PERSON>y", "select", "multiselect", "multiple", "Android", "IOS", "Iphone", "Ipad", "Windows", "cross-browser", "cross-device", "popup", "disable", "selected", "placeholder"], "homepage": "https://github.com/HemantNegi/jquery.sumoselect", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://sqeets.com/"}, "repository": {"type": "git", "url": "https://github.com/HemantNegi/jquery.sumoselect.git"}, "bugs": "https://github.com/HemantNegi/jquery.sumoselect/issues", "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "dependencies": {"jquery": ">=1.6"}}