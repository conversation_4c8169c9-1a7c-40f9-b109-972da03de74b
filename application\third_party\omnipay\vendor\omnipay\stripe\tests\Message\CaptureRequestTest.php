<?php

namespace Omnipay\Stripe\Message;

use Omnipay\Tests\TestCase;

class CaptureRequestTest extends TestCase
{
    public function setUp()
    {
        $this->request = new CaptureRequest($this->getHttpClient(), $this->getHttpRequest());
        $this->request->setTransactionReference('foo');
    }

    public function testEndpoint()
    {
        $this->assertSame('https://api.stripe.com/v1/charges/foo/capture', $this->request->getEndpoint());
    }

    public function testAmount()
    {
        // default is no amount
        $this->assertArrayNotHasKey('amount', $this->request->getData());

        $this->request->setAmount('10.00');

        $data = $this->request->getData();
        $this->assertSame(1000, $data['amount']);
    }

    public function testSendSuccess()
    {
        $this->setMockHttpResponse('CaptureSuccess.txt');
        $response = $this->request->send();

        $this->assertTrue($response->isSuccessful());
        $this->assertFalse($response->isRedirect());
        $this->assertSame('ch_1lvgjcQgrNWUuZ', $response->getTransactionReference());
        $this->assertNull($response->getCardReference());
        $this->assertNull($response->getMessage());
    }

    public function testSendError()
    {
        $this->setMockHttpResponse('CaptureFailure.txt');
        $response = $this->request->send();

        $this->assertFalse($response->isSuccessful());
        $this->assertFalse($response->isRedirect());
        $this->assertNull($response->getTransactionReference());
        $this->assertNull($response->getCardReference());
        $this->assertSame('Charge ch_1lvgjcQgrNWUuZ has already been captured.', $response->getMessage());
    }
}
