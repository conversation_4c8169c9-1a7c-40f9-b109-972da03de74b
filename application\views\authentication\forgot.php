<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width,initial-scale=1" name="viewport">
	<meta name="keywords" content="">
    <meta name="description" content="eLima School Management System">
    <meta name="author" content="eLima">
	<title><?php echo translate('password_restoration');?></title>
	<link rel="shortcut icon" href="<?php echo base_url('icon_elima.svg');?>">

    <!-- Web Fonts  -->
	<link href="<?php echo is_secure('fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');?>" rel="stylesheet">
	<link rel="stylesheet" href="<?php echo base_url('assets/vendor/font-awesome/css/all.min.css'); ?>">
	<script src="<?php echo base_url('assets/vendor/jquery/jquery.js');?>"></script>

	<!-- sweetalert js/css -->
	<link rel="stylesheet" href="<?php echo base_url('assets/vendor/sweetalert/sweetalert-custom.css');?>">
	<script src="<?php echo base_url('assets/vendor/sweetalert/sweetalert.min.js');?>"></script>

	<!-- Modern Forgot Password Styles -->
	<style>
		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			font-family: 'Inter', sans-serif;
			min-height: 100vh;
			background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
			position: relative;
			overflow-x: hidden;
		}

		/* Gradient Orbs Background */
		.gradient-orb-1 {
			position: absolute;
			top: 0;
			left: 0;
			width: 24rem;
			height: 24rem;
			background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(20, 184, 166, 0.2) 100%);
			border-radius: 50%;
			filter: blur(3rem);
			transform: translate(-50%, -50%);
		}

		.gradient-orb-2 {
			position: absolute;
			bottom: 0;
			right: 0;
			width: 24rem;
			height: 24rem;
			background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.2) 100%);
			border-radius: 50%;
			filter: blur(3rem);
			transform: translate(50%, 50%);
		}

		.main-container {
			position: relative;
			z-index: 10;
			min-height: 100vh;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 1rem;
			padding-bottom: 5rem;
		}

		.forgot-card {
			width: 100%;
			max-width: 28rem;
			background: rgba(255, 255, 255, 0.1);
			backdrop-filter: blur(16px);
			border: 1px solid rgba(255, 255, 255, 0.2);
			border-radius: 1rem;
			box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
			padding: 2rem;
		}

		.alert-msg {
			padding: 1rem;
			border-radius: 0.5rem;
			margin-bottom: 1.5rem;
			font-size: 0.875rem;
		}

		.alert-msg:not(.danger) {
			background: rgba(16, 185, 129, 0.1);
			border: 1px solid rgba(16, 185, 129, 0.3);
			color: #10b981;
		}

		.alert-msg.danger {
			background: rgba(239, 68, 68, 0.1);
			border: 1px solid rgba(239, 68, 68, 0.3);
			color: #ef4444;
		}
	</style>

	<script type="text/javascript">
		var base_url = '<?php echo base_url() ?>';
	</script>
</head>
	<body>
		<!-- Gradient Orbs Background -->
		<div class="gradient-orb-1"></div>
		<div class="gradient-orb-2"></div>

		<div class="main-container">
			<div class="forgot-card">
				<!-- Form Header -->
				<div style="text-align: center; margin-bottom: 2rem;">
					<!-- eLima Logo -->
					<div style="margin-bottom: 1.5rem;">
						<img src="<?php echo base_url('eLima_logo.svg'); ?>" alt="eLima Logo" style="height: 4rem; width: auto;">
					</div>
					<h2 style="font-size: 1.875rem; font-weight: 700; color: white; margin-bottom: 0.5rem;">eLima</h2>
					<p style="color: #94a3b8; margin-bottom: 1.5rem;">Récupération de mot de passe</p>
				</div>

				<!-- Alert Messages -->
				<?php
					if($this->session->flashdata('reset_res')){
						if($this->session->flashdata('reset_res') == 'true'){
							echo '<div class="alert-msg">Email de réinitialisation envoyé avec succès. Vérifiez votre email</div>';
						}elseif($this->session->flashdata('reset_res') == 'false'){
							echo '<div class="alert-msg danger">Vous avez entré une mauvaise adresse email</div>';
						}
					}
				?>

				<!-- Contact Information Section -->
				<div style="text-align: center; margin-bottom: 2rem; padding: 1.5rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.75rem; border: 1px solid rgba(255, 255, 255, 0.1);">
					<div style="display: inline-flex; align-items: center; justify-content: center; width: 3rem; height: 3rem; background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%); border-radius: 50%; margin-bottom: 1rem;">
						<i class="fas fa-headset" style="color: white; font-size: 1.25rem;"></i>
					</div>
					<h4 style="color: white; font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem;">
						Besoin d'aide pour récupérer votre mot de passe ?
					</h4>

					<!-- Contact Information -->
					<div style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); border-radius: 0.75rem; padding: 1.5rem; margin-bottom: 1.5rem;">
						<h5 style="color: #10b981; font-size: 1rem; font-weight: 600; margin-bottom: 1rem;">
							<i class="fas fa-phone" style="margin-right: 0.5rem;"></i>
							Contactez notre support
						</h5>

						<!-- Email Support -->
						<div style="display: flex; align-items: center; justify-content: center; margin-bottom: 1rem; color: white;">
							<i class="fas fa-envelope" style="color: #10b981; margin-right: 0.75rem; font-size: 1.1rem;"></i>
							<div>
								<div style="font-weight: 600; font-size: 0.95rem;">Email Support</div>
								<a href="mailto:<EMAIL>" style="color: #10b981; text-decoration: none; font-size: 0.9rem;"><EMAIL></a>
							</div>
						</div>

						<!-- Phone Support -->
						<div style="display: flex; align-items: center; justify-content: center; color: white;">
							<i class="fas fa-phone" style="color: #10b981; margin-right: 0.75rem; font-size: 1.1rem;"></i>
							<div>
								<div style="font-weight: 600; font-size: 0.95rem;">Téléphone</div>
								<a href="tel:+243970688665" style="color: #10b981; text-decoration: none; font-size: 0.9rem;">+243 970 688 665</a>
							</div>
						</div>
					</div>

					<p style="color: #94a3b8; font-size: 0.875rem; line-height: 1.4;">
						Notre équipe de support vous aidera à récupérer l'accès à votre compte rapidement et en toute sécurité.
					</p>
				</div>

				<!-- Email Recovery Form -->
				<div style="margin-bottom: 2rem;">
					<div style="text-align: center; margin-bottom: 1.5rem; padding: 1rem; background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 0.75rem;">
						<div style="display: inline-flex; align-items: center; justify-content: center; width: 2.5rem; height: 2.5rem; background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%); border-radius: 50%; margin-bottom: 0.75rem;">
							<i class="fas fa-envelope" style="color: white; font-size: 1rem;"></i>
						</div>
						<h5 style="color: white; font-weight: 600; margin-bottom: 0.5rem;">Récupération par email</h5>
						<p style="color: #94a3b8; font-size: 0.875rem; margin: 0;">
							Entrez votre nom d'utilisateur ou email pour recevoir un lien de réinitialisation
						</p>
					</div>

					<?php echo form_open($this->uri->uri_string()); ?>
						<!-- Username/Email Field -->
						<div style="margin-bottom: 1.5rem;">
							<div style="position: relative;">
								<i class="fas fa-envelope" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #94a3b8; font-size: 1.25rem; transition: color 0.3s ease;"></i>
								<input
									type="text"
									name="username"
									value="<?php echo set_value('username');?>"
									placeholder="Nom d'utilisateur ou Email"
									autocomplete="off"
									style="width: 100%; height: 3.5rem; padding-left: 3rem; padding-right: 1rem; background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 0.75rem; color: white; font-size: 1rem; transition: all 0.3s ease;"
									onfocus="this.style.borderColor='#10b981'; this.style.boxShadow='0 0 0 3px rgba(16, 185, 129, 0.1)';"
									onblur="this.style.borderColor='rgba(255, 255, 255, 0.2)'; this.style.boxShadow='none';"
								/>
							</div>
							<?php if (form_error('username')): ?>
								<span style="color: #ef4444; font-size: 0.875rem; margin-top: 0.25rem; display: block;"><?php echo form_error('username'); ?></span>
							<?php endif; ?>
						</div>

						<!-- Submit Button -->
						<button
							type="submit"
							id="btn_submit"
							style="width: 100%; height: 3.5rem; background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%); border: none; border-radius: 0.75rem; color: white; font-weight: 600; font-size: 1rem; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1); margin-bottom: 1.5rem;"
							onmouseover="this.style.transform='scale(1.02)'; this.style.boxShadow='0 20px 40px -10px rgba(0, 0, 0, 0.2)';"
							onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 10px 25px -5px rgba(0, 0, 0, 0.1)';"
						>
							<i class="far fa-paper-plane" style="margin-right: 0.5rem;"></i>
							Envoyer les instructions
						</button>
					<?php echo form_close();?>
				</div>

				<!-- Back to Login Link -->
				<div style="text-align: center; margin-top: 1.5rem;">
					<a href="<?php echo base_url("authentication"); ?>" style="color: #10b981; text-decoration: none; font-size: 0.875rem; transition: color 0.3s ease; display: inline-flex; align-items: center; gap: 0.5rem;">
						<i class="fas fa-arrow-left"></i>
						<?php echo translate('back_to_login');?>
					</a>
				</div>

				<!-- Footer -->
				<div style="margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid rgba(255, 255, 255, 0.1); text-align: center;">
					<p style="color: #94a3b8; font-size: 0.875rem;">
						© 2025 eLima - powered by
						<span style="background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; font-weight: 500;">
							PASS-DRC
						</span>
					</p>
				</div>
			</div>
		</div>

		<!-- Footer -->
		<div style="position: fixed; bottom: 0; left: 0; right: 0; padding: 1rem; text-align: center; background: linear-gradient(to top, rgba(15, 23, 42, 0.8) 0%, transparent 100%); backdrop-filter: blur(8px);">
			<p style="color: #64748b; font-size: 0.875rem;">
				© 2025 eLima - powered by
				<span style="background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; font-weight: 500;">
					PASS-DRC
				</span>
			</p>
		</div>

		<!-- JavaScript -->
		<script>
			// Add form validation and loading states
			document.addEventListener('DOMContentLoaded', function() {
				// Form submission handling
				const form = document.querySelector('form');
				if (form) {
					const submitBtn = document.getElementById('btn_submit');
					const originalBtnText = submitBtn.innerHTML;

					form.addEventListener('submit', function() {
						submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>Envoi en cours...';
						submitBtn.disabled = true;

						// Re-enable button after 5 seconds in case of issues
						setTimeout(function() {
							submitBtn.innerHTML = originalBtnText;
							submitBtn.disabled = false;
						}, 5000);
					});
				}
			});
		</script>
	</body>
</html>