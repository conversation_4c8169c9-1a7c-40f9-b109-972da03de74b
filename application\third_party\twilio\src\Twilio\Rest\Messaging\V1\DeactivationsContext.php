<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Messaging\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;


class DeactivationsContext extends InstanceContext
    {
    /**
     * Initialize the DeactivationsContext
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/Deactivations';
    }

    /**
     * Fetch the DeactivationsInstance
     *
     * @param array|Options $options Optional Arguments
     * @return DeactivationsInstance Fetched DeactivationsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(array $options = []): DeactivationsInstance
    {

        $options = new Values($options);

        $params = Values::of([
            'Date' =>
                Serialize::iso8601Date($options['date']),
        ]);

        $payload = $this->version->fetch('GET', $this->uri, $params);

        return new DeactivationsInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Messaging.V1.DeactivationsContext ' . \implode(' ', $context) . ']';
    }
}
