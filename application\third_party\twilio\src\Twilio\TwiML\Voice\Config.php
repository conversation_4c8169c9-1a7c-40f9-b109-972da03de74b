<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\TwiML\Voice;

use <PERSON><PERSON><PERSON>\TwiML\TwiML;

class Config extends TwiML {
    /**
     * Config constructor.
     *
     * @param array $attributes Optional attributes
     */
    public function __construct($attributes = []) {
        parent::__construct('Config', null, $attributes);
    }

    /**
     * Add Name attribute.
     *
     * @param string $name The name of the custom config
     */
    public function setName($name): self {
        return $this->setAttribute('name', $name);
    }

    /**
     * Add Value attribute.
     *
     * @param string $value The value of the custom config
     */
    public function setValue($value): self {
        return $this->setAttribute('value', $value);
    }
}