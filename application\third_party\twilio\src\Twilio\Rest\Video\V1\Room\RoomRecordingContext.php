<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Video\V1\Room;

use Twilio\Exceptions\TwilioException;
use Twilio\Version;
use Twilio\InstanceContext;


class RoomRecordingContext extends InstanceContext
    {
    /**
     * Initialize the RoomRecordingContext
     *
     * @param Version $version Version that contains the resource
     * @param string $roomSid The SID of the room with the RoomRecording resource to delete.
     * @param string $sid The SID of the RoomRecording resource to delete.
     */
    public function __construct(
        Version $version,
        $roomSid,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'roomSid' =>
            $roomSid,
        'sid' =>
            $sid,
        ];

        $this->uri = '/Rooms/' . \rawurlencode($roomSid)
        .'/Recordings/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Delete the RoomRecordingInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the RoomRecordingInstance
     *
     * @return RoomRecordingInstance Fetched RoomRecordingInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): RoomRecordingInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new RoomRecordingInstance(
            $this->version,
            $payload,
            $this->solution['roomSid'],
            $this->solution['sid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Video.V1.RoomRecordingContext ' . \implode(' ', $context) . ']';
    }
}
