<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit7807b821c5856409517b1a9459168887
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            '<PERSON><PERSON><PERSON>ailer\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit7807b821c5856409517b1a9459168887::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit7807b821c5856409517b1a9459168887::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit7807b821c5856409517b1a9459168887::$classMap;

        }, null, ClassLoader::class);
    }
}
