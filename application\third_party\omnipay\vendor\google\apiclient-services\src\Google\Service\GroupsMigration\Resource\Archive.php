<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

/**
 * The "archive" collection of methods.
 * Typical usage is:
 *  <code>
 *   $groupsmigrationService = new Google_Service_GroupsMigration(...);
 *   $archive = $groupsmigrationService->archive;
 *  </code>
 */
class Google_Service_GroupsMigration_Resource_Archive extends Google_Service_Resource
{
  /**
   * Inserts a new mail into the archive of the Google group. (archive.insert)
   *
   * @param string $groupId The group ID
   * @param array $optParams Optional parameters.
   * @return Google_Service_GroupsMigration_Groups
   */
  public function insert($groupId, $optParams = array())
  {
    $params = array('groupId' => $groupId);
    $params = array_merge($params, $optParams);
    return $this->call('insert', array($params), "Google_Service_GroupsMigration_Groups");
  }
}
