<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_QPXExpress_SegmentPricing extends Google_Collection
{
  protected $collection_key = 'freeBaggageOption';
  public $fareId;
  protected $freeBaggageOptionType = 'Google_Service_QPXExpress_FreeBaggageAllowance';
  protected $freeBaggageOptionDataType = 'array';
  public $kind;
  public $segmentId;

  public function setFareId($fareId)
  {
    $this->fareId = $fareId;
  }
  public function getFareId()
  {
    return $this->fareId;
  }
  public function setFreeBaggageOption($freeBaggageOption)
  {
    $this->freeBaggageOption = $freeBaggageOption;
  }
  public function getFreeBaggageOption()
  {
    return $this->freeBaggageOption;
  }
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  public function getKind()
  {
    return $this->kind;
  }
  public function setSegmentId($segmentId)
  {
    $this->segmentId = $segmentId;
  }
  public function getSegmentId()
  {
    return $this->segmentId;
  }
}
