<?php
/**
 * HTTP 500 Error Fix for Hostinger
 * This bypasses CodeIgniter restrictions to test the real issue
 * Upload this to: /domains/passdrc.com/public_html/school/
 * Access via: https://school.passdrc.com/fix_500_error.php
 */

// Enable full error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>🔧 HTTP 500 Error Fix - Hostinger</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";

// Test 1: Direct database connection (bypass CodeIgniter)
echo "<h2>🗄️ Direct Database Test</h2>";

// Manually extract database config
$db_config = null;
$config_file = 'application/config/database.php';

if (file_exists($config_file)) {
    // Read file content and extract database config manually
    $content = file_get_contents($config_file);
    
    // Extract database credentials using regex
    if (preg_match("/'hostname'\s*=>\s*'([^']+)'/", $content, $matches)) {
        $hostname = $matches[1];
    }
    if (preg_match("/'username'\s*=>\s*'([^']+)'/", $content, $matches)) {
        $username = $matches[1];
    }
    if (preg_match("/'password'\s*=>\s*'([^']+)'/", $content, $matches)) {
        $password = $matches[1];
    }
    if (preg_match("/'database'\s*=>\s*'([^']+)'/", $content, $matches)) {
        $database = $matches[1];
    }
    
    if (isset($hostname, $username, $password, $database)) {
        echo "<p>✅ <strong>Database credentials extracted</strong></p>";
        echo "<p><strong>Host:</strong> $hostname</p>";
        echo "<p><strong>Database:</strong> $database</p>";
        echo "<p><strong>Username:</strong> $username</p>";
        
        // Test connection
        try {
            $connection = new mysqli($hostname, $username, $password, $database);
            
            if ($connection->connect_error) {
                echo "<p>❌ <strong>Database Connection Failed:</strong> " . $connection->connect_error . "</p>";
                echo "<p><strong>Error Code:</strong> " . $connection->connect_errno . "</p>";
                
                // Try alternative host
                echo "<h3>🔄 Trying alternative host (127.0.0.1)...</h3>";
                $connection2 = new mysqli('127.0.0.1', $username, $password, $database);
                if ($connection2->connect_error) {
                    echo "<p>❌ <strong>Alternative host also failed:</strong> " . $connection2->connect_error . "</p>";
                } else {
                    echo "<p>✅ <strong>Alternative host (127.0.0.1) works!</strong></p>";
                    echo "<p><strong>🔧 FIX NEEDED:</strong> Change hostname from 'localhost' to '127.0.0.1' in database config</p>";
                    $connection2->close();
                }
                
            } else {
                echo "<p>✅ <strong>Database Connection Successful!</strong></p>";
                echo "<p><strong>MySQL Version:</strong> " . $connection->server_info . "</p>";
                
                // Test critical table
                $result = $connection->query("SELECT COUNT(*) as count FROM global_settings");
                if ($result) {
                    $row = $result->fetch_assoc();
                    echo "<p>✅ <strong>global_settings table accessible, records:</strong> " . $row['count'] . "</p>";
                } else {
                    echo "<p>❌ <strong>Cannot access global_settings table:</strong> " . $connection->error . "</p>";
                }
                
                $connection->close();
            }
            
        } catch (Exception $e) {
            echo "<p>❌ <strong>Database Exception:</strong> " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ <strong>Could not extract database credentials from config file</strong></p>";
    }
} else {
    echo "<p>❌ <strong>Database config file not found</strong></p>";
}

// Test 2: Check CodeIgniter bootstrap issue
echo "<h2>🚀 CodeIgniter Bootstrap Test</h2>";

// Try to manually bootstrap CodeIgniter
try {
    // Set up basic constants
    $system_path = 'system';
    $application_folder = 'application';
    
    if (($_temp = realpath($system_path)) !== FALSE) {
        $system_path = $_temp . DIRECTORY_SEPARATOR;
    }
    
    define('BASEPATH', $system_path);
    define('FCPATH', dirname(__FILE__) . DIRECTORY_SEPARATOR);
    define('APPPATH', $application_folder . DIRECTORY_SEPARATOR);
    define('ENVIRONMENT', 'development'); // Force development mode to see errors
    
    echo "<p>✅ <strong>Constants defined successfully</strong></p>";
    
    // Check if we can load the main CodeIgniter file
    $ci_file = BASEPATH . 'core/CodeIgniter.php';
    if (file_exists($ci_file)) {
        echo "<p>✅ <strong>CodeIgniter core file found</strong></p>";
        
        // Try to include it and see what happens
        echo "<p>🔄 <strong>Attempting to load CodeIgniter...</strong></p>";
        
        // Capture any output/errors
        ob_start();
        $error_occurred = false;
        
        try {
            // This might cause the 500 error, so we'll catch it
            include_once $ci_file;
            echo "<p>✅ <strong>CodeIgniter loaded successfully!</strong></p>";
        } catch (Error $e) {
            $error_occurred = true;
            echo "<p>❌ <strong>CodeIgniter Fatal Error:</strong> " . $e->getMessage() . "</p>";
            echo "<p><strong>File:</strong> " . $e->getFile() . " <strong>Line:</strong> " . $e->getLine() . "</p>";
        } catch (Exception $e) {
            $error_occurred = true;
            echo "<p>❌ <strong>CodeIgniter Exception:</strong> " . $e->getMessage() . "</p>";
        }
        
        $output = ob_get_clean();
        if (!empty($output)) {
            echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo "<strong>CodeIgniter Output:</strong><br>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            echo "</div>";
        }
        
    } else {
        echo "<p>❌ <strong>CodeIgniter core file missing:</strong> $ci_file</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Bootstrap Error:</strong> " . $e->getMessage() . "</p>";
}

// Test 3: Check file permissions
echo "<h2>🔐 File Permissions Check</h2>";
$critical_files = [
    'index.php',
    '.htaccess',
    'application/config/config.php',
    'application/config/database.php',
    'application/cache',
    'application/logs',
    'system/core/CodeIgniter.php'
];

foreach ($critical_files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms_str = substr(sprintf('%o', $perms), -4);
        $readable = is_readable($file) ? '✅' : '❌';
        $writable = is_writable($file) ? '✅' : '❌';
        
        echo "<p><strong>$file:</strong> $perms_str | Read: $readable | Write: $writable</p>";
    } else {
        echo "<p><strong>$file:</strong> ❌ Not found</p>";
    }
}

// Test 4: Check .htaccess content
echo "<h2>🔧 .htaccess Analysis</h2>";
if (file_exists('.htaccess')) {
    $htaccess = file_get_contents('.htaccess');
    echo "<p>✅ <strong>.htaccess file exists</strong></p>";
    
    // Check for problematic rules
    if (strpos($htaccess, 'RewriteEngine On') !== false) {
        echo "<p>✅ URL rewriting enabled</p>";
    } else {
        echo "<p>⚠️ URL rewriting not found</p>";
    }
    
    if (strpos($htaccess, 'index.php') !== false) {
        echo "<p>✅ CodeIgniter routing rules found</p>";
    } else {
        echo "<p>⚠️ CodeIgniter routing rules missing</p>";
    }
} else {
    echo "<p>❌ <strong>.htaccess file missing</strong></p>";
}

echo "<hr>";
echo "<h2>🎯 Diagnosis Summary</h2>";
echo "<p>Based on the tests above, the most likely issues are:</p>";
echo "<ul>";
echo "<li><strong>Database connection problem</strong> - Check if localhost vs 127.0.0.1 works</li>";
echo "<li><strong>CodeIgniter bootstrap failure</strong> - Look for fatal errors in the bootstrap test</li>";
echo "<li><strong>File permissions</strong> - Ensure proper permissions are set</li>";
echo "<li><strong>.htaccess issues</strong> - URL rewriting problems</li>";
echo "</ul>";

echo "<h2>🔧 Recommended Fixes</h2>";
echo "<ol>";
echo "<li>If database connection failed, update hostname in application/config/database.php</li>";
echo "<li>If CodeIgniter bootstrap failed, check the specific error message</li>";
echo "<li>Set proper file permissions: Files=644, Directories=755, Cache/Logs=777</li>";
echo "<li>Check Hostinger error logs for more details</li>";
echo "</ol>";
?>
