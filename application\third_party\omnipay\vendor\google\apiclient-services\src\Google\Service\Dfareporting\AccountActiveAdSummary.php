<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Dfareporting_AccountActiveAdSummary extends Google_Model
{
  public $accountId;
  public $activeAds;
  public $activeAdsLimitTier;
  public $availableAds;
  public $kind;

  public function setAccountId($accountId)
  {
    $this->accountId = $accountId;
  }
  public function getAccountId()
  {
    return $this->accountId;
  }
  public function setActiveAds($activeAds)
  {
    $this->activeAds = $activeAds;
  }
  public function getActiveAds()
  {
    return $this->activeAds;
  }
  public function setActiveAdsLimitTier($activeAdsLimitTier)
  {
    $this->activeAdsLimitTier = $activeAdsLimitTier;
  }
  public function getActiveAdsLimitTier()
  {
    return $this->activeAdsLimitTier;
  }
  public function setAvailableAds($availableAds)
  {
    $this->availableAds = $availableAds;
  }
  public function getAvailableAds()
  {
    return $this->availableAds;
  }
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  public function getKind()
  {
    return $this->kind;
  }
}
