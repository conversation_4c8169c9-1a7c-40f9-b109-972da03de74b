<?xml version="1.0" ?>
<config>
    <localeversion suppressWarning="false">0.9.0</localeversion>
    <version>830</version>
    <help url="https://test-install.blindsidenetworks.com/help.html"/>
    <javaTest url="https://test-install.blindsidenetworks.com/testjava.html"/>
    <porttest host="rtmp://test-install.blindsidenetworks.com" application="video/portTest" timeout="10000"/>
    <bwMon server="rtmp://test-install.blindsidenetworks.com" application="video/bwTest"/>
    <application uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
                 host="https://test-install.blindsidenetworks.com/bigbluebutton/api/enter"/>
    <language userSelectionEnabled="true" rtlEnabled="false"/>
    <skinning url="https://test-install.blindsidenetworks.com/client/branding/css/V2Theme.css.swf?v=830"/>
    <branding logo="logos/logo.swf"
              copyright="&#169; 2017 &lt;u&gt;&lt;a href=&quot;https://test-install.blindsidenetworks.com/home.html&quot; target=&quot;_blank&quot;&gt;BigBlueButton Inc.&lt;/a&gt;&lt;/u&gt; (build {0})"
              background="" toolbarColor="" showQuote="true"/>
    <shortcutKeys showButton="true"/>
    <browserVersions chrome="62" firefox="56" flash="23"/>
    <layout showLogButton="false" defaultLayout="bbb.layout.name.defaultlayout"
            showToolbar="true" showFooter="true" showMeetingName="true" showHelpButton="true"
            showLogoutWindow="true" showLayoutTools="true" confirmLogout="true" showNetworkMonitor="false"
            showRecordingNotification="true" logoutOnStopRecording="false"
            askForFeedbackOnLogout="true"/>
    <breakoutRooms enabled="true" record="false" privateChateEnabled="true"/>
    <logging enabled="true" logTarget="trace" level="info" format="{dateUTC} {time} :: {name} :: [{logLevel}] {message}"
             uri="https://test-install.blindsidenetworks.com/log" logPattern=".*"/>
    <lock disableCam="false" disableMic="false" disablePrivateChat="false"
          disablePublicChat="false" lockedLayout="false" lockOnJoin="true" lockOnJoinConfigurable="false"/>

    <modules>

        <module name="ChatModule" url="https://test-install.blindsidenetworks.com/client/ChatModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
                dependsOn="UsersModule"
                privateEnabled="true"
                fontSize="14"
                baseTabIndex="801"
                colorPickerIsVisible="false"
                maxMessageLength="1024"
        />

        <module name="UsersModule" url="https://test-install.blindsidenetworks.com/client/UsersModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
                allowKickUser="true"
                enableEmojiStatus="true"
                enableSettingsButton="true"
                enableGuestUI="false"
                moderatorUnmute="true"
                baseTabIndex="301"
        />

        <module name="ScreenshareModule"
                url="https://test-install.blindsidenetworks.com/client/ScreenshareModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/screenshare"
                showButton="true"
                enablePause="true"
                tryKurentoWebRTC="false"
                tryWebRTCFirst="false"
                chromeExtensionLink="https://test-install.blindsidenetworks.com/webstore/detail/screenshare-test-extensio/ejokbfbdobjaghidlecoojgmpianklha"
                chromeExtensionKey="ejokbfbdobjaghidlecoojgmpianklha"
                baseTabIndex="201"
                help="https://test-install.blindsidenetworks.com/client/help/screenshare-help.html"
        />

        <module name="PhoneModule" url="https://test-install.blindsidenetworks.com/client/PhoneModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/sip"
                dependsOn="UsersModule"
                autoJoin="true"
                listenOnlyMode="true"
                forceListenOnly="false"
                skipCheck="false"
                showButton="true"
                enabledEchoCancel="true"
                useWebRTCIfAvailable="true"
                showPhoneOption="false"
                showWebRTCStats="false"
                showWebRTCMOS="false"
                echoTestApp="9196"
        />

        <module name="VideoconfModule" url="https://test-install.blindsidenetworks.com/client/VideoconfModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/video"
                dependsOn="UsersModule"
                baseTabIndex="401"
                autoStart="false"
                skipCamSettingsCheck="false"
                showButton="true"
                applyConvolutionFilter="false"
                convolutionFilter="-1, 0, -1, 0, 6, 0, -1, 0, -1"
                filterBias="0"
                filterDivisor="4"
                displayAvatar="false"
                priorityRatio="0.67"
        />

        <module name="WhiteboardModule"
                url="https://test-install.blindsidenetworks.com/client/WhiteboardModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
                dependsOn="PresentModule"
                baseTabIndex="601"
                keepToolbarVisible="false"
        />

        <module name="PollingModule" url="https://test-install.blindsidenetworks.com/client/PollingModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
                dependsOn="PresentModule"
        />

        <module name="PresentModule" url="https://test-install.blindsidenetworks.com/client/PresentModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
                dependsOn="UsersModule"
                host="https://test-install.blindsidenetworks.com"
                showPresentWindow="true"
                showWindowControls="true"
                openExternalFileUploadDialog="false"
                baseTabIndex="501"
                maxFileSize="30"
                enableDownload="true"
        />

        <module name="CaptionModule" url="https://test-install.blindsidenetworks.com/client/CaptionModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
                dependsOn="UsersModule"
                maxPasteLength="1024"
                baseTabIndex="701"
        />

        <module name="LayoutModule" url="https://test-install.blindsidenetworks.com/client/LayoutModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
                layoutConfig="https://test-install.blindsidenetworks.com/client/conf/layout.xml"
                enableEdit="false"
        />

        <module name="SharedNotesModule"
                url="https://test-install.blindsidenetworks.com/client/SharedNotesModule.swf?v=830"
                uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
                dependsOn="UsersModule"
                refreshDelay="500"
                toolbarVisibleByDefault="false"
                showToolbarButton="true"
                fontSize="14"
                maxMessageLength="5000"
                maxNoteLength="10000"
                enableDeleteNotes="false"
                hideAdditionalNotes="false"
        />

        <!--
            <module name="NotesModule" url="https://test-install.blindsidenetworks.com/client/NotesModule.swf?v=830"
              saveURL="https://test-install.blindsidenetworks.com"
              position="top-left"
            />

            <module name="BroadcastModule" url="https://test-install.blindsidenetworks.com/client/BroadcastModule.swf?v=830"
              uri="rtmp://test-install.blindsidenetworks.com/bigbluebutton"
              streamsUri="https://test-install.blindsidenetworks.com/streams.xml"
              position="top-left"
              showStreams="true"
              autoPlay="false"
              dependsOn="UsersModule"
            />
        -->

    </modules>
</config>
