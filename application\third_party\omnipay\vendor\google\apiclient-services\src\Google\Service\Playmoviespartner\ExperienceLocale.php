<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Playmoviespartner_ExperienceLocale extends Google_Collection
{
  protected $collection_key = 'pphNames';
  public $altCutId;
  public $approvedTime;
  public $channelId;
  public $country;
  public $createdTime;
  public $customIds;
  public $earliestAvailStartTime;
  public $editLevelEidr;
  public $elId;
  public $inventoryId;
  public $language;
  public $name;
  public $normalizedPriority;
  public $playableSequenceId;
  public $pphNames;
  public $presentationId;
  public $priority;
  public $status;
  public $studioName;
  public $titleLevelEidr;
  public $trailerId;
  public $type;
  public $videoId;

  public function setAltCutId($altCutId)
  {
    $this->altCutId = $altCutId;
  }
  public function getAltCutId()
  {
    return $this->altCutId;
  }
  public function setApprovedTime($approvedTime)
  {
    $this->approvedTime = $approvedTime;
  }
  public function getApprovedTime()
  {
    return $this->approvedTime;
  }
  public function setChannelId($channelId)
  {
    $this->channelId = $channelId;
  }
  public function getChannelId()
  {
    return $this->channelId;
  }
  public function setCountry($country)
  {
    $this->country = $country;
  }
  public function getCountry()
  {
    return $this->country;
  }
  public function setCreatedTime($createdTime)
  {
    $this->createdTime = $createdTime;
  }
  public function getCreatedTime()
  {
    return $this->createdTime;
  }
  public function setCustomIds($customIds)
  {
    $this->customIds = $customIds;
  }
  public function getCustomIds()
  {
    return $this->customIds;
  }
  public function setEarliestAvailStartTime($earliestAvailStartTime)
  {
    $this->earliestAvailStartTime = $earliestAvailStartTime;
  }
  public function getEarliestAvailStartTime()
  {
    return $this->earliestAvailStartTime;
  }
  public function setEditLevelEidr($editLevelEidr)
  {
    $this->editLevelEidr = $editLevelEidr;
  }
  public function getEditLevelEidr()
  {
    return $this->editLevelEidr;
  }
  public function setElId($elId)
  {
    $this->elId = $elId;
  }
  public function getElId()
  {
    return $this->elId;
  }
  public function setInventoryId($inventoryId)
  {
    $this->inventoryId = $inventoryId;
  }
  public function getInventoryId()
  {
    return $this->inventoryId;
  }
  public function setLanguage($language)
  {
    $this->language = $language;
  }
  public function getLanguage()
  {
    return $this->language;
  }
  public function setName($name)
  {
    $this->name = $name;
  }
  public function getName()
  {
    return $this->name;
  }
  public function setNormalizedPriority($normalizedPriority)
  {
    $this->normalizedPriority = $normalizedPriority;
  }
  public function getNormalizedPriority()
  {
    return $this->normalizedPriority;
  }
  public function setPlayableSequenceId($playableSequenceId)
  {
    $this->playableSequenceId = $playableSequenceId;
  }
  public function getPlayableSequenceId()
  {
    return $this->playableSequenceId;
  }
  public function setPphNames($pphNames)
  {
    $this->pphNames = $pphNames;
  }
  public function getPphNames()
  {
    return $this->pphNames;
  }
  public function setPresentationId($presentationId)
  {
    $this->presentationId = $presentationId;
  }
  public function getPresentationId()
  {
    return $this->presentationId;
  }
  public function setPriority($priority)
  {
    $this->priority = $priority;
  }
  public function getPriority()
  {
    return $this->priority;
  }
  public function setStatus($status)
  {
    $this->status = $status;
  }
  public function getStatus()
  {
    return $this->status;
  }
  public function setStudioName($studioName)
  {
    $this->studioName = $studioName;
  }
  public function getStudioName()
  {
    return $this->studioName;
  }
  public function setTitleLevelEidr($titleLevelEidr)
  {
    $this->titleLevelEidr = $titleLevelEidr;
  }
  public function getTitleLevelEidr()
  {
    return $this->titleLevelEidr;
  }
  public function setTrailerId($trailerId)
  {
    $this->trailerId = $trailerId;
  }
  public function getTrailerId()
  {
    return $this->trailerId;
  }
  public function setType($type)
  {
    $this->type = $type;
  }
  public function getType()
  {
    return $this->type;
  }
  public function setVideoId($videoId)
  {
    $this->videoId = $videoId;
  }
  public function getVideoId()
  {
    return $this->videoId;
  }
}
