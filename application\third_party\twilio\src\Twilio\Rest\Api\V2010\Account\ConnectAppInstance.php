<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Api\V2010\Account;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;


/**
 * @property string|null $accountSid
 * @property string|null $authorizeRedirectUrl
 * @property string|null $companyName
 * @property string|null $deauthorizeCallbackMethod
 * @property string|null $deauthorizeCallbackUrl
 * @property string|null $description
 * @property string|null $friendlyName
 * @property string|null $homepageUrl
 * @property string[]|null $permissions
 * @property string|null $sid
 * @property string|null $uri
 */
class ConnectAppInstance extends InstanceResource
{
    /**
     * Initialize the ConnectAppInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $accountSid The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the ConnectApp resource to fetch.
     * @param string $sid The Twilio-provided string that uniquely identifies the ConnectApp resource to fetch.
     */
    public function __construct(Version $version, array $payload, string $accountSid, string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'authorizeRedirectUrl' => Values::array_get($payload, 'authorize_redirect_url'),
            'companyName' => Values::array_get($payload, 'company_name'),
            'deauthorizeCallbackMethod' => Values::array_get($payload, 'deauthorize_callback_method'),
            'deauthorizeCallbackUrl' => Values::array_get($payload, 'deauthorize_callback_url'),
            'description' => Values::array_get($payload, 'description'),
            'friendlyName' => Values::array_get($payload, 'friendly_name'),
            'homepageUrl' => Values::array_get($payload, 'homepage_url'),
            'permissions' => Values::array_get($payload, 'permissions'),
            'sid' => Values::array_get($payload, 'sid'),
            'uri' => Values::array_get($payload, 'uri'),
        ];

        $this->solution = ['accountSid' => $accountSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return ConnectAppContext Context for this ConnectAppInstance
     */
    protected function proxy(): ConnectAppContext
    {
        if (!$this->context) {
            $this->context = new ConnectAppContext(
                $this->version,
                $this->solution['accountSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the ConnectAppInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the ConnectAppInstance
     *
     * @return ConnectAppInstance Fetched ConnectAppInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): ConnectAppInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the ConnectAppInstance
     *
     * @param array|Options $options Optional Arguments
     * @return ConnectAppInstance Updated ConnectAppInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): ConnectAppInstance
    {

        return $this->proxy()->update($options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.ConnectAppInstance ' . \implode(' ', $context) . ']';
    }
}

