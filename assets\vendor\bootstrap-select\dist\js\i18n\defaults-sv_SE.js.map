{"version": 3, "sources": ["../../../js/i18n/defaults-sv_SE.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AACpC,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;AACtD,IAAI,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC1D,MAAM,MAAM,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AACnF,IAAI,EAAE,CAAC;AACP,IAAI,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,MAAM,CAAC,CAAC,CAAC;AACf,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC;AAC7C,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;AACjD,MAAM,EAAE,CAAC;AACT,IAAI,EAAE,CAAC;AACP,IAAI,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AACnC,IAAI,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AACvC,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,EAAE,EAAE,CAAC;AACL,GAAG,MAAM,EAAE,CAAC", "file": "defaults-sv_SE.js", "sourcesContent": ["(function ($) {\r\n  $.fn.selectpicker.defaults = {\r\n    noneSelectedText: 'Inget valt',\r\n    noneResultsText: 'Inget sökresultat matchar {0}',\r\n    countSelectedText: function (numSelected, numTotal) {\r\n      return (numSelected === 1) ? '{0} alternativ valt' : '{0} alternativ valda';\r\n    },\r\n    maxOptionsText: function (numAll, numGroup) {\r\n      return [\r\n        '<PERSON><PERSON><PERSON><PERSON> uppnåd (max {n} alternativ)',\r\n        '<PERSON>r<PERSON><PERSON> uppnåd (max {n} gruppalternativ)'\r\n      ];\r\n    },\r\n    selectAllText: 'Markera alla',\r\n    deselectAllText: 'Avmarkera alla',\r\n    multipleSeparator: ', '\r\n  };\r\n})(jQuery);\r\n"]}