<?php
/**
 * Error Log Checker for Hostinger
 * Upload this to: /domains/passdrc.com/public_html/school/
 * Access via: https://school.passdrc.com/check_errors.php
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>📋 Error Log Checker</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";

// Check for PHP error logs
echo "<h2>🔍 PHP Error Logs</h2>";

$error_log_locations = [
    'application/logs/php_errors.log',
    'application/logs/log-' . date('Y-m-d') . '.php',
    'error_log',
    '../error_log',
    '../../error_log'
];

$found_logs = false;

foreach ($error_log_locations as $log_file) {
    if (file_exists($log_file) && is_readable($log_file)) {
        $found_logs = true;
        echo "<h3>📄 $log_file</h3>";
        
        $log_content = file_get_contents($log_file);
        if (!empty($log_content)) {
            // Get last 20 lines
            $lines = explode("\n", $log_content);
            $recent_lines = array_slice($lines, -20);
            
            echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: scroll;'>";
            echo "<pre>" . htmlspecialchars(implode("\n", $recent_lines)) . "</pre>";
            echo "</div>";
        } else {
            echo "<p>📝 Log file exists but is empty</p>";
        }
    }
}

if (!$found_logs) {
    echo "<p>⚠️ No error log files found in common locations</p>";
}

// Check CodeIgniter logs
echo "<h2>📊 CodeIgniter Logs</h2>";
$ci_log_dir = 'application/logs/';
if (is_dir($ci_log_dir)) {
    $log_files = glob($ci_log_dir . '*.php');
    if (!empty($log_files)) {
        foreach ($log_files as $log_file) {
            echo "<h3>📄 " . basename($log_file) . "</h3>";
            $content = file_get_contents($log_file);
            
            // Remove PHP opening tag and show content
            $content = str_replace('<?php defined(\'BASEPATH\') OR exit(\'No direct script access allowed\'); ?>', '', $content);
            $content = trim($content);
            
            if (!empty($content)) {
                echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: scroll;'>";
                echo "<pre>" . htmlspecialchars($content) . "</pre>";
                echo "</div>";
            } else {
                echo "<p>📝 Log file exists but is empty</p>";
            }
        }
    } else {
        echo "<p>📝 No CodeIgniter log files found</p>";
    }
} else {
    echo "<p>❌ CodeIgniter logs directory not found</p>";
}

// System information
echo "<h2>ℹ️ System Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>Script Path:</strong> " . __FILE__ . "</p>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";

// Check .htaccess
echo "<h2>🔧 .htaccess Check</h2>";
if (file_exists('.htaccess')) {
    echo "<p>✅ .htaccess file exists</p>";
    $htaccess_content = file_get_contents('.htaccess');
    echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: scroll;'>";
    echo "<pre>" . htmlspecialchars($htaccess_content) . "</pre>";
    echo "</div>";
} else {
    echo "<p>❌ .htaccess file not found</p>";
}

// Check .user.ini
echo "<h2>⚙️ .user.ini Check</h2>";
if (file_exists('.user.ini')) {
    echo "<p>✅ .user.ini file exists</p>";
    $userini_content = file_get_contents('.user.ini');
    echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: scroll;'>";
    echo "<pre>" . htmlspecialchars($userini_content) . "</pre>";
    echo "</div>";
} else {
    echo "<p>❌ .user.ini file not found</p>";
}

echo "<hr>";
echo "<h2>🎯 Next Steps</h2>";
echo "<ul>";
echo "<li>Check the error logs above for specific error messages</li>";
echo "<li>Look for 'Fatal error', 'Parse error', or 'Database connection' errors</li>";
echo "<li>Check Hostinger control panel for additional error logs</li>";
echo "<li>If no errors found here, check Hostinger's main error logs</li>";
echo "</ul>";
?>
