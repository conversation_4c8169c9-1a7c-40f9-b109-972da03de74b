<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Api\V2010\Account\Call;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $accountSid
 * @property int|null $callCount
 * @property int|null $callFeedbackCount
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property \DateTime|null $endDate
 * @property bool|null $includeSubaccounts
 * @property array[]|null $issues
 * @property string|null $qualityScoreAverage
 * @property string|null $qualityScoreMedian
 * @property string|null $qualityScoreStandardDeviation
 * @property string|null $sid
 * @property \DateTime|null $startDate
 * @property string $status
 */
class FeedbackSummaryInstance extends InstanceResource
{
    /**
     * Initialize the FeedbackSummaryInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $accountSid The unique id of the [Account](https://www.twilio.com/docs/iam/api/account) responsible for this resource.
     * @param string $sid A 34 character string that uniquely identifies this resource.
     */
    public function __construct(Version $version, array $payload, string $accountSid, string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'callCount' => Values::array_get($payload, 'call_count'),
            'callFeedbackCount' => Values::array_get($payload, 'call_feedback_count'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'endDate' => Deserialize::dateTime(Values::array_get($payload, 'end_date')),
            'includeSubaccounts' => Values::array_get($payload, 'include_subaccounts'),
            'issues' => Values::array_get($payload, 'issues'),
            'qualityScoreAverage' => Values::array_get($payload, 'quality_score_average'),
            'qualityScoreMedian' => Values::array_get($payload, 'quality_score_median'),
            'qualityScoreStandardDeviation' => Values::array_get($payload, 'quality_score_standard_deviation'),
            'sid' => Values::array_get($payload, 'sid'),
            'startDate' => Deserialize::dateTime(Values::array_get($payload, 'start_date')),
            'status' => Values::array_get($payload, 'status'),
        ];

        $this->solution = ['accountSid' => $accountSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return FeedbackSummaryContext Context for this FeedbackSummaryInstance
     */
    protected function proxy(): FeedbackSummaryContext
    {
        if (!$this->context) {
            $this->context = new FeedbackSummaryContext(
                $this->version,
                $this->solution['accountSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the FeedbackSummaryInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the FeedbackSummaryInstance
     *
     * @return FeedbackSummaryInstance Fetched FeedbackSummaryInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): FeedbackSummaryInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.FeedbackSummaryInstance ' . \implode(' ', $context) . ']';
    }
}

