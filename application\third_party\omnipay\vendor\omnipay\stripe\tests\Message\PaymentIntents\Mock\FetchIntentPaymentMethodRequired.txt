HTTP/1.1 200 OK
Server: nginx
Date: Sun, 05 May 2013 08:51:15 GMT
Content-Type: application/json;charset=utf-8
Content-Length: 997
Connection: keep-alive
Cache-Control: no-cache, no-store
Access-Control-Max-Age: 300
Access-Control-Allow-Credentials: true

{
	"id": "pi_1Ev1ZFFSbr6xR4YAlcdtdqGH",
	"object": "payment_intent",
	"amount": 8000,
	"amount_capturable": 0,
	"amount_received": 0,
	"application": null,
	"application_fee_amount": null,
	"canceled_at": null,
	"cancellation_reason": null,
	"capture_method": "manual",
	"charges": {
		"object": "list",
		"data": [],
		"has_more": false,
		"total_count": 0,
		"url": "\/v1\/charges?payment_intent=pi_1Ev1ZFFSbr6xR4YAlcdtdqGH"
	},
	"client_secret": "pi_1Ev1ZFFSbr6xR4YAlcdtdqGH_secret_dpqra3YEDAA6JTVQdyZj8us0Q",
	"confirmation_method": "manual",
	"created": 1562848809,
	"currency": "usd",
	"customer": null,
	"description": "Order #184",
	"invoice": null,
	"last_payment_error": {
		"code": "payment_intent_authentication_failure",
		"doc_url": "https:\/\/stripe.com\/docs\/error-codes\/payment-intent-authentication-failure",
		"message": "The provided PaymentMethod has failed authentication. You can provide payment_method_data or a new PaymentMethod to attempt to fulfill this PaymentIntent again.",
		"payment_method": {
			"id": "pm_1Ev1ZCFSbr6xR4YA2vAxx9L7",
			"object": "payment_method",
			"billing_details": {
				"address": {
					"city": null,
					"country": null,
					"line1": null,
					"line2": null,
					"postal_code": null,
					"state": null
				},
				"email": "<EMAIL>",
				"name": "Just Guy",
				"phone": null
			},
			"card": {
				"brand": "visa",
				"checks": {
					"address_line1_check": null,
					"address_postal_code_check": null,
					"cvc_check": "unchecked"
				},
				"country": "IE",
				"exp_month": 12,
				"exp_year": 2020,
				"fingerprint": "TLkivWVGoP3a2M2U",
				"funding": "credit",
				"generated_from": null,
				"last4": "3220",
				"three_d_secure_usage": {
					"supported": true
				},
				"wallet": null
			},
			"created": 1562848806,
			"customer": null,
			"livemode": false,
			"metadata": [],
			"type": "card"
		},
		"type": "invalid_request_error"
	},
	"livemode": false,
	"metadata": {
		"order_id": "184",
		"order_number": "fd17b4d8d756814bc6a258ba578fc359",
		"transaction_reference": "4a846a6e34639f22926e14e283ff8f90",
		"client_ip": "::1"
	},
	"next_action": null,
	"on_behalf_of": null,
	"payment_method": null,
	"payment_method_types": [
		"card"
	],
	"receipt_email": null,
	"review": null,
	"setup_future_usage": null,
	"shipping": null,
	"source": null,
	"statement_descriptor": null,
	"status": "requires_payment_method",
	"transfer_data": null,
	"transfer_group": null
}
