<!DOCTYPE html>
<!--
Copyright (c) 2003-2020, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
-->
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>Inline Editing by Code &mdash; CKEditor Sample</title>
	<script src="../../ckeditor.js"></script>
	<link href="sample.css" rel="stylesheet">
	<meta name="description" content="Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.">
	<style>

		#editable
		{
			padding: 10px;
			float: left;
		}

	</style>
</head>
<body>
	<h1 class="samples">
		<a href="index.html">CKEditor Samples</a> &raquo; Inline Editing by Code
	</h1>
	<div class="warning deprecated">
		This sample is not maintained anymore. Check out its <a href="https://ckeditor.com/docs/ckeditor4/latest/examples/inline.html">brand new version in CKEditor Examples</a>.
	</div>
	<div class="description">
		<p>
			This sample shows how to create an inline editor instance of CKEditor. It is created
			with a JavaScript call using the following code:
		</p>
<pre class="samples">
// This property tells CKEditor to not activate every element with contenteditable=true element.
CKEDITOR.disableAutoInline = true;

var editor = CKEDITOR.inline( document.getElementById( 'editable' ) );
</pre>
		<p>
			Note that <code>editable</code> in the code above is the <code>id</code>
			attribute of the <code>&lt;div&gt;</code> element to be converted into an inline instance.
		</p>
	</div>
	<div id="editable" contenteditable="true">
		<h1><img alt="Saturn V carrying Apollo 11" class="right" src="assets/sample.jpg" /> Apollo 11</h1>

		<p><b>Apollo 11</b> was the spaceflight that landed the first humans, Americans <a href="http://en.wikipedia.org/wiki/Neil_Armstrong" title="Neil Armstrong">Neil Armstrong</a> and <a href="http://en.wikipedia.org/wiki/Buzz_Aldrin" title="Buzz Aldrin">Buzz Aldrin</a>, on the Moon on July 20, 1969, at 20:18 UTC. Armstrong became the first to step onto the lunar surface 6 hours later on July 21 at 02:56 UTC.</p>

		<p>Armstrong spent about <s>three and a half</s> two and a half hours outside the spacecraft, Aldrin slightly less; and together they collected 47.5 pounds (21.5&nbsp;kg) of lunar material for return to Earth. A third member of the mission, <a href="http://en.wikipedia.org/wiki/Michael_Collins_(astronaut)" title="Michael Collins (astronaut)">Michael Collins</a>, piloted the <a href="http://en.wikipedia.org/wiki/Apollo_Command/Service_Module" title="Apollo Command/Service Module">command</a> spacecraft alone in lunar orbit until Armstrong and Aldrin returned to it for the trip back to Earth.</p>

		<h2>Broadcasting and <em>quotes</em> <a id="quotes" name="quotes"></a></h2>

		<p>Broadcast on live TV to a world-wide audience, Armstrong stepped onto the lunar surface and described the event as:</p>

		<blockquote>
			<p>One small step for [a] man, one giant leap for mankind.</p>
		</blockquote>

		<p>Apollo 11 effectively ended the <a href="http://en.wikipedia.org/wiki/Space_Race" title="Space Race">Space Race</a> and fulfilled a national goal proposed in 1961 by the late U.S. President <a href="http://en.wikipedia.org/wiki/John_F._Kennedy" title="John F. Kennedy">John F. Kennedy</a> in a speech before the United States Congress:</p>

		<blockquote>
			<p>[...] before this decade is out, of landing a man on the Moon and returning him safely to the Earth.</p>
		</blockquote>

		<h2>Technical details <a id="tech-details" name="tech-details"></a></h2>

		<table align="right" border="1" bordercolor="#ccc" cellpadding="5" cellspacing="0" style="border-collapse:collapse;margin:10px 0 10px 15px;">
			<caption><strong>Mission crew</strong></caption>
			<thead>
			<tr>
				<th scope="col">Position</th>
				<th scope="col">Astronaut</th>
			</tr>
			</thead>
			<tbody>
			<tr>
				<td>Commander</td>
				<td>Neil A. Armstrong</td>
			</tr>
			<tr>
				<td>Command Module Pilot</td>
				<td>Michael Collins</td>
			</tr>
			<tr>
				<td>Lunar Module Pilot</td>
				<td>Edwin &quot;Buzz&quot; E. Aldrin, Jr.</td>
			</tr>
			</tbody>
		</table>

		<p>Launched by a <strong>Saturn V</strong> rocket from <a href="http://en.wikipedia.org/wiki/Kennedy_Space_Center" title="Kennedy Space Center">Kennedy Space Center</a> in Merritt Island, Florida on July 16, Apollo 11 was the fifth manned mission of <a href="http://en.wikipedia.org/wiki/NASA" title="NASA">NASA</a>&#39;s Apollo program. The Apollo spacecraft had three parts:</p>

		<ol>
			<li><strong>Command Module</strong> with a cabin for the three astronauts which was the only part which landed back on Earth</li>
			<li><strong>Service Module</strong> which supported the Command Module with propulsion, electrical power, oxygen and water</li>
			<li><strong>Lunar Module</strong> for landing on the Moon.</li>
		</ol>

		<p>After being sent to the Moon by the Saturn V&#39;s upper stage, the astronauts separated the spacecraft from it and travelled for three days until they entered into lunar orbit. Armstrong and Aldrin then moved into the Lunar Module and landed in the <a href="http://en.wikipedia.org/wiki/Mare_Tranquillitatis" title="Mare Tranquillitatis">Sea of Tranquility</a>. They stayed a total of about 21 and a half hours on the lunar surface. After lifting off in the upper part of the Lunar Module and rejoining Collins in the Command Module, they returned to Earth and landed in the <a href="http://en.wikipedia.org/wiki/Pacific_Ocean" title="Pacific Ocean">Pacific Ocean</a> on July 24.</p>

		<hr />
		<p style="text-align: right;"><small>Source: <a href="http://en.wikipedia.org/wiki/Apollo_11">Wikipedia.org</a></small></p>
	</div>

	<script>
		// We need to turn off the automatic editor creation first.
		CKEDITOR.disableAutoInline = true;

		var editor = CKEDITOR.inline( 'editable' );
	</script>
	<div id="footer">
		<hr>
		<p contenteditable="true">
			CKEditor - The text editor for the Internet - <a class="samples" href="https://ckeditor.com/">
				https://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2020, <a class="samples" href="https://cksource.com/">CKSource</a>
			- Frederico Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
