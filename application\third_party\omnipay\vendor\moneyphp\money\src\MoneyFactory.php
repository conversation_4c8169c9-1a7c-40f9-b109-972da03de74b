<?php

namespace Money;

/**
 * This is a generated file. Do not edit it manually!
 *
 * @method static Money AED(string|int $amount)
 * @method static Money ALL(string|int $amount)
 * @method static Money AMD(string|int $amount)
 * @method static Money ANG(string|int $amount)
 * @method static Money AOA(string|int $amount)
 * @method static Money ARS(string|int $amount)
 * @method static Money AUD(string|int $amount)
 * @method static Money AWG(string|int $amount)
 * @method static Money AZN(string|int $amount)
 * @method static Money BAM(string|int $amount)
 * @method static Money BBD(string|int $amount)
 * @method static Money BDT(string|int $amount)
 * @method static Money BGN(string|int $amount)
 * @method static Money BHD(string|int $amount)
 * @method static Money BIF(string|int $amount)
 * @method static Money BMD(string|int $amount)
 * @method static Money BND(string|int $amount)
 * @method static Money BOB(string|int $amount)
 * @method static Money BOV(string|int $amount)
 * @method static Money BRL(string|int $amount)
 * @method static Money BSD(string|int $amount)
 * @method static Money BTN(string|int $amount)
 * @method static Money BWP(string|int $amount)
 * @method static Money BYN(string|int $amount)
 * @method static Money BZD(string|int $amount)
 * @method static Money CAD(string|int $amount)
 * @method static Money CDF(string|int $amount)
 * @method static Money CHE(string|int $amount)
 * @method static Money CHF(string|int $amount)
 * @method static Money CHW(string|int $amount)
 * @method static Money CLF(string|int $amount)
 * @method static Money CLP(string|int $amount)
 * @method static Money CNY(string|int $amount)
 * @method static Money COP(string|int $amount)
 * @method static Money COU(string|int $amount)
 * @method static Money CRC(string|int $amount)
 * @method static Money CUC(string|int $amount)
 * @method static Money CUP(string|int $amount)
 * @method static Money CVE(string|int $amount)
 * @method static Money CZK(string|int $amount)
 * @method static Money DJF(string|int $amount)
 * @method static Money DKK(string|int $amount)
 * @method static Money DOP(string|int $amount)
 * @method static Money DZD(string|int $amount)
 * @method static Money EGP(string|int $amount)
 * @method static Money ERN(string|int $amount)
 * @method static Money ETB(string|int $amount)
 * @method static Money EUR(string|int $amount)
 * @method static Money FJD(string|int $amount)
 * @method static Money FKP(string|int $amount)
 * @method static Money GBP(string|int $amount)
 * @method static Money GEL(string|int $amount)
 * @method static Money GHS(string|int $amount)
 * @method static Money GIP(string|int $amount)
 * @method static Money GMD(string|int $amount)
 * @method static Money GNF(string|int $amount)
 * @method static Money GTQ(string|int $amount)
 * @method static Money GYD(string|int $amount)
 * @method static Money HKD(string|int $amount)
 * @method static Money HNL(string|int $amount)
 * @method static Money HRK(string|int $amount)
 * @method static Money HTG(string|int $amount)
 * @method static Money HUF(string|int $amount)
 * @method static Money IDR(string|int $amount)
 * @method static Money ILS(string|int $amount)
 * @method static Money INR(string|int $amount)
 * @method static Money IQD(string|int $amount)
 * @method static Money IRR(string|int $amount)
 * @method static Money ISK(string|int $amount)
 * @method static Money JMD(string|int $amount)
 * @method static Money JOD(string|int $amount)
 * @method static Money JPY(string|int $amount)
 * @method static Money KES(string|int $amount)
 * @method static Money KGS(string|int $amount)
 * @method static Money KHR(string|int $amount)
 * @method static Money KMF(string|int $amount)
 * @method static Money KPW(string|int $amount)
 * @method static Money KRW(string|int $amount)
 * @method static Money KWD(string|int $amount)
 * @method static Money KYD(string|int $amount)
 * @method static Money KZT(string|int $amount)
 * @method static Money LAK(string|int $amount)
 * @method static Money LBP(string|int $amount)
 * @method static Money LKR(string|int $amount)
 * @method static Money LRD(string|int $amount)
 * @method static Money LSL(string|int $amount)
 * @method static Money LYD(string|int $amount)
 * @method static Money MAD(string|int $amount)
 * @method static Money MDL(string|int $amount)
 * @method static Money MGA(string|int $amount)
 * @method static Money MKD(string|int $amount)
 * @method static Money MMK(string|int $amount)
 * @method static Money MNT(string|int $amount)
 * @method static Money MOP(string|int $amount)
 * @method static Money MRU(string|int $amount)
 * @method static Money MUR(string|int $amount)
 * @method static Money MVR(string|int $amount)
 * @method static Money MWK(string|int $amount)
 * @method static Money MXN(string|int $amount)
 * @method static Money MXV(string|int $amount)
 * @method static Money MYR(string|int $amount)
 * @method static Money MZN(string|int $amount)
 * @method static Money NAD(string|int $amount)
 * @method static Money NGN(string|int $amount)
 * @method static Money NIO(string|int $amount)
 * @method static Money NOK(string|int $amount)
 * @method static Money NPR(string|int $amount)
 * @method static Money NZD(string|int $amount)
 * @method static Money OMR(string|int $amount)
 * @method static Money PAB(string|int $amount)
 * @method static Money PEN(string|int $amount)
 * @method static Money PGK(string|int $amount)
 * @method static Money PHP(string|int $amount)
 * @method static Money PKR(string|int $amount)
 * @method static Money PLN(string|int $amount)
 * @method static Money PYG(string|int $amount)
 * @method static Money QAR(string|int $amount)
 * @method static Money RON(string|int $amount)
 * @method static Money RSD(string|int $amount)
 * @method static Money RUB(string|int $amount)
 * @method static Money RWF(string|int $amount)
 * @method static Money SAR(string|int $amount)
 * @method static Money SBD(string|int $amount)
 * @method static Money SCR(string|int $amount)
 * @method static Money SDG(string|int $amount)
 * @method static Money SEK(string|int $amount)
 * @method static Money SGD(string|int $amount)
 * @method static Money SHP(string|int $amount)
 * @method static Money SLL(string|int $amount)
 * @method static Money SOS(string|int $amount)
 * @method static Money SRD(string|int $amount)
 * @method static Money SSP(string|int $amount)
 * @method static Money STN(string|int $amount)
 * @method static Money SVC(string|int $amount)
 * @method static Money SYP(string|int $amount)
 * @method static Money SZL(string|int $amount)
 * @method static Money THB(string|int $amount)
 * @method static Money TJS(string|int $amount)
 * @method static Money TMT(string|int $amount)
 * @method static Money TND(string|int $amount)
 * @method static Money TOP(string|int $amount)
 * @method static Money TRY(string|int $amount)
 * @method static Money TTD(string|int $amount)
 * @method static Money TWD(string|int $amount)
 * @method static Money TZS(string|int $amount)
 * @method static Money UAH(string|int $amount)
 * @method static Money UGX(string|int $amount)
 * @method static Money USD(string|int $amount)
 * @method static Money USN(string|int $amount)
 * @method static Money UYI(string|int $amount)
 * @method static Money UYU(string|int $amount)
 * @method static Money UYW(string|int $amount)
 * @method static Money UZS(string|int $amount)
 * @method static Money VES(string|int $amount)
 * @method static Money VND(string|int $amount)
 * @method static Money VUV(string|int $amount)
 * @method static Money WST(string|int $amount)
 * @method static Money XAF(string|int $amount)
 * @method static Money XAG(string|int $amount)
 * @method static Money XAU(string|int $amount)
 * @method static Money XBA(string|int $amount)
 * @method static Money XBB(string|int $amount)
 * @method static Money XBC(string|int $amount)
 * @method static Money XBD(string|int $amount)
 * @method static Money XBT(string|int $amount)
 * @method static Money XCD(string|int $amount)
 * @method static Money XDR(string|int $amount)
 * @method static Money XOF(string|int $amount)
 * @method static Money XPD(string|int $amount)
 * @method static Money XPF(string|int $amount)
 * @method static Money XPT(string|int $amount)
 * @method static Money XSU(string|int $amount)
 * @method static Money XTS(string|int $amount)
 * @method static Money XUA(string|int $amount)
 * @method static Money XXX(string|int $amount)
 * @method static Money YER(string|int $amount)
 * @method static Money ZAR(string|int $amount)
 * @method static Money ZMW(string|int $amount)
 * @method static Money ZWL(string|int $amount)
 */
trait MoneyFactory
{
    /**
     * Convenience factory method for a Money object.
     *
     * <code>
     * $fiveDollar = Money::USD(500);
     * </code>
     *
     * @param string $method
     * @param array  $arguments
     *
     * @return Money
     *
     * @throws \InvalidArgumentException If amount is not integer(ish)
     */
    public static function __callStatic($method, $arguments)
    {
        return new Money($arguments[0], new Currency($method));
    }
}
