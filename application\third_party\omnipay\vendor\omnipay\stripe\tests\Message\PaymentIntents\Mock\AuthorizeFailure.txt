HTTP/1.1 400 Bad Request
server: nginx
date: Thu, 11 Jul 2019 08:46:44 GMT
content-type: application/json
content-length: 247
access-control-allow-credentials: true
access-control-allow-methods: GET, POST, HEAD, OPTIONS, DELETE
access-control-allow-origin: *
access-control-expose-headers: Request-Id, Stripe-Manage-Version, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required
access-control-max-age: 300
cache-control: no-cache, no-store
request-id: req_IznB7F29VYnxZj
stripe-version: 2019-03-14
strict-transport-security: max-age=31556926; includeSubDomains; preload

{
  "error": {
    "code": "resource_missing",
    "doc_url": "https://stripe.com/docs/error-codes/resource-missing",
    "message": "No such payment_method: pm_invalid_method",
    "param": "payment_method",
    "type": "invalid_request_error"
  }
}