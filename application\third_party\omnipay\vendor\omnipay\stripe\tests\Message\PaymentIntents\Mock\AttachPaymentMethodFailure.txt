HTTP/1.1 402 Payment Required
Server: nginx
Date: Tue, 26 Feb 2013 16:17:02 GMT
Content-Type: application/json;charset=utf-8
Content-Length: 139
Connection: keep-alive
Access-Control-Max-Age: 300
Access-Control-Allow-Credentials: true
Cache-Control: no-cache, no-store

{
  "error": {
    "message": "This PaymentMethod was previously used without being attached to a Customer or was detached from a Customer, and may not be used again.",
    "type": "invalid_request_error"
  }
}