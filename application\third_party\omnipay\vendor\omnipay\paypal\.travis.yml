language: php

php:
  - 5.6
  - 7.0
  - 7.1
  - 7.2

env:
  global:
    - setup=basic

matrix:
  include:
    - php: 5.6
      env: setup=lowest

sudo: false

before_install:
  - travis_retry composer self-update

install:
  - if [[ $setup = 'basic' ]]; then travis_retry composer install --no-interaction --prefer-dist; fi
  - if [[ $setup = 'lowest' ]]; then travis_retry composer update --prefer-dist --no-interaction --prefer-lowest --prefer-stable; fi

script: vendor/bin/phpcs --standard=PSR2 src && vendor/bin/phpunit --coverage-text
