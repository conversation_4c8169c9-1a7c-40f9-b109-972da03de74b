<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\FlexApi\V1;

use Twilio\Options;
use Twilio\Values;

abstract class InsightsQuestionnairesCategoryOptions
{
    /**
     * @param string $authorization The Authorization HTTP request header
     * @return CreateInsightsQuestionnairesCategoryOptions Options builder
     */
    public static function create(
        
        string $authorization = Values::NONE

    ): CreateInsightsQuestionnairesCategoryOptions
    {
        return new CreateInsightsQuestionnairesCategoryOptions(
            $authorization
        );
    }

    /**
     * @param string $authorization The Authorization HTTP request header
     * @return DeleteInsightsQuestionnairesCategoryOptions Options builder
     */
    public static function delete(
        
        string $authorization = Values::NONE

    ): DeleteInsightsQuestionnairesCategoryOptions
    {
        return new DeleteInsightsQuestionnairesCategoryOptions(
            $authorization
        );
    }

    /**
     * @param string $authorization The Authorization HTTP request header
     * @return ReadInsightsQuestionnairesCategoryOptions Options builder
     */
    public static function read(
        
        string $authorization = Values::NONE

    ): ReadInsightsQuestionnairesCategoryOptions
    {
        return new ReadInsightsQuestionnairesCategoryOptions(
            $authorization
        );
    }

    /**
     * @param string $authorization The Authorization HTTP request header
     * @return UpdateInsightsQuestionnairesCategoryOptions Options builder
     */
    public static function update(
        
        string $authorization = Values::NONE

    ): UpdateInsightsQuestionnairesCategoryOptions
    {
        return new UpdateInsightsQuestionnairesCategoryOptions(
            $authorization
        );
    }

}

class CreateInsightsQuestionnairesCategoryOptions extends Options
    {
    /**
     * @param string $authorization The Authorization HTTP request header
     */
    public function __construct(
        
        string $authorization = Values::NONE

    ) {
        $this->options['authorization'] = $authorization;
    }

    /**
     * The Authorization HTTP request header
     *
     * @param string $authorization The Authorization HTTP request header
     * @return $this Fluent Builder
     */
    public function setAuthorization(string $authorization): self
    {
        $this->options['authorization'] = $authorization;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.CreateInsightsQuestionnairesCategoryOptions ' . $options . ']';
    }
}

class DeleteInsightsQuestionnairesCategoryOptions extends Options
    {
    /**
     * @param string $authorization The Authorization HTTP request header
     */
    public function __construct(
        
        string $authorization = Values::NONE

    ) {
        $this->options['authorization'] = $authorization;
    }

    /**
     * The Authorization HTTP request header
     *
     * @param string $authorization The Authorization HTTP request header
     * @return $this Fluent Builder
     */
    public function setAuthorization(string $authorization): self
    {
        $this->options['authorization'] = $authorization;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.DeleteInsightsQuestionnairesCategoryOptions ' . $options . ']';
    }
}

class ReadInsightsQuestionnairesCategoryOptions extends Options
    {
    /**
     * @param string $authorization The Authorization HTTP request header
     */
    public function __construct(
        
        string $authorization = Values::NONE

    ) {
        $this->options['authorization'] = $authorization;
    }

    /**
     * The Authorization HTTP request header
     *
     * @param string $authorization The Authorization HTTP request header
     * @return $this Fluent Builder
     */
    public function setAuthorization(string $authorization): self
    {
        $this->options['authorization'] = $authorization;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.ReadInsightsQuestionnairesCategoryOptions ' . $options . ']';
    }
}

class UpdateInsightsQuestionnairesCategoryOptions extends Options
    {
    /**
     * @param string $authorization The Authorization HTTP request header
     */
    public function __construct(
        
        string $authorization = Values::NONE

    ) {
        $this->options['authorization'] = $authorization;
    }

    /**
     * The Authorization HTTP request header
     *
     * @param string $authorization The Authorization HTTP request header
     * @return $this Fluent Builder
     */
    public function setAuthorization(string $authorization): self
    {
        $this->options['authorization'] = $authorization;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.UpdateInsightsQuestionnairesCategoryOptions ' . $options . ']';
    }
}

