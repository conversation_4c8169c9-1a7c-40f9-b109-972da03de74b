HTTP/1.1 404 Not Found
Server: nginx
Date: Sun, 24 Jan 2016 22:29:41 GMT
Content-Type: application/json
Content-Length: 188
Connection: keep-alive
Access-Control-Allow-Credentials: true
Cache-Control: no-cache, no-store

{
  "error": {
    "type": "invalid_request_error",
    "message": "Customer cus_7lqqgOm33t4xSU does not have a subscription with ID sub_7mU0DonX8GQZFW",
    "param": "subscription"
  }
}