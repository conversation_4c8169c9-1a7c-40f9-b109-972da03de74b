<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Insights\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Rest\Insights\V1\Conference\ConferenceParticipantList;


/**
 * @property ConferenceParticipantList $conferenceParticipants
 * @method \Twilio\Rest\Insights\V1\Conference\ConferenceParticipantContext conferenceParticipants(string $participantSid)
 */
class ConferenceContext extends InstanceContext
    {
    protected $_conferenceParticipants;

    /**
     * Initialize the ConferenceContext
     *
     * @param Version $version Version that contains the resource
     * @param string $conferenceSid The unique SID identifier of the Conference.
     */
    public function __construct(
        Version $version,
        $conferenceSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'conferenceSid' =>
            $conferenceSid,
        ];

        $this->uri = '/Conferences/' . \rawurlencode($conferenceSid)
        .'';
    }

    /**
     * Fetch the ConferenceInstance
     *
     * @return ConferenceInstance Fetched ConferenceInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): ConferenceInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new ConferenceInstance(
            $this->version,
            $payload,
            $this->solution['conferenceSid']
        );
    }


    /**
     * Access the conferenceParticipants
     */
    protected function getConferenceParticipants(): ConferenceParticipantList
    {
        if (!$this->_conferenceParticipants) {
            $this->_conferenceParticipants = new ConferenceParticipantList(
                $this->version,
                $this->solution['conferenceSid']
            );
        }

        return $this->_conferenceParticipants;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name): ListResource
    {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Insights.V1.ConferenceContext ' . \implode(' ', $context) . ']';
    }
}
