<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Oauth
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Oauth\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;


/**
 * @property string|null $issuer
 * @property string|null $authorizationEndpoint
 * @property string|null $deviceAuthorizationEndpoint
 * @property string|null $tokenEndpoint
 * @property string|null $userinfoEndpoint
 * @property string|null $revocationEndpoint
 * @property string|null $jwkUri
 * @property string[]|null $responseTypeSupported
 * @property string[]|null $subjectTypeSupported
 * @property string[]|null $idTokenSigningAlgValuesSupported
 * @property string[]|null $scopesSupported
 * @property string[]|null $claimsSupported
 * @property string|null $url
 */
class OpenidDiscoveryInstance extends InstanceResource
{
    /**
     * Initialize the OpenidDiscoveryInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     */
    public function __construct(Version $version, array $payload)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'issuer' => Values::array_get($payload, 'issuer'),
            'authorizationEndpoint' => Values::array_get($payload, 'authorization_endpoint'),
            'deviceAuthorizationEndpoint' => Values::array_get($payload, 'device_authorization_endpoint'),
            'tokenEndpoint' => Values::array_get($payload, 'token_endpoint'),
            'userinfoEndpoint' => Values::array_get($payload, 'userinfo_endpoint'),
            'revocationEndpoint' => Values::array_get($payload, 'revocation_endpoint'),
            'jwkUri' => Values::array_get($payload, 'jwk_uri'),
            'responseTypeSupported' => Values::array_get($payload, 'response_type_supported'),
            'subjectTypeSupported' => Values::array_get($payload, 'subject_type_supported'),
            'idTokenSigningAlgValuesSupported' => Values::array_get($payload, 'id_token_signing_alg_values_supported'),
            'scopesSupported' => Values::array_get($payload, 'scopes_supported'),
            'claimsSupported' => Values::array_get($payload, 'claims_supported'),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = [];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return OpenidDiscoveryContext Context for this OpenidDiscoveryInstance
     */
    protected function proxy(): OpenidDiscoveryContext
    {
        if (!$this->context) {
            $this->context = new OpenidDiscoveryContext(
                $this->version
            );
        }

        return $this->context;
    }

    /**
     * Fetch the OpenidDiscoveryInstance
     *
     * @return OpenidDiscoveryInstance Fetched OpenidDiscoveryInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): OpenidDiscoveryInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Oauth.V1.OpenidDiscoveryInstance ' . \implode(' ', $context) . ']';
    }
}

