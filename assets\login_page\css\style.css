body {
    background-color: #fff;
    color: #000;
	font-family: 'Signika', sans-serif;
	line-height: 22px;
	font-size: 14px;
	width: 100%;
}

.auth-main{
	min-height: 100vh;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	padding: 25px 10px;
}

input.form-control {
	width: 100%;
	height: 40px;
	-webkit-border-radius: 500px;
	border-radius: 500px;
}

.form-control {
    color: #fff;
    background-color: #212121;
    border-color: #444;
	-webkit-border-radius: 4px;
	border-radius: 4px;
}

a {
    color: #535353;
}

a:hover, a:focus {
	color: #6e6e6e;
    text-decoration: none;
}

.no-padding {
    padding: 0;
}

.fitxt-center {
	z-index:1;
	text-align: center;
}

.img-hol-p {
	padding-bottom: 22px;
}

/* image area */
.image-area {
	background: url('../image/sidebox.jpg');
	background-position: center;
	background-size: cover;
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	height: 600px;
	border-radius: 12px;
	z-index: 1;
	position: relative;
	overflow: hidden;
}

.image-area:before {
	position: absolute;
	content: '';
	z-index: -1;
	border-radius: 12px;
	height: 100%;
	width: 100%;
	background: #e4173b;
    background: -webkit-linear-gradient(left, #5061ab, #f51414);
    background: -o-linear-gradient(left, #5061ab, #f51414);
    background: -moz-linear-gradient(left, #5061ab, #f51414);
    background: linear-gradient(left, #5061ab, #f51414);
	opacity: .80;
	top: 0;
	left: 0;
}

.image-area .content{
	height: 100px;
	padding: 170px 0;
}

.image-area .image-hader {
	padding-bottom: 22px;
}

.image-area .image-hader h2{
	color: #fff;
	text-transform: uppercase;
	position: relative;
}

.image-area .image-hader h2:before {
	content: '';
	background: #fff;
	width: 85px;
	height: 2px;
	position: absolute;
	bottom: -6px;
}

.content .address p{
	color: #fff;
	font-size: 13px;
	font-weight: normal;
	padding-bottom: 10px;
}

.f-social-links a span {
    font-size: 16px;
    color: #fff;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 38px;
    position: relative;
    z-index: 1;
    overflow: hidden;
    margin-right: 5px;
    border: 1px solid #437df3;
	-moz-box-shadow: 5px 7px 10px #482c2c;
	-webkit-box-shadow: 5px 7px 10px #482c2c;
	box-shadow: 5px 7px 10px #482c2c;
}

.f-social-links a span:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    background: #320f4b;
    z-index: -1;
}

.f-social-links a span:hover:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
    background: #320f4b;
}

/* sign area */
.sign-area {
    z-index: 1;
    background: #212121;
    position: relative;
    color: #fff;
    width: 100%;
    height: 566px;
    -webkit-border-top-right-radius: 12px;
    -webkit-border-bottom-right-radius: 12px;
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
    padding: 3.5em 6em;
    margin-top: 17px;
}

.sign-area::before {
    border-right: 60px solid #212121;
    border-top: 70px solid transparent;
    border-bottom: 70px solid transparent;
    content: '';
    height: 0;
    left: -59px;
    width: 0;
    position: absolute;
    pointer-events: none;
    top: 213px;
}

.sign-area .sign-hader {
	text-align: center;
	padding-top: 30px;
	padding-bottom: 30px;
}

/* btn round */
.btn-round {
	background-color: #212121;
	border-color: #444;
	margin-top: 25px;
	height: 42px;
	-webkit-border-radius: 50px;
	-moz-border-radius: 50px;
	border-radius: 50px;
	-moz-box-shadow: 2px 3px 6px #2c2c2c;
	-webkit-box-shadow: 2px 3px 6px #2c2c2c;
	box-shadow: 2px 3px 6px #2c2c2c;
}

.btn-round:hover,
.btn-round:focus,
.btn-round:active {
    background-color: #2d2c2c;
    color: #fff;
    border-color: #3a3939;
    outline: none !important;

}

.forgot-text {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 10px;
}

.sign-footer p {
	color: #444;
	text-align: center;
	margin-top: 30px !important;
}

.forgot-header {
	color: #545454;
    padding: 12px;
    margin-bottom: 20px;
    border: 1px solid #444;
    border-radius: 4px;
	background: #212121;
	text-align: center;
}

.forgot-header h4 {
	color:#fff;
	margin-top:0;
	margin-bottom:6px;
}

.alert-msg {
	color: #6ca115;
    padding: 8px;
	margin-top: -8px;
    margin-bottom: 12px;
    border: 1px solid #6ca115;
    border-radius: 4px;
	background: #212121;
	text-align: center;
}
			
.alert-msg.danger {
	color: #de1313;
    border-color: #de1313;
}

/* form - input icon */
.input-group-icon {
	width: 100%;
	table-layout: fixed;
}

.input-group-icon input.form-control {
	font-size: 1.2rem;
	padding-right: 36px;
}

.input-group-icon input.form-control:first-child,
.input-group-icon input.form-control:last-child {
	-webkit-border-radius: 500px;
	border-radius: 500px;
}

.input-group-icon .input-group-addon {
	position: relative;
	padding: 0;
	border: 0 none;
	width: 0;
}

.input-group-icon .input-group-addon span.icon {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	border: 0;
	z-index: 4;
	width: auto;
	display: inline-block;
	vertical-align: middle;
	text-align: center;
	padding: 11px 16px;
	background: transparent;
	line-height: 1.42857143;
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	pointer-events: none;
}

.input-group-icon .input-group-addon:last-child span.icon {
	left: auto;
	right: 0;
}

.input-group-icon .input-group-addon + input.form-control {
	padding-right: 12px;
	padding-left: 38px;
}

/* Form - Custom Checkbox */
.checkbox-replace .i-checks {
	padding-left: 20px;
	cursor: pointer;
	margin-bottom: 0;
}

.checkbox-replace .i-checks input {
	position: absolute;
	margin-left: -20px;
	opacity: 0;
}

.checkbox-replace .i-checks input:checked + span .active {
	display: inherit;
}

.checkbox-replace .i-checks > span {
	margin-left: -20px;
}

.checkbox-replace .i-checks > span .active {
	display: none;
}

.checkbox-replace .i-checks > i {
	position: relative;
	display: inline-block;
	width: 20px;
	height: 20px;
	margin-top: -2px;
	margin-right: 4px;
	margin-left: -20px;
	line-height: 1;
	vertical-align: middle;
	background-color: transparent;
	border: 1px solid #444;
	-webkit-transition: all .2s;
	transition: all .2s;
}

html.dark .checkbox-replace .i-checks > i {
	border-color: #535353;
}

.checkbox-replace .i-checks > i:before {
	content: "";
	position: absolute;
	top: 4px;
	left: 4px;
	width: 10px;
	height: 10px;
	background-color: #ffbd2e;
	-webkit-transform: scale(0);
	-moz-transform: scale(0);
	-ms-transform: scale(0);
	transform: scale(0);
	-webkit-transition: all .2s;
	transition: all .2s;
}

.checkbox-replace .i-checks input:checked + i {
	border-color: #ffbd2e;
}

html.dark .checkbox-replace .i-checks input:checked + i{
	border-color: #ff685c;
}

.checkbox-replace .i-checks input:checked + i:before {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
}

html.dark .checkbox-replace .i-checks input:checked + i:before {
	background-color: #ff685c;
}

.checkbox-replace .i-checks input:disabled + i {
	border-color: #dee5e7;
}

.checkbox-replace .i-checks input:disabled + i:before{
	background-color: #dee5e7;
}

html.dark .checkbox-replace .i-checks input:disabled + i{
	border-color: #4c4c4d;
}

html.dark .checkbox-replace .i-checks input:disabled + i:before{
	background-color: #4c4c4d;
}

/* Forms Validations */
label.valid {
	display: inline-block;
	text-indent: -9999px;
}

span.error {
	color: #C10000;
	font-size: 0.9em;
	font-weight: normal;
	margin-top: -5px;
	margin-bottom: 0;
	padding: 0;
}

/* animation */
.slideIn {
	-webkit-animation-name: slideIn;
	-moz-animation-name: slideIn;
	-o-animation-name: slideIn;
	animation-name: slideIn;
	-webkit-animation-timing-function: ease;
	-moz-animation-timing-function: ease;
	-o-animation-timing-function: ease;
	animation-timing-function: ease;
	-webkit-animation-duration: 1s;
	-moz-animation-duration: 1s;
	-ms-animation-duration: 1s;
	-o-animation-duration: 1s;
	animation-duration: 1s;
	visibility: visible !important;
}
			
@-webkit-keyframes slideIn {
    0% {
		opacity: 0;
		-webkit-transform: translateX(-20px);
	}
    100% {
		opacity: 1;
		-webkit-transform: translateX(0);
	}
}
			
@-moz-keyframes slideIn {
    0% {
		opacity: 0;
		-moz-transform: translateX(-20px);
	}
    100% {
		opacity: 1;
		-moz-transform: translateX(0);
	}
}
@-o-keyframes slideIn {
    0% {
		opacity: 0;
		-o-transform: translateX(-20px);
	}
    100% {
		opacity: 1;
		-o-transform: translateX(0);
	}
}
@keyframes slideIn {
    0% {
		opacity: 0;
		transform: translateX(-20px);
	}
    100% {
		opacity: 1;
		transform: translateX(0);
	}
}

/* media queries */
@media(max-width:991px) {
    .sign-area {
        -webkit-border-radius: 12px;
        -moz-border-radius: 12px;
        border-radius: 12px;
    }

	.sign-area::before {
	    display: none;
	}

	.sign-area {
	    padding: 3.5em 2em;
	}

	.w-full {
	    padding: 8px 5px;
	}
}

@media(max-width:1199px) {
	.sign-area::before {
	    border-right-width: 50px; 
	    border-top-width: 60px; 
	    border-bottom-width: 60px; 
	    left: -50px;
	    top: 223px;
	}
}