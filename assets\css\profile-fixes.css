/*
 * Profile Layout Fixes - Modern Design Implementation
 * This file implements the new profile design with proper layout
 * Upload this file to: assets/css/profile-fixes.css
 */

/* Modern profile layout structure */
.profile-head {
    display: flex !important;
    flex-wrap: wrap;
    align-items: flex-start !important;
    min-height: 400px;
    position: relative;
    overflow: visible !important;
    padding: 30px 20px !important;
    background: linear-gradient(135deg, #0f172a, #1e293b) !important; /* Updated to Slate 900 to Slate 800 gradient */
}

/* Profile image container - left side */
.profile-head .col-lg-4,
.profile-head .col-xl-3 {
    flex: 0 0 35% !important;
    max-width: 35% !important;
    position: relative !important;
    z-index: 1;
    padding-right: 25px !important;
    display: flex !important;
    justify-content: center !important;
}

/* Profile content area - right side */
.profile-head .col-lg-5,
.profile-head .col-xl-5 {
    flex: 0 0 65% !important;
    max-width: 65% !important;
    position: relative !important;
    z-index: 2;
    padding-left: 25px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
}

/* Modern profile image container with social icons */
.image-content-center.user-pro {
    position: relative !important;
    z-index: 2 !important;
    width: 100% !important;
    max-width: 320px !important;
    margin: 0 auto !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(52, 211, 153, 0.1) !important; /* Updated to Emerald 400 with transparency */
    border-radius: 15px !important;
    padding: 20px !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.2) !important; /* Updated to Emerald 500 shadow */
}

/* Social icons positioned vertically on the left side like in the design */
.image-content-center.user-pro .social-icon-one {
    position: absolute !important;
    left: -20px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
    z-index: 5 !important;
    list-style: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.image-content-center.user-pro .social-icon-one li {
    margin: 0 !important;
    padding: 0 !important;
}

.image-content-center.user-pro .social-icon-one li a {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 50px !important;
    height: 50px !important;
    background: #3b82f6 !important;
    border-radius: 8px !important;
    color: #fff !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    font-size: 18px !important;
}

/* Different colors for each social platform */
.image-content-center.user-pro .social-icon-one li:nth-child(1) a {
    background: #1877f2 !important; /* Facebook blue */
}

.image-content-center.user-pro .social-icon-one li:nth-child(2) a {
    background: #1da1f2 !important; /* Twitter blue */
}

.image-content-center.user-pro .social-icon-one li:nth-child(3) a {
    background: #0077b5 !important; /* LinkedIn blue */
}

.image-content-center.user-pro .social-icon-one li a:hover {
    transform: translateX(5px) scale(1.05) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3) !important;
}

/* Profile image styling - modern rounded rectangle like in design */
.image-content-center.user-pro .preview {
    position: relative !important;
    z-index: 3 !important;
    width: 100% !important;
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.image-content-center.user-pro .preview img {
    width: 200px !important;
    height: 200px !important;
    border-radius: 15px !important;
    object-fit: cover !important;
    border: 4px solid rgba(52, 211, 153, 0.8) !important; /* Updated to Emerald 400 border */
    position: relative !important;
    z-index: 4 !important;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.25) !important; /* Updated to Emerald 500 shadow */
}

/* Modern profile text styling to match the design */
.profile-head h5 {
    position: static !important;
    z-index: 5 !important;
    width: 100% !important;
    margin: 0 0 15px 0 !important;
    padding: 0 !important;
    text-align: left !important;
    font-size: 36px !important;
    font-weight: bold !important;
    color: #ffffff !important; /* White text for contrast */
    text-transform: none !important;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(15, 23, 42, 0.5) !important; /* Updated to Slate 900 shadow */
}

.profile-head p {
    position: static !important;
    z-index: 5 !important;
    width: 100% !important;
    margin: 0 0 25px 0 !important;
    padding: 0 !important;
    text-align: left !important;
    font-size: 20px !important;
    color: #cbd5e1 !important; /* Updated to Slate 300 for secondary text */
    text-transform: none !important;
    line-height: 1.3;
    font-weight: 400 !important;
}

/* Profile information list styling with diamond icons like in design */
.profile-head ul {
    position: static !important;
    z-index: 5 !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.profile-head ul li {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 15px !important;
    padding: 0 !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    position: static !important;
    width: 100% !important;
    color: #cbd5e1 !important; /* Updated to Slate 300 for list text */
    font-size: 16px !important;
    line-height: 1.4;
    font-weight: 400 !important;
}

/* Diamond-shaped icon holders like in the design */
.profile-head ul li .icon-holder {
    flex-shrink: 0;
    margin-right: 15px !important;
    position: static !important;
    z-index: 6 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 35px !important;
    height: 35px !important;
    background: #10b981 !important; /* Updated to Emerald 500 */
    transform: rotate(45deg) !important;
    border-radius: 6px !important;
    box-shadow: 0 3px 10px rgba(16, 185, 129, 0.3) !important; /* Updated shadow color */
}

.profile-head ul li .icon-holder i {
    transform: rotate(-45deg) !important;
    color: #fff !important;
    font-size: 14px !important;
}

/* Responsive design for tablets and smaller laptops */
@media only screen and (max-width: 1199px) {
    .profile-head .col-lg-4,
    .profile-head .col-xl-3 {
        flex: 0 0 40% !important;
        max-width: 40% !important;
    }

    .profile-head .col-lg-5,
    .profile-head .col-xl-5 {
        flex: 0 0 60% !important;
        max-width: 60% !important;
        padding-left: 20px !important;
    }

    .profile-head h5 {
        font-size: 32px !important;
    }

    .profile-head p {
        font-size: 18px !important;
    }
}

/* Responsive fixes for tablets */
@media only screen and (max-width: 991px) {
    .profile-head {
        flex-direction: column !important;
        align-items: center !important;
        text-align: center !important;
        padding: 30px 20px !important;
    }

    .profile-head .col-lg-4,
    .profile-head .col-xl-3,
    .profile-head .col-lg-5,
    .profile-head .col-xl-5 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
        margin-bottom: 30px !important;
    }

    .image-content-center.user-pro {
        margin: 0 auto 35px auto !important;
        max-width: 350px !important;
    }

    .profile-head h5 {
        text-align: center !important;
        font-size: 30px !important;
    }

    .profile-head p {
        text-align: center !important;
        font-size: 18px !important;
    }

    .profile-head ul {
        text-align: left !important;
        max-width: 400px !important;
        margin: 0 auto !important;
    }
}

/* Responsive fixes for mobile phones */
@media only screen and (max-width: 767px) {
    .profile-head {
        padding: 20px 15px !important;
        min-height: auto !important;
    }

    .profile-head .col-lg-4,
    .profile-head .col-xl-3,
    .profile-head .col-lg-5,
    .profile-head .col-xl-5 {
        padding-left: 10px !important;
        padding-right: 10px !important;
        margin-bottom: 25px !important;
    }

    .image-content-center.user-pro {
        max-width: 280px !important;
        padding: 15px !important;
    }

    .image-content-center.user-pro .preview img {
        width: 150px !important;
        height: 150px !important;
    }

    .image-content-center.user-pro .social-icon-one {
        left: -15px !important;
        gap: 8px !important;
    }

    .image-content-center.user-pro .social-icon-one li a {
        width: 40px !important;
        height: 40px !important;
        font-size: 16px !important;
    }

    .profile-head h5 {
        font-size: 26px !important;
        margin-bottom: 12px !important;
        text-align: center !important;
    }

    .profile-head p {
        font-size: 17px !important;
        margin-bottom: 20px !important;
        text-align: center !important;
    }

    .profile-head ul {
        max-width: 350px !important;
    }

    .profile-head ul li {
        font-size: 15px !important;
        margin-bottom: 12px !important;
    }
}

/* Additional optimizations and overrides */

/* Ensure Bootstrap grid system works properly with our flexbox layout */
.profile-head [class*="col-"] {
    position: relative !important;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    float: none !important;
}

/* Override any conflicting absolute positioning from main CSS */
.profile-head .col-lg-5 h5,
.profile-head .col-xl-5 h5,
.profile-head .col-lg-5 p,
.profile-head .col-xl-5 p,
.profile-head .col-lg-5 ul,
.profile-head .col-xl-5 ul {
    position: static !important;
    transform: none !important;
    left: auto !important;
    right: auto !important;
    top: auto !important;
    bottom: auto !important;
}

/* Ensure text content doesn't escape its container */
.profile-head .col-lg-5 *,
.profile-head .col-xl-5 * {
    max-width: 100% !important;
    overflow-wrap: break-word;
    word-break: break-word;
}

/* Force layout recalculation */
.profile-head * {
    box-sizing: border-box;
}

/* Ensure proper clearfix */
.profile-head::after {
    content: "";
    display: table;
    clear: both;
}

/* Extra small screens optimization */
@media only screen and (max-width: 480px) {
    .profile-head {
        padding: 15px 10px !important;
    }

    .image-content-center.user-pro {
        max-width: 250px !important;
        padding: 12px !important;
    }

    .image-content-center.user-pro .preview img {
        width: 120px !important;
        height: 120px !important;
    }

    .image-content-center.user-pro .social-icon-one li a {
        width: 35px !important;
        height: 35px !important;
        font-size: 14px !important;
    }

    .profile-head h5 {
        font-size: 22px !important;
    }

    .profile-head p {
        font-size: 16px !important;
    }

    .profile-head ul li {
        font-size: 14px !important;
    }

    .profile-head ul li .icon-holder {
        width: 30px !important;
        height: 30px !important;
    }

    .profile-head ul li .icon-holder i {
        font-size: 12px !important;
    }
}
