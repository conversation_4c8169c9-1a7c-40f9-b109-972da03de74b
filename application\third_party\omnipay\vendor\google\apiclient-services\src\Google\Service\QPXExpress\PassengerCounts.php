<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_QPXExpress_PassengerCounts extends Google_Model
{
  public $adultCount;
  public $childCount;
  public $infantInLapCount;
  public $infantInSeatCount;
  public $kind;
  public $seniorCount;

  public function setAdultCount($adultCount)
  {
    $this->adultCount = $adultCount;
  }
  public function getAdultCount()
  {
    return $this->adultCount;
  }
  public function setChildCount($childCount)
  {
    $this->childCount = $childCount;
  }
  public function getChildCount()
  {
    return $this->childCount;
  }
  public function setInfantInLapCount($infantInLapCount)
  {
    $this->infantInLapCount = $infantInLapCount;
  }
  public function getInfantInLapCount()
  {
    return $this->infantInLapCount;
  }
  public function setInfantInSeatCount($infantInSeatCount)
  {
    $this->infantInSeatCount = $infantInSeatCount;
  }
  public function getInfantInSeatCount()
  {
    return $this->infantInSeatCount;
  }
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  public function getKind()
  {
    return $this->kind;
  }
  public function setSeniorCount($seniorCount)
  {
    $this->seniorCount = $seniorCount;
  }
  public function getSeniorCount()
  {
    return $this->seniorCount;
  }
}
