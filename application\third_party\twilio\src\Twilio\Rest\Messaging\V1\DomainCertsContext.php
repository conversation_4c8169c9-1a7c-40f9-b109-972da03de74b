<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Messaging\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class DomainCertsContext extends InstanceContext
    {
    /**
     * Initialize the DomainCertsContext
     *
     * @param Version $version Version that contains the resource
     * @param string $domainSid Unique string used to identify the domain that this certificate should be associated with.
     */
    public function __construct(
        Version $version,
        $domainSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'domainSid' =>
            $domainSid,
        ];

        $this->uri = '/LinkShortening/Domains/' . \rawurlencode($domainSid)
        .'/Certificate';
    }

    /**
     * Delete the DomainCertsInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the DomainCertsInstance
     *
     * @return DomainCertsInstance Fetched DomainCertsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): DomainCertsInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new DomainCertsInstance(
            $this->version,
            $payload,
            $this->solution['domainSid']
        );
    }


    /**
     * Update the DomainCertsInstance
     *
     * @param string $tlsCert Contains the full TLS certificate and private for this domain in PEM format: https://en.wikipedia.org/wiki/Privacy-Enhanced_Mail. Twilio uses this information to process HTTPS traffic sent to your domain.
     * @return DomainCertsInstance Updated DomainCertsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(string $tlsCert): DomainCertsInstance
    {

        $data = Values::of([
            'TlsCert' =>
                $tlsCert,
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new DomainCertsInstance(
            $this->version,
            $payload,
            $this->solution['domainSid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Messaging.V1.DomainCertsContext ' . \implode(' ', $context) . ']';
    }
}
