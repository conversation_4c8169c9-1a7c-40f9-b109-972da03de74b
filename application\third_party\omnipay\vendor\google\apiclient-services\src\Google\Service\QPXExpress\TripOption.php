<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_QPXExpress_TripOption extends Google_Collection
{
  protected $collection_key = 'slice';
  public $id;
  public $kind;
  protected $pricingType = 'Google_Service_QPXExpress_PricingInfo';
  protected $pricingDataType = 'array';
  public $saleTotal;
  protected $sliceType = 'Google_Service_QPXExpress_SliceInfo';
  protected $sliceDataType = 'array';

  public function setId($id)
  {
    $this->id = $id;
  }
  public function getId()
  {
    return $this->id;
  }
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  public function getKind()
  {
    return $this->kind;
  }
  public function setPricing($pricing)
  {
    $this->pricing = $pricing;
  }
  public function getPricing()
  {
    return $this->pricing;
  }
  public function setSaleTotal($saleTotal)
  {
    $this->saleTotal = $saleTotal;
  }
  public function getSaleTotal()
  {
    return $this->saleTotal;
  }
  public function setSlice($slice)
  {
    $this->slice = $slice;
  }
  public function getSlice()
  {
    return $this->slice;
  }
}
