<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_CloudUserAccounts_LinuxUserView extends Google_Model
{
  public $gecos;
  public $gid;
  public $homeDirectory;
  public $shell;
  public $uid;
  public $username;

  public function setGecos($gecos)
  {
    $this->gecos = $gecos;
  }
  public function getGecos()
  {
    return $this->gecos;
  }
  public function setGid($gid)
  {
    $this->gid = $gid;
  }
  public function getGid()
  {
    return $this->gid;
  }
  public function setHomeDirectory($homeDirectory)
  {
    $this->homeDirectory = $homeDirectory;
  }
  public function getHomeDirectory()
  {
    return $this->homeDirectory;
  }
  public function setShell($shell)
  {
    $this->shell = $shell;
  }
  public function getShell()
  {
    return $this->shell;
  }
  public function setUid($uid)
  {
    $this->uid = $uid;
  }
  public function getUid()
  {
    return $this->uid;
  }
  public function setUsername($username)
  {
    $this->username = $username;
  }
  public function getUsername()
  {
    return $this->username;
  }
}
