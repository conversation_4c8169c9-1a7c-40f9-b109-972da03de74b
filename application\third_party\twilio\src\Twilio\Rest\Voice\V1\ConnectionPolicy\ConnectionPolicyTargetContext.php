<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Voice
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Voice\V1\ConnectionPolicy;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;


class ConnectionPolicyTargetContext extends InstanceContext
    {
    /**
     * Initialize the ConnectionPolicyTargetContext
     *
     * @param Version $version Version that contains the resource
     * @param string $connectionPolicySid The SID of the Connection Policy that owns the Target.
     * @param string $sid The unique string that we created to identify the Target resource to delete.
     */
    public function __construct(
        Version $version,
        $connectionPolicySid,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'connectionPolicySid' =>
            $connectionPolicySid,
        'sid' =>
            $sid,
        ];

        $this->uri = '/ConnectionPolicies/' . \rawurlencode($connectionPolicySid)
        .'/Targets/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Delete the ConnectionPolicyTargetInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the ConnectionPolicyTargetInstance
     *
     * @return ConnectionPolicyTargetInstance Fetched ConnectionPolicyTargetInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): ConnectionPolicyTargetInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new ConnectionPolicyTargetInstance(
            $this->version,
            $payload,
            $this->solution['connectionPolicySid'],
            $this->solution['sid']
        );
    }


    /**
     * Update the ConnectionPolicyTargetInstance
     *
     * @param array|Options $options Optional Arguments
     * @return ConnectionPolicyTargetInstance Updated ConnectionPolicyTargetInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): ConnectionPolicyTargetInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' =>
                $options['friendlyName'],
            'Target' =>
                $options['target'],
            'Priority' =>
                $options['priority'],
            'Weight' =>
                $options['weight'],
            'Enabled' =>
                Serialize::booleanToString($options['enabled']),
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new ConnectionPolicyTargetInstance(
            $this->version,
            $payload,
            $this->solution['connectionPolicySid'],
            $this->solution['sid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Voice.V1.ConnectionPolicyTargetContext ' . \implode(' ', $context) . ']';
    }
}
