<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_ToolResults_SkippedDetail extends Google_Model
{
  public $incompatibleAppVersion;
  public $incompatibleArchitecture;
  public $incompatibleDevice;

  public function setIncompatibleAppVersion($incompatibleAppVersion)
  {
    $this->incompatibleAppVersion = $incompatibleAppVersion;
  }
  public function getIncompatibleAppVersion()
  {
    return $this->incompatibleAppVersion;
  }
  public function setIncompatibleArchitecture($incompatibleArchitecture)
  {
    $this->incompatibleArchitecture = $incompatibleArchitecture;
  }
  public function getIncompatibleArchitecture()
  {
    return $this->incompatibleArchitecture;
  }
  public function setIncompatibleDevice($incompatibleDevice)
  {
    $this->incompatibleDevice = $incompatibleDevice;
  }
  public function getIncompatibleDevice()
  {
    return $this->incompatibleDevice;
  }
}
