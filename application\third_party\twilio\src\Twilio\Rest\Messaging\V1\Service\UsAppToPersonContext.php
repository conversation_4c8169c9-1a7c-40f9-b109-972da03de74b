<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Messaging\V1\Service;

use Twilio\Exceptions\TwilioException;
use Twilio\Version;
use Twilio\InstanceContext;


class UsAppToPersonContext extends InstanceContext
    {
    /**
     * Initialize the UsAppToPersonContext
     *
     * @param Version $version Version that contains the resource
     * @param string $messagingServiceSid The SID of the [Messaging Service](https://www.twilio.com/docs/messaging/services/api) to create the resources from.
     * @param string $sid The SID of the US A2P Compliance resource to delete `QE2c6890da8086d771620e9b13fadeba0b`.
     */
    public function __construct(
        Version $version,
        $messagingServiceSid,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'messagingServiceSid' =>
            $messagingServiceSid,
        'sid' =>
            $sid,
        ];

        $this->uri = '/Services/' . \rawurlencode($messagingServiceSid)
        .'/Compliance/Usa2p/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Delete the UsAppToPersonInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the UsAppToPersonInstance
     *
     * @return UsAppToPersonInstance Fetched UsAppToPersonInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): UsAppToPersonInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new UsAppToPersonInstance(
            $this->version,
            $payload,
            $this->solution['messagingServiceSid'],
            $this->solution['sid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Messaging.V1.UsAppToPersonContext ' . \implode(' ', $context) . ']';
    }
}
