{"name": "omnipay/paypal", "type": "library", "description": "PayPal gateway for Omnipay payment processing library", "keywords": ["gateway", "merchant", "omnipay", "pay", "payment", "paypal", "purchase"], "homepage": "https://github.com/thephpleague/omnipay-paypal", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Omnipay Contributors", "homepage": "https://github.com/thephpleague/omnipay-paypal/contributors"}], "autoload": {"psr-4": {"Omnipay\\PayPal\\": "src/"}}, "require": {"omnipay/common": "^3"}, "require-dev": {"omnipay/tests": "^3", "squizlabs/php_codesniffer": "^3", "phpro/grumphp": "^0.14"}, "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "prefer-stable": true}