<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Messaging\V1\BrandRegistration;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Version;


class BrandRegistrationOtpList extends ListResource
    {
    /**
     * Construct the BrandRegistrationOtpList
     *
     * @param Version $version Version that contains the resource
     * @param string $brandRegistrationSid Brand Registration Sid of Sole Proprietor Brand.
     */
    public function __construct(
        Version $version,
        string $brandRegistrationSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'brandRegistrationSid' =>
            $brandRegistrationSid,
        
        ];

        $this->uri = '/a2p/BrandRegistrations/' . \rawurlencode($brandRegistrationSid)
        .'/SmsOtp';
    }

    /**
     * Create the BrandRegistrationOtpInstance
     *
     * @return BrandRegistrationOtpInstance Created BrandRegistrationOtpInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(): BrandRegistrationOtpInstance
    {

        $payload = $this->version->create('POST', $this->uri);

        return new BrandRegistrationOtpInstance(
            $this->version,
            $payload,
            $this->solution['brandRegistrationSid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Messaging.V1.BrandRegistrationOtpList]';
    }
}
