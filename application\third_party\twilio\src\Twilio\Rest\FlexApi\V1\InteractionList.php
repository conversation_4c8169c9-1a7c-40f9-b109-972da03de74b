<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\FlexApi\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Serialize;


class InteractionList extends ListResource
    {
    /**
     * Construct the InteractionList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/Interactions';
    }

    /**
     * Create the InteractionInstance
     *
     * @param array $channel The Interaction's channel.
     * @param array $routing The Interaction's routing logic.
     * @param array|Options $options Optional Arguments
     * @return InteractionInstance Created InteractionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(array $channel, array $routing, array $options = []): InteractionInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'Channel' =>
                Serialize::jsonObject($channel),
            'Routing' =>
                Serialize::jsonObject($routing),
            'InteractionContextSid' =>
                $options['interactionContextSid'],
        ]);

        $payload = $this->version->create('POST', $this->uri, [], $data);

        return new InteractionInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Constructs a InteractionContext
     *
     * @param string $sid The SID of the Interaction resource to fetch.
     */
    public function getContext(
        string $sid
        
    ): InteractionContext
    {
        return new InteractionContext(
            $this->version,
            $sid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.FlexApi.V1.InteractionList]';
    }
}
