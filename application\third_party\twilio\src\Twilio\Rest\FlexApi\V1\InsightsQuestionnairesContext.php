<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\FlexApi\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;


class InsightsQuestionnairesContext extends InstanceContext
    {
    /**
     * Initialize the InsightsQuestionnairesContext
     *
     * @param Version $version Version that contains the resource
     * @param string $questionnaireSid The SID of the questionnaire
     */
    public function __construct(
        Version $version,
        $questionnaireSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'questionnaireSid' =>
            $questionnaireSid,
        ];

        $this->uri = '/Insights/QualityManagement/Questionnaires/' . \rawurlencode($questionnaireSid)
        .'';
    }

    /**
     * Delete the InsightsQuestionnairesInstance
     *
     * @param array|Options $options Optional Arguments
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(array $options = []): bool
    {

        $options = new Values($options);

        $headers = Values::of(['Authorization' => $options['authorization']]);

        return $this->version->delete('DELETE', $this->uri, [], [], $headers);
    }


    /**
     * Fetch the InsightsQuestionnairesInstance
     *
     * @param array|Options $options Optional Arguments
     * @return InsightsQuestionnairesInstance Fetched InsightsQuestionnairesInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(array $options = []): InsightsQuestionnairesInstance
    {

        $options = new Values($options);

        $headers = Values::of(['Authorization' => $options['authorization']]);

        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new InsightsQuestionnairesInstance(
            $this->version,
            $payload,
            $this->solution['questionnaireSid']
        );
    }


    /**
     * Update the InsightsQuestionnairesInstance
     *
     * @param bool $active The flag to enable or disable questionnaire
     * @param array|Options $options Optional Arguments
     * @return InsightsQuestionnairesInstance Updated InsightsQuestionnairesInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(bool $active, array $options = []): InsightsQuestionnairesInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'Active' =>
                Serialize::booleanToString($active),
            'Name' =>
                $options['name'],
            'Description' =>
                $options['description'],
            'QuestionSids' =>
                Serialize::map($options['questionSids'], function ($e) { return $e; }),
        ]);

        $headers = Values::of(['Authorization' => $options['authorization']]);

        $payload = $this->version->update('POST', $this->uri, [], $data, $headers);

        return new InsightsQuestionnairesInstance(
            $this->version,
            $payload,
            $this->solution['questionnaireSid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.FlexApi.V1.InsightsQuestionnairesContext ' . \implode(' ', $context) . ']';
    }
}
