<?php
defined('BASEPATH') or exit('No direct script access allowed');

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON>Mailer\PHPMailer\SMTP;
require_once(APPPATH . 'third_party/phpmailer/autoload.php');

class Mailer
{
    private $CI;
    public function __construct()
    {
        $this->CI = &get_instance();
    }

    public function send($data = array(), $err = false)
    {
        try {
            // Validate required data
            if (empty($data['branch_id'])) {
                if (ENVIRONMENT === 'development') {
                    log_message('error', 'Mailer: Branch ID is required');
                }
                return $err ? 'Branch ID is required' : false;
            }

            if (empty($data['recipient'])) {
                if (ENVIRONMENT === 'development') {
                    log_message('error', 'Mailer: Recipient email is required');
                }
                return $err ? 'Recipient email is required' : false;
            }

            // Get email configuration
            $getConfig = $this->CI->db->get_where('email_config', array('branch_id' => $data['branch_id']))->row();

            if (empty($getConfig)) {
                if (ENVIRONMENT === 'development') {
                    log_message('error', 'Mailer: Email configuration not found for branch_id: ' . $data['branch_id']);
                }
                return $err ? 'Email configuration not found for branch ID: ' . $data['branch_id'] : false;
            }

            // Validate email configuration
            if (empty($getConfig->email)) {
                if (ENVIRONMENT === 'development') {
                    log_message('error', 'Mailer: System email not configured');
                }
                return $err ? 'System email not configured' : false;
            }

            $school_name = get_global_setting('institute_name');
            $mail = new PHPMailer(true); // Enable exceptions

            // Configure mail settings based on protocol
            if ($getConfig->protocol == 'smtp') {
                // Validate SMTP configuration
                if (empty($getConfig->smtp_host) || empty($getConfig->smtp_port)) {
                    if (ENVIRONMENT === 'development') {
                        log_message('error', 'Mailer: SMTP host or port not configured');
                    }
                    return $err ? 'SMTP host or port not configured' : false;
                }

                $mail->isSMTP();
                $mail->SMTPDebug = (ENVIRONMENT === 'development') ? SMTP::DEBUG_SERVER : SMTP::DEBUG_OFF;
                $mail->Host = trim($getConfig->smtp_host);
                $mail->Port = trim($getConfig->smtp_port);

                if (!empty($getConfig->smtp_encryption)) {
                    $mail->SMTPSecure = $getConfig->smtp_encryption;
                }

                $mail->SMTPAuth = $getConfig->smtp_auth;

                if ($getConfig->smtp_auth) {
                    if (empty($getConfig->smtp_user) || empty($getConfig->smtp_pass)) {
                        if (ENVIRONMENT === 'development') {
                            log_message('error', 'Mailer: SMTP authentication enabled but username/password not configured');
                        }
                        return $err ? 'SMTP username or password not configured' : false;
                    }
                    $mail->Username = trim($getConfig->smtp_user);
                    $mail->Password = trim($getConfig->smtp_pass);
                }
            } else {
                $mail->isSendmail();
            }

            // Add attachment if provided
            if (!empty($data['file'])) {
               $mail->addStringAttachment($data['file'], $data['file_name']);
            }

            // Set email headers and content
            $mail->setFrom($getConfig->email, $school_name);
            $mail->addReplyTo($getConfig->email, $school_name);
            $mail->addAddress($data['recipient']);
            $mail->Subject = $data['subject'];
            $mail->isHTML(true); // Set email format to HTML
            $mail->Body = $data['message'];
            $mail->AltBody = strip_tags($data['message']); // Plain text version

            // Log sending attempt (development only)
            if (ENVIRONMENT === 'development') {
                log_message('debug', 'Mailer: Attempting to send email to: ' . $data['recipient'] . ' via ' . $getConfig->protocol);
            }

            // Send email
            if ($mail->send()) {
                if (ENVIRONMENT === 'development') {
                    log_message('debug', 'Mailer: Email sent successfully to: ' . $data['recipient']);
                }
                return true;
            } else {
                if (ENVIRONMENT === 'development') {
                    log_message('error', 'Mailer: Failed to send email - ' . $mail->ErrorInfo);
                }
                return $err ? $mail->ErrorInfo : false;
            }

        } catch (Exception $e) {
            if (ENVIRONMENT === 'development') {
                log_message('error', 'Mailer: Exception - ' . $e->getMessage());
            }
            return $err ? $e->getMessage() : false;
        }
    }
}
