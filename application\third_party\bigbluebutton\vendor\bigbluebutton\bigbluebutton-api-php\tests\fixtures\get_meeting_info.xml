<?xml version="1.0" encoding="UTF-8"?>
<response>
    <returncode>SUCCESS</returncode>
    <meetingName>Mock meeting for testing getMeetingInfo API method</meetingName>
    <meetingID>117b12ae2656972d330b6bad58878541-28-15</meetingID>
    <internalMeetingID>178757fcedd9449054536162cdfe861ddebc70ba-1453206317376</internalMeetingID>
    <createTime>1453206317376</createTime>
    <createDate>Tue Jan 19 07:25:17 EST 2016</createDate>
    <voiceBridge>70100</voiceBridge>
    <dialNumber>613-555-1234</dialNumber>
    <attendeePW>dbfc7207321527bbb870c82028</attendeePW>
    <moderatorPW>4bfbbeeb4a65cacaefe3676633</moderatorPW>
    <running>true</running>
    <duration>20</duration>
    <hasUserJoined>true</hasUserJoined>
    <recording>true</recording>
    <hasBeenForciblyEnded>false</hasBeenForciblyEnded>
    <startTime>1453206317380</startTime>
    <endTime>1453206325002</endTime>
    <participantCount>2</participantCount>
    <listenerCount>1</listenerCount>
    <voiceParticipantCount>2</voiceParticipantCount>
    <videoCount>1</videoCount>
    <maxUsers>20</maxUsers>
    <moderatorCount>2</moderatorCount>
    <attendees>
        <attendee>
            <userID>amslzbgzzddp</userID>
            <fullName>Ernie Abernathy</fullName>
            <role>MODERATOR</role>
            <isPresenter>true</isPresenter>
            <isListeningOnly>false</isListeningOnly>
            <hasJoinedVoice>true</hasJoinedVoice>
            <hasVideo>true</hasVideo>
            <clientType>HTML5</clientType>
            <customdata></customdata>
        </attendee>
        <attendee>
            <userID>xi7y7gpmyq1g</userID>
            <fullName>Barrett Kutch</fullName>
            <role>MODERATOR</role>
            <isPresenter>false</isPresenter>
            <isListeningOnly>false</isListeningOnly>
            <hasJoinedVoice>true</hasJoinedVoice>
            <hasVideo>false</hasVideo>
            <clientType>FLASH</clientType>
            <customdata>
                <skipCheck>true</skipCheck>
                <backgroundColor>#FF0033</backgroundColor>
                <customStyle>a:focus{color:#0181eb}</customStyle>
            </customdata>
        </attendee>
    </attendees>
    <isBreakout>true</isBreakout>
    <parentMeetingID>b97b512f2c92c0ffe7a3476152525807daa1c676-1524213151782</parentMeetingID>
    <sequence>1</sequence>
    <metadata>
        <bbb-context>Best BBB Developers Club</bbb-context>
        <bn-origin>Moodle</bn-origin>
        <bn-recording-ready-url>
            http://bigbluebutton.org/moodle/mod/bigbluebuttonbn/bbb_broker.php?action=recording_ready
        </bn-recording-ready-url>
        <bbb-origin-tag>moodle-mod_bigbluebuttonbn (2015080609)</bbb-origin-tag>
        <bbb-origin-version>3.0.2 (Build: 20160111)</bbb-origin-version>
        <bbb-origin-server-common-name></bbb-origin-server-common-name>
        <bbb-origin-server-name>bigbluebutton.org</bbb-origin-server-name>
        <bbb-recording-description></bbb-recording-description>
        <bbb-recording-name>Bigbluebutton "Mock meeting for testing getMeetingInfo"</bbb-recording-name>
        <bbb-recording-tags></bbb-recording-tags>
    </metadata>
    <messageKey></messageKey>
    <message></message>
</response>