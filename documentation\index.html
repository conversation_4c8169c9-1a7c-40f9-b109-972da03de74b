<!doctype html>
<!--[if IE 6 ]><html lang="en-us" class="ie6"> <![endif]-->
<!--[if IE 7 ]><html lang="en-us" class="ie7"> <![endif]-->
<!--[if IE 8 ]><html lang="en-us" class="ie8"> <![endif]-->
<!--[if (gt IE 7)|!(IE)]><!-->
<html lang="en-us">
<!--<![endif]-->

<head>
    <meta charset="utf-8">

    <title>PASS-DRC- School Management System</title>

    <meta name="description" content="An easy way to generate your documentation!">
    <meta name="author" content="PASS-DRC
    <meta name="copyright" content="PASS-DRC>
    <meta name="generator" content="Documenter v2.0 http://rxa.li/documenter">
    <meta name="date" content="2019-01-17T00:00:00+01:00">

    <link rel="stylesheet" href="assets/css/documenter_style.css" media="all">
    <link rel="stylesheet" href="assets/js/google-code-prettify/prettify.css" media="screen">
    <script src="assets/js/google-code-prettify/prettify.js"></script>


    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico" />

    <script src="assets/js/jquery.js"></script>
    <script src="assets/js/cufon.js"></script>
    <script src="assets/js/font.js"></script>
    <script>
        Cufon.replace("h1, h2, h3, h4, h5, h6");
    </script>
    <script src="assets/js/jquery.scrollTo.js"></script>
    <script src="assets/js/jquery.easing.js"></script>

    <script>
        document.createElement('section');
        var duration = '450',
            easing = 'easeOutBack';
    </script>
    <script src="assets/js/script.js"></script>

    <style>
            html {
            background-color: #F3F3F3;
            color: #585858;
        }
        
        ::-moz-selection {
            background: #111111;
            color: #F1F1F1;
        }
        
        ::selection {
            background: #111111;
            color: #F1F1F1;
        }
        
        #documenter_sidebar #documenter_logo {
            background-image: url(assets/images/image_16.png);
        }
        
        a {
            color: #111111;
        }
        
        .btn {
            border-radius: 3px;
        }
        
        .btn-primary {
            background-image: -moz-linear-gradient(top, #585858, #3B3B3B);
            background-image: -ms-linear-gradient(top, #585858, #3B3B3B);
            background-image: -webkit-gradient(linear, 0 0, 0 585858%, from(#333333), to(#3B3B3B));
            background-image: -webkit-linear-gradient(top, #585858, #3B3B3B);
            background-image: -o-linear-gradient(top, #585858, #3B3B3B);
            background-image: linear-gradient(top, #585858, #3B3B3B);
            filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#585858', endColorstr='#3B3B3B', GradientType=0);
            border-color: #3B3B3B #3B3B3B #bfbfbf;
            color: #F9F9F9;
        }
        
        .btn-primary:hover,
        .btn-primary:active,
        .btn-primary.active,
        .btn-primary.disabled,
        .btn-primary[disabled] {
            border-color: #585858 #585858 #bfbfbf;
            background-color: #3B3B3B;
        }
        
        hr {
            border-top: 1px solid #E5E5E5;
            border-bottom: 1px solid #F9F9F9;
        }
        
        #documenter_sidebar,
        #documenter_sidebar ul a {
            background-color: #333333;
            color: #F1F1F1;
            http: //static.revaxarts-themes.com/noise.gif}
            #documenter_sidebar ul a {
                -webkit-text-shadow: 1px 1px 0px #444444;
                -moz-text-shadow: 1px 1px 0px #444444;
                text-shadow: 1px 1px 0px #444444;
            }
            #documenter_sidebar ul {
                border-top: 1px solid #222222;
            }
            #documenter_sidebar ul a {
                border-top: 1px solid #444444;
                border-bottom: 1px solid #222222;
                color: #F1F1F1;
            }
            #documenter_sidebar ul a:hover {
                background: #111111;
                color: #F1F1F1;
                border-top: 1px solid #111111;
            }
            #documenter_sidebar ul a.current {
                background: #111111;
                color: #F1F1F1;
                border-top: 1px solid #111111;
            }
            #documenter_copyright {
                display: block !important;
                visibility: visible !important;
            }
            img.img-responsive {
                display: block;
                max-width: 100%;
                height: auto;
            }
    </style>

</head>

<body class="documenter-project-ramom-school-management-system">
    <div id="documenter_sidebar">
        <a href="#documenter_cover" id="documenter_logo"></a>
        <ul id="documenter_nav">
            <li><a class="current" href="#documenter_cover">Start</a></li>
            <li><a href="#system_requirements">System Requirements</a></li>
            <li><a href="#installation">Installation</a></li>
            <li><a href="#local_server_installation">Local Server Installation</a></li>
            <li><a href="#faq">FAQ</a></li>
            <li><a href="#usage_instructions">Usage Instructions</a></li>
            <li><a href="#changelog">Changelog</a></li>
            <li><a href="#supporters">Customer Support</a></li>
        </ul>
        <div id="documenter_copyright">powered by PASS-DRC since 2021 <br> made with the <a href="http://rxa.li/documenter">Documenter v2.0</a>
        </div>
    </div>
    <div id="documenter_content">
        <section id="documenter_cover">
            <h1>PASS-DRC - Multi Branch School Management System</h1>

            <div id="documenter_buttons">
                <a href="http://revaxarts-themes.com/documenter/" class="btn btn-primary btn-large">Documenter WebApp</a>
            </div>
            <hr>
            <ul>
                <li>Created: 13 / January / 2021</li>
                <li>Latest update: 08 / June / 2025</li>
                <li>Created by : PASS-DRC</li>
                <li>Email : <EMAIL></li>
            </ul>
            <p>Thank you for choosing PASS-DRC Multi-Branch School Management System. Please read the full documentation carefully before starting.</p>
        </section>

        <section id="system_requirements">
            <div class="page-header">
                <h3>System Requirements</h3>
                <hr class="notop">
            </div>
            <ul>
                <li style="box-sizing: border-box;">PHP 5.6+
                </li>
                <li style="box-sizing: border-box;">MySQL 5.1+
                </li>
                <li style="box-sizing: border-box;">mod_rewrite Apache
                </li>
                <li style="box-sizing: border-box;">MySQLi PHP Extension
                </li>
                <li style="box-sizing: border-box;">PDO PHP Extension
                </li>
                <li style="box-sizing: border-box;">cURL PHP Extension
                </li>
                <li style="box-sizing: border-box;">OpenSSL PHP Extension
                </li>
                <li style="box-sizing: border-box;">MBString PHP Extension
                </li>
                <li style="box-sizing: border-box;">GD PHP Extension
                </li>
                <li style="box-sizing: border-box;">Zip PHP Extension
                </li>
                <li style="box-sizing: border-box;">allow_url_fopen enabled
                </li>
            </ul>
            * In most hosting accounts, these extensions are enabled by default. But you should check with your hosting provider.
        </section>
        <section id="installation">
            <div class="page-header">
                <h3>PASS-DRC School Installation</h3>
                <hr class="notop">
            </div>
            <h5>
                <strong>Configure database connection</strong>
            </h5>
            <p>You need to follow a few steps to complete the installation. First step Login to cPanel and create a brand new database.</p>
            <ul>
                <li>Login to cPanel by accessing www.yourdomain.com/cpanel and navigate go MySQL Databases</li>
                <li>Create database.</li>
                <li>Create user and set up user password. (write in a note username and password because needs to be re-used later) </li>
                <li>Add the user to the database by selecting the database and the username. </li>
                <li>Make sure you have checked All privileges when adding the user to the database. </li>
            </ul>
            <hr>
            <h5><strong> - Upload Files And Install</strong></h5>

            <p>Upload the downloaded zip file from PASS-DRC to your server. You can upload anywhere in your public_html folder or any sub-folder. Just remember that the directory where you uploaded it.</p>
            <h5>After installation <b>PASS-DRC School</b> to work properly, you must make few directories/files writeable. Below are a list of directories/files you should ensure that have write permissions.</h5>
            <ul style="word-wrap: break-word;">
                <li>installation_dir/application/config/config.php</li>
                <li>installation_dir/application/config/database.php</li>
                <li>installation_dir/application/config/autoload.php</li>
                <li>installation_dir/application/config/routes.php</li>
                <li>installation_dir/application/config/purchase_key.php</li>
                <li>installation_dir/temp</li>
                <li>installation_dir/uploads</li>
            </ul>
            <p class="note">
                <strong>You must pass the server requirements to install PASS-DRC School.</strong>
            </p>
            <p><img src="assets/images/install-1.png" alt="img" class="img-responsive"></p>
            <p class="note">
                <strong> If all server requirements passed click 'Start The Installation' button otherwise consult with your hosting provider to fix/enable them.</strong>
            </p>
            <p><img src="assets/images/install-2.png" alt="img" class="img-responsive"></p>
            <p class="note">
                <strong>Enter the PASS-DRCpurchase code and then click.</strong>
            </p>
            <p><img src="assets/images/install-3.png" alt="img" class="img-responsive"></p>
            <p class="note">
                <strong>Enter the database credentials you created previously and if the database connection is successfully you will be passed to the next step otherwise, please re-check your credentials<br> and hostname. (Generally hostnames are ‘localhost’, but you should check with your hosting provider or cPanel)</strong>
            </p>
            <p><img src="assets/images/install-4.png" alt="img" class="img-responsive"></p>
            <p class="note">
                <strong>Fill-Up the required information and click on the 'Install' button. This will save your school name and the Superadmin login credentials which will later be required to login to the application. <br> If the installation is successful you will see the message 'Congratulations!! The installation was successfull'</strong>
            </p>
            <p><img src="assets/images/install-5.png" alt="img" class="img-responsive"></p>
            <p class="note">
                <strong>PASS-DRC School has successfully installed. Now ready to login, you can click the URL to login</strong>
            </p>
        </section>
        <section id="local_server_installation">
            <div class="page-header">
                <h3>Local Server Installation</h3>
                <hr class="notop">
            </div>
            <h5>
                <strong>WAMP</strong>
            </h5>
            <p>Even if you are trying to install PASS-DRC School on WAMP Server you need to make sure to enable the apache 'rewrite_module' and PHP extensions 'php_openssl'.</p>
            <strong>STEP 1: Click on the WAMP icon in your taskbar and click on Apache => Apache modules => rewrite_module</strong><br>
            <strong>STEP 2: Click on the WAMP icon in your taskbar and click on PHP => PHP extensions => php_openssl</strong>
            <p><img src="assets/images/image_1.png" alt="img" class="img-responsive"></p>
        </section>
        <section id="faq">
            <div class="page-header">
                <h3>FAQ</h3>
                <hr class="notop">
            </div>
            <p class="info" style="border: 1px solid #6F98C3;background-color: #6EC4D2;">If you get 404 page not found error after installation.</p>
            <p>If you are getting 404 not found after you install PASS-DRC School this means that you need to adjust the main folder in .htaccess file.</p>
            <p>The .htaccess should look like this:</p>
            <pre style="margin-left: 20px;">
RewriteEngine On
RewriteBase /
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/$1 [L]
</pre>

            <h5>Installation on subdomain</h5>
            <pre style="margin-left: 20px;">
RewriteEngine On
RewriteBase /sub_foldername/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/$1 [L]
</pre>

            <p class="info" style="border: 1px solid #6F98C3;background-color: #6EC4D2;">If there are 500 errors after installation, try the following code as .htaccess</p>
            <pre style="margin-left: 20px;">
RewriteEngine on
RewriteRule ^([a-z0-9_-]+)\.html$ index.php/page/$1 [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond $1 !^(index\.php|asset|robots\.txt)
RewriteRule ^(.*)$ index.php?/$1 [QSA,L]
</pre>

            <p class="info" style="border: 1px solid #6F98C3;background-color: #6EC4D2;">If there are 'No Input File' errors after installation, try the following code as .htaccess</p>
            <pre style="margin-left: 20px;">
RewriteEngine On
RewriteBase /
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/?$1 [L]
</pre>
            <p>This error means that your servers PHP version is not 5.3 or higher. Contact your web host in order to update your PHP version to 5.3 or higher.</p>

            <p class="info" style="border: 1px solid #6F98C3;background-color: #6EC4D2;">If there are 'no input file specified' errors after installation, try the following code as .htaccess</p>
            <pre style="margin-left: 20px;">
&lt;IfModule mod_rewrite.c&gt; 
RewriteEngine On 
RewriteCond $1 !\.(gif|jpe?g|png)$ [NC] 
RewriteCond %{REQUEST_FILENAME} !-f 
RewriteCond %{REQUEST_FILENAME} !-d 
RewriteRule ^(.*)$ /index.php?/$1 [L] 
&lt;/IfModule&gt; 
</pre>
        </section>
        <section id="usage_instructions">
            <div class="page-header">
                <h3>PASS-DRC Multi-Branch School Usage Instructions</h3>
                <hr class="notop">
            </div>
            <h5>PASS-DRC School Benefits & Overview</h5>
            <p>PASS-DRC School Management is Multi-Branching education ERP System. This application will help the organization that has many Branch Schools and a SuperAdmin can control all the branches and staff. Each branch will be controlled by Admin. Information
                about different branches, cannot be viewed or modified by other branch users. All information will be separate and secure. It is effective and helpful for both types of schools, polytechnics and universities. There are language translation
                systems, Any user can change language and sessions and see all the systems in different languages and sessions record. Multiple branch systems will save your time and money, just one solution.</p>
            <p><strong>PASS-DRC Multi-Branch School ERP system provides the role of 7 users - Superadmin, Admin, Teacher, Accountant, Librarian, Student and their Parent.</strong></p>
            <p>
                <h4>Superadmin Panel</h4>
            </p>
            <pre class="prettyprint">
Login to the first Superadmin account.
</pre>
            <p><img src="assets/images/login.png" alt="img" class="img-responsive"></p>
            <p>After successful login you will see the Super Admin Dashboard. You will find all the features of the Navigational overview on the screen.</p>
            <p><img src="assets/images/dashboard.png" alt="img" class="img-responsive"></p>
            <h5>Super admin panel 17 modules included - </h5>
            <ul>
                <li>01. Branch - Super admin can maintain and create the all branch. Branch is consist of Branch List, Create branch.</li>
                <li>02. Admission - Consist of Create Admission, Multiple Import, Category.</li>
                <li>03. Student Details - Consist of Student List, Id Card Generate And Deactivate Account.</li>
                <li>04. Parents - Consist of Parents List, Add Parent And Deactivate Account.</li>
                <li>05. Employee - Consist of Employee List, Add Department, Add Designation, Add Employee And Deactivate Account.</li>
                <li>06. Human Resource - Consist of <strong>Payroll</strong> : Salary Template, Payment Type, Salary Assign, Salary Payment, Advance Salary, Generate Payslip, Payroll Summary / <strong>Leave Control</strong> : Category, Application / <strong>Award</strong>.</li>
                <li>07. Academic - Consist of <strong>Class and Section</strong> : Control Classes, Assign Class Teacher / <strong>Subject</strong> : Subject, Class Assign, Teacher Assign / <strong>Timetable</strong> : Class Timetable, Set Exam Timetable,
                    Exam Timetable And <strong>Student Promotion</strong>.</li>
                <li>08. Attachments Book - Consist of <strong>Upload Content</strong> and <strong>Attachment Type</strong>.</li>
                <li>09. Exam Master - Consist of <strong>Exam</strong> : Exam List, Set Exam Term, Exam Hall / <strong>Marks</strong> : Mark Entries, Grades Range And Tabulation Sheet.</li>
                <li>10. Supervision - Consist of <strong>Hostel</strong> : Hostel Master, Hostel Room, Category and Allocation List / <strong>Transport</strong> : Route Master, Route Master, Stoppage, Assign Vehicle, Allocation Report.</li>
                <li>11. Attendance - Consist of <strong>Set</strong> : Student Attendance, Employee Attendance and Exam Attendance / <strong>Reports</strong> : Student, Employee and Exam Attendance Report </li>
                <li>12. Library - Consist of Books Entry, Books Category and Books Issue.</li>
                <li>13. Events - Consist of Event Type and Add Event.</li>
                <li>14. Student Accounting - Consist of <strong>Fees</strong> : Create Single Invoice, Create Multi Invoice, Fees Pay/Invoice and Fee Category / <strong>Reports</strong> : Fee Payment History, Fees Summary Report, Fee Paid Report and Fee Due
                    Report.</li>
                <li>15. Office Accounting - Consist of Under Group, Bank Account, Ledger Account, Opening Balance and Create Voucher / <strong>Reports</strong> : Day Book, Cash Book, Bank Book, Ledger Book and Trail Balance.</li>
                <li>16. Message - Consist of Mailbox Folder and Inbox.</li>
                <li>17. Settings - Consist of <strong>Global Settings</strong> : General Settings, Theme Settings and Logo Settings / <strong>Paymeny Settings</strong> : Paypal Config, Stripe Config and Payumoney Config / <strong>SMS Settings</strong> : SMS
                    Config and SMS Triggers / <br><strong>Email Settings</strong> : Email Config and Email Triggers / <strong>Session Settings / Translations / Database Backup</strong> </li>
            </ul>

            <p>Go to the SuperAdmin Navigation Bar Settings > Global Settings within General Settings / Theme Settings / Logo and you can update your information. <br> * Note: Global settings are applicable for All Branches. Admin can not change this setting.</p>
            <p><img src="assets/images/g_setting.png" alt="img" class="img-responsive"></p>
            <p>Systems Theme setting.</p>
            <p><img src="assets/images/t_setting.png" alt="img" class="img-responsive"></p>
            <p>Systems Logo setting.</p>
            <p><img src="assets/images/l_setting.png" alt="img" class="img-responsive"></p>
            <p>After updating all the settings and logos, you first need to add a <strong>Branch</strong>.</p>
            <p>

                <h5>Add Branch :</h5>
                <ul>
                    <li>Login as Superadmin.</li>
                    <li>Superadmin can maintain and create the Branch.</li>
                    <li>First you have to create a new Branch, click the <b>Branch</b> > <b>Create Branch</b> tab and fill out all the information and click the Save button.</li>
                </ul>
                <p><b>* Note</b> : Admin can't manage branch information.</p>
                <p><img src="assets/images/img_01.png" alt="img" class="img-responsive"></p>

                <h5>Add Sections :</h5>
                <ul>
                    <li>Login as Superadmin / Admin.</li>
                    <li>For adding sections go to <b>Academic > Class &amp; Section > Control Classes</b> click tab right side <b>'Section'</b> and fill up form and click save button.</li>
                </ul>
                <p><img src="assets/images/img_02.png" alt="img" class="img-responsive"></p>

                <h5>Add Class :</h5>
                <ul>
                    <li>Login as Superadmin / Admin.</li>
                    <li>For adding class go to <b>Academic > Class &amp; Section > Control Classes</b> click tab right side <b>'Class'</b> and fill up form and click save button.</li>
                    <li>You can set up multiple Section. <b>(Example : For Class 1 can choose for Section A, Section B &amp; Section C)</b></li>
                </ul>
                <p><img src="assets/images/img_03.png" alt="img" class="img-responsive"></p>

                <h5>Assign Class Teacher :</h5>
                <ul>
                    <li>Login as Superadmin / Admin.</li>
                    <li>For adding class go to <b>Academic > Class &amp; Section > Assign Class Teacher</b> select Class and Section then select Class Teacher and click on <b>Save</b> button.</li>
                    <li>You must first add teachers to add <b>Class Teacher</b>.</li>
                </ul>
                <p><img src="assets/images/img_04.png" alt="img" class="img-responsive"></p>

                <h5>Student Category :</h5>
                <ul>
                    <li>Login as Superadmin / Admin / Teacher.</li>
                    <li>For Student Addmission First we will add <b>Student Category</b> then take Student Admission then search Students List.</li>
                    <li>Go to <b>Admission > Category</b> Add all student categories here. Student categories can be caste, community or group wise.</li>
                </ul>
                <p><img src="assets/images/img_05.png" alt="img" class="img-responsive"></p>


                <h5>Student Addmission :</h5>
                <ul>
                    <li>Login as Superadmin / Admin / Teacher.</li>
                    <li>For Student Addmission Go to <b>Admission > Create Admission</b> and fill form...</li>
                    <li><b>01. Academic Details</b>- (Academic Year, Register No, Roll, Admission Date, Class, Section, Category)</li>
                    <li><b>02. Student Details </b>- First Name, Last Name, Blood group, Gender, Date Of Birth and etc..(This * symbol means value is required).</li>
                    <li><b>03. Login Details </b>- Login email and Login password</li>
                    <li><b>04. Guardian Details </b>- Parent information already available check on <b>"Guardian Already Exist"</b> and select <b>"Guardian"</b> in list, otherwise fillup Guardian information.</li>
                    <li><b>05. Transport Details </b>- You can select student <b>Transportation routes and Vehicles no</b>, otherwise skip this.</li>
                    <li><b>06. Hostel Details </b>- You can select student <b>Hostel and Room</b>, otherwise skip this.</li>
                    <li><b>07. Previous School Details - </b> The details of the previous school are not mandatory.</li>
                </ul>
                <p><img src="assets/images/img_06.png" alt="img" class="img-responsive"></p>
                <p><img src="assets/images/img_07.png" alt="img" class="img-responsive"></p>


                <h5>Multiple Import :</h5>
                <ul>
                    <li>Login as Superadmin / Admin</li>
                    <li>For Student Addmission Go to <b>Admission > Multiple Import</b></li>
                    <li>Follow the instructions inside the page. Multiple students have been successfully added and will get a great deal of information on the student list completely.</li>
                </ul>
                <p><img src="assets/images/img_08.png" alt="img" class="img-responsive"></p>

                <h5>Student Details :</h5>
                <ul>
                    <li>It maintain <b>Student List, ID Card Denerate and Login Deactivate.</b></li>
                    <li>Go to <b>Student Details > Student List</b> inside page has Student list click and open the selecting field as like <b>Branch, Class, Section</b> and press search button.</li>
                </ul>
                <p><img src="assets/images/img_09.png" alt="img" class="img-responsive"></p>

                <h5>Add Employee :</h5>
                <ul>
                    <li>Login as Superadmin / Admin.</li>
                    <li>Superadmin and Admin can create, edit and delete employee.</li>
                    <li><b>Designation and Department</b> for Employees to be added will be must required.</li>
                    <li>For adding employee Go to <b>Employee > Add Employee</b> And fill up all information and click <b>Save</b> button.</li>
                </ul>
            </p>
            <p><img src="assets/images/img_10.png" alt="img" class="img-responsive"></p>

            <section id="changelog">
                <div class="page-header">
                    <h3>Changelog</h3>
                    <hr class="notop">
                </div>
                <strong>Version 7.0 – 08, June, 2025</strong>
<pre>Added - Php 8.4 Support (Beta).
Added - Server-Side Datatables To Manage Large Number Of Branches And Students.
Added - Transport Fee Will Be Automatically Added To The Invoice.
Added - Multiple Stoppage Can Be Added To The Same Route.
Added - Student List Getting Without Class And Section Filters.
Added - Admit Card Print Option In Student/Parent Panel.
Added - Report Card Print / Pdf Downlad Same As Admin Portal.
Added - Online Admission Application Form Upload.
Added - Online Exam Published Email Templates.
Added - Datatable Page Length Switcher.
Added - Datatable Settings Preserve.
Added - Sidebar Toggle Preserve.
Updated - Revamped Student List Table.
Updated - Teacher Role Will Only Be Able To View Own Timetable.
Updated - "Homework" Server-Side Datatable.
Updated - "Homework Report" Server-Side Datatable.
Updated - Homework Attachment File Are Not Mandatory.
Updated - Revamped Homework Student Portal.
Updated - Section And Subject Are Not Mandatory In "Homework" List Search.
Updated - "Homework Report" Server-Side Datatables.
Updated - "Fees Pay / Invoice" Server-Side Datatables.
Updated - Class And Section Are Not Mandatory In "Fees Pay / Invoice" List Search.
Updated - "Due Fees Invoice" Server-Side Datatables.
Updated - Event Server-Side Datatables.
Updated - Inventory Purchase,Sales,Issue Server-Side Datatables.
Updated - Office Accounting Deposit,Expense,All Transactions Server-Side Datatables.
Updated - Stripe Api.
Updated - Mpdf Libraries.
Fixed - Some Users Were Experiencing Issues During Installation.
Fixed - Front-End News Order By Desc.
Fixed - Front-End News Read More Not Working (Unicode Character).
Fixed - Showing Custom Fields In "Student List" Table.
Fixed - Bulk Pdf Download Issue In Invoice.
Fixed - Offline Payments List Issue.
- Performance Has Been Improved.
- Fixed All Known Bugs.</pre>

                <strong>Version 6.9 – 06, March, 2025</strong>
                <pre>Added - Disabled Students Will Not Appear In Any List.
Added - After Updating To A New Version, The Cache Of .CSS /.JS Files Will Be Automatically Cleared.
Added - Bkash Payment Gateway.
Added - Cache Control Enable / Disable.
Added - Branch Name In Fee Report (Print).
Added - Front-End Support RTL Print.
Fixed - When The Super Admin Set A New Session, An Error Was Generated When Logging Into The Student Portal.
Fixed - TRUE/FALSE Questions In Online Exams Were Not Being Counted If They Were Answered FALSE.
Fixed - The Fee Section Has Been Hidden From The Student's "Profile" Who Does Not Have Fee Collection Permission.
Fixed - Fees Invoice From One Branch Were Able To Be Viewed By Users From Another Branch.
Fixed - Logo Instan Uploading Issue
Fixed - Front-End News Page Php Error Issue.
Fixed - Attachments Mp4,Mp3 Downloading Issue.
Fixed - Bulk Invoice Print Issue.
Fixed - Pagination 8.2 Error Issue.
Fixed - Due Fees Invoice: Collect Button Issue.
Fixed - Fees Report: Calculation Issue.
Fixed - Receipts Report: Calculation Issue.
Fixed - Fees Revert Then Financial Income Report Section Not Reflected.
Fixed - Sometime Exam Result Error.
Fixed - Inventory "Purchase Bill" Not Showing Admin Portal.
Fixed - Front-End Print Now Support Mobile 
Fixed - Some Issues With Custom Fields.
Fixed - Redirected To Wrong Page After Admission Payment
Updated - Zoom API.
Fixed All Known Bugs.</pre>
                <strong>Version 6.7 – 15, June, 2024</strong>
                <pre>- Fixed - Some Users Have Face "Config Not Found" During Addon Installation.
- Fixed - 'Registration Number' And 'Roll' Already Exist Error Message When Edit Student Profile.
- Fixed - Searching Global Student Shows Other Student Profiles. 
- Fixed - Promotion - Carry Forward Due In Next Session Not Working Issue.
- Fixed - Student Profile Promotion History Issues.
- Fixed - Front-End Student Result Search Issue.
- Fixed - User Role Result Issue.
- Fixed - User Role Perioid Attendance Showing Issue.
- Fixed - In Perioid Other Attentdenc Not Access Issue.</pre>
                <strong>Version 6.6 – 23, May, 2024</strong>
                <pre>- Added - Generate Marksheet PDF.
- Added - Email Marksheet PDF To Students.
- Added - Generate Fee Invoice PDF.
- Added - Email Fee Invoice PDF To Students.
- Added - Marksheet Template in {exam_name} tag.
- Fixed - Student And Parent Login Issue.
- Fixed - Online Admission Submission Issue.
- Fixed - Online Admission Address Not Showing Issue.
- Fixed - Online Admission Custom Field Not Showing Issue.
- Fixed All Known Bugs.</pre>
                <strong>Version 6.5 – 12, May, 2024</strong>
                <pre>- Added - PHP 8.2 Support (Beta).
- Added - Addon Will Support Online Updates.
- Added - Marksheet Template with Background and Signature.
- Added - Subject Wise Attendance.
- Added - Alumni Modules.
- Added - Multi Class.
- Added - Added User Login Log.
- Added - News in Front-end.
- Added - Check Admission Status By Front-End Online Admission Page.
- Added - "Student / Parent" Panel Teachers "Mobile/email" show/hide Configable Option.
- Added - Student/Parent Login Enable/Disable Option.
- Added - Student Profile Page Sibling Information.
- Added - "Show only own questions" config option to Online Exams.
- Added - Attendance overview reports.
- Added - Student profile page in fee collect button.
- Added - Pagination List To Front-End Events Page.
- Update - Student Promotion Modules.
- Update - Student Profile Page Support System Student Field.
- Update - Razorpay Payment API.
- Fixed - RTL Issue.
- Fixed - Mobile Printing Issue.
- Fixed - Fee Collect Loading Issue.
- Fixed - Invoice Order Issue.
- Fixed - Gmeet Issue.
- Fixed - DATE OF BIRTH wHen Not Entered Shows Date Of "10Th-Sept-1960".
- Fixed - Toyyibpay Issue.
- Fixed - Recaptcha Issue.
- Fixed - Student Profile Loading/Opening Issue.
- Fixed - Payment History Showing Issue.
- Fixed - Product Unit Shown Category List Issue.
- Fixed - Product List Shown Other Branch Issue.
- Fixed - Event Showing Website Issue.
- Fixed - Student Promotion Issue.
- Fixed - Front-End Responsive Design Issues.
- Fixed All Known Bugs.
- We have made many minor changes and performance improvements.</pre>
                <strong>Version 6.2 – 18, August, 2023</strong>
                <pre>- Added - Addon Installer.
- Added - Addon Will Support Online Updates.
- Added - RTL ( Right to Left ).
- Added - Exam Class Possitin/Rank.
- Added - Exam Status Enable/Disable Option.
- Added - Enable/Disable Option To Publish Exam Results.
- Added - Exam Mark Register Validation Check.
- Added - Report Card Principal's Comment And Teachers Comment Save.
- Added - Offline Payment Automatic Fine Add.
- Added - Currency Format Configable.
- Added - Currency Symbol position Configable.
- Added - Login Page Each School Name And Address.
- Added - Quick Exam Schedule Creation Feature.
- Added - Quick Class Schedule Creation Feature.
- Added - Office Accounting Attachment File Download Option.
- Fixed - Branch Loading Issue.
- Fixed - Roll Already Exists In Other Session.
- Fixed - Inventory Category Edit Issue.
- Fixed - Inventory Module Not Hide In Sidebar.
- Fixed - Inventory Sales Not Found In Admin Role.
- Fixed - All Branch Product Showing Selling Page.
- Fixed - Parent Not Being Added.
- Updated twilio SMS API.
- Updated Zoom API Update JWT to OAuth.
- Fixed All Known Bugs.</pre>
                <strong>Version 6.0 – 28, June, 2023</strong>
                <pre>- Added Inventory Modules.
- Added Google Meet Live Class.
- Added Nepalste Payment Gateway (Nepali).
- Added Student Fees Revert will be Adjust with Office Accounting.
- Added "Next Follow Up Date" On the Admission Enquiry Page.
- Updated Zoom API.
- Fixed - Exam Result, Certificate, Progress Reports Generate and Print Issue.
- Fixed - Student ID Card User Details Not Match Issue.
- Fixed - Due Fees Report Frist Name Dobule Issue.
- Fixed - Due Fees Report "Previous Session Balance" Not Included Issue.
- Fixed - Due Fees Invoice "Previous Session Balance" Not Found Issue.
- Fixed - Fees Pay Status "Partly Paid" Showing Issue.
- Fixed - Whatsapp Subtitle Issue.
- Fixed - Office Accounting "Opening Balance" Issue.
- Fixed - Website Contact Form Email Issue.
- Fixed - Student Panel Own Attendance View Issue.
- Fixed - Student Panel Profile Photo Upload Issue.
- Fixed - Some Users Have Issue Importing(CSV) Online Exam Questions.
- Fixed - Exam Schedule List Appears Duplicate In Student Panel.
- Fixed - After Students Submit Their Homework To The Teacher, It is Not Reflecting as "Submitted".
- Fixed - User Panel Payment History Tab Under Fees History is Responsive Issue.
- Fixed - All Known Bugs.</pre>
                <strong>Version 5.7 – 14, May, 2023</strong>
                <pre>- Added Phpmailer Engine.
- Added Smtp Send Test Email In Email Setting Page.
- Added Different Registration Number Prefix For Each Branch.
- Added Register Number Start From And Digit Number Configurable.
- Added Weekend For Each Branch.
- Added Holiday Event Link To Attendance And Automatic Holiday Status In Attendance Report.
- Added In Attendance "Half Day" Option.
- Added Attendance Percentage (%) In Report Page.
- Added Daily Attendance Reports.
- Added Custom Sms Support dlt_template_id.
- Added Fee Reminder dlt_template_id.
- Added Login Out Then Redirect Own School Website .
- Added Event Edit Option And Events Module Has Been Updated.
- Added Paytm Payment Gateway (Indian).
- Added Toyyibpay Payment Gateway (Malaysian).
- Added Payhere Payment Gateway(Sri Lankan).
- Fixed - Global Email Updating Feature.
- Fixed - 3 Copy Slip Admin Role Access Denied.
- Fixed - Custom Sms Not Sent From "Bulk Sms And Email" Module.
- Fixed - Faq Page And Event Page Banner Images Are The Same.</pre>
                <strong>Version 5.6 – 20, April, 2023</strong>
                <pre>- Added Online exam CSV questions Import.
- Added 3 Copies Of Students Fee Payslip Print.
- Added Footer Branch Switcher Enable/disable Feature.
- Update Front-end bootstrap 5.2.
- Update Flutterwave API.
- Fixed - Custom Sms Sending Issue.
- Fixed - Section Click Then Redirect Home Page.
- Fixed - Employee Payslip Global Logo Issue.
- Fixed All Known Bugs.</pre>
                <strong>Version 5.5 – 24, Mar, 2023</strong>
                <pre>- Added Module Manager.
- Added Fees Offline Payments.
- Added Fully Customizable Front-End Any Color.
- Added Custom Sms.
- Added Student Disable Reason.
- Added Different Language For Each Branch.
- Added Different TimeZone For Each Branch.
- Added Google Analytics.
- Added In Invoice Register No, Father Name, Payment ID.
- Added Url New Routes.
- Added Login Credential Reports.
- Added Admission Reports.
- Added Class And Section Reports.
- Update "Bulk Sms And Email" With Dlt Template Id.
- Improved Security.
- Fixed - "Exam Progress Report" Showing Wrong Subjects Position.
- Fixed - Live Class Status.
- Fixed - Midtrans Payment Gateway Issue. 
- Fixed - Invoice Print Logo Issue.
- Fixed - Superadmin Profile Update Issue.
- Fixed - Gallery Album Issue.
- Removed - Front End Theme Options.
- Fixed All Known Bugs.</pre>
                <strong>Version 5.3 – 25, SEP, 2022</strong>
                <pre>- Added Reception Module.
- Added Birthday Wishes.
- Added Online Exam Fee Payment Feature.
- Added Online Exam Rank (Position With Remark).
- Added Online Exam Question List View.
- Added Online Exam Published Sms Notification.
- Added Different Currency And Symbols For Each Branch.
- Added Report Card Studnt Image.
- Added Report Card Remark.
- Added Exam Progress Report Card (With Cumulative Average, Class Average, Subject Position).
- Added New Receptionist Role.
- Added Bulksmsbd.Net Sms Gateway(Bangladeshi).
- Update Bulksms API.
- Fixed Student Profile Issue.
- Fixed Report Card Remark Issue.
- Fixed All Known Bugs.</pre>
                <strong>Version 5.2 – 10, AUG, 2022</strong>
                <pre>- Added Online Examination.
- Added Promotion History.
- Added Student Profile Edit Enable / Disable.
- Added MSG 91 dlt_template_id.
- Update Student Role Profile Page.
- Update Zoom Meeting API.
- Update Font-Awesome Icon.
- Fixed Communication Message Send Time Issue.
- Fixed Dashboard Graph Counting Issue.
- Fixed Parents Profile Child Issue.
- Fixed Exam tabulation sheet subjects Issue.
- Fixed all known bugs.</pre>
                <strong>Version 5.0 – 16, May, 2022</strong>
                <pre>- Support PHP 8 (Beta).
- Added Whatsapp chat with time schedule.
- Added System admission field customizable.
- Added Online admission field customizable.
- Added Multiple fee collection with discount.
- Added Carry Forward Due in Next Session.
- Added Documents can be uploaded for online admission.
- Added Photo can be uploaded for online admission.
- Added Online admission payment status view in admin side.
- Added Uploaded photo and file size and allowed extension configurable.
- Added Students will be able to submit homework.
- Added Flutterwave payment gateway.
- Added Deleting student will delete fee details and custom fields data.
- Improved student promotion.
- Update CodeIgniter version 3.1.13.
- Update Smscountry API.
- Update Textlocal API.
- Update Zoom Metting API.
- Fixed Student promotion related issue.
- Fixed Admin – gallery category edit issue.
- Fixed Front-end contact iMap saving issue.
- Fixed Student ID card qr-code issue.
- Fixed Fees invoice print generate issue.
- Fixed Fees invoice some user opening issue.
- Fixed all known bugs.</pre>
                <strong>Version 4.5 – 24, May, 2021</strong>
                <pre>- Added Online Admission Fees Payment Feature.
- Added Online Admission Student Application Copy Download Feature.
- Added Midtrans Indonesian Payment Gateway.
- Added SSLcommerz Bangladeshi Payment Gateway.
- Added Jazzcash Pakistani Payment Gateway.
- Update Stripe Payment Gateway.
- Added Front-end Home Page Section Visible / Hidden Feature.
- Added Front-end Home Page Customizable Counter Section.
- Added Teacher Schedule.
- Added "Apply Online Admission" Email Triggers.
- Added "Student Admission" Email Triggers.
- Added Feature For Uploading Login Page Side-Box Image From The "Global Settings" Page.
- Added Feature For Uploading Profile Page Image From The "Global Settings" Page.
- Print Examination Results In Student And Parent Panel.
- Update Email Libraries.
- Fixed All Email Sending Issue.
- Fixed Subject "No Information Available" Issue.
- Fixed Teacher Restricted Issue.
- Fixed JPEG format uploading issue.
- Fixed All Known Bugs.</pre>
                <strong>Version 4.0 – 20, April, 2021</strong>
                <pre>- Added each logo for each branch.
- Added front-end submenu.
- Added front-end system menu can be managed separately for each branch.
- Added front-end gallery.
- Added Sms country sms API.
- Added full fees can be paid with one click.
- Added fees revert.
- Added front-end exam results view and print.
- Added front-end certificates view and print.
- Added front-end admin card view and print.
- Update Zoom API.
- Fixed front-end menu issue.
- Fixed multiple fees assign issue.
- Fixed email sending issue
- Fixed all known bugs.</pre>
                <strong>Version 3.5 – 08, October, 2020</strong>
                <pre>- Added BigBlueButton.
- Zoom module has been redesigned.
- Added Each Staff will be able to add their own Zoom API credentials.
- Added Each Student will be able to add their own Zoom API credentials.
- Added Live class report with student participation detail.
- Added Multiple live classes can be taken at the same time.
- Added Preloader enable / disable feature.
- Fixed some bugs.</pre>
                <strong>Version 3.0 – 24, June, 2020</strong>
                <pre>- CMS Website(Separate URL for each Branch).
- Student Online Admission with custom field.
- Fully customized able certificate, student/staff ID card, admit card.
- Attachment Book supports MP4 video with play button.
- Ranking / position wise tabulation sheet.
- Configurable Auto-generate student and parent login credential.
- Email is not required for student and guardian login.
- Add datatable sort.
- Automatically system update feature.
- Fixed Zoom issue and update SDK.
- All known bugs have been fixed.</pre>
                <strong>Version 2.5 – 02, May, 2020</strong>
                <Pre>- Live Class Rooms (Zoom Integration).
- Accounting Links (Student fees, salary payments will automatically enter the office accounting).
- Teachers Have Been Restricted.
- Custom Field.
- Staff Multiple Import(CSV).
- Reports Section Has Been Updated.
- Msg91 Sms Api Has Been Updated.
- All Known Bug Have Been Fixed.
</pre>
                <strong>Version 2.0 – 02, April, 2020</strong>
                <Pre>- Role And Permission.
- 3 Type Exam Setup(Marks, GPA, Marks And GPA).
- Exam Marks Distribution.
- New Student Fee Module.
- Fees Fine Setup, After Due Date Automatically Add Extra Charges.
- Single Click Fee Invoice And Payroll Print.
- New Office Accounting.
- Bulk SMS And Email With Schedule.
- Exam Report Card.
- Student Leave And Leave Request.
- Student Documents.
- Homework With Publish Schedule And SMS notification.
- New Two Payment Gateway(Razorpay And Paystack).
- Staff Can Requset For Advance Salary And Leave.
- Fixed Some Minor Bugs.
</pre>
                <strong>Version 1.1 – 20, December, 2019</strong>
                <pre>- Update CodeIgniter Version.
- Add DataTables Export Button.
- Student Registration Number And Invoice Number Will Be As Per Serial.
- Fixed Some Minor Bugs.
</pre>
                <strong>Version 1.0 (14/10/2019)</strong>
                <pre class="prettyprint lang-plain linenums">
Released Date: 29 Nov, 2019
</pre>
            </section>
            <section id="supporters">
                <div class="page-header">
                    <h3>Support</h3>
                    <hr class="notop">
                </div>
                <p>We provide customer support through our support portal <a href="www.passdr.com">http://www.passdr.com</a> so please open a support ticket for your issues, our technical team will be happy to assist you and solve
                    it.</p>
            </section>


    </div>
</body>

</html>