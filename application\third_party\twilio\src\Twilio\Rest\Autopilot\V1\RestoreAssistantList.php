<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Autopilot
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Autopilot\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Values;
use Twilio\Version;


class RestoreAssistantList extends ListResource
    {
    /**
     * Construct the RestoreAssistantList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/Assistants/Restore';
    }

    /**
     * Update the RestoreAssistantInstance
     *
     * @param string $assistant The Twilio-provided string that uniquely identifies the Assistant resource to restore.
     * @return RestoreAssistantInstance Updated RestoreAssistantInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(string $assistant): RestoreAssistantInstance
    {

        $data = Values::of([
            'Assistant' =>
                $assistant,
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new RestoreAssistantInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Autopilot.V1.RestoreAssistantList]';
    }
}
