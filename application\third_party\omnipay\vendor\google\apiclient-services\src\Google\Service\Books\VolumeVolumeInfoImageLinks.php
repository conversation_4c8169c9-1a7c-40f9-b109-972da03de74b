<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Books_VolumeVolumeInfoImageLinks extends Google_Model
{
  public $extraLarge;
  public $large;
  public $medium;
  public $small;
  public $smallThumbnail;
  public $thumbnail;

  public function setExtraLarge($extraLarge)
  {
    $this->extraLarge = $extraLarge;
  }
  public function getExtraLarge()
  {
    return $this->extraLarge;
  }
  public function setLarge($large)
  {
    $this->large = $large;
  }
  public function getLarge()
  {
    return $this->large;
  }
  public function setMedium($medium)
  {
    $this->medium = $medium;
  }
  public function getMedium()
  {
    return $this->medium;
  }
  public function setSmall($small)
  {
    $this->small = $small;
  }
  public function getSmall()
  {
    return $this->small;
  }
  public function setSmallThumbnail($smallThumbnail)
  {
    $this->smallThumbnail = $smallThumbnail;
  }
  public function getSmallThumbnail()
  {
    return $this->smallThumbnail;
  }
  public function setThumbnail($thumbnail)
  {
    $this->thumbnail = $thumbnail;
  }
  public function getThumbnail()
  {
    return $this->thumbnail;
  }
}
