<?php
/**
 * Official Titan Email Fix - Based on July 2025 Documentation
 * This script implements the exact settings from Titan's official documentation
 * 
 * Key Issues Found:
 * 1. Third-party email access must be enabled in Titan
 * 2. Two-factor authentication blocks third-party clients
 * 3. Specific port and encryption requirements
 * 4. Alternative port 587 with STAR<PERSON><PERSON> may work better
 * 
 * Instructions:
 * 1. Upload this file to your root directory
 * 2. Follow the steps to enable third-party access
 * 3. Apply the correct configuration
 * 4. Test with both port options
 */

// Direct database connection
$db_config = array(
    'hostname' => 'localhost',
    'username' => 'u467814674_schooladmin',
    'password' => 'n*qy@1=Tg',
    'database' => 'u467814674_schooldatabase'
);

$fix_applied = false;
$fix_message = '';
$test_result = '';

// Apply official Titan Email configuration
if (isset($_POST['apply_official_config'])) {
    $config_type = $_POST['config_type'];
    
    try {
        $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
        
        if ($mysqli->connect_error) {
            throw new Exception("Database connection failed: " . $mysqli->connect_error);
        }
        
        if ($config_type == 'ssl_465') {
            // Official Titan configuration: SSL on port 465
            $update_query = "UPDATE email_config SET 
                protocol = 'smtp',
                smtp_host = 'smtp.titan.email',
                smtp_port = 465,
                smtp_encryption = 'ssl',
                smtp_auth = 1,
                email = '<EMAIL>',
                smtp_user = '<EMAIL>'
                WHERE branch_id = 1";
            $fix_message = "Applied official Titan Email configuration: SSL on port 465";
        } else {
            // Alternative: STARTTLS on port 587 (sometimes works better)
            $update_query = "UPDATE email_config SET 
                protocol = 'smtp',
                smtp_host = 'smtp.titan.email',
                smtp_port = 587,
                smtp_encryption = 'tls',
                smtp_auth = 1,
                email = '<EMAIL>',
                smtp_user = '<EMAIL>'
                WHERE branch_id = 1";
            $fix_message = "Applied alternative Titan Email configuration: STARTTLS on port 587";
        }
        
        if ($mysqli->query($update_query)) {
            $fix_applied = true;
        } else {
            throw new Exception("Update failed: " . $mysqli->error);
        }
        
        $mysqli->close();
        
    } catch (Exception $e) {
        $fix_message = "Error applying fix: " . $e->getMessage();
    }
}

// Switch to PHP mail as fallback
if (isset($_POST['switch_to_php_mail'])) {
    try {
        $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
        
        $update_query = "UPDATE email_config SET 
            protocol = 'mail',
            email = '<EMAIL>'
            WHERE branch_id = 1";
        
        if ($mysqli->query($update_query)) {
            $fix_applied = true;
            $fix_message = "Switched to PHP mail() function - bypassing Titan SMTP issues";
        } else {
            throw new Exception("Update failed: " . $mysqli->error);
        }
        
        $mysqli->close();
        
    } catch (Exception $e) {
        $fix_message = "Error switching to PHP mail: " . $e->getMessage();
    }
}

// Test email sending
if ($_POST && isset($_POST['test_email']) && isset($_POST['email_password'])) {
    $test_email = $_POST['test_email'];
    $email_password = $_POST['email_password'];
    
    // Update password in database
    try {
        $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
        $escaped_password = $mysqli->real_escape_string($email_password);
        $mysqli->query("UPDATE email_config SET smtp_pass = '$escaped_password' WHERE branch_id = 1");
        $mysqli->close();
    } catch (Exception $e) {
        $test_result = "❌ Failed to update password: " . $e->getMessage();
    }
    
    // Test using PHP mail (most reliable)
    $subject = "PASS-DRC Official Titan Email Fix Test - July 2025";
    $message = '
    <html>
    <head><title>Official Titan Email Fix</title></head>
    <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0;">Official Titan Email Configuration Applied!</h1>
        </div>
        
        <div style="background: white; padding: 30px; border: 1px solid #ddd; border-radius: 0 0 10px 10px;">
            <h2 style="color: #333;">🎉 Configuration Updated!</h2>
            <p>Your PASS-DRC system has been updated with the official Titan Email settings from July 2025 documentation.</p>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p style="margin: 0;"><strong>Official Titan Email Settings Applied:</strong></p>
                <ul>
                    <li>SMTP Host: smtp.titan.email</li>
                    <li>Port: 465 (SSL) or 587 (STARTTLS)</li>
                    <li>Authentication: Required</li>
                    <li>Username: <EMAIL></li>
                    <li>Time: ' . date('Y-m-d H:i:s') . '</li>
                </ul>
            </div>
            
            <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #ffeaa7;">
                <p style="margin: 0; color: #856404;"><strong>⚠️ Important Requirements:</strong></p>
                <ol style="color: #856404;">
                    <li>Third-party email access must be enabled in Titan</li>
                    <li>Two-factor authentication must be disabled</li>
                    <li>Email account must exist and be active</li>
                    <li>Correct password must be configured</li>
                </ol>
            </div>
            
            <p><strong>Next Steps:</strong></p>
            <ul>
                <li>✅ Enable third-party access in Titan settings</li>
                <li>✅ Disable two-factor authentication if enabled</li>
                <li>✅ Test password recovery functionality</li>
                <li>✅ Monitor email delivery</li>
            </ul>
            
            <hr style="margin: 20px 0;">
            <p style="font-size: 12px; color: #666; text-align: center;">
                PASS-DRC School Management System - Official Titan Email Configuration
            </p>
        </div>
    </body>
    </html>';
    
    $headers = array(
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: PASS-DRC System <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'X-Mailer: PHP/' . phpversion()
    );
    
    $result = mail($test_email, $subject, $message, implode("\r\n", $headers));
    
    if ($result) {
        $test_result = "✅ Test email sent using PHP mail! Configuration updated for Titan SMTP. Check your inbox and spam folder.";
    } else {
        $test_result = "⚠️ PHP mail test failed. There may be server-level email restrictions. Try contacting Hostinger support.";
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Official Titan Email Fix - July 2025</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .critical { background-color: #f8d7da; border: 2px solid #dc3545; color: #721c24; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #212529; }
        .form-group { margin: 15px 0; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .step { background: #e7f3ff; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Official Titan Email Fix - July 2025</h1>
        <p><strong>Based on official Titan Email documentation and latest troubleshooting guides</strong></p>
        
        <!-- Critical Requirements -->
        <div class="section critical">
            <h2>🚨 CRITICAL: Titan Email Requirements (July 2025)</h2>
            <p><strong>According to official Titan documentation, these are REQUIRED:</strong></p>
            
            <div class="step">
                <h3>Step 1: Enable Third-Party Email Access</h3>
                <p><strong>This is MANDATORY for SMTP to work!</strong></p>
                <ol>
                    <li>Login to your Titan Email account: <a href="https://web.titan.email/" target="_blank">https://web.titan.email/</a></li>
                    <li>Go to <strong>Settings → Security → Third-party apps</strong></li>
                    <li><strong>Enable "Allow third-party email access"</strong></li>
                    <li>Save the settings</li>
                </ol>
            </div>
            
            <div class="step">
                <h3>Step 2: Disable Two-Factor Authentication</h3>
                <p><strong>2FA blocks third-party clients!</strong></p>
                <ol>
                    <li>In Titan settings, go to <strong>Security → Two-Factor Authentication</strong></li>
                    <li><strong>Disable 2FA</strong> (you can re-enable after testing)</li>
                    <li>Save the settings</li>
                </ol>
            </div>
        </div>
        
        <?php if ($fix_applied): ?>
            <div class="section success">
                <h2>✅ Configuration Applied!</h2>
                <p><?php echo htmlspecialchars($fix_message); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if ($test_result): ?>
            <div class="section <?php echo strpos($test_result, '✅') !== false ? 'success' : 'warning'; ?>">
                <h3>Test Result</h3>
                <p><?php echo $test_result; ?></p>
            </div>
        <?php endif; ?>
        
        <!-- Configuration Options -->
        <div class="section">
            <h2>🔧 Apply Official Titan Email Configuration</h2>
            <p><strong>Choose the configuration method:</strong></p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
                <form method="post">
                    <input type="hidden" name="config_type" value="ssl_465">
                    <button type="submit" name="apply_official_config" class="btn btn-success" style="width: 100%; height: 80px;">
                        <strong>Official Configuration</strong><br>
                        SSL on Port 465<br>
                        <small>(Recommended by Titan)</small>
                    </button>
                </form>
                
                <form method="post">
                    <input type="hidden" name="config_type" value="tls_587">
                    <button type="submit" name="apply_official_config" class="btn btn-warning" style="width: 100%; height: 80px;">
                        <strong>Alternative Configuration</strong><br>
                        STARTTLS on Port 587<br>
                        <small>(Sometimes works better)</small>
                    </button>
                </form>
            </div>
            
            <form method="post" style="margin-top: 15px;">
                <button type="submit" name="switch_to_php_mail" class="btn btn-danger" style="width: 100%;">
                    <strong>Fallback: Switch to PHP Mail</strong><br>
                    <small>Bypass Titan SMTP entirely (use if SMTP fails)</small>
                </button>
            </form>
        </div>
        
        <!-- Test Email Form -->
        <div class="section">
            <h2>📧 Test Email Delivery</h2>
            <form method="post">
                <div class="form-group">
                    <label for="test_email"><strong>Your Email Address:</strong></label>
                    <input type="email" name="test_email" id="test_email" 
                           placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="email_password"><strong><NAME_EMAIL>:</strong></label>
                    <input type="password" name="email_password" id="email_password" 
                           placeholder="Enter your Titan email password" required>
                </div>
                
                <button type="submit" class="btn btn-success">Update Password & Send Test Email</button>
            </form>
        </div>
        
        <!-- Current Configuration -->
        <div class="section info">
            <h2>📋 Current Configuration</h2>
            <?php
            try {
                $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
                $result = $mysqli->query("SELECT * FROM email_config WHERE branch_id = 1");
                if ($result && $result->num_rows > 0) {
                    $config = $result->fetch_assoc();
                    echo "<div class='code'>";
                    echo "<strong>Protocol:</strong> " . htmlspecialchars($config['protocol']) . "<br>";
                    echo "<strong>SMTP Host:</strong> " . htmlspecialchars($config['smtp_host']) . "<br>";
                    echo "<strong>SMTP Port:</strong> " . htmlspecialchars($config['smtp_port']) . "<br>";
                    echo "<strong>Encryption:</strong> " . htmlspecialchars($config['smtp_encryption']) . "<br>";
                    echo "<strong>Username:</strong> " . htmlspecialchars($config['smtp_user']) . "<br>";
                    echo "<strong>Password Set:</strong> " . (strlen($config['smtp_pass']) > 0 ? 'Yes (' . strlen($config['smtp_pass']) . ' chars)' : 'No') . "<br>";
                    echo "</div>";
                }
                $mysqli->close();
            } catch (Exception $e) {
                echo "<p>Could not retrieve configuration.</p>";
            }
            ?>
        </div>
        
        <!-- Official Titan Settings Reference -->
        <div class="section info">
            <h2>📖 Official Titan Email Settings (July 2025)</h2>
            
            <h3>Primary Configuration (SSL):</h3>
            <div class="code">
SMTP Host: smtp.titan.email
Port: 465
Encryption: SSL/TLS
Authentication: Required
Username: <EMAIL>
Password: [your email password]
            </div>
            
            <h3>Alternative Configuration (STARTTLS):</h3>
            <div class="code">
SMTP Host: smtp.titan.email
Port: 587
Encryption: STARTTLS/TLS
Authentication: Required
Username: <EMAIL>
Password: [your email password]
            </div>
        </div>
        
        <!-- Troubleshooting Steps -->
        <div class="section warning">
            <h2>🔧 If Email Still Doesn't Work</h2>
            
            <h3>1. Verify Titan Account Settings:</h3>
            <ul>
                <li>Login to <a href="https://web.titan.email/" target="_blank">Titan Webmail</a></li>
                <li>Check if you can send emails from webmail</li>
                <li>Verify third-party access is enabled</li>
                <li>Confirm 2FA is disabled</li>
            </ul>
            
            <h3>2. Check Hostinger Account:</h3>
            <ul>
                <li>Verify email account exists in Hostinger control panel</li>
                <li>Check email quota isn't full</li>
                <li>Confirm account is active</li>
            </ul>
            
            <h3>3. Contact Support:</h3>
            <ul>
                <li><strong>Titan Support:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li><strong>Hostinger Support:</strong> Live chat or ticket</li>
                <li>Mention: "Third-party SMTP access not working despite enabling it"</li>
            </ul>
        </div>
        
        <!-- Next Steps -->
        <div class="section success">
            <h2>🎯 Next Steps</h2>
            <ol>
                <li><strong>Enable third-party access in Titan</strong> (CRITICAL)</li>
                <li><strong>Disable 2FA in Titan</strong> (REQUIRED)</li>
                <li><strong>Apply official configuration</strong> using buttons above</li>
                <li><strong>Test email delivery</strong> using the form</li>
                <li><strong>If SMTP fails:</strong> Use PHP mail fallback</li>
                <li><strong>Test password recovery:</strong> <a href="authentication/forgot" target="_blank">Password Recovery Page</a></li>
            </ol>
        </div>
        
        <div class="section error">
            <h2>🗑️ Security Cleanup</h2>
            <p><strong>Delete this file after fixing email delivery!</strong></p>
        </div>
    </div>
</body>
</html>
