HTTP/1.1 200 OK
Server: nginx
Date: Fri, 11 Feb 2016 20:23:14 GMT
Content-Type: application/json
Content-Length: 281
Connection: keep-alive
Access-Control-Allow-Credentials: true
Cache-Control: no-cache, no-store

{
  "id": "basic",
  "object": "plan",
  "active": false,
  "aggregate_usage": null,
  "amount": 1900,
  "created": **********,
  "currency": "usd",
  "amount_decimal": "2000",
  "billing_scheme": "per_unit",
  "interval": "month",
  "interval_count": 1,
  "livemode": false,
  "metadata": {},
  "nickname": "Amazing Gold Plan",
  "product": "prod_GWN5y0jpQeU9yj",
  "tiers": null,
  "tiers_mode": null,
  "transform_usage": null,
  "trial_period_days": null,
  "usage_type": "licensed"
}
