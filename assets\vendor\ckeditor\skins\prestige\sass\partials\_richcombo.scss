/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/license
*/

/*
richcombo.css (part of editor.css)
=================================
This file holds the style set of the "Rich Combo" widget which is commonly used
in the toolbar. It doesn't, however, styles the panel that is displayed when
clicking on the combo, which is instead styled by panel.css.
The visual representation of a rich combo widget looks as follows:
+-- .cke_combo----------------------------------------------------------------------+
| +-- .cke_combo_label --+ +-- .cke_combo_button ---------------------------------+ |
| |                      | | +-- .cke_combo_text --+ +-- .cke_combo_open -------+ | |
| |                      | | |                     | | +-- .cke_combo_arrow --+ | | |
| |                      | | |                     | | |                      | | | |
| |                      | | |                     | | +----------------------+ | | |
| |                      | | +---------------------+ +--------------------------+ | |
| +----------------------+ +------------------------------------------------------+ |
+-----------------------------------------------------------------------------------+
*/


/* The box that hold the entire combo widget */
.cke_combo
{
	display: inline-block;
	float: left;
	position: relative;
	margin-bottom: 5px;
}

.cke_rtl .cke_combo
{
	float: right;
}

.cke_hc .cke_combo
{
	margin-top: 1px;
	margin-bottom: 10px;
}


/* Combo separators. */
/* Separators after every combo. */
.cke_combo:after
{
	content: "";
	position: absolute;
	height: 18px;
	width: 0;
	border-right: 1px solid rgba(255,255,255,0.1);
	margin-top: 5px;
	top: 0;
	right: 0;
}

.cke_rtl .cke_combo:after
{
	border-right: 0;
	border-left: 1px solid #bcbcbc;
	right: auto;
	left: 0;
}

.cke_hc .cke_combo:after
{
	border-color: #000;
}


/* Combo button. */
/* The container for combo text and arrow. */
a.cke_combo_button
{
	cursor: default;
	display: inline-block;
	float: left;
	margin: 0;
	padding: 1px;
}

.cke_rtl a.cke_combo_button
{
	float: right;
}

.cke_hc a.cke_combo_button
{
	padding: 4px;
}


/* Combo states. */
.cke_combo_on a.cke_combo_button,
.cke_combo_off a.cke_combo_button:hover,
.cke_combo_off a.cke_combo_button:focus,
.cke_combo_off a.cke_combo_button:active
{
	background: $color-dialog;
	border: 1px solid $color-border;
	/* Move combo so borders cover separators. Adjust padding to borders. */
	padding: 0 0 0 1px;
	margin-left: -1px;
}

.cke_combo_off a.cke_combo_button:focus {
	outline: none;
}



.cke_combo_on a.cke_combo_button,
.cke_combo_off a.cke_combo_button:active
{
	background: $color-dialog;
}

.cke_rtl .cke_combo_on a.cke_combo_button,
.cke_rtl .cke_combo_off a.cke_combo_button:hover,
.cke_rtl .cke_combo_off a.cke_combo_button:focus,
.cke_rtl .cke_combo_off a.cke_combo_button:active
{
	/* Move combo so borders cover separators. Adjust padding to borders. */
	padding: 0 1px 0 0;
	margin-left: 0;
	margin-right: -1px;
}

.cke_hc .cke_combo_on a.cke_combo_button,
.cke_hc .cke_combo_off a.cke_combo_button:hover,
.cke_hc .cke_combo_off a.cke_combo_button:focus,
.cke_hc .cke_combo_off a.cke_combo_button:active
{
	border: 3px solid $color-border;
	padding: 1px 1px 1px 2px;
}

.cke_hc.cke_rtl .cke_combo_on a.cke_combo_button,
.cke_hc.cke_rtl .cke_combo_off a.cke_combo_button:hover,
.cke_hc.cke_rtl .cke_combo_off a.cke_combo_button:focus,
.cke_hc.cke_rtl .cke_combo_off a.cke_combo_button:active
{
	padding: 1px 2px 1px 1px;
}


/* First combo in a group. */
.cke_toolbar_start + .cke_combo_on a.cke_combo_button,
.cke_toolbar_start + .cke_combo_off a.cke_combo_button:hover,
.cke_toolbar_start + .cke_combo_off a.cke_combo_button:focus,
.cke_toolbar_start + .cke_combo_off a.cke_combo_button:active
{
	/* Move first combo so borders cover separators. Adjust padding to borders. */
	padding: 0 0 0 3px;
	margin-left: -3px;
}

.cke_rtl .cke_toolbar_start + .cke_combo_on a.cke_combo_button,
.cke_rtl .cke_toolbar_start + .cke_combo_off a.cke_combo_button:hover,
.cke_rtl .cke_toolbar_start + .cke_combo_off a.cke_combo_button:focus,
.cke_rtl .cke_toolbar_start + .cke_combo_off a.cke_combo_button:active
{
	/* Move first combo so borders cover separators. Adjust padding to borders. */
	padding: 0 3px 0 0;
	margin-left: 0;
	margin-right: -3px;
}

.cke_hc .cke_toolbar > .cke_toolbar_start + .cke_combo_on a.cke_combo_button,
.cke_hc .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:hover,
.cke_hc .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:focus,
.cke_hc .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:active
{
	/* Move first combo so borders cover separators. Adjust padding to borders. */
	padding: 1px 1px 1px 7px;
	margin-left: -6px;
}

.cke_hc.cke_rtl .cke_toolbar > .cke_toolbar_start + .cke_combo_on a.cke_combo_button,
.cke_hc.cke_rtl .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:hover,
.cke_hc.cke_rtl .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:focus,
.cke_hc.cke_rtl .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:active
{
	/* Move first combo so borders cover separators. Adjust padding to borders. */
	padding: 1px 7px 1px 1px;
	margin-left: 0;
	margin-right: -6px;
}


/* First combo in a toolbar. */
.cke_toolbox .cke_toolbar:first-child > .cke_toolbar_start + .cke_combo_on a.cke_combo_button,
.cke_toolbox .cke_toolbar:first-child > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:hover,
.cke_toolbox .cke_toolbar:first-child > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:focus,
.cke_toolbox .cke_toolbar:first-child > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:active,
/* First combo in a group in a row. */
.cke_toolbar_break + .cke_toolbar > .cke_toolbar_start + .cke_combo_on a.cke_combo_button,
.cke_toolbar_break + .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:hover,
.cke_toolbar_break + .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:focus,
.cke_toolbar_break + .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:active
{
	/* If first combo in a row do not use negative margin so it is aligned with other rows on hover. */
	padding: 0;
	margin: 0;
}

.cke_hc .cke_toolbox .cke_toolbar:first-child > .cke_toolbar_start + .cke_combo_on a.cke_combo_button,
.cke_hc .cke_toolbox .cke_toolbar:first-child > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:hover,
.cke_hc .cke_toolbox .cke_toolbar:first-child > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:focus,
.cke_hc .cke_toolbox .cke_toolbar:first-child > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:active,
.cke_hc .cke_toolbar_break + .cke_toolbar > .cke_toolbar_start + .cke_combo_on a.cke_combo_button,
.cke_hc .cke_toolbar_break + .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:hover,
.cke_hc .cke_toolbar_break + .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:focus,
.cke_hc .cke_toolbar_break + .cke_toolbar > .cke_toolbar_start + .cke_combo_off a.cke_combo_button:active
{
	/* If first combo in a row do not use negative margin so it is aligned with other rows on hover. */
	padding: 1px;
	margin: 0;
}


/* Add Margin after last combo to keep next .cke_toolgroup same distance as in every other case. */
.cke_toolbar .cke_combo + .cke_toolbar_end,
.cke_toolbar .cke_combo + .cke_toolgroup
{
	margin-right: 0;
	margin-left: 2px;
}

.cke_rtl .cke_toolbar .cke_combo + .cke_toolbar_end,
.cke_rtl .cke_toolbar .cke_combo + .cke_toolgroup
{
	margin-left: 0;
	margin-right: 2px;
}

.cke_hc .cke_toolbar .cke_combo + .cke_toolbar_end,
.cke_hc .cke_toolbar .cke_combo + .cke_toolgroup
{
	margin-left: 5px;
}

.cke_hc.cke_rtl .cke_toolbar .cke_combo + .cke_toolbar_end,
.cke_hc.cke_rtl .cke_toolbar .cke_combo + .cke_toolgroup
{
	margin-left: 0;
	margin-right: 5px;
}


/* No separator after last combo in a row. */
.cke_toolbar.cke_toolbar_last .cke_combo:nth-last-child(-n + 2):after
{
	content: none;
	border: none;
	width: 0;
	height: 0;
}


/* Combo inner elements. */
/* The label that shows the current value of the rich combo.
   By default, it holds the name of the property.
   See: .cke_combo_inlinelabel */
.cke_combo_text
{
	line-height: 26px;
	padding-left: 10px;
	text-overflow: ellipsis;
	overflow: hidden;
	float: left;
	cursor: default;
	color: #484848;
    width: 60px;
}

.cke_rtl .cke_combo_text
{
	float: right;
	text-align: right;
	padding-left: 0;
	padding-right: 10px;
}

.cke_hc .cke_combo_text
{
	line-height: 18px;
	font-size: 12px;
}

/* The handler which opens the panel of rich combo properties.
   It holds an arrow as a visual indicator. */
.cke_combo_open
{
	cursor: default;
	display: inline-block;
	font-size: 0;
	height: 19px;
	line-height: 17px;
	margin: 1px 10px 1px;
	width: 5px;
}

.cke_hc .cke_combo_open
{
	height: 12px;
}

/* The arrow which is displayed inside of the .cke_combo_open handler. */
.cke_combo_arrow
{
	cursor: default;
	margin: 11px 0 0;
	float: left;

	/* Pure CSS Arrow */
	height: 0;
	width: 0;
	font-size: 0;
	border-left: 3px solid transparent;
	border-right: 3px solid transparent;
	border-top: 3px solid #484848;
}

.cke_hc .cke_combo_arrow
{
	font-size: 10px;
	width: auto;
	border: 0;
	margin-top: 3px;
}

/* The label of the combo widget. It is invisible by default, yet
   it's important for semantics and accessibility. */
.cke_combo_label
{
	display: none;
	float: left;
	line-height: 26px;
	vertical-align: top;
	margin-right: 5px;
}

.cke_rtl .cke_combo_label
{
	float: right;
	margin-left: 5px;
	margin-right: 0;
}

/* Disabled combo button styles. */
.cke_combo_disabled .cke_combo_inlinelabel,
.cke_combo_disabled .cke_combo_open
{
	opacity: 0.3;
}