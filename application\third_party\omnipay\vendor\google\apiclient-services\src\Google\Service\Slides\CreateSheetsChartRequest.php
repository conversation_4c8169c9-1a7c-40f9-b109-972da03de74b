<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Slides_CreateSheetsChartRequest extends Google_Model
{
  public $chartId;
  protected $elementPropertiesType = 'Google_Service_Slides_PageElementProperties';
  protected $elementPropertiesDataType = '';
  public $linkingMode;
  public $objectId;
  public $spreadsheetId;

  public function setChartId($chartId)
  {
    $this->chartId = $chartId;
  }
  public function getChartId()
  {
    return $this->chartId;
  }
  public function setElementProperties(Google_Service_Slides_PageElementProperties $elementProperties)
  {
    $this->elementProperties = $elementProperties;
  }
  public function getElementProperties()
  {
    return $this->elementProperties;
  }
  public function setLinkingMode($linkingMode)
  {
    $this->linkingMode = $linkingMode;
  }
  public function getLinkingMode()
  {
    return $this->linkingMode;
  }
  public function setObjectId($objectId)
  {
    $this->objectId = $objectId;
  }
  public function getObjectId()
  {
    return $this->objectId;
  }
  public function setSpreadsheetId($spreadsheetId)
  {
    $this->spreadsheetId = $spreadsheetId;
  }
  public function getSpreadsheetId()
  {
    return $this->spreadsheetId;
  }
}
