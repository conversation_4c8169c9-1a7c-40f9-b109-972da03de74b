<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Verify\V2\Service;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Rest\Verify\V2\Service\Entity\FactorList;
use Twilio\Rest\Verify\V2\Service\Entity\NewFactorList;
use Twilio\Rest\Verify\V2\Service\Entity\ChallengeList;


/**
 * @property FactorList $factors
 * @property NewFactorList $newFactors
 * @property ChallengeList $challenges
 * @method \Twilio\Rest\Verify\V2\Service\Entity\FactorContext factors(string $sid)
 * @method \Twilio\Rest\Verify\V2\Service\Entity\ChallengeContext challenges(string $sid)
 */
class EntityContext extends InstanceContext
    {
    protected $_factors;
    protected $_newFactors;
    protected $_challenges;

    /**
     * Initialize the EntityContext
     *
     * @param Version $version Version that contains the resource
     * @param string $serviceSid The unique SID identifier of the Service.
     * @param string $identity The unique external identifier for the Entity of the Service. This identifier should be immutable, not PII, length between 8 and 64 characters, and generated by your external system, such as your user's UUID, GUID, or SID. It can only contain dash (-) separated alphanumeric characters.
     */
    public function __construct(
        Version $version,
        $serviceSid,
        $identity
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'serviceSid' =>
            $serviceSid,
        'identity' =>
            $identity,
        ];

        $this->uri = '/Services/' . \rawurlencode($serviceSid)
        .'/Entities/' . \rawurlencode($identity)
        .'';
    }

    /**
     * Delete the EntityInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the EntityInstance
     *
     * @return EntityInstance Fetched EntityInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): EntityInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new EntityInstance(
            $this->version,
            $payload,
            $this->solution['serviceSid'],
            $this->solution['identity']
        );
    }


    /**
     * Access the factors
     */
    protected function getFactors(): FactorList
    {
        if (!$this->_factors) {
            $this->_factors = new FactorList(
                $this->version,
                $this->solution['serviceSid'],
                $this->solution['identity']
            );
        }

        return $this->_factors;
    }

    /**
     * Access the newFactors
     */
    protected function getNewFactors(): NewFactorList
    {
        if (!$this->_newFactors) {
            $this->_newFactors = new NewFactorList(
                $this->version,
                $this->solution['serviceSid'],
                $this->solution['identity']
            );
        }

        return $this->_newFactors;
    }

    /**
     * Access the challenges
     */
    protected function getChallenges(): ChallengeList
    {
        if (!$this->_challenges) {
            $this->_challenges = new ChallengeList(
                $this->version,
                $this->solution['serviceSid'],
                $this->solution['identity']
            );
        }

        return $this->_challenges;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name): ListResource
    {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Verify.V2.EntityContext ' . \implode(' ', $context) . ']';
    }
}
