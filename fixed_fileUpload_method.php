<?php
/* Clean fileUpload method for Addons.php - replace the existing fileUpload method with this */

private function fileUpload()
{
    if (empty($_FILES["zip_file"]['name'])) {
        return ['status' => 'fail', 'message' => "No ZIP file provided."];
    }

    $dir = 'uploads/addons';
    if (!is_dir($dir)) {
        mkdir($dir, 0777, true);
        fopen($dir . '/index.html', 'w');
    }

    // Purchase code removed - set default value for compatibility
    $purchaseCode = 'FREE-ADDON-' . date('Y-m-d');
    $uploadPath = "uploads/addons/";
    
    // Configure upload
    $config = array(
        'upload_path' => './uploads/addons/',
        'allowed_types' => 'zip',
        'overwrite' => TRUE,
        'encrypt_name' => FALSE
    );
    
    $this->upload->initialize($config);
    
    if (!$this->upload->do_upload("zip_file")) {
        return ['status' => 'fail', 'message' => $this->upload->display_errors('<p>', '</p>')];
    }

    $zipped_fileName = $this->upload->data('file_name');
    $random_dir = generate_encryption_key();
    $this->extractPath = FCPATH . "{$uploadPath}{$random_dir}";

    // Extract ZIP file
    $zip = new ZipArchive;
    $res = $zip->open($uploadPath . $zipped_fileName);
    
    if ($res !== true) {
        unlink($uploadPath . $zipped_fileName);
        return ['status' => 'fail', 'message' => "Failed to open ZIP file."];
    }

    $fileName = trim($zip->getNameIndex(0), '/');
    $res = $zip->extractTo($uploadPath . $random_dir);
    $zip->close();
    unlink($uploadPath . $zipped_fileName);

    if (!$res) {
        return ['status' => 'fail', 'message' => "Failed to extract ZIP file."];
    }

    // Check for config.json
    $configPath = "{$uploadPath}{$random_dir}/{$fileName}/config.json";
    if (!file_exists($configPath)) {
        $this->addons_model->directoryRecursive($this->extractPath);
        return ['status' => 'fail', 'message' => "Config file not found in addon."];
    }

    $config = file_get_contents($configPath);
    if (empty($config)) {
        $this->addons_model->directoryRecursive($this->extractPath);
        return ['status' => 'fail', 'message' => "Config file is empty."];
    }

    $json = json_decode($config);
    if (!$json || empty($json->name) || empty($json->unique_prefix) || empty($json->version)) {
        $this->addons_model->directoryRecursive($this->extractPath);
        return ['status' => 'fail', 'message' => "Invalid config file format."];
    }

    // Check if addon already installed
    if (!$this->addons_model->addonInstalled($json->unique_prefix)) {
        $this->addons_model->directoryRecursive($this->extractPath);
        return ['status' => 'fail', 'message' => "This addon is already installed."];
    }

    // Check system version compatibility (if specified)
    if (!empty($json->system_version)) {
        $current_version = $this->addons_model->get_current_db_version();
        if ($json->system_version > $current_version) {
            $this->addons_model->directoryRecursive($this->extractPath);
            $requiredSystem = wordwrap($json->system_version, 1, '.', true);
            $current_version = wordwrap($current_version, 1, '.', true);
            return ['status' => 'fail', 'message' => "Minimum system version required: {$requiredSystem}, your version: {$current_version}"];
        }
    }

    // Execute SQL file if exists
    $sqlFilePath = "{$uploadPath}{$random_dir}/{$fileName}/install.sql";
    if (file_exists($sqlFilePath)) {
        $sqlContent = file_get_contents($sqlFilePath);
        if (!empty($sqlContent)) {
            $this->db->query('USE ' . $this->db->database . ';');
            foreach (explode(";\n", $sqlContent) as $sql) {
                $sql = trim($sql);
                if (!empty($sql)) {
                    $this->db->query($sql);
                }
            }
        }
    }

    // Copy addon files
    $this->addons_model->copyDirectory("{$uploadPath}{$random_dir}/{$fileName}/", './');
    if (file_exists('./config.json')) {
        unlink('./config.json');
    }

    // Execute initClass if exists
    if (!empty($json->initClass)) {
        $initClassPath = FCPATH . "{$uploadPath}{$random_dir}/{$fileName}/{$json->initClass}";
        if (file_exists($initClassPath) && is_readable($initClassPath) && include($initClassPath)) {
            $init = new InitClass();
            $init->up();
            unlink("./{$json->initClass}");
        }
    }

    // Insert addon details in database
    $arrayAddon = array(
        'name' => $json->name,
        'prefix' => $json->unique_prefix,
        'version' => $json->version,
        'purchase_code' => $purchaseCode,
        'items_code' => isset($json->items_code) ? $json->items_code : '',
        'created_at' => date('Y-m-d H:i:s'),
    );
    $this->db->insert('addon', $arrayAddon);

    // Clean up temporary files
    $this->addons_model->directoryRecursive($this->extractPath);

    $message = "<div class='alert alert-success mt-lg'><div>
        <h4>Congratulations! {$json->name} has been successfully installed.</h4>
        <p>This window will reload automatically in 5 seconds. You are strongly recommended to manually clear your browser cache.</p>
    </div></div>";

    return ['status' => 'success', 'message' => $message];
}
?>
