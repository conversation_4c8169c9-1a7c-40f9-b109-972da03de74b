<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Logging_LogLine extends Google_Model
{
  public $logMessage;
  public $severity;
  protected $sourceLocationType = 'Google_Service_Logging_SourceLocation';
  protected $sourceLocationDataType = '';
  public $time;

  public function setLogMessage($logMessage)
  {
    $this->logMessage = $logMessage;
  }
  public function getLogMessage()
  {
    return $this->logMessage;
  }
  public function setSeverity($severity)
  {
    $this->severity = $severity;
  }
  public function getSeverity()
  {
    return $this->severity;
  }
  public function setSourceLocation(Google_Service_Logging_SourceLocation $sourceLocation)
  {
    $this->sourceLocation = $sourceLocation;
  }
  public function getSourceLocation()
  {
    return $this->sourceLocation;
  }
  public function setTime($time)
  {
    $this->time = $time;
  }
  public function getTime()
  {
    return $this->time;
  }
}
