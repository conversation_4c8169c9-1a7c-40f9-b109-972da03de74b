HTTP/1.1 404 Not Found 
Server: nginx 
Date: Wed, 24 Jul 2013 13:40:31 GMT 
Content-Type: application/json;charset=utf-8 
Content-Length: 132 
Connection: keep-alive 
Access-Control-Allow-Credentials: true 
Access-Control-Max-Age: 300 
Cache-Control: no-cache, no-store 

{ 
	"error": { 
		"type": "invalid_request_error", 
		"message": "No such application fee: fee_1FITlv123YJsynqe3nOIfake",
		"param": "id" 
	} 
}
