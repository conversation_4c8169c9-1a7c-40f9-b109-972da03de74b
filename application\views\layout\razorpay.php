<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<form name='razorpayform' action="<?=base_url($this->router->fetch_class() . '/razorpay_verify')?>" method="POST">
    <input type="hidden" name="razorpay_payment_id" value="" id="razorpay_payment_id">
    <input type="hidden" name="razorpay_signature" value="" id="razorpay_signature">
</form>
<script>

// Checkout details as a json
var options = <?php echo $pay_data?>;
options.handler = function (response){
    document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
    document.getElementById('razorpay_signature').value = response.razorpay_signature;
	document.razorpayform.submit();
};

// Boolean whether to show image inside a white frame. (default: true)
options.theme.image_padding = false;

options.modal = {
    ondismiss: function() {
		window.location.href = "<?=$return_url?>";
    },
    // Boolean indicating whether pressing escape key 
    // should close the checkout form. (default: true)
    escape: true,
    // Bo<PERSON>an indicating whether clicking translucent blank
    // space outside checkout form should close the form. (default: false)
    backdropclose: false
};

var rzp = new Razorpay(options);
pay();
function pay(e){
	rzp.open();
};
</script>