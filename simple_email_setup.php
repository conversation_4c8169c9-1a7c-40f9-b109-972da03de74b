<?php
/**
 * Simple Email Configuration Setup - No CSRF Required
 * This script bypasses CodeIgniter's CSRF protection for email configuration
 * 
 * Instructions:
 * 1. Place this file in the root directory of your school management system
 * 2. Access it via browser: http://yourdomain.com/simple_email_setup.php
 * 3. Configure your email settings for Hostinger
 * 4. Remove this file after configuration is complete
 */

// Direct database connection (bypassing CodeIgniter)
$db_config = array(
    'hostname' => 'localhost',
    'username' => 'u467814674_schooladmin',
    'password' => 'n*qy@1=Tg',
    'database' => 'u467814674_schooldatabase'
);

$success_message = '';
$error_message = '';

// Handle form submission
if ($_POST && isset($_POST['setup_email'])) {
    try {
        // Connect to database
        $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
        
        if ($mysqli->connect_error) {
            throw new Exception("Database connection failed: " . $mysqli->connect_error);
        }
        
        $branch_id = 1; // Default branch ID
        
        // Prepare email configuration data
        $email = $mysqli->real_escape_string($_POST['system_email']);
        $protocol = $mysqli->real_escape_string($_POST['protocol']);
        $smtp_host = $mysqli->real_escape_string($_POST['smtp_host']);
        $smtp_port = intval($_POST['smtp_port']);
        $smtp_user = $mysqli->real_escape_string($_POST['smtp_user']);
        $smtp_pass = $mysqli->real_escape_string($_POST['smtp_pass']);
        $smtp_encryption = $mysqli->real_escape_string($_POST['smtp_encryption']);
        
        // Check if configuration already exists
        $check_query = "SELECT id FROM email_config WHERE branch_id = $branch_id";
        $result = $mysqli->query($check_query);
        
        if ($result->num_rows > 0) {
            // Update existing configuration
            $update_query = "UPDATE email_config SET 
                email = '$email',
                protocol = '$protocol',
                smtp_host = '$smtp_host',
                smtp_port = $smtp_port,
                smtp_user = '$smtp_user',
                smtp_pass = '$smtp_pass',
                smtp_encryption = '$smtp_encryption',
                smtp_auth = 1
                WHERE branch_id = $branch_id";
            
            if ($mysqli->query($update_query)) {
                $success_message = "Email configuration updated successfully!";
            } else {
                throw new Exception("Update failed: " . $mysqli->error);
            }
        } else {
            // Insert new configuration
            $insert_query = "INSERT INTO email_config (branch_id, email, protocol, smtp_host, smtp_port, smtp_user, smtp_pass, smtp_encryption, smtp_auth) 
                VALUES ($branch_id, '$email', '$protocol', '$smtp_host', $smtp_port, '$smtp_user', '$smtp_pass', '$smtp_encryption', 1)";
            
            if ($mysqli->query($insert_query)) {
                $success_message = "Email configuration created successfully!";
            } else {
                throw new Exception("Insert failed: " . $mysqli->error);
            }
        }
        
        $mysqli->close();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Handle email template setup
if ($_POST && isset($_POST['setup_template'])) {
    try {
        // Connect to database
        $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
        
        if ($mysqli->connect_error) {
            throw new Exception("Database connection failed: " . $mysqli->connect_error);
        }
        
        $branch_id = 1; // Default branch ID
        
        // Check if template exists
        $check_query = "SELECT id FROM email_templates_details WHERE template_id = 2 AND branch_id = $branch_id";
        $result = $mysqli->query($check_query);
        
        $subject = "Réinitialisation de mot de passe - PASS-DRC";
        $template_body = '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 24px;">Réinitialisation de mot de passe</h1>
    </div>
    
    <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <p style="font-size: 16px; color: #333; margin-bottom: 20px;">Bonjour <strong>{name}</strong>,</p>
        
        <p style="font-size: 14px; color: #666; line-height: 1.6;">
            Vous avez demandé la réinitialisation de votre mot de passe pour votre compte sur <strong>{institute_name}</strong>.
        </p>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0; font-size: 14px; color: #666;">
                <strong>Nom d\'utilisateur:</strong> {username}<br>
                <strong>Email:</strong> {email}
            </p>
        </div>
        
        <p style="font-size: 14px; color: #666; line-height: 1.6;">
            Cliquez sur le bouton ci-dessous pour réinitialiser votre mot de passe:
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{reset_url}" style="background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
                Réinitialiser mon mot de passe
            </a>
        </div>
        
        <p style="font-size: 12px; color: #999; line-height: 1.6;">
            Si vous n\'avez pas demandé cette réinitialisation, ignorez simplement cet email. Votre mot de passe restera inchangé.
        </p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
        
        <p style="font-size: 12px; color: #999; text-align: center;">
            © 2025 {institute_name} - Système de gestion scolaire PASS-DRC
        </p>
    </div>
</div>';
        
        $subject_escaped = $mysqli->real_escape_string($subject);
        $template_body_escaped = $mysqli->real_escape_string($template_body);
        
        if ($result->num_rows > 0) {
            // Update existing template
            $update_query = "UPDATE email_templates_details SET 
                notified = 1,
                subject = '$subject_escaped',
                template_body = '$template_body_escaped'
                WHERE template_id = 2 AND branch_id = $branch_id";
            
            if ($mysqli->query($update_query)) {
                $success_message = "Email template updated successfully!";
            } else {
                throw new Exception("Template update failed: " . $mysqli->error);
            }
        } else {
            // Insert new template
            $insert_query = "INSERT INTO email_templates_details (template_id, branch_id, notified, subject, template_body) 
                VALUES (2, $branch_id, 1, '$subject_escaped', '$template_body_escaped')";
            
            if ($mysqli->query($insert_query)) {
                $success_message = "Email template created successfully!";
            } else {
                throw new Exception("Template insert failed: " . $mysqli->error);
            }
        }
        
        $mysqli->close();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get current configuration
try {
    $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
    $current_config = null;
    $current_template = null;
    
    if (!$mysqli->connect_error) {
        $config_result = $mysqli->query("SELECT * FROM email_config WHERE branch_id = 1");
        if ($config_result && $config_result->num_rows > 0) {
            $current_config = $config_result->fetch_assoc();
        }
        
        $template_result = $mysqli->query("SELECT * FROM email_templates_details WHERE template_id = 2 AND branch_id = 1");
        if ($template_result && $template_result->num_rows > 0) {
            $current_template = $template_result->fetch_assoc();
        }
        
        $mysqli->close();
    }
} catch (Exception $e) {
    // Ignore errors for display
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Email Setup - No CSRF</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .current-config { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Simple Email Setup - CSRF Free</h1>
        <p><strong>Note:</strong> This script bypasses CSRF protection for easy email configuration on Hostinger.</p>
        
        <?php if ($success_message): ?>
            <div class="section success">
                <strong>✓ Success:</strong> <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="section error">
                <strong>❌ Error:</strong> <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Current Configuration Display -->
        <?php if ($current_config): ?>
            <div class="section">
                <h3>📋 Current Email Configuration</h3>
                <div class="current-config">
                    <strong>System Email:</strong> <?php echo htmlspecialchars($current_config['email']); ?><br>
                    <strong>Protocol:</strong> <?php echo htmlspecialchars($current_config['protocol']); ?><br>
                    <?php if ($current_config['protocol'] == 'smtp'): ?>
                        <strong>SMTP Host:</strong> <?php echo htmlspecialchars($current_config['smtp_host']); ?><br>
                        <strong>SMTP Port:</strong> <?php echo htmlspecialchars($current_config['smtp_port']); ?><br>
                        <strong>SMTP Encryption:</strong> <?php echo htmlspecialchars($current_config['smtp_encryption']); ?><br>
                        <strong>SMTP Username:</strong> <?php echo htmlspecialchars($current_config['smtp_user']); ?><br>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Email Configuration Form -->
        <div class="section">
            <h3>⚙️ Email Configuration (No CSRF Required)</h3>
            <form method="post">
                <div class="form-group">
                    <label for="system_email">System Email Address *</label>
                    <input type="email" name="system_email" id="system_email" 
                           value="<?php echo $current_config ? htmlspecialchars($current_config['email']) : '<EMAIL>'; ?>" 
                           placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="protocol">Email Protocol *</label>
                    <select name="protocol" id="protocol" required>
                        <option value="smtp" <?php echo ($current_config && $current_config['protocol'] == 'smtp') ? 'selected' : 'selected'; ?>>SMTP (Recommended)</option>
                        <option value="mail" <?php echo ($current_config && $current_config['protocol'] == 'mail') ? 'selected' : ''; ?>>PHP Mail</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="smtp_host">SMTP Host *</label>
                    <input type="text" name="smtp_host" id="smtp_host" 
                           value="<?php echo $current_config ? htmlspecialchars($current_config['smtp_host']) : 'mail.passdrc.com'; ?>" 
                           placeholder="mail.passdrc.com">
                </div>
                
                <div class="form-group">
                    <label for="smtp_port">SMTP Port *</label>
                    <select name="smtp_port" id="smtp_port">
                        <option value="587" <?php echo ($current_config && $current_config['smtp_port'] == '587') ? 'selected' : 'selected'; ?>>587 (TLS)</option>
                        <option value="465" <?php echo ($current_config && $current_config['smtp_port'] == '465') ? 'selected' : ''; ?>>465 (SSL)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="smtp_encryption">SMTP Encryption *</label>
                    <select name="smtp_encryption" id="smtp_encryption">
                        <option value="tls" <?php echo ($current_config && $current_config['smtp_encryption'] == 'tls') ? 'selected' : 'selected'; ?>>TLS</option>
                        <option value="ssl" <?php echo ($current_config && $current_config['smtp_encryption'] == 'ssl') ? 'selected' : ''; ?>>SSL</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="smtp_user">SMTP Username *</label>
                    <input type="email" name="smtp_user" id="smtp_user" 
                           value="<?php echo $current_config ? htmlspecialchars($current_config['smtp_user']) : '<EMAIL>'; ?>" 
                           placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="smtp_pass">SMTP Password *</label>
                    <input type="password" name="smtp_pass" id="smtp_pass" 
                           value="<?php echo $current_config ? htmlspecialchars($current_config['smtp_pass']) : ''; ?>" 
                           placeholder="Your email password">
                </div>
                
                <button type="submit" name="setup_email" class="btn">Save Email Configuration</button>
            </form>
        </div>
        
        <!-- Email Template Setup -->
        <div class="section">
            <h3>📝 Email Template Setup</h3>
            <?php if ($current_template): ?>
                <div class="current-config">
                    <strong>Template Status:</strong> <?php echo $current_template['notified'] ? 'Enabled' : 'Disabled'; ?><br>
                    <strong>Subject:</strong> <?php echo htmlspecialchars($current_template['subject']); ?><br>
                </div>
            <?php else: ?>
                <div class="warning">
                    <strong>⚠️ Warning:</strong> Forgot password email template is not configured.
                </div>
            <?php endif; ?>
            
            <form method="post">
                <button type="submit" name="setup_template" class="btn btn-success">
                    <?php echo $current_template ? 'Update' : 'Create'; ?> Email Template
                </button>
            </form>
        </div>
        
        <div class="section warning">
            <h3>⚠️ Next Steps</h3>
            <ol>
                <li>Configure your email settings above</li>
                <li>Create the email template</li>
                <li>Test email delivery using: <a href="email_diagnostic.php" target="_blank">Email Diagnostic</a></li>
                <li>Test password recovery: <a href="authentication/forgot" target="_blank">Forgot Password</a></li>
                <li><strong>Delete this file</strong> after configuration for security</li>
            </ol>
        </div>
    </div>
</body>
</html>
