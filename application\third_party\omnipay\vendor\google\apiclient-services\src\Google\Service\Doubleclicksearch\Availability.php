<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Doubleclicksearch_Availability extends Google_Model
{
  public $advertiserId;
  public $agencyId;
  public $availabilityTimestamp;
  public $segmentationId;
  public $segmentationName;
  public $segmentationType;

  public function setAdvertiserId($advertiserId)
  {
    $this->advertiserId = $advertiserId;
  }
  public function getAdvertiserId()
  {
    return $this->advertiserId;
  }
  public function setAgencyId($agencyId)
  {
    $this->agencyId = $agencyId;
  }
  public function getAgencyId()
  {
    return $this->agencyId;
  }
  public function setAvailabilityTimestamp($availabilityTimestamp)
  {
    $this->availabilityTimestamp = $availabilityTimestamp;
  }
  public function getAvailabilityTimestamp()
  {
    return $this->availabilityTimestamp;
  }
  public function setSegmentationId($segmentationId)
  {
    $this->segmentationId = $segmentationId;
  }
  public function getSegmentationId()
  {
    return $this->segmentationId;
  }
  public function setSegmentationName($segmentationName)
  {
    $this->segmentationName = $segmentationName;
  }
  public function getSegmentationName()
  {
    return $this->segmentationName;
  }
  public function setSegmentationType($segmentationType)
  {
    $this->segmentationType = $segmentationType;
  }
  public function getSegmentationType()
  {
    return $this->segmentationType;
  }
}
