## Test Addon Structure for Debugging

Create a simple test addon with this structure:

test_addon/
├── config.json
├── install.sql (optional)
└── test_file.txt

### config.json content:
{
    "name": "Test Addon",
    "unique_prefix": "test_addon",
    "items_code": "12345",
    "version": "100",
    "system_version": "650",
    "last_update": "2024-01-01 12:00:00"
}

### install.sql content (optional):
-- Test SQL for addon installation
CREATE TABLE IF NOT EXISTS `test_addon_table` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

### test_file.txt content:
This is a test addon file.

## Instructions:
1. Create these files in a folder called "test_addon"
2. Zip the entire "test_addon" folder
3. Upload the ZIP file through your admin panel
4. Check the PHP error logs for debug output
