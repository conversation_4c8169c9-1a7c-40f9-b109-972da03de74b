!function(t,e){'use strict';'function'==typeof define&&define.amd&&'function'==typeof require&&'function'==typeof require.specified&&require.specified('knockout')?define(['jquery','knockout'],e):e(t.j<PERSON><PERSON>y,t.ko)}(this,function(r,u){'use strict';function l(t,e){this.$select=r(t),this.options=this.mergeOptions(r.extend({},e,this.$select.data())),this.$select.attr('data-placeholder')&&(this.options.nonSelectedText=this.$select.data('placeholder')),this.originalOptions=this.$select.clone()[0].options,this.query='',this.searchTimeout=null,this.lastToggledInput=null,this.options.multiple='multiple'===this.$select.attr('multiple'),this.options.onChange=r.proxy(this.options.onChange,this),this.options.onSelectAll=r.proxy(this.options.onSelectAll,this),this.options.onDeselectAll=r.proxy(this.options.onDeselectAll,this),this.options.onDropdownShow=r.proxy(this.options.onDropdownShow,this),this.options.onDropdownHide=r.proxy(this.options.onDropdownHide,this),this.options.onDropdownShown=r.proxy(this.options.onDropdownShown,this),this.options.onDropdownHidden=r.proxy(this.options.onDropdownHidden,this),this.options.onInitialized=r.proxy(this.options.onInitialized,this),this.options.onFiltering=r.proxy(this.options.onFiltering,this),this.buildContainer(),this.buildButton(),this.buildDropdown(),this.buildReset(),this.buildSelectAll(),this.buildDropdownOptions(),this.buildFilter(),this.updateButtonText(),this.updateSelectAll(!0),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups(),this.options.wasDisabled=this.$select.prop('disabled'),this.options.disableIfEmpty&&r('option',this.$select).length<=0&&this.disable(),this.$select.wrap('<span class="multiselect-native-select" />').after(this.$container),this.options.onInitialized(this.$select,this.$container)}void 0!==u&&u.bindingHandlers&&!u.bindingHandlers.multiselect&&(u.bindingHandlers.multiselect={after:['options','value','selectedOptions','enable','disable'],init:function(t,e,i){var s=r(t),l=u.toJS(e());if(s.multiselect(l),i.has('options')){var o=i.get('options');u.isObservable(o)&&u.computed({read:function(){o(),setTimeout(function(){var t=s.data('multiselect');t&&t.updateOriginalOptions(),s.multiselect('rebuild')},1)},disposeWhenNodeIsRemoved:t})}if(i.has('value')){var n=i.get('value');u.isObservable(n)&&u.computed({read:function(){n(),setTimeout(function(){s.multiselect('refresh')},1)},disposeWhenNodeIsRemoved:t}).extend({rateLimit:100,notifyWhenChangesStop:!0})}if(i.has('selectedOptions')){var a=i.get('selectedOptions');u.isObservable(a)&&u.computed({read:function(){a(),setTimeout(function(){s.multiselect('refresh')},1)},disposeWhenNodeIsRemoved:t}).extend({rateLimit:100,notifyWhenChangesStop:!0})}function p(t){setTimeout(function(){t?s.multiselect('enable'):s.multiselect('disable')})}if(i.has('enable')){var h=i.get('enable');u.isObservable(h)?u.computed({read:function(){p(h())},disposeWhenNodeIsRemoved:t}).extend({rateLimit:100,notifyWhenChangesStop:!0}):p(h)}if(i.has('disable')){var c=i.get('disable');u.isObservable(c)?u.computed({read:function(){p(!c())},disposeWhenNodeIsRemoved:t}).extend({rateLimit:100,notifyWhenChangesStop:!0}):p(!c)}u.utils.domNodeDisposal.addDisposeCallback(t,function(){s.multiselect('destroy')})},update:function(t,e){var i=r(t),s=u.toJS(e());i.multiselect('setOptions',s),i.multiselect('rebuild')}}),l.prototype={defaults:{buttonText:function(t,e){if(0<this.disabledText.length&&(e.prop('disabled')||0===t.length&&this.disableIfEmpty))return this.disabledText;if(0===t.length)return this.nonSelectedText;if(this.allSelectedText&&t.length===r('option',r(e)).length&&1!==r('option',r(e)).length&&this.multiple)return this.selectAllNumber?this.allSelectedText+' ('+t.length+')':this.allSelectedText;if(0!==this.numberDisplayed&&t.length>this.numberDisplayed)return t.length+' '+this.nSelectedText;var i='',s=this.delimiterText;return t.each(function(){var t=void 0!==r(this).attr('label')?r(this).attr('label'):r(this).text();i+=t+s}),i.substr(0,i.length-this.delimiterText.length)},buttonTitle:function(t){if(0===t.length)return this.nonSelectedText;var e='',i=this.delimiterText;return t.each(function(){var t=void 0!==r(this).attr('label')?r(this).attr('label'):r(this).text();e+=t+i}),e.substr(0,e.length-this.delimiterText.length)},checkboxName:function(){return!1},optionLabel:function(t){return r(t).attr('label')||r(t).text()},optionClass:function(t){return r(t).attr('class')||''},onChange:function(){},onDropdownShow:function(){},onDropdownHide:function(){},onDropdownShown:function(){},onDropdownHidden:function(){},onSelectAll:function(){},onDeselectAll:function(){},onInitialized:function(){},onFiltering:function(){},enableHTML:!1,buttonClass:'btn btn-default',inheritClass:!1,buttonWidth:'auto',buttonContainer:'<div class="btn-group" />',dropRight:!1,dropUp:!1,selectedClass:'active',maxHeight:!1,includeSelectAllOption:!1,includeSelectAllIfMoreThan:0,selectAllText:' Select all',selectAllValue:'multiselect-all',selectAllName:!1,selectAllNumber:!0,selectAllJustVisible:!0,enableFiltering:!1,enableCaseInsensitiveFiltering:!1,enableFullValueFiltering:!1,enableClickableOptGroups:!1,enableCollapsibleOptGroups:!1,collapseOptGroupsByDefault:!1,filterPlaceholder:'Search',filterBehavior:'text',includeFilterClearBtn:!0,preventInputChangeEvent:!1,nonSelectedText:'None selected',nSelectedText:'selected',allSelectedText:'All selected',numberDisplayed:3,disableIfEmpty:!1,disabledText:'',delimiterText:', ',includeResetOption:!1,includeResetDivider:!1,resetText:'Reset',templates:{button:'<button type="button" class="multiselect dropdown-toggle" data-toggle="dropdown"><span class="multiselect-selected-text"></span> <b class="caret"></b></button>',ul:'<ul class="multiselect-container dropdown-menu"></ul>',filter:'<li class="multiselect-item multiselect-filter"><div class="input-group"><span class="input-group-addon"><i class="glyphicon glyphicon-search"></i></span><input class="form-control multiselect-search" type="text" /></div></li>',filterClearBtn:'<span class="input-group-btn"><button class="btn btn-default multiselect-clear-filter" type="button"><i class="glyphicon glyphicon-remove-circle"></i></button></span>',li:'<li><a tabindex="0"><label></label></a></li>',divider:'<li class="multiselect-item divider"></li>',liGroup:'<li class="multiselect-item multiselect-group"><label></label></li>',resetButton:'<li class="multiselect-reset text-center"><div class="input-group"><a class="btn btn-default btn-block"></a></div></li>'}},constructor:l,buildContainer:function(){this.$container=r(this.options.buttonContainer),this.$container.on('show.bs.dropdown',this.options.onDropdownShow),this.$container.on('hide.bs.dropdown',this.options.onDropdownHide),this.$container.on('shown.bs.dropdown',this.options.onDropdownShown),this.$container.on('hidden.bs.dropdown',this.options.onDropdownHidden)},buildButton:function(){this.$button=r(this.options.templates.button).addClass(this.options.buttonClass),this.$select.attr('class')&&this.options.inheritClass&&this.$button.addClass(this.$select.attr('class')),this.$select.prop('disabled')?this.disable():this.enable(),this.options.buttonWidth&&'auto'!==this.options.buttonWidth&&(this.$button.css({width:'100%',overflow:'hidden','text-overflow':'ellipsis'}),this.$container.css({width:this.options.buttonWidth}));var t=this.$select.attr('tabindex');t&&this.$button.attr('tabindex',t),this.$container.prepend(this.$button)},buildDropdown:function(){if(this.$ul=r(this.options.templates.ul),this.options.dropRight&&this.$ul.addClass('pull-right'),this.options.maxHeight&&this.$ul.css({'max-height':this.options.maxHeight+'px','overflow-y':'auto','overflow-x':'hidden'}),this.options.dropUp){var t=Math.min(this.options.maxHeight,26*r('option[data-role!="divider"]',this.$select).length+19*r('option[data-role="divider"]',this.$select).length+(this.options.includeSelectAllOption?26:0)+(this.options.enableFiltering||this.options.enableCaseInsensitiveFiltering?44:0)),e=t+34;this.$ul.css({'max-height':t+'px','overflow-y':'auto','overflow-x':'hidden','margin-top':'-'+e+'px'})}this.$container.append(this.$ul)},buildDropdownOptions:function(){this.$select.children().each(r.proxy(function(t,e){var i=r(e),s=i.prop('tagName').toLowerCase();i.prop('value')!==this.options.selectAllValue&&('optgroup'===s?this.createOptgroup(e):'option'===s&&('divider'===i.data('role')?this.createDivider():this.createOptionValue(e)))},this)),r(this.$ul).off('change','li:not(.multiselect-group) input[type="checkbox"], li:not(.multiselect-group) input[type="radio"]'),r(this.$ul).on('change','li:not(.multiselect-group) input[type="checkbox"], li:not(.multiselect-group) input[type="radio"]',r.proxy(function(t){var e=r(t.target),i=e.prop('checked')||!1,s=e.val()===this.options.selectAllValue;this.options.selectedClass&&(i?e.closest('li').addClass(this.options.selectedClass):e.closest('li').removeClass(this.options.selectedClass));var l=e.val(),o=this.getOptionByValue(l),n=r('option',this.$select).not(o),a=r('input',this.$container).not(e);if(s?i?this.selectAll(this.options.selectAllJustVisible,!0):this.deselectAll(this.options.selectAllJustVisible,!0):(i?(o.prop('selected',!0),this.options.multiple?o.prop('selected',!0):(this.options.selectedClass&&r(a).closest('li').removeClass(this.options.selectedClass),r(a).prop('checked',!1),n.prop('selected',!1),this.$button.click()),'active'===this.options.selectedClass&&n.closest('a').css('outline','')):o.prop('selected',!1),this.options.onChange(o,i),this.updateSelectAll(),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups()),this.$select.change(),this.updateButtonText(),this.options.preventInputChangeEvent)return!1},this)),r('li a',this.$ul).on('mousedown',function(t){if(t.shiftKey)return!1}),r(this.$ul).on('touchstart click','li a',r.proxy(function(t){t.stopPropagation();var e=r(t.target);if(t.shiftKey&&this.options.multiple){e.is('label')&&(t.preventDefault(),(e=e.find('input')).prop('checked',!e.prop('checked')));var i=e.prop('checked')||!1;if(null!==this.lastToggledInput&&this.lastToggledInput!==e){var s=this.$ul.find('li:visible').index(e.parents('li')),l=this.$ul.find('li:visible').index(this.lastToggledInput.parents('li'));if(l<s){var o=l;l=s,s=o}++l;var n=this.$ul.find('li').not('.multiselect-filter-hidden').slice(s,l).find('input');n.prop('checked',i),this.options.selectedClass&&n.closest('li').toggleClass(this.options.selectedClass,i);for(var a=0,p=n.length;a<p;a++){var h=r(n[a]);this.getOptionByValue(h.val()).prop('selected',i)}}e.trigger('change')}e.is('input')&&!e.closest('li').is('.multiselect-item')&&(this.lastToggledInput=e),e.blur()},this)),this.$container.off('keydown.multiselect').on('keydown.multiselect',r.proxy(function(t){if(!r('input[type="text"]',this.$container).is(':focus'))if(9===t.keyCode&&this.$container.hasClass('open'))this.$button.click();else{var e=r(this.$container).find('li:not(.divider):not(.disabled) a').filter(':visible');if(!e.length)return;var i=e.index(e.filter(':focus'));38===t.keyCode&&0<i?i--:40===t.keyCode&&i<e.length-1?i++:~i||(i=0);var s=e.eq(i);if(s.focus(),32===t.keyCode||13===t.keyCode){var l=s.find('input');l.prop('checked',!l.prop('checked')),l.change()}t.stopPropagation(),t.preventDefault()}},this)),this.options.enableClickableOptGroups&&this.options.multiple&&r('li.multiselect-group input',this.$ul).on('change',r.proxy(function(t){t.stopPropagation();var l=r(t.target).prop('checked')||!1,e=r(t.target).closest('li'),i=e.nextUntil('li.multiselect-group').not('.multiselect-filter-hidden').not('.disabled').find('input'),o=[];this.options.selectedClass&&(l?e.addClass(this.options.selectedClass):e.removeClass(this.options.selectedClass)),r.each(i,r.proxy(function(t,e){var i=r(e).val(),s=this.getOptionByValue(i);l?(r(e).prop('checked',!0),r(e).closest('li').addClass(this.options.selectedClass),s.prop('selected',!0)):(r(e).prop('checked',!1),r(e).closest('li').removeClass(this.options.selectedClass),s.prop('selected',!1)),o.push(this.getOptionByValue(i))},this)),this.options.onChange(o,l),this.$select.change(),this.updateButtonText(),this.updateSelectAll()},this)),this.options.enableCollapsibleOptGroups&&this.options.multiple&&(r('li.multiselect-group .caret-container',this.$ul).on('click',r.proxy(function(t){var e=r(t.target).closest('li').nextUntil('li.multiselect-group').not('.multiselect-filter-hidden'),i=!0;e.each(function(){i=i&&!r(this).hasClass('multiselect-collapsible-hidden')}),i?e.hide().addClass('multiselect-collapsible-hidden'):e.show().removeClass('multiselect-collapsible-hidden')},this)),r('li.multiselect-all',this.$ul).css('background','#f3f3f3').css('border-bottom','1px solid #eaeaea'),r('li.multiselect-all > a > label.checkbox',this.$ul).css('padding','3px 20px 3px 35px'),r('li.multiselect-group > a > input',this.$ul).css('margin','4px 0px 5px -20px'))},createOptionValue:function(t){var e=r(t);e.is(':selected')&&e.prop('selected',!0);var i=this.options.optionLabel(t),s=this.options.optionClass(t),l=e.val(),o=this.options.multiple?'checkbox':'radio',n=r(this.options.templates.li),a=r('label',n);a.addClass(o),a.attr('title',i),n.addClass(s),this.options.collapseOptGroupsByDefault&&'optgroup'===r(t).parent().prop('tagName').toLowerCase()&&(n.addClass('multiselect-collapsible-hidden'),n.hide()),this.options.enableHTML?a.html(' '+i):a.text(' '+i);var p=r('<input/>').attr('type',o),h=this.options.checkboxName(e);h&&p.attr('name',h),a.prepend(p);var c=e.prop('selected')||!1;p.val(l),l===this.options.selectAllValue&&(n.addClass('multiselect-item multiselect-all'),p.parent().parent().addClass('multiselect-all')),a.attr('title',e.attr('title')),this.$ul.append(n),e.is(':disabled')&&p.attr('disabled','disabled').prop('disabled',!0).closest('a').attr('tabindex','-1').closest('li').addClass('disabled'),p.prop('checked',c),c&&this.options.selectedClass&&p.closest('li').addClass(this.options.selectedClass)},createDivider:function(){var t=r(this.options.templates.divider);this.$ul.append(t)},createOptgroup:function(t){var e=r(t).attr('label'),i=r(t).attr('value'),s=r('<li class="multiselect-item multiselect-group"><a href="javascript:void(0);"><label><b></b></label></a></li>'),l=this.options.optionClass(t);s.addClass(l),this.options.enableHTML?r('label b',s).html(' '+e):r('label b',s).text(' '+e),this.options.enableCollapsibleOptGroups&&this.options.multiple&&r('a',s).append('<span class="caret-container"><b class="caret"></b></span>'),this.options.enableClickableOptGroups&&this.options.multiple&&r('a label',s).prepend('<input type="checkbox" value="'+i+'"/>'),r(t).is(':disabled')&&s.addClass('disabled'),this.$ul.append(s),r('option',t).each(r.proxy(function(t,e){this.createOptionValue(e)},this))},buildReset:function(){if(this.options.includeResetOption){this.options.includeResetDivider&&this.$ul.prepend(r(this.options.templates.divider));var t=r(this.options.templates.resetButton);this.options.enableHTML?r('a',t).html(this.options.resetText):r('a',t).text(this.options.resetText),r('a',t).click(r.proxy(function(){this.clearSelection()},this)),this.$ul.prepend(t)}},buildSelectAll:function(){if('number'==typeof this.options.selectAllValue&&(this.options.selectAllValue=this.options.selectAllValue.toString()),!this.hasSelectAll()&&this.options.includeSelectAllOption&&this.options.multiple&&r('option',this.$select).length>this.options.includeSelectAllIfMoreThan){this.options.includeSelectAllDivider&&this.$ul.prepend(r(this.options.templates.divider));var t=r(this.options.templates.li);r('label',t).addClass('checkbox'),this.options.enableHTML?r('label',t).html(' '+this.options.selectAllText):r('label',t).text(' '+this.options.selectAllText),this.options.selectAllName?r('label',t).prepend('<input type="checkbox" name="'+this.options.selectAllName+'" />'):r('label',t).prepend('<input type="checkbox" />');var e=r('input',t);e.val(this.options.selectAllValue),t.addClass('multiselect-item multiselect-all'),e.parent().parent().addClass('multiselect-all'),this.$ul.prepend(t),e.prop('checked',!1)}},buildFilter:function(){if(this.options.enableFiltering||this.options.enableCaseInsensitiveFiltering){var t=Math.max(this.options.enableFiltering,this.options.enableCaseInsensitiveFiltering);if(this.$select.find('option').length>=t){if(this.$filter=r(this.options.templates.filter),r('input',this.$filter).attr('placeholder',this.options.filterPlaceholder),this.options.includeFilterClearBtn){var e=r(this.options.templates.filterClearBtn);e.on('click',r.proxy(function(){clearTimeout(this.searchTimeout),this.query='',this.$filter.find('.multiselect-search').val(''),r('li',this.$ul).show().removeClass('multiselect-filter-hidden'),this.updateSelectAll(),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups()},this)),this.$filter.find('.input-group').append(e)}this.$ul.prepend(this.$filter),this.$filter.val(this.query).on('click',function(t){t.stopPropagation()}).on('input keydown',r.proxy(function(t){13===t.which&&t.preventDefault(),clearTimeout(this.searchTimeout),this.searchTimeout=this.asyncFunction(r.proxy(function(){var a,p;this.query!==t.target.value&&(this.query=t.target.value,r.each(r('li',this.$ul),r.proxy(function(t,e){var i=0<r('input',e).length?r('input',e).val():'',s=r('label',e).text(),l='';if('text'===this.options.filterBehavior?l=s:'value'===this.options.filterBehavior?l=i:'both'===this.options.filterBehavior&&(l=s+'\n'+i),i!==this.options.selectAllValue&&s){var o=!1;if(this.options.enableCaseInsensitiveFiltering&&(l=l.toLowerCase(),this.query=this.query.toLowerCase()),this.options.enableFullValueFiltering&&'both'!==this.options.filterBehavior){var n=l.trim().substring(0,this.query.length);-1<this.query.indexOf(n)&&(o=!0)}else-1<l.indexOf(this.query)&&(o=!0);o||(r(e).css('display','none'),r(e).addClass('multiselect-filter-hidden')),o&&(r(e).css('display','block'),r(e).removeClass('multiselect-filter-hidden')),r(e).hasClass('multiselect-group')?(a=e,p=o):(o&&r(a).show().removeClass('multiselect-filter-hidden'),!o&&p&&r(e).show().removeClass('multiselect-filter-hidden'))}},this)));this.updateSelectAll(),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups(),this.options.onFiltering(t.target)},this),300,this)},this))}}},destroy:function(){this.$container.remove(),this.$select.show(),this.$select.prop('disabled',this.options.wasDisabled),this.$select.data('multiselect',null)},refresh:function(){var l={};r('li input',this.$ul).each(function(){l[r(this).val()]=r(this)}),r('option',this.$select).each(r.proxy(function(t,e){var i=r(e),s=l[r(e).val()];i.is(':selected')?(s.prop('checked',!0),this.options.selectedClass&&s.closest('li').addClass(this.options.selectedClass)):(s.prop('checked',!1),this.options.selectedClass&&s.closest('li').removeClass(this.options.selectedClass)),i.is(':disabled')?s.attr('disabled','disabled').prop('disabled',!0).closest('li').addClass('disabled'):s.prop('disabled',!1).closest('li').removeClass('disabled')},this)),this.updateButtonText(),this.updateSelectAll(),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups()},select:function(t,e){r.isArray(t)||(t=[t]);for(var i=0;i<t.length;i++){var s=t[i];if(null!=s){var l=this.getOptionByValue(s),o=this.getInputByValue(s);void 0!==l&&void 0!==o&&(this.options.multiple||this.deselectAll(!1),this.options.selectedClass&&o.closest('li').addClass(this.options.selectedClass),o.prop('checked',!0),l.prop('selected',!0),e&&this.options.onChange(l,!0))}}this.updateButtonText(),this.updateSelectAll(),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups()},clearSelection:function(){this.deselectAll(!1),this.updateButtonText(),this.updateSelectAll(),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups()},deselect:function(t,e){r.isArray(t)||(t=[t]);for(var i=0;i<t.length;i++){var s=t[i];if(null!=s){var l=this.getOptionByValue(s),o=this.getInputByValue(s);void 0!==l&&void 0!==o&&(this.options.selectedClass&&o.closest('li').removeClass(this.options.selectedClass),o.prop('checked',!1),l.prop('selected',!1),e&&this.options.onChange(l,!1))}}this.updateButtonText(),this.updateSelectAll(),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups()},selectAll:function(t,e){t=void 0===t||t;var i=r('li:not(.divider):not(.disabled):not(.multiselect-group)',this.$ul),s=r('li:not(.divider):not(.disabled):not(.multiselect-group):not(.multiselect-filter-hidden):not(.multiselect-collapisble-hidden)',this.$ul).filter(':visible');t?(r('input:enabled',s).prop('checked',!0),s.addClass(this.options.selectedClass),r('input:enabled',s).each(r.proxy(function(t,e){var i=r(e).val(),s=this.getOptionByValue(i);r(s).prop('selected',!0)},this))):(r('input:enabled',i).prop('checked',!0),i.addClass(this.options.selectedClass),r('input:enabled',i).each(r.proxy(function(t,e){var i=r(e).val(),s=this.getOptionByValue(i);r(s).prop('selected',!0)},this))),r('li input[value="'+this.options.selectAllValue+'"]',this.$ul).prop('checked',!0),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups(),e&&this.options.onSelectAll()},deselectAll:function(t,e){t=void 0===t||t;var i=r('li:not(.divider):not(.disabled):not(.multiselect-group)',this.$ul),s=r('li:not(.divider):not(.disabled):not(.multiselect-group):not(.multiselect-filter-hidden):not(.multiselect-collapisble-hidden)',this.$ul).filter(':visible');t?(r('input[type="checkbox"]:enabled',s).prop('checked',!1),s.removeClass(this.options.selectedClass),r('input[type="checkbox"]:enabled',s).each(r.proxy(function(t,e){var i=r(e).val(),s=this.getOptionByValue(i);r(s).prop('selected',!1)},this))):(r('input[type="checkbox"]:enabled',i).prop('checked',!1),i.removeClass(this.options.selectedClass),r('input[type="checkbox"]:enabled',i).each(r.proxy(function(t,e){var i=r(e).val(),s=this.getOptionByValue(i);r(s).prop('selected',!1)},this))),r('li input[value="'+this.options.selectAllValue+'"]',this.$ul).prop('checked',!1),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups(),e&&this.options.onDeselectAll()},rebuild:function(){this.$ul.html(''),this.options.multiple='multiple'===this.$select.attr('multiple'),this.buildSelectAll(),this.buildDropdownOptions(),this.buildFilter(),this.updateButtonText(),this.updateSelectAll(!0),this.options.enableClickableOptGroups&&this.options.multiple&&this.updateOptGroups(),this.options.disableIfEmpty&&r('option',this.$select).length<=0?this.disable():this.enable(),this.options.dropRight&&this.$ul.addClass('pull-right')},dataprovider:function(t){var o=0,n=this.$select.empty();r.each(t,function(t,e){var s;if(r.isArray(e.children))o++,s=r('<optgroup/>').attr({label:e.label||'Group '+o,disabled:!!e.disabled,value:e.value}),function(t,e){for(var i=0;i<t.length;++i)e(t[i],i)}(e.children,function(t){var e={value:t.value,label:t.label||t.value,title:t.title,selected:!!t.selected,disabled:!!t.disabled};for(var i in t.attributes)e['data-'+i]=t.attributes[i];s.append(r('<option/>').attr(e))});else{var i={value:e.value,label:e.label||e.value,title:e.title,class:e.class,selected:!!e.selected,disabled:!!e.disabled};for(var l in e.attributes)i['data-'+l]=e.attributes[l];(s=r('<option/>').attr(i)).text(e.label||e.value)}n.append(s)}),this.rebuild()},enable:function(){this.$select.prop('disabled',!1),this.$button.prop('disabled',!1).removeClass('disabled')},disable:function(){this.$select.prop('disabled',!0),this.$button.prop('disabled',!0).addClass('disabled')},setOptions:function(t){this.options=this.mergeOptions(t)},mergeOptions:function(t){return r.extend(!0,{},this.defaults,this.options,t)},hasSelectAll:function(){return 0<r('li.multiselect-all',this.$ul).length},updateOptGroups:function(){var t=r('li.multiselect-group',this.$ul),i=this.options.selectedClass;t.each(function(){var t=r(this).nextUntil('li.multiselect-group').not('.multiselect-filter-hidden').not('.disabled'),e=!0;t.each(function(){r('input',this).prop('checked')||(e=!1)}),i&&(e?r(this).addClass(i):r(this).removeClass(i)),r('input',this).prop('checked',e)})},updateSelectAll:function(){if(this.hasSelectAll()){var t=r('li:not(.multiselect-item):not(.multiselect-filter-hidden):not(.multiselect-group):not(.disabled) input:enabled',this.$ul),e=t.length,i=t.filter(':checked').length,s=r('li.multiselect-all',this.$ul),l=s.find('input');0<i&&i===e?(l.prop('checked',!0),s.addClass(this.options.selectedClass)):(l.prop('checked',!1),s.removeClass(this.options.selectedClass))}},updateButtonText:function(){var t=this.getSelected();this.options.enableHTML?r('.multiselect .multiselect-selected-text',this.$container).html(this.options.buttonText(t,this.$select)):r('.multiselect .multiselect-selected-text',this.$container).text(this.options.buttonText(t,this.$select)),r('.multiselect',this.$container).attr('title',this.options.buttonTitle(t,this.$select))},getSelected:function(){return r('option',this.$select).filter(':selected')},getOptionByValue:function(t){for(var e=r('option',this.$select),i=t.toString(),s=0;s<e.length;s+=1){var l=e[s];if(l.value===i)return r(l)}},getInputByValue:function(t){for(var e=r('li input:not(.multiselect-search)',this.$ul),i=t.toString(),s=0;s<e.length;s+=1){var l=e[s];if(l.value===i)return r(l)}},updateOriginalOptions:function(){this.originalOptions=this.$select.clone()[0].options},asyncFunction:function(t,e,i){var s=Array.prototype.slice.call(arguments,3);return setTimeout(function(){t.apply(i||window,s)},e)},setAllSelectedText:function(t){this.options.allSelectedText=t,this.updateButtonText()}},r.fn.multiselect=function(e,i,s){return this.each(function(){var t=r(this).data('multiselect');t||(t=new l(this,'object'==typeof e&&e),r(this).data('multiselect',t)),'string'==typeof e&&(t[e](i,s),'destroy'===e&&r(this).data('multiselect',!1))})},r.fn.multiselect.Constructor=l,r(function(){r('select[data-role=multiselect]').multiselect()})});