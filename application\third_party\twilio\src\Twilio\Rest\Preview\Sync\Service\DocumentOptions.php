<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Preview\Sync\Service;

use Twilio\Options;
use Twilio\Values;

abstract class DocumentOptions
{
    /**
     * @param string $uniqueName 
     * @param array $data 
     * @return CreateDocumentOptions Options builder
     */
    public static function create(
        
        string $uniqueName = Values::NONE,
        array $data = Values::ARRAY_NONE

    ): CreateDocumentOptions
    {
        return new CreateDocumentOptions(
            $uniqueName,
            $data
        );
    }




    /**
     * @param string $ifMatch The If-Match HTTP request header
     * @return UpdateDocumentOptions Options builder
     */
    public static function update(
        
        string $ifMatch = Values::NONE

    ): UpdateDocumentOptions
    {
        return new UpdateDocumentOptions(
            $ifMatch
        );
    }

}

class CreateDocumentOptions extends Options
    {
    /**
     * @param string $uniqueName 
     * @param array $data 
     */
    public function __construct(
        
        string $uniqueName = Values::NONE,
        array $data = Values::ARRAY_NONE

    ) {
        $this->options['uniqueName'] = $uniqueName;
        $this->options['data'] = $data;
    }

    /**
     * 
     *
     * @param string $uniqueName 
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * 
     *
     * @param array $data 
     * @return $this Fluent Builder
     */
    public function setData(array $data): self
    {
        $this->options['data'] = $data;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Sync.CreateDocumentOptions ' . $options . ']';
    }
}




class UpdateDocumentOptions extends Options
    {
    /**
     * @param string $ifMatch The If-Match HTTP request header
     */
    public function __construct(
        
        string $ifMatch = Values::NONE

    ) {
        $this->options['ifMatch'] = $ifMatch;
    }

    /**
     * The If-Match HTTP request header
     *
     * @param string $ifMatch The If-Match HTTP request header
     * @return $this Fluent Builder
     */
    public function setIfMatch(string $ifMatch): self
    {
        $this->options['ifMatch'] = $ifMatch;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Sync.UpdateDocumentOptions ' . $options . ']';
    }
}

