<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Autopilot
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Autopilot\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;
use Twilio\Rest\Autopilot\V1\Assistant\FieldTypeList;
use Twilio\Rest\Autopilot\V1\Assistant\ModelBuildList;
use Twilio\Rest\Autopilot\V1\Assistant\QueryList;
use Twilio\Rest\Autopilot\V1\Assistant\WebhookList;
use Twilio\Rest\Autopilot\V1\Assistant\TaskList;
use Twilio\Rest\Autopilot\V1\Assistant\DefaultsList;
use Twilio\Rest\Autopilot\V1\Assistant\StyleSheetList;
use Twilio\Rest\Autopilot\V1\Assistant\DialogueList;


/**
 * @property FieldTypeList $fieldTypes
 * @property ModelBuildList $modelBuilds
 * @property QueryList $queries
 * @property WebhookList $webhooks
 * @property TaskList $tasks
 * @property DefaultsList $defaults
 * @property StyleSheetList $styleSheet
 * @property DialogueList $dialogues
 * @method \Twilio\Rest\Autopilot\V1\Assistant\TaskContext tasks(string $sid)
 * @method \Twilio\Rest\Autopilot\V1\Assistant\DefaultsContext defaults()
 * @method \Twilio\Rest\Autopilot\V1\Assistant\WebhookContext webhooks(string $sid)
 * @method \Twilio\Rest\Autopilot\V1\Assistant\DialogueContext dialogues(string $sid)
 * @method \Twilio\Rest\Autopilot\V1\Assistant\ModelBuildContext modelBuilds(string $sid)
 * @method \Twilio\Rest\Autopilot\V1\Assistant\FieldTypeContext fieldTypes(string $sid)
 * @method \Twilio\Rest\Autopilot\V1\Assistant\StyleSheetContext styleSheet()
 * @method \Twilio\Rest\Autopilot\V1\Assistant\QueryContext queries(string $sid)
 */
class AssistantContext extends InstanceContext
    {
    protected $_fieldTypes;
    protected $_modelBuilds;
    protected $_queries;
    protected $_webhooks;
    protected $_tasks;
    protected $_defaults;
    protected $_styleSheet;
    protected $_dialogues;

    /**
     * Initialize the AssistantContext
     *
     * @param Version $version Version that contains the resource
     * @param string $sid The Twilio-provided string that uniquely identifies the Assistant resource to delete.
     */
    public function __construct(
        Version $version,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'sid' =>
            $sid,
        ];

        $this->uri = '/Assistants/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Delete the AssistantInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the AssistantInstance
     *
     * @return AssistantInstance Fetched AssistantInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): AssistantInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new AssistantInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Update the AssistantInstance
     *
     * @param array|Options $options Optional Arguments
     * @return AssistantInstance Updated AssistantInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): AssistantInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' =>
                $options['friendlyName'],
            'LogQueries' =>
                Serialize::booleanToString($options['logQueries']),
            'UniqueName' =>
                $options['uniqueName'],
            'CallbackUrl' =>
                $options['callbackUrl'],
            'CallbackEvents' =>
                $options['callbackEvents'],
            'StyleSheet' =>
                Serialize::jsonObject($options['styleSheet']),
            'Defaults' =>
                Serialize::jsonObject($options['defaults']),
            'DevelopmentStage' =>
                $options['developmentStage'],
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new AssistantInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Access the fieldTypes
     */
    protected function getFieldTypes(): FieldTypeList
    {
        if (!$this->_fieldTypes) {
            $this->_fieldTypes = new FieldTypeList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_fieldTypes;
    }

    /**
     * Access the modelBuilds
     */
    protected function getModelBuilds(): ModelBuildList
    {
        if (!$this->_modelBuilds) {
            $this->_modelBuilds = new ModelBuildList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_modelBuilds;
    }

    /**
     * Access the queries
     */
    protected function getQueries(): QueryList
    {
        if (!$this->_queries) {
            $this->_queries = new QueryList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_queries;
    }

    /**
     * Access the webhooks
     */
    protected function getWebhooks(): WebhookList
    {
        if (!$this->_webhooks) {
            $this->_webhooks = new WebhookList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_webhooks;
    }

    /**
     * Access the tasks
     */
    protected function getTasks(): TaskList
    {
        if (!$this->_tasks) {
            $this->_tasks = new TaskList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_tasks;
    }

    /**
     * Access the defaults
     */
    protected function getDefaults(): DefaultsList
    {
        if (!$this->_defaults) {
            $this->_defaults = new DefaultsList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_defaults;
    }

    /**
     * Access the styleSheet
     */
    protected function getStyleSheet(): StyleSheetList
    {
        if (!$this->_styleSheet) {
            $this->_styleSheet = new StyleSheetList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_styleSheet;
    }

    /**
     * Access the dialogues
     */
    protected function getDialogues(): DialogueList
    {
        if (!$this->_dialogues) {
            $this->_dialogues = new DialogueList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_dialogues;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name): ListResource
    {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Autopilot.V1.AssistantContext ' . \implode(' ', $context) . ']';
    }
}
