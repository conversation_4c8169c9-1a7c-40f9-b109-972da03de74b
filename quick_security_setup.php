<?php
/**
 * Quick Security Question Setup for Existing Users
 * High-Security School Management System
 * 
 * This script quickly sets up default security questions for existing admin-created accounts
 * so they can immediately use the password recovery system.
 * 
 * IMPORTANT: Run this once, then delete this file for security!
 */

// Database configuration - Update these with your actual database details
$db_host = 'localhost';
$db_username = 'your_db_username';  // Update this
$db_password = 'your_db_password';  // Update this
$db_name = 'your_db_name';          // Update this

// Default security questions
$default_questions = array(
    "What is the name of your first school?",
    "What is your mother's maiden name?", 
    "What is the name of your first pet?"
);

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_username, $db_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>🔐 Quick Security Question Setup</h1>";
    echo "<p><strong>Setting up security questions for existing admin-created accounts...</strong></p>";
    
    // Get all active users who don't have security questions
    $stmt = $pdo->query("
        SELECT lc.id, lc.username, lc.email, lc.role 
        FROM login_credential lc 
        LEFT JOIN security_questions sq ON lc.id = sq.login_credential_id 
        WHERE sq.login_credential_id IS NULL 
        AND lc.active = 1
        ORDER BY lc.username
    ");
    
    $users_without_questions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users_without_questions)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "✅ <strong>All users already have security questions set up!</strong><br>";
        echo "No action needed. You can delete this file.";
        echo "</div>";
        exit;
    }
    
    echo "<h2>📋 Found " . count($users_without_questions) . " users without security questions:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Username</th><th style='padding: 8px;'>Email</th><th style='padding: 8px;'>Role</th></tr>";
    
    foreach ($users_without_questions as $user) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($user['username']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($user['role']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if user wants to proceed
    if (!isset($_POST['confirm_setup'])) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; color: #856404; margin: 20px 0;'>";
        echo "<h3>⚠️ What this will do:</h3>";
        echo "<ul>";
        echo "<li><strong>Create default security questions</strong> for all users listed above</li>";
        echo "<li><strong>Set safe default answers</strong> that users must change on first login</li>";
        echo "<li><strong>Enable immediate password recovery</strong> for these accounts</li>";
        echo "<li><strong>Maintain high security</strong> - only admin-created accounts affected</li>";
        echo "</ul>";
        echo "<h3>🔧 Default Setup:</h3>";
        echo "<ul>";
        echo "<li><strong>Question 1:</strong> \"What is the name of your first school?\" → Answer: <em>username</em></li>";
        echo "<li><strong>Question 2:</strong> \"What is your mother's maiden name?\" → Answer: <em>changeme</em></li>";
        echo "<li><strong>Question 3:</strong> \"What is the name of your first pet?\" → Answer: <em>changeme</em></li>";
        echo "</ul>";
        echo "<p><strong>Users should update these answers immediately after setup!</strong></p>";
        echo "</div>";
        
        echo "<form method='post'>";
        echo "<button type='submit' name='confirm_setup' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
        echo "🚀 Setup Security Questions for All Users";
        echo "</button>";
        echo "</form>";
        
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 20px 0;'>";
        echo "<h3>🗑️ Important:</h3>";
        echo "<p><strong>Delete this file after running it for security!</strong></p>";
        echo "</div>";
        
        exit;
    }
    
    // User confirmed, proceed with setup
    echo "<h2>🚀 Setting up security questions...</h2>";
    
    $setup_count = 0;
    $errors = array();
    
    foreach ($users_without_questions as $user) {
        try {
            // Create default security questions for this user
            $stmt = $pdo->prepare("
                INSERT INTO security_questions 
                (login_credential_id, question_1, answer_1, question_2, answer_2, question_3, answer_3, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $result = $stmt->execute([
                $user['id'],
                $default_questions[0], // "What is the name of your first school?"
                strtolower($user['username']), // Default: username (user should change)
                $default_questions[1], // "What is your mother's maiden name?"
                'changeme', // Default: changeme (user must change)
                $default_questions[2], // "What is the name of your first pet?"
                'changeme'  // Default: changeme (user must change)
            ]);
            
            if ($result) {
                $setup_count++;
                echo "<div style='color: #28a745;'>✅ Setup completed for: " . htmlspecialchars($user['username']) . "</div>";
            } else {
                $errors[] = "Failed to setup for: " . $user['username'];
            }
            
        } catch (Exception $e) {
            $errors[] = "Error setting up " . $user['username'] . ": " . $e->getMessage();
        }
    }
    
    // Show results
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; color: #155724; margin: 20px 0;'>";
    echo "<h2>🎉 Setup Complete!</h2>";
    echo "<p><strong>Successfully set up security questions for {$setup_count} users.</strong></p>";
    
    if (!empty($errors)) {
        echo "<h3 style='color: #dc3545;'>❌ Errors:</h3>";
        foreach ($errors as $error) {
            echo "<div style='color: #dc3545;'>• " . htmlspecialchars($error) . "</div>";
        }
    }
    
    echo "<h3>📋 Next Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Test the system:</strong> Try password recovery at <code>authentication/forgot</code></li>";
    echo "<li><strong>User training:</strong> Tell users to update their security questions at <code>authentication/security_setup</code></li>";
    echo "<li><strong>Default answers:</strong> Users can login with username + 'changeme' answers initially</li>";
    echo "<li><strong>Security:</strong> Users should change default answers immediately</li>";
    echo "<li><strong>Delete this file:</strong> Remove this script for security</li>";
    echo "</ol>";
    
    echo "<h3>🔗 Quick Links:</h3>";
    echo "<ul>";
    echo "<li><a href='authentication/forgot' target='_blank'>Test Forgot Password</a></li>";
    echo "<li><a href='authentication/security_setup' target='_blank'>Security Question Setup</a></li>";
    echo "<li><a href='authentication/security_reset' target='_blank'>Security Question Reset</a></li>";
    echo "</ul>";
    echo "</div>";
    
    // Show sample user for testing
    if ($setup_count > 0) {
        $sample_user = $users_without_questions[0];
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460; margin: 20px 0;'>";
        echo "<h3>🧪 Test with Sample User:</h3>";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($sample_user['username']) . "</p>";
        echo "<p><strong>Default Answers:</strong></p>";
        echo "<ul>";
        echo "<li>Question 1 Answer: <code>" . htmlspecialchars($sample_user['username']) . "</code></li>";
        echo "<li>Question 2 Answer: <code>changeme</code></li>";
        echo "<li>Question 3 Answer: <code>changeme</code></li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h2>❌ Database Connection Error</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Please check your database configuration at the top of this file.</strong></p>";
    echo "<h3>Update these values:</h3>";
    echo "<ul>";
    echo "<li><code>\$db_host</code> - Your database host (usually 'localhost')</li>";
    echo "<li><code>\$db_username</code> - Your database username</li>";
    echo "<li><code>\$db_password</code> - Your database password</li>";
    echo "<li><code>\$db_name</code> - Your database name</li>";
    echo "</ul>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h1, h2, h3 { color: #333; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>

<script>
// Auto-refresh prevention
if (window.history.replaceState) {
    window.history.replaceState(null, null, window.location.href);
}
</script>
