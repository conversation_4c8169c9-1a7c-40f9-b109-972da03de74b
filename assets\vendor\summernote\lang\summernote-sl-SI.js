(function ($) {
  $.extend($.summernote.lang, {
    'sl-SI': {
      font: {
        bold: '<PERSON>re<PERSON><PERSON>',
        italic: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        underline: 'Podčrtano',
        clear: '<PERSON>čisti oblikovanje izbire',
        height: '<PERSON><PERSON><PERSON><PERSON> med vrsticami',
        name: '<PERSON><PERSON><PERSON>',
        strikethrough: '<PERSON><PERSON><PERSON><PERSON>',
        subscript: 'Podpisano',
        superscript: 'Nadpisano',
        size: 'Velikost pisave'
      },
      image: {
        image: 'Slika',
        insert: 'Vstavi sliko',
        resizeFull: '<PERSON>z<PERSON><PERSON> na polno velikost',
        resizeHalf: '<PERSON>zš<PERSON> na polovico velikosti',
        resizeQuarter: '<PERSON>zširi na četrtino velikosti',
        floatLeft: 'Leva poravnava',
        floatRight: 'Desna poravnava',
        floatNone: 'Brez poravnave',
        dragImageHere: 'Sem povlecite sliko',
        selectFromFiles: '<PERSON>zberi sliko za nalaganje',
        url: 'URL naslov slike',
        remove: 'Odstrani sliko'
      },
      video: {
        video: 'Video',
        videoLink: 'Video povezava',
        insert: 'Vstavi video',
        url: 'Povezava do videa',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion ali Youku)'
      },
      link: {
        link: 'Povezava',
        insert: 'Vstavi povezavo',
        unlink: 'Odstrani povezavo',
        edit: 'Uredi',
        textToDisplay: 'Prikazano besedilo',
        url: 'Povezava',
        openInNewWindow: 'Odpri v novem oknu'
      },
      table: {
        table: 'Tabela'
      },
      hr: {
        insert: 'Vstavi horizontalno črto'
      },
      style: {
        style: 'Slogi',
        p: 'Navadno besedilo',
        blockquote: 'Citat',
        pre: 'Koda',
        h1: 'Naslov 1',
        h2: 'Naslov 2',
        h3: 'Naslov 3',
        h4: 'Naslov 4',
        h5: 'Naslov 5',
        h6: 'Naslov 6'
      },
      lists: {
        unordered: 'Označen seznam',
        ordered: 'Oštevilčen seznam'
      },
      options: {
        help: 'Pomoč',
        fullscreen: 'Celozaslonski način',
        codeview: 'Pregled HTML kode'
      },
      paragraph: {
        paragraph: 'Slogi odstavka',
        outdent: 'Zmanjšaj odmik',
        indent: 'Povečaj odmik',
        left: 'Leva poravnava',
        center: 'Desna poravnava',
        right: 'Sredinska poravnava',
        justify: 'Obojestranska poravnava'
      },
      color: {
        recent: 'Uporabi zadnjo barvo',
        more: 'Več barv',
        background: 'Barva ozadja',
        foreground: 'Barva besedila',
        transparent: 'Brez barve',
        setTransparent: 'Brez barve',
        reset: 'Ponastavi',
        resetToDefault: 'Ponastavi na privzeto'
      },
      shortcut: {
        shortcuts: 'Bljižnice',
        close: 'Zapri',
        textFormatting: 'Oblikovanje besedila',
        action: 'Dejanja',
        paragraphFormatting: 'Oblikovanje odstavka',
        documentStyle: 'Oblikovanje naslova'
      },
      history: {
        undo: 'Razveljavi',
        redo: 'Uveljavi'
      }
    }
  });
})(jQuery);
