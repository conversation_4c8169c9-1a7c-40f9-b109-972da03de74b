<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Preview\Wireless;

use Twilio\Options;
use Twilio\Values;

abstract class RatePlanOptions
{
    /**
     * @param string $uniqueName 
     * @param string $friendlyName 
     * @param bool $dataEnabled 
     * @param int $dataLimit 
     * @param string $dataMetering 
     * @param bool $messagingEnabled 
     * @param bool $voiceEnabled 
     * @param bool $commandsEnabled 
     * @param bool $nationalRoamingEnabled 
     * @param string[] $internationalRoaming 
     * @return CreateRatePlanOptions Options builder
     */
    public static function create(
        
        string $uniqueName = Values::NONE,
        string $friendlyName = Values::NONE,
        bool $dataEnabled = Values::BOOL_NONE,
        int $dataLimit = Values::INT_NONE,
        string $dataMetering = Values::NONE,
        bool $messagingEnabled = Values::BOOL_NONE,
        bool $voiceEnabled = Values::BOOL_NONE,
        bool $commandsEnabled = Values::BOOL_NONE,
        bool $nationalRoamingEnabled = Values::BOOL_NONE,
        array $internationalRoaming = Values::ARRAY_NONE

    ): CreateRatePlanOptions
    {
        return new CreateRatePlanOptions(
            $uniqueName,
            $friendlyName,
            $dataEnabled,
            $dataLimit,
            $dataMetering,
            $messagingEnabled,
            $voiceEnabled,
            $commandsEnabled,
            $nationalRoamingEnabled,
            $internationalRoaming
        );
    }




    /**
     * @param string $uniqueName 
     * @param string $friendlyName 
     * @return UpdateRatePlanOptions Options builder
     */
    public static function update(
        
        string $uniqueName = Values::NONE,
        string $friendlyName = Values::NONE

    ): UpdateRatePlanOptions
    {
        return new UpdateRatePlanOptions(
            $uniqueName,
            $friendlyName
        );
    }

}

class CreateRatePlanOptions extends Options
    {
    /**
     * @param string $uniqueName 
     * @param string $friendlyName 
     * @param bool $dataEnabled 
     * @param int $dataLimit 
     * @param string $dataMetering 
     * @param bool $messagingEnabled 
     * @param bool $voiceEnabled 
     * @param bool $commandsEnabled 
     * @param bool $nationalRoamingEnabled 
     * @param string[] $internationalRoaming 
     */
    public function __construct(
        
        string $uniqueName = Values::NONE,
        string $friendlyName = Values::NONE,
        bool $dataEnabled = Values::BOOL_NONE,
        int $dataLimit = Values::INT_NONE,
        string $dataMetering = Values::NONE,
        bool $messagingEnabled = Values::BOOL_NONE,
        bool $voiceEnabled = Values::BOOL_NONE,
        bool $commandsEnabled = Values::BOOL_NONE,
        bool $nationalRoamingEnabled = Values::BOOL_NONE,
        array $internationalRoaming = Values::ARRAY_NONE

    ) {
        $this->options['uniqueName'] = $uniqueName;
        $this->options['friendlyName'] = $friendlyName;
        $this->options['dataEnabled'] = $dataEnabled;
        $this->options['dataLimit'] = $dataLimit;
        $this->options['dataMetering'] = $dataMetering;
        $this->options['messagingEnabled'] = $messagingEnabled;
        $this->options['voiceEnabled'] = $voiceEnabled;
        $this->options['commandsEnabled'] = $commandsEnabled;
        $this->options['nationalRoamingEnabled'] = $nationalRoamingEnabled;
        $this->options['internationalRoaming'] = $internationalRoaming;
    }

    /**
     * 
     *
     * @param string $uniqueName 
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * 
     *
     * @param string $friendlyName 
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * 
     *
     * @param bool $dataEnabled 
     * @return $this Fluent Builder
     */
    public function setDataEnabled(bool $dataEnabled): self
    {
        $this->options['dataEnabled'] = $dataEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param int $dataLimit 
     * @return $this Fluent Builder
     */
    public function setDataLimit(int $dataLimit): self
    {
        $this->options['dataLimit'] = $dataLimit;
        return $this;
    }

    /**
     * 
     *
     * @param string $dataMetering 
     * @return $this Fluent Builder
     */
    public function setDataMetering(string $dataMetering): self
    {
        $this->options['dataMetering'] = $dataMetering;
        return $this;
    }

    /**
     * 
     *
     * @param bool $messagingEnabled 
     * @return $this Fluent Builder
     */
    public function setMessagingEnabled(bool $messagingEnabled): self
    {
        $this->options['messagingEnabled'] = $messagingEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param bool $voiceEnabled 
     * @return $this Fluent Builder
     */
    public function setVoiceEnabled(bool $voiceEnabled): self
    {
        $this->options['voiceEnabled'] = $voiceEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param bool $commandsEnabled 
     * @return $this Fluent Builder
     */
    public function setCommandsEnabled(bool $commandsEnabled): self
    {
        $this->options['commandsEnabled'] = $commandsEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param bool $nationalRoamingEnabled 
     * @return $this Fluent Builder
     */
    public function setNationalRoamingEnabled(bool $nationalRoamingEnabled): self
    {
        $this->options['nationalRoamingEnabled'] = $nationalRoamingEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param string[] $internationalRoaming 
     * @return $this Fluent Builder
     */
    public function setInternationalRoaming(array $internationalRoaming): self
    {
        $this->options['internationalRoaming'] = $internationalRoaming;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Wireless.CreateRatePlanOptions ' . $options . ']';
    }
}




class UpdateRatePlanOptions extends Options
    {
    /**
     * @param string $uniqueName 
     * @param string $friendlyName 
     */
    public function __construct(
        
        string $uniqueName = Values::NONE,
        string $friendlyName = Values::NONE

    ) {
        $this->options['uniqueName'] = $uniqueName;
        $this->options['friendlyName'] = $friendlyName;
    }

    /**
     * 
     *
     * @param string $uniqueName 
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * 
     *
     * @param string $friendlyName 
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Wireless.UpdateRatePlanOptions ' . $options . ']';
    }
}

