<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Proximitybeacon_EphemeralIdRegistrationParams extends Google_Model
{
  public $maxRotationPeriodExponent;
  public $minRotationPeriodExponent;
  public $serviceEcdhPublicKey;

  public function setMaxRotationPeriodExponent($maxRotationPeriodExponent)
  {
    $this->maxRotationPeriodExponent = $maxRotationPeriodExponent;
  }
  public function getMaxRotationPeriodExponent()
  {
    return $this->maxRotationPeriodExponent;
  }
  public function setMinRotationPeriodExponent($minRotationPeriodExponent)
  {
    $this->minRotationPeriodExponent = $minRotationPeriodExponent;
  }
  public function getMinRotationPeriodExponent()
  {
    return $this->minRotationPeriodExponent;
  }
  public function setServiceEcdhPublicKey($serviceEcdhPublicKey)
  {
    $this->serviceEcdhPublicKey = $serviceEcdhPublicKey;
  }
  public function getServiceEcdhPublicKey()
  {
    return $this->serviceEcdhPublicKey;
  }
}
