<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twi<PERSON>\TwiML\Voice;

use <PERSON><PERSON><PERSON>\TwiML\TwiML;

class Hangup extends TwiML {
    /**
     * Hangup constructor.
     */
    public function __construct() {
        parent::__construct('Hangup', null);
    }

    /**
     * Add Parameter child.
     *
     * @param array $attributes Optional attributes
     * @return Parameter Child element.
     */
    public function parameter($attributes = []): Parameter {
        return $this->nest(new Parameter($attributes));
    }
}