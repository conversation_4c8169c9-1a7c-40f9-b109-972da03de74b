<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Preview\DeployedDevices;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Rest\Preview\DeployedDevices\Fleet\CertificateList;
use Twilio\Rest\Preview\DeployedDevices\Fleet\DeviceList;
use Twilio\Rest\Preview\DeployedDevices\Fleet\KeyList;
use Twilio\Rest\Preview\DeployedDevices\Fleet\DeploymentList;


/**
 * @property CertificateList $certificates
 * @property DeviceList $devices
 * @property KeyList $keys
 * @property DeploymentList $deployments
 * @method \Twilio\Rest\Preview\DeployedDevices\Fleet\DeviceContext devices(string $sid)
 * @method \Twilio\Rest\Preview\DeployedDevices\Fleet\KeyContext keys(string $sid)
 * @method \Twilio\Rest\Preview\DeployedDevices\Fleet\DeploymentContext deployments(string $sid)
 * @method \Twilio\Rest\Preview\DeployedDevices\Fleet\CertificateContext certificates(string $sid)
 */
class FleetContext extends InstanceContext
    {
    protected $_certificates;
    protected $_devices;
    protected $_keys;
    protected $_deployments;

    /**
     * Initialize the FleetContext
     *
     * @param Version $version Version that contains the resource
     * @param string $sid Provides a 34 character string that uniquely identifies the requested Fleet resource.
     */
    public function __construct(
        Version $version,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'sid' =>
            $sid,
        ];

        $this->uri = '/Fleets/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Delete the FleetInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the FleetInstance
     *
     * @return FleetInstance Fetched FleetInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): FleetInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new FleetInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Update the FleetInstance
     *
     * @param array|Options $options Optional Arguments
     * @return FleetInstance Updated FleetInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): FleetInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' =>
                $options['friendlyName'],
            'DefaultDeploymentSid' =>
                $options['defaultDeploymentSid'],
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new FleetInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Access the certificates
     */
    protected function getCertificates(): CertificateList
    {
        if (!$this->_certificates) {
            $this->_certificates = new CertificateList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_certificates;
    }

    /**
     * Access the devices
     */
    protected function getDevices(): DeviceList
    {
        if (!$this->_devices) {
            $this->_devices = new DeviceList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_devices;
    }

    /**
     * Access the keys
     */
    protected function getKeys(): KeyList
    {
        if (!$this->_keys) {
            $this->_keys = new KeyList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_keys;
    }

    /**
     * Access the deployments
     */
    protected function getDeployments(): DeploymentList
    {
        if (!$this->_deployments) {
            $this->_deployments = new DeploymentList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_deployments;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name): ListResource
    {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Preview.DeployedDevices.FleetContext ' . \implode(' ', $context) . ']';
    }
}
