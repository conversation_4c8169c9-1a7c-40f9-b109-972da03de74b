/*! asColorPicker - v0.3.1 - 2014-11-04
* https://github.com/amazingSurge/jquery-asColorPicker
* Copyright (c) 2014 amazingSurge; Licensed GPL */
!function(a,b,c,d,e){"use strict";function f(a){a.id=g,g++}var g=0,h=c.asColorPicker=function(a,b){this.element=a,this.$element=c(a),this.opened=!1,this.firstOpen=!0,this.disabled=!1,this.initialed=!1,this.originValue=this.element.value,this.isEmpty=!1,f(this),this.options=c.extend(!0,{},h.defaults,b,this.$element.data()),this.namespace=this.options.namespace,this.classes={wrap:this.namespace+"-wrap",dropdown:this.namespace+"-dropdown",input:this.namespace+"-input",skin:this.namespace+"_"+this.options.skin,open:this.namespace+"_open",mask:this.namespace+"-mask",hideInput:this.namespace+"_hideInput",disabled:this.namespace+"_disabled",mode:this.namespace+"-mode_"+this.options.mode},this.options.hideInput&&this.$element.addClass(this.classes.hideInput),this.components=h.modes[this.options.mode],this._components=c.extend(!0,{},this._components),this._trigger("init"),this.init()};h.prototype={constructor:h,_components:{},init:function(){this.color=new d(this.element.value,this.options.color),this._create(),this.options.skin&&(this.$dropdown.addClass(this.classes.skin),this.$element.parent().addClass(this.classes.skin)),this.options.readonly&&this.$element.prop("readonly",!0),this._bindEvent(),this.initialed=!0,this._trigger("ready")},_create:function(){var a=this;this.$dropdown=c('<div class="'+this.classes.dropdown+'" data-mode="'+this.options.mode+'"></div>'),this.$element.wrap('<div class="'+this.classes.wrap+'"></div>').addClass(this.classes.input),this.$wrap=this.$element.parent(),this.$body=c("body"),this.$dropdown.data("asColorPicker",this);var b;c.each(this.components,function(d,f){f===!0&&(f={}),a.options[d]!==e&&(f=c.extend(!0,{},f,a.options[d])),a._components[d]&&(b=a._components[d](),b.init(a,f))}),this._trigger("create")},_bindEvent:function(){var a=this;this.$element.on({"click.asColorPicker":function(){return a.opened||a.open(),!1},"keydown.asColorPicker":function(b){9===b.keyCode?a.close():13===b.keyCode&&(a.val(a.element.value),a.close())},"keyup.asColorPicker":function(){a.color.matchString(a.element.value)&&a.val(a.element.value)}})},_trigger:function(a){var b=Array.prototype.slice.call(arguments,1),c=[this].concat(b);this.$element.trigger("asColorPicker::"+a,c),a=a.replace(/\b\w+\b/g,function(a){return a.substring(0,1).toUpperCase()+a.substring(1)});var d="on"+a;"function"==typeof this.options[d]&&this.options[d].apply(this,b)},opacity:function(a){return a?void this.color.alpha(a):this.color.alpha()},position:function(){var b,d,e=!this.$element.is(":visible"),f=e?this.$trigger.offset():this.$element.offset(),g=e?this.$trigger.outerHeight():this.$element.outerHeight(),h=e?this.$trigger.outerWidth():this.$element.outerWidth()+this.$trigger.outerWidth(),i=this.$dropdown.outerWidth(!0),j=this.$dropdown.outerHeight(!0);b=j+f.top>c(a).height()+c(a).scrollTop()?f.top-j:f.top+g,d=i+f.left>c(a).width()+c(a).scrollLeft()?f.left-i+h:f.left,this.$dropdown.css({position:"absolute",top:b,left:d})},open:function(){if(!this.disabled){this.originValue=this.element.value;var b=this;this.$dropdown[0]!==this.$body.children().last()[0]&&this.$dropdown.detach().appendTo(this.$body),this.$mask=c("."+b.classes.mask),0===this.$mask.length&&this.createMask(),this.$dropdown.prev()[0]!==this.$mask[0]&&this.$dropdown.before(this.$mask),c("#asColorPicker-dropdown").removeAttr("id"),this.$dropdown.attr("id","asColorPicker-dropdown"),this.$mask.show(),this.position(),c(a).on("resize.asColorPicker",c.proxy(this.position,this)),this.$dropdown.addClass(this.classes.open),this.opened=!0,this.firstOpen&&(this.firstOpen=!1,this._trigger("firstOpen")),this._setup(),this._trigger("open")}},createMask:function(){this.$mask=c(b.createElement("div")),this.$mask.attr("class",this.classes.mask),this.$mask.hide(),this.$mask.appendTo(this.$body),this.$mask.on("mousedown touchstart click",function(a){var b,d=c("#asColorPicker-dropdown");d.length>0&&(b=d.data("asColorPicker"),b.opened&&(b.options.hideFireChange?b.apply():b.cancel()),a.preventDefault(),a.stopPropagation())})},close:function(){this.opened=!1,this.$element.blur(),this.$mask.hide(),this.$dropdown.removeClass(this.classes.open),c(a).off("resize.asColorPicker"),this._trigger("close")},clear:function(){this.val("")},cancel:function(){this.close(),this.set(this.originValue)},apply:function(){this._trigger("apply",this.color),this.close()},val:function(a){return"undefined"==typeof a?this.color.toString():void this.set(a)},_update:function(){this._trigger("update",this.color),this._updateInput()},_updateInput:function(){var a=this.color.toString();this.isEmpty&&(a=""),this._trigger("change",a,this.options.name,"asColorPicker"),this.$element.val(a)},set:function(a){return this.isEmpty=""!==a?!1:!0,this._set(a)},_set:function(a){"string"==typeof a?this.color.val(a):this.color.set(a),this._update()},_setup:function(){this._trigger("setup",this.color)},get:function(){return this.color},enable:function(){return this.disabled=!1,this.$parent.addClass(this.classes.disabled),this},disable:function(){return this.disabled=!0,this.$parent.removeClass(this.classes.disabled),this},destroy:function(){}},h.registerComponent=function(a,b){h.prototype._components[a]=b},h.localization=[],h.defaults={namespace:"asColorPicker",readonly:!1,skin:null,hideInput:!1,hideFireChange:!0,keyboard:!1,color:{format:!1,alphaConvert:{RGB:"RGBA",HSL:"HSLA",HEX:"RGBA",NAME:"RGBA"},shortenHex:!1,hexUseName:!1,reduceAlpha:!0,nameDegradation:"HEX",invalidValue:"",zeroAlphaAsTransparent:!0},mode:"simple",onInit:null,onReady:null,onChange:null,onClose:null,onOpen:null,onApply:null},h.modes={simple:{trigger:!0,clear:!0,saturation:!0,hue:!0,alpha:!0},palettes:{trigger:!0,clear:!0,palettes:!0},complex:{trigger:!0,clear:!0,preview:!0,palettes:!0,saturation:!0,hue:!0,alpha:!0,hex:!0,buttons:!0},gradient:{trigger:!0,clear:!0,preview:!0,palettes:!0,saturation:!0,hue:!0,alpha:!0,hex:!0,gradient:!0}},c.fn.asColorPicker=function(a){if("string"!=typeof a)return this.each(function(){c.data(this,"asColorPicker")||c.data(this,"asColorPicker",new h(this,a))});var b=a,d=Array.prototype.slice.call(arguments,1);if(/^\_/.test(b))return!1;if(!(/^(get)$/.test(b)||"val"===b&&0===d.length))return this.each(function(){var a=c.data(this,"asColorPicker");a&&"function"==typeof a[b]&&a[b].apply(a,d)});var e=this.first().data("asColorPicker");return e&&"function"==typeof e[b]?e[b].apply(e,d):void 0}}(window,document,jQuery,function(a){return void 0===a.asColor?!1:a.asColor}(jQuery)),function(a){"use strict";a.asColorPicker.registerComponent("trigger",function(){return{defaults:{template:function(a){return'<div class="'+a+'-trigger"><span></span></div>'}},init:function(b,c){this.options=a.extend(this.defaults,c),b.$trigger=a(this.options.template.call(this,b.namespace)),this.$trigger_inner=b.$trigger.children("span"),b.$trigger.insertAfter(b.$element),b.$trigger.on("click",function(){return b.opened?b.close():b.open(),!1});var d=this;b.$element.on("asColorPicker::update",function(a,b,c,e){"undefined"==typeof e&&(e=!1),d.update(c,e)}),this.update(b.color)},update:function(a,b){b?this.$trigger_inner.css("background",b.toString(!0)):this.$trigger_inner.css("background",a.toRGBA())},destroy:function(a){a.$trigger.remove()}}})}(jQuery),function(a){"use strict";a.asColorPicker.registerComponent("clear",function(){return{defaults:{template:function(a){return'<a href="#" class="'+a+'-clear"></a>'}},init:function(b,c){b.options.hideInput||(this.options=a.extend(this.defaults,c),this.$clear=a(this.options.template.call(this,b.namespace)).insertAfter(b.$element),this.$clear.on("click",function(){return b.clear(),!1}))}}})}(jQuery),function(a,b,c){"use strict";var d=c(b),e={keys:{UP:38,DOWN:40,LEFT:37,RIGHT:39,RETURN:13,ESCAPE:27,BACKSPACE:8,SPACE:32},map:{},bound:!1,press:function(a){var b=a.keyCode||a.which;return b in e.map&&"function"==typeof e.map[b]&&e.map[b](a),!1},attach:function(a){var b,c;for(b in a)a.hasOwnProperty(b)&&(c=b.toUpperCase(),c in e.keys?e.map[e.keys[c]]=a[b]:e.map[c]=a[b]);e.bound||(e.bound=!0,d.bind("keydown",e.press))},detach:function(){e.bound=!1,e.map={},d.unbind("keydown",e.press)}};d.on("asColorPicker::init",function(a,b){b.options.keyboard===!0&&(b._keyboard=e)})}(window,document,jQuery),function(a){"use strict";a.asColorPicker.registerComponent("alpha",function(){return{size:150,defaults:{direction:"vertical",template:function(a){return'<div class="'+a+"-alpha "+a+"-alpha-"+this.direction+'"><i></i></div>'}},data:{},init:function(b,c){var d=this;this.options=a.extend(this.defaults,c),d.direction=this.options.direction,this.api=b,this.$alpha=a(this.options.template.call(d,b.namespace)).appendTo(b.$dropdown),this.$handle=this.$alpha.find("i"),b.$element.on("asColorPicker::firstOpen",function(){d.size="vertical"===d.direction?d.$alpha.height():d.$alpha.width(),d.step=d.size/360,d.bindEvents(),d.keyboard()}),b.$element.on("asColorPicker::update asColorPicker::setup",function(a,b,c){d.update(c)})},bindEvents:function(){var b=this;this.$alpha.on("mousedown.asColorPicker",function(c){var d=c.which?3===c.which:2===c.button;return d?!1:void a.proxy(b.mousedown,b)(c)})},mousedown:function(b){var c=this.$alpha.offset();return"vertical"===this.direction?(this.data.startY=b.pageY,this.data.top=b.pageY-c.top,this.move(this.data.top)):(this.data.startX=b.pageX,this.data.left=b.pageX-c.left,this.move(this.data.left)),this.mousemove=function(a){var b;return b="vertical"===this.direction?this.data.top+(a.pageY||this.data.startY)-this.data.startY:this.data.left+(a.pageX||this.data.startX)-this.data.startX,this.move(b),!1},this.mouseup=function(){return a(document).off({mousemove:this.mousemove,mouseup:this.mouseup}),"vertical"===this.direction?this.data.top=this.data.cach:this.data.left=this.data.cach,!1},a(document).on({mousemove:a.proxy(this.mousemove,this),mouseup:a.proxy(this.mouseup,this)}),!1},move:function(a,b,c){a=Math.max(0,Math.min(this.size,a)),this.data.cach=a,"undefined"==typeof b&&(b=1-a/this.size),b=Math.max(0,Math.min(1,b)),this.$handle.css("vertical"===this.direction?{top:a}:{left:a}),c!==!1&&this.api.set({a:Math.round(100*b)/100})},moveLeft:function(){var a=this.step,b=this.data;b.left=Math.max(0,Math.min(this.width,b.left-a)),this.move(b.left)},moveRight:function(){var a=this.step,b=this.data;b.left=Math.max(0,Math.min(this.width,b.left+a)),this.move(b.left)},moveUp:function(){var a=this.step,b=this.data;b.top=Math.max(0,Math.min(this.width,b.top-a)),this.move(b.top)},moveDown:function(){var a=this.step,b=this.data;b.top=Math.max(0,Math.min(this.width,b.top+a)),this.move(b.top)},keyboard:function(){var b,c=this;return this.api._keyboard?(b=a.extend(!0,{},this.api._keyboard),void this.$alpha.attr("tabindex","0").on("focus",function(){return b.attach("vertical"===this.direction?{up:function(){c.moveUp()},down:function(){c.moveDown()}}:{left:function(){c.moveLeft()},right:function(){c.moveRight()}}),!1}).on("blur",function(){b.detach()})):!1},update:function(a){var b=this.size*(1-a.value.a);this.$alpha.css("backgroundColor",a.toHEX()),this.move(b,a.value.a,!1)},destroy:function(){a(document).off({mousemove:this.mousemove,mouseup:this.mouseup})}}})}(jQuery),function(a){"use strict";a.asColorPicker.registerComponent("buttons",function(){return{defaults:{apply:!1,cancel:!0,applyText:"apply",cancelText:"cancel",template:function(a){return'<div class="'+a+'-buttons"></div>'},applyTemplate:function(a){return'<a href="#" alt="'+this.options.applyText+'" class="'+a+'-buttons-apply">'+this.options.applyText+"</a>"},cancelTemplate:function(a){return'<a href="#" alt="'+this.options.cancelText+'" class="'+a+'-buttons-apply">'+this.options.cancelText+"</a>"}},init:function(b,c){var d=this;this.options=a.extend(this.defaults,c),this.$buttons=a(this.options.template.call(this,b.namespace)).appendTo(b.$dropdown),b.$element.on("asColorPicker::firstOpen",function(){d.options.apply&&(d.$apply=a(d.options.applyTemplate.call(d,b.namespace)).appendTo(d.$buttons).on("click",function(){return b.apply(),!1})),d.options.cancel&&(d.$cancel=a(d.options.cancelTemplate.call(d,b.namespace)).appendTo(d.$buttons).on("click",function(){return b.cancel(),!1}))})}}})}(jQuery),function(a){"use strict";a.asColorPicker.registerComponent("hex",function(){return{init:function(b){var c='<input type="text" class="'+b.namespace+'-hex" />';this.$hex=a(c).appendTo(b.$dropdown),this.$hex.on("change",function(){b.set(this.value)});var d=this;b.$element.on("asColorPicker::update asColorPicker::setup",function(a,b,c){d.update(c)})},update:function(a){this.$hex.val(a.toHEX())}}})}(jQuery),function(a){"use strict";a.asColorPicker.registerComponent("hue",function(){return{size:150,defaults:{direction:"vertical",template:function(){var a=this.api.namespace;return'<div class="'+a+"-hue "+a+"-hue-"+this.direction+'"><i></i></div>'}},data:{},init:function(b,c){var d=this;this.options=a.extend(this.defaults,c),this.direction=this.options.direction,this.api=b,this.$hue=a(this.options.template.call(d)).appendTo(b.$dropdown),this.$handle=this.$hue.find("i"),b.$element.on("asColorPicker::firstOpen",function(){d.size="vertical"===d.direction?d.$hue.height():d.$hue.width(),d.step=d.size/360,d.bindEvents(b),d.keyboard(b)}),b.$element.on("asColorPicker::update asColorPicker::setup",function(a,b,c){d.update(c)})},bindEvents:function(){var b=this;this.$hue.on("mousedown.asColorPicker",function(c){var d=c.which?3===c.which:2===c.button;return d?!1:void a.proxy(b.mousedown,b)(c)})},mousedown:function(b){var c=this.$hue.offset();return"vertical"===this.direction?(this.data.startY=b.pageY,this.data.top=b.pageY-c.top,this.move(this.data.top)):(this.data.startX=b.pageX,this.data.left=b.pageX-c.left,this.move(this.data.left)),this.mousemove=function(a){var b;return b="vertical"===this.direction?this.data.top+(a.pageY||this.data.startY)-this.data.startY:this.data.left+(a.pageX||this.data.startX)-this.data.startX,this.move(b),!1},this.mouseup=function(){return a(document).off({mousemove:this.mousemove,mouseup:this.mouseup}),"vertical"===this.direction?this.data.top=this.data.cach:this.data.left=this.data.cach,!1},a(document).on({mousemove:a.proxy(this.mousemove,this),mouseup:a.proxy(this.mouseup,this)}),!1},move:function(a,b,c){a=Math.max(0,Math.min(this.size,a)),this.data.cach=a,"undefined"==typeof b&&(b=360*(1-a/this.size)),b=Math.max(0,Math.min(360,b)),this.$handle.css("vertical"===this.direction?{top:a}:{left:a}),c!==!1&&this.api.set({h:b})},moveLeft:function(){var a=this.step,b=this.data;b.left=Math.max(0,Math.min(this.width,b.left-a)),this.move(b.left)},moveRight:function(){var a=this.step,b=this.data;b.left=Math.max(0,Math.min(this.width,b.left+a)),this.move(b.left)},moveUp:function(){var a=this.step,b=this.data;b.top=Math.max(0,Math.min(this.width,b.top-a)),this.move(b.top)},moveDown:function(){var a=this.step,b=this.data;b.top=Math.max(0,Math.min(this.width,b.top+a)),this.move(b.top)},keyboard:function(){var b,c=this;return this.api._keyboard?(b=a.extend(!0,{},this.api._keyboard),void this.$hue.attr("tabindex","0").on("focus",function(){return b.attach("vertical"===this.direction?{up:function(){c.moveUp()},down:function(){c.moveDown()}}:{left:function(){c.moveLeft()},right:function(){c.moveRight()}}),!1}).on("blur",function(){b.detach()})):!1},update:function(a){var b=0===a.value.h?0:this.size*(1-a.value.h/360);this.move(b,a.value.h,!1)},destroy:function(){a(document).off({mousemove:this.mousemove,mouseup:this.mouseup})}}})}(jQuery),function(a){"use strict";a.asColorPicker.registerComponent("info",function(){return{color:["white","black","transparent"],init:function(b){var c='<ul class="'+b.namespace+'-info"><li><label>R:<input type="text" data-type="r"/></label></li><li><label>G:<input type="text" data-type="g"/></label></li><li><label>B:<input type="text" data-type="b"/></label></li><li><label>A:<input type="text" data-type="a"/></label></li></ul>';this.$info=a(c).appendTo(b.$dropdown),this.$r=this.$info.find('[data-type="r"]'),this.$g=this.$info.find('[data-type="g"]'),this.$b=this.$info.find('[data-type="b"]'),this.$a=this.$info.find('[data-type="a"]'),this.$info.delegate("input","keyup update change",function(c){var d,e=a(c.target).data("type");switch(e){case"r":case"g":case"b":d=parseInt(this.value,10),d>255?d=255:0>d&&(d=0);break;case"a":d=parseFloat(this.value,10),d>1?d=1:0>d&&(d=0)}isNaN(d)&&(d=0);var f={};f[e]=d,b.set(f)});var d=this;b.$element.on("asColorPicker::update asColorPicker::setup",function(a,b){d.update(b)})},update:function(a){this.$r.val(a.value.r),this.$g.val(a.value.g),this.$b.val(a.value.b),this.$a.val(a.value.a)}}})}(jQuery),function(a){"use strict";function b(){}window.localStorage||(window.localStorage=b),a.asColorPicker.registerComponent("palettes",function(){return{defaults:{template:function(a){return'<ul class="'+a+'-palettes"></ul>'},item:function(a,b){return'<li data-color="'+b+'"><span style="background-color:'+b+'" /></li>'},colors:["white","black","red","blue","yellow"],max:10,localStorage:!0},init:function(b,c){var d,e=this,f=new a.asColor;if(this.options=a.extend(!0,{},this.defaults,c),this.colors=[],this.options.localStorage){var g=b.namespace+"_palettes_"+b.id;d=this.getLocal(g),d||(d=this.options.colors,this.setLocal(g,d))}else d=this.options.colors;for(var h in d)this.colors.push(f.val(d[h]).toRGBA());var i="";a.each(this.colors,function(a,c){i+=e.options.item(b.namespace,c)}),this.$palettes=a(this.options.template.call(this,b.namespace)).html(i).appendTo(b.$dropdown),this.$palettes.delegate("li","click",function(c){var d=a(this).data("color");b.set(d),c.preventDefault(),c.stopPropagation()}),b.$element.on("asColorPicker::apply",function(b,c,d){"function"!=typeof d.toRGBA&&(d=d.get().color);var f=d.toRGBA();-1===a.inArray(f,e.colors)&&(e.colors.length>=e.options.max&&(e.colors.shift(),e.$palettes.find("li").eq(0).remove()),e.colors.push(f),e.$palettes.append(e.options.item(c.namespace,d)),e.options.localStorage&&e.setLocal(g,e.colors))})},setLocal:function(a,b){var c=JSON.stringify(b);localStorage[a]=c},getLocal:function(a){var b=localStorage[a];return b?JSON.parse(b):b}}})}(jQuery),function(a){"use strict";a.asColorPicker.registerComponent("preview",function(){return{defaults:{template:function(a){return'<ul class="'+a+'-preview"><li class="'+a+'-preview-current"><span /></li><li class="'+a+'-preview-previous"><span /></li></ul>'}},init:function(b,c){var d=this;this.options=a.extend(this.defaults,c),this.$preview=a(this.options.template.call(d,b.namespace)).appendTo(b.$dropdown),this.$current=this.$preview.find("."+b.namespace+"-preview-current span"),this.$previous=this.$preview.find("."+b.namespace+"-preview-previous span"),b.$element.on("asColorPicker::firstOpen",function(){d.$previous.on("click",function(){return b.set(a(this).data("color")),!1})}),b.$element.on("asColorPicker::setup",function(a,b,c){d.updateCurrent(c),d.updatePreview(c)}),b.$element.on("asColorPicker::update",function(a,b,c){d.updateCurrent(c)})},updateCurrent:function(a){this.$current.css("backgroundColor",a.toRGBA())},updatePreview:function(a){this.$previous.css("backgroundColor",a.toRGBA()),this.$previous.data("color",{r:a.value.r,g:a.value.g,b:a.value.b,a:a.value.a})}}})}(jQuery),function(a){"use strict";a.asColorPicker.registerComponent("saturation",function(){return{defaults:{template:function(a){return'<div class="'+a+'-saturation"><i><b></b></i></div>'}},width:0,height:0,size:6,data:{},init:function(b,c){var d=this;this.options=a.extend(this.defaults,c),this.api=b,this.$saturation=a(this.options.template.call(d,b.namespace)).appendTo(b.$dropdown),this.$handle=this.$saturation.find("i"),b.$element.on("asColorPicker::firstOpen",function(){d.width=d.$saturation.width(),d.height=d.$saturation.height(),d.step={left:d.width/20,top:d.height/20},d.size=d.$handle.width()/2,d.bindEvents(),d.keyboard(b)}),b.$element.on("asColorPicker::update asColorPicker::setup",function(a,b,c){d.update(c)})},bindEvents:function(){var a=this;this.$saturation.on("mousedown.asColorPicker",function(b){var c=b.which?3===b.which:2===b.button;return c?!1:void a.mousedown(b)})},mousedown:function(b){var c=this.$saturation.offset();return this.data.startY=b.pageY,this.data.startX=b.pageX,this.data.top=b.pageY-c.top,this.data.left=b.pageX-c.left,this.data.cach={},this.move(this.data.left,this.data.top),this.mousemove=function(a){var b=this.data.left+(a.pageX||this.data.startX)-this.data.startX,c=this.data.top+(a.pageY||this.data.startY)-this.data.startY;return this.move(b,c),!1},this.mouseup=function(){return a(document).off({mousemove:this.mousemove,mouseup:this.mouseup}),this.data.left=this.data.cach.left,this.data.top=this.data.cach.top,!1},a(document).on({mousemove:a.proxy(this.mousemove,this),mouseup:a.proxy(this.mouseup,this)}),!1},move:function(a,b,c){b=Math.max(0,Math.min(this.height,b)),a=Math.max(0,Math.min(this.width,a)),void 0===this.data.cach&&(this.data.cach={}),this.data.cach.left=a,this.data.cach.top=b,this.$handle.css({top:b-this.size,left:a-this.size}),c!==!1&&this.api.set({s:a/this.width,v:1-b/this.height})},update:function(b){void 0===b.value.h&&(b.value.h=0),this.$saturation.css("backgroundColor",a.asColor.HSLToHEX({h:b.value.h,s:1,l:.5}));var c=b.value.s*this.width,d=(1-b.value.v)*this.height;this.move(c,d,!1)},moveLeft:function(){var a=this.step.left,b=this.data;b.left=Math.max(0,Math.min(this.width,b.left-a)),this.move(b.left,b.top)},moveRight:function(){var a=this.step.left,b=this.data;b.left=Math.max(0,Math.min(this.width,b.left+a)),this.move(b.left,b.top)},moveUp:function(){var a=this.step.top,b=this.data;b.top=Math.max(0,Math.min(this.width,b.top-a)),this.move(b.left,b.top)},moveDown:function(){var a=this.step.top,b=this.data;b.top=Math.max(0,Math.min(this.width,b.top+a)),this.move(b.left,b.top)},keyboard:function(){var b,c=this;return this.api._keyboard?(b=a.extend(!0,{},this.api._keyboard),void this.$saturation.attr("tabindex","0").on("focus",function(){return b.attach({left:function(){c.moveLeft()},right:function(){c.moveRight()},up:function(){c.moveUp()},down:function(){c.moveDown()}}),!1}).on("blur",function(){b.detach()})):!1},destroy:function(){a(document).off({mousemove:this.mousemove,mouseup:this.mouseup})}}})}(jQuery),function(a,b){function c(a){return 0>a?a=0:a>1&&(a=1),100*a+"%"}a.asColorPicker.registerComponent("gradient",function(){return{defaults:{switchable:!0,switchText:"Gradient",cancelText:"Cancel",settings:{forceStandard:!0,angleUseKeyword:!0,emptyString:"",degradationFormat:!1,cleanPosition:!1,forceColorFormat:"rgb"},template:function(){var a=this.api.namespace,b='<div class="'+a+'-gradient-control">';return this.options.switchable&&(b+='<a href="#" class="'+a+'-gradient-switch">'+this.options.switchText+"</a>"),b+='<a href="#" class="'+a+'-gradient-cancel">'+this.options.cancelText+"</a></div>",b+'<div class="'+a+'-gradient"><div class="'+a+'-gradient-preview"><ul class="'+a+'-gradient-markers"></ul></div><div class="'+a+'-gradient-wheel"><i></i></div><input class="'+a+'-gradient-angle" type="text" value="" size="3" /></div>'}},init:function(b,c){var e=this;b.$element.on("asColorPicker::ready",function(f,g){"gradient"===g.options.mode&&(e.defaults.settings.color=b.options.color,c=a.extend(!0,e.defaults,c),b.gradient=new d(b,c))})}}});var d=function(d,e){this.api=d,this.options=e,this.classes={enable:d.namespace+"-gradient_enable",marker:d.namespace+"-gradient-marker",active:d.namespace+"-gradient-marker_active",focus:d.namespace+"-gradient_focus"},this.isEnabled=!1,this.initialized=!1,this.current=null,this.value=new b(this.options.settings),this.$doc=a(document);var f=this;a.extend(f,{init:function(){f.$wrap=a(f.options.template.call(f)).appendTo(d.$dropdown),f.$gradient=f.$wrap.filter("."+d.namespace+"-gradient"),this.angle.init(),this.preview.init(),this.markers.init(),this.wheel.init(),this.bind(),f.options.switchable===!1?f.enable():this.value.matchString(d.element.value)&&f.enable(),this.initialized=!0},bind:function(){var a=d.namespace;f.$gradient.on("update",function(){var a=f.value.getById(f.current);a&&d._trigger("update",a.color,f.value),d.element.value!==f.value.toString()&&d._updateInput()}),f.options.switchable&&f.$wrap.on("click","."+a+"-gradient-switch",function(){return f.isEnabled?f.disable():f.enable(),!1}),f.$wrap.on("click","."+a+"-gradient-cancel",function(){return(f.options.switchable===!1||b.matchString(d.originValue))&&f.overrideCore(),d.cancel(),!1})},overrideCore:function(){d.set=function(a){if(d.isEmpty=""!==a?!1:!0,"string"==typeof a)f.options.switchable===!1||b.matchString(a)?f.isEnabled?(f.val(a),d.color=f.value,f.$gradient.trigger("update",f.value.value)):f.enable(a):(f.disable(),d.val(a));else{var c=f.value.getById(f.current);c&&(c.color.val(a),d._trigger("update",c.color,f.value)),f.$gradient.trigger("update",{id:f.current,stop:c})}},d._setup=function(){var a=f.value.getById(f.current);d._trigger("setup",a.color)}},revertCore:function(){d.set=a.proxy(d._set,d),d._setup=function(){d._trigger("setup",d.color)}},preview:{init:function(){var a=this;f.$preview=f.$gradient.find("."+d.namespace+"-gradient-preview"),f.$gradient.on("add del update empty",function(){a.render()})},render:function(){f.$preview.css({"background-image":f.value.toStringWithAngle("to right",!0)}),f.$preview.css({"background-image":f.value.toStringWithAngle("to right")})}},markers:{width:160,init:function(){f.$markers=f.$gradient.find("."+d.namespace+"-gradient-markers").attr("tabindex",0);var b=this;f.$gradient.on("add",function(a,c){b.add(c.stop)}),f.$gradient.on("active",function(a,c){b.active(c.id)}),f.$gradient.on("del",function(a,c){b.del(c.id)}),f.$gradient.on("update",function(a,c){c.stop&&b.update(c.stop.id,c.stop.color)}),f.$gradient.on("empty",function(){b.empty()}),f.$markers.on("mousedown.asColorPicker",function(a){var b=a.which?3===a.which:2===a.button;if(b)return!1;var c=parseFloat((a.pageX-f.$markers.offset().left)/f.markers.width,10);return f.add("#fff",c),!1}),f.$markers.on("mousedown.asColorPicker","li",function(a){var c=a.which?3===a.which:2===a.button;return c?!1:(b.mousedown(this,a),!1)}),f.$doc.on("keydown.asColorPicker",function(a){if(f.api.opened&&f.$markers.is("."+f.classes.focus)){var b=a.keyCode||a.which;if(46===b||8===b)return f.value.length<=2?!1:(f.del(f.current),!1)}}),f.$markers.on("focus.asColorPicker",function(){f.$markers.addClass(f.classes.focus)}).on("blur.asColorPicker",function(){f.$markers.removeClass(f.classes.focus)}),f.$markers.on("click","li",function(){var b=a(this).data("id");f.active(b)})},getMarker:function(a){return f.$markers.find('[data-id="'+a+'"]')},update:function(a,b){var c=this.getMarker(a);c.find("span").css("background-color",b.toHEX()),c.find("i").css("background-color",b.toHEX())},add:function(b){a('<li data-id="'+b.id+'" style="left:'+c(b.position)+'" class="'+f.classes.marker+'"><span style="background-color: '+b.color.toHEX()+'"></span><i style="background-color: '+b.color.toHEX()+'"></i></li>').appendTo(f.$markers)},empty:function(){f.$markers.html("")},del:function(a){var b=this.getMarker(a),c=b.prev();0===c.length&&(c=b.next()),f.active(c.data("id")),b.remove()},active:function(a){f.$markers.children().removeClass(f.classes.active);var b=this.getMarker(a);b.addClass(f.classes.active),f.$markers.focus()},mousedown:function(b,c){var d,e=this,g=a(b).data("id"),h=a(b).position().left,i=c.pageX;return this.mousemove=function(a){d=a.pageX||i;var c=(h+d-i)/this.width;return e.move(b,c,g),!1},this.mouseup=function(){return a(document).off({mousemove:this.mousemove,mouseup:this.mouseup}),!1},f.$doc.on({mousemove:a.proxy(this.mousemove,this),mouseup:a.proxy(this.mouseup,this)}),f.active(g),!1},move:function(b,d,e){f.api.isEmpty=!1,d=Math.max(0,Math.min(1,d)),a(b).css({left:c(d)}),e||(e=a(b).data("id")),f.value.getById(e).setPosition(d),f.$gradient.trigger("update",{id:a(b).data("id"),position:d})}},wheel:{init:function(){var a=this;f.$wheel=f.$gradient.find("."+d.namespace+"-gradient-wheel"),f.$pointer=f.$wheel.find("i"),f.$gradient.on("update",function(b,c){"undefined"!=typeof c.angle&&a.position(c.angle)}),f.$wheel.on("mousedown.asColorPicker",function(b){var c=b.which?3===b.which:2===b.button;return c?!1:(a.mousedown(b,f),!1)})},mousedown:function(b,c){var d=c.$wheel.offset(),e=c.$wheel.width()/2,f=d.left+e,g=d.top+e,h=c.$doc,i=this;this.r=e,this.wheelMove=function(a){var b=a.pageX-f,d=g-a.pageY,e=i.getPosition(b,d),h=i.calAngle(e.x,e.y);c.api.isEmpty=!1,c.setAngle(h)},this.wheelMouseup=function(){return h.off({mousemove:this.wheelMove,mouseup:this.wheelMouseup}),!1},h.on({mousemove:a.proxy(this.wheelMove,this),mouseup:a.proxy(this.wheelMouseup,this)}),this.wheelMove(b)},getPosition:function(a,b){var c=this.r,d=a/Math.sqrt(a*a+b*b)*c,e=b/Math.sqrt(a*a+b*b)*c;return{x:d,y:e}},calAngle:function(a,b){var c=Math.round(Math.atan(Math.abs(a/b))*(180/Math.PI));return 0>a&&b>0?360-c:0>a&&0>=b?c+180:a>=0&&0>=b?180-c:a>=0&&b>0?c:void 0},set:function(a){f.value.angle(a),f.$gradient.trigger("update",{angle:a})},position:function(a){var b=this.r||f.$wheel.width()/2,c=this.calPointer(a,b);f.$pointer.css({left:c.x,top:c.y})},calPointer:function(a,b){var c=Math.sin(a*Math.PI/180)*b,d=Math.cos(a*Math.PI/180)*b;return{x:b+c,y:b-d}}},angle:{init:function(){f.$angle=f.$gradient.find("."+d.namespace+"-gradient-angle"),f.$angle.on("blur.asColorPicker",function(){return f.setAngle(this.value),!1}).on("keydown.asColorPicker",function(b){var c=b.keyCode||b.which;return 13===c?(f.api.isEmpty=!1,a(this).blur(),!1):void 0}),f.$gradient.on("update",function(a,b){"undefined"!=typeof b.angle&&f.$angle.val(b.angle)})},set:function(a){f.value.angle(a),f.$gradient.trigger("update",{angle:a})}}}),this.init()};d.prototype={constructor:d,enable:function(a){this.isEnabled!==!0&&(this.isEnabled=!0,this.overrideCore(),this.$gradient.addClass(this.classes.enable),this.markers.width=this.$markers.width(),"undefined"==typeof a&&(a=this.api.element.value),this.api.isEmpty=""!==a?!1:!0,!b.matchString(a)&&this._last?this.value=this._last:this.val(a),this.api.color=this.value,this.$gradient.trigger("update",this.value.value),this.api.opened&&this.api.position())},val:function(b){if(""===b||this.value.toString()!==b){if(this.empty(),this.value.val(b),this.value.reorder(),this.value.length<2){var c=b;a.asColor.matchString(b)||(c="rgba(0,0,0,1)"),0===this.value.length&&this.value.append(c,0),1===this.value.length&&this.value.append(c,1)}for(var d,e=0;e<this.value.length;e++)d=this.value.get(e),d&&this.$gradient.trigger("add",{stop:d});this.active(d.id)}},disable:function(){this.isEnabled!==!1&&(this.isEnabled=!1,this.revertCore(),this.$gradient.removeClass(this.classes.enable),this._last=this.value,this.api.color=this.api.color.getCurrent().color,this.api.set(this.api.color.value),this.api.opened&&this.api.position())},active:function(a){this.current!==a&&(this.current=a,this.value.setCurrentById(a),this.$gradient.trigger("active",{id:a}))},empty:function(){this.value.empty(),this.$gradient.trigger("empty")},add:function(a,b){var c=this.value.insert(a,b);return this.api.isEmpty=!1,this.value.reorder(),this.$gradient.trigger("add",{stop:c}),this.active(c.id),this.$gradient.trigger("update",{stop:c}),c},del:function(a){this.value.length<=2||(this.value.removeById(a),this.value.reorder(),this.$gradient.trigger("del",{id:a}),this.$gradient.trigger("update",{}))},setAngle:function(a){this.value.angle(a),this.$gradient.trigger("update",{angle:a})}}}(jQuery,function(a){return void 0===a.asGradient?!1:a.asGradient}(jQuery));