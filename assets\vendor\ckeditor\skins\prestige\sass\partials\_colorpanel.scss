/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/license
*/

/*
colorpanel.css (part of editor.css)
=====================================
The color panel is related to the contents part of the panels that are
displayed when clicking the color buttons of the toolbar. See panels.css for
styles related to the outer part of panels.
The following is the visual representation of the color panel contents:
+-- .cke_panel_block.cke_colorblock --+
| +-- a.cke_colorauto --------------+ |
| |                                 | |
| |         AUTOMATIC COLOR         | |
| |                                 | |
| +---------------------------------+ |
| +-- table ------------------------+ |
| |                                 | |
| |          COLOR PALETTE          | |
| |                                 | |
| |---------------------------------| |
| |          "More Colors"          | |
| +---------------------------------+ |
+-------------------------------------+
The AUTOMATIC COLOR section is an <a> containing a table with two cells with
the following contents:
+-- TD -----------------+ +-- TD -----------------+
| +-- .cke_colorbox --+ | |                       |
| |                   | | |      "Automatic"      |
| +-------------------+ | |                       |
+-----------------------+ +-----------------------+
The COLOR PALETTE section instead is a table with a variable number of cells
(by default 8). Each cell represents a color box, with the following structure:
+-- A.cke_colorbox ---------+
| +-- SPAN.cke_colorbox --+ |
| |                       | |
| +-----------------------+ |
+---------------------------+
*/

/* The container of the color palette. */
.cke_colorblock
{
	padding: 10px;
	font-size: 11px;
	font-family: 'Microsoft Sans Serif', Tahoma, Arial, Verdana, Sans-Serif;
	background-color: $color-dialog;
	overflow: hidden;
}

.cke_colorblock,
.cke_colorblock a
{
	text-decoration: none;
	color: $color-text;
}

/* The wrapper of the span.cke_colorbox. It provides an extra border and padding. */
a.cke_colorbox
{
	padding: 2px;
	float: left;
	width: 20px;
	height: 20px;
}

.cke_rtl a.cke_colorbox
{
	float: right;
}

/* Different states of the a.cke_colorbox wrapper. */
a:hover.cke_colorbox,
a:focus.cke_colorbox,
a:active.cke_colorbox
{
	outline: none;
	padding: 0;
	border: 2px solid $color-border;
	border-radius: 100%;
}

a:hover.cke_colorbox
{
	border-color: $color-border;
}

/* The box which is to represent a single color on the color palette.
   It is a small, square-shaped element which can be selected from the palette. */
span.cke_colorbox
{
	width: 20px;
	height: 20px;
	float: left;
	border-radius: 100%;
}

.cke_rtl span.cke_colorbox
{
	float: right;
}

/* Buttons which are visible at the top/bottom of the color palette:
   - cke_colorauto (TOP) applies the automatic color.
   - cke_colormore (BOTTOM) executes the color dialog.
*/
a.cke_colorauto,
a.cke_colormore
{
	border: 1px solid $color-border;
	padding: 3px;
	display: block;
	cursor: pointer;
}

a.cke_colorauto
{
	padding: 0;
	border: 1px solid transparent;
	margin-bottom: 6px;
	height: 26px;
	line-height: 26px;
}

a.cke_colormore
{
	margin-top: 10px;
	height: 20px;
	line-height: 19px;
}

/* Different states of cke_colorauto/cke_colormore buttons. */
a:hover.cke_colorauto,
a:hover.cke_colormore,
a:focus.cke_colorauto,
a:focus.cke_colormore,
a:active.cke_colorauto,
a:active.cke_colormore
{
	outline: none;
	border: solid 1px $color-border;
}

a:hover.cke_colorauto,
a:hover.cke_colormore
{
	border-color: $color-border;
}

.cke_colorauto span.cke_colorbox
{
	width: 18px;
	height: 18px;
	border: 1px solid $color-border;
	margin-left: 1px;
	margin-top: 3px;
}

.cke_rtl .cke_colorauto span.cke_colorbox
{
	margin-left: 0;
	margin-right: 1px;
}

span.cke_colorbox[style*="#ffffff"],
span.cke_colorbox[style*="#FFFFFF"],
span.cke_colorbox[style="background-color:#fff"],
span.cke_colorbox[style="background-color:#FFF"],

/* IE, Edge */
span.cke_colorbox[style*="rgb(255,255,255)"],
span.cke_colorbox[style*="rgb(255, 255, 255)"]
{
	border: 1px solid $color-border;
	width: 18px;
	height: 18px;
}