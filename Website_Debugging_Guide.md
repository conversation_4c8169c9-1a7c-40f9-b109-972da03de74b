# Website Debugging and Modification Guide for Non-Technical Users

*A comprehensive guide for maintaining and troubleshooting your school management system*

---

## Table of Contents
1. [Browser Developer Tools (F12) Debugging](#part-1-browser-developer-tools-f12-debugging)
2. [File Structure Navigation for Hostinger Subdomain Setup](#part-2-file-structure-navigation-for-hostinger-subdomain-setup)
3. [Common Modification Tasks](#part-3-common-modification-tasks)
4. [Best Practices and Safety](#part-4-best-practices-and-safety)

---

## Part 1: Browser Developer Tools (F12) Debugging

### 🔧 Opening Developer Tools

**Method 1: Keyboard Shortcut**
- **Windows/Linux:** Press `F12` or `Ctrl + Shift + I`
- **Mac:** Press `Cmd + Option + I`

**Method 2: Right-Click Menu**
1. Right-click anywhere on your webpage
2. Select "Inspect" or "Inspect Element"

**Method 3: Browser Menu**
- **Chrome:** Menu → More Tools → Developer Tools
- **Firefox:** <PERSON><PERSON> → Web Developer → Inspector

### 🐛 Identifying JavaScript Errors in Console Tab

**Step-by-Step Instructions:**

1. **Open Developer Tools** (F12)
2. **Click the "Console" tab** at the top
3. **Look for red error messages** - these indicate problems

**Common Error Types:**
```
❌ Failed to load resource: the server responded with a status of 500 ()
❌ Uncaught ReferenceError: echarts is not defined
❌ TypeError: Cannot read property 'init' of undefined
```

**What Each Error Means:**
- **500 Error:** Server can't find or load a file (like CSS/JS)
- **ReferenceError:** JavaScript library not loaded properly
- **TypeError:** Code trying to use something that doesn't exist

**Real Example from Our Pie Chart Fix:**
```
Before Fix: passdrc.css:1 Failed to load resource: the server responded with a status of 500 ()
After Fix: ✅ Charts initialized successfully
```

### 🌐 Tracing CSS Loading Issues in Network Tab

**Step-by-Step Instructions:**

1. **Open Developer Tools** (F12)
2. **Click "Network" tab**
3. **Refresh your page** (Ctrl+F5 or Cmd+Shift+R)
4. **Look for red entries** in the list

**How to Read Network Tab:**
- **Green/Black entries:** Files loaded successfully ✅
- **Red entries:** Files failed to load ❌
- **Status codes:** 200 = OK, 404 = Not Found, 500 = Server Error

**Finding CSS Problems:**
1. **Filter by "CSS"** (click CSS button in Network tab)
2. **Look for failed CSS files** (red entries)
3. **Click on failed file** to see error details

### 🎨 Inspecting HTML Elements and CSS

**Step-by-Step Instructions:**

1. **Right-click on any element** you want to inspect
2. **Select "Inspect Element"**
3. **The Elements tab opens** showing HTML structure
4. **On the right side:** See CSS styles affecting that element

**Finding Which CSS File Controls an Element:**
1. **Inspect the element** you want to modify
2. **Look at the Styles panel** on the right
3. **Each CSS rule shows the filename** (e.g., `ramom.css:1122`)
4. **Click the filename link** to open that CSS file

**Example:**
```css
/* This shows the CSS rule comes from ramom.css at line 1122 */
.quick_image .user-img-circle {
    border: 2px solid #10b981;  /* ramom.css:1122 */
}
```

### ✏️ Temporarily Modifying CSS for Testing

**Step-by-Step Instructions:**

1. **Inspect the element** you want to change
2. **In the Styles panel:** Click on any CSS value
3. **Type your new value** (e.g., change `#10b981` to `#ff0000`)
4. **Press Enter** to see the change immediately
5. **Changes are temporary** - refresh page to revert

**⚠️ Important:** These changes are only for testing! To make permanent changes, you must edit the actual CSS file.

### 🔍 Common Error Patterns to Look For

**1. File Not Found (404 Errors):**
```
GET https://yoursite.com/assets/css/missing-file.css 404 (Not Found)
```
**Solution:** Check if file exists in correct location

**2. Server Error (500 Errors):**
```
GET https://yoursite.com/assets/css/ramom.css 500 (Internal Server Error)
```
**Solution:** File exists but has syntax errors or permission issues

**3. JavaScript Library Missing:**
```
Uncaught ReferenceError: echarts is not defined
```
**Solution:** ECharts library not loaded before your chart code

**4. CSS File Reference Wrong:**
```
Failed to load resource: passdrc.css (when actual file is ramom.css)
```
**Solution:** Fix file reference in header.php

---

## Part 2: File Structure Navigation for Hostinger Subdomain Setup

### 📁 Complete File Structure Map

Your school management system is located at: `passdrc.com/public_html/school/`

```
public_html/school/                    ← Your website root
├── application/                       ← Core application files
│   ├── views/                        ← HTML templates
│   │   ├── dashboard/
│   │   │   └── index.php            ← Dashboard page (pie charts)
│   │   ├── layout/
│   │   │   ├── header.php           ← Site header (CSS links)
│   │   │   └── footer.php           ← Site footer
│   │   └── userrole/                ← User-specific pages
│   ├── models/                       ← Database logic
│   │   └── Dashboard_model.php      ← Dashboard data processing
│   └── controllers/                  ← Page logic
├── assets/                           ← Static files (CSS, JS, images)
│   ├── css/
│   │   ├── ramom.css               ← Main stylesheet (1817 lines)
│   │   └── custom-style.css        ← Additional styles
│   ├── js/                         ← JavaScript files
│   └── vendor/                     ← Third-party libraries
│       └── echarts/                ← Chart library
├── system/                          ← Framework files (don't modify)
└── index.php                       ← Main entry point
```

### 🗺️ File Type Locations

**HTML Templates (Views):**
- **Location:** `application/views/`
- **Purpose:** Control what users see
- **Key files:** `dashboard/index.php`, `layout/header.php`

**Stylesheets (CSS):**
- **Location:** `assets/css/`
- **Main file:** `ramom.css` (contains all custom styles)
- **Purpose:** Control colors, fonts, layout

**JavaScript Files:**
- **Location:** `assets/js/` and `assets/vendor/`
- **Purpose:** Interactive features, charts, animations

**Data Processing (Models):**
- **Location:** `application/models/`
- **Purpose:** Handle database queries and data formatting

### 🔗 Mapping Browser Errors to File Locations

**Error Message → File Location:**

```
Console Error: "Failed to load ramom.css"
→ Check: assets/css/ramom.css
→ Also check: application/views/layout/header.php (CSS reference)

Console Error: "echarts is not defined"
→ Check: assets/vendor/echarts/echarts.common.min.js
→ Also check: Dashboard view file loading order

Console Error: "Dashboard data undefined"
→ Check: application/models/Dashboard_model.php
→ Also check: application/views/dashboard/index.php
```

### 📝 File Naming Conventions

**Pattern Recognition:**
- **Views end with `.php`** and contain HTML + PHP code
- **CSS files end with `.css`** and contain styling rules
- **JavaScript files end with `.js`** and contain interactive code
- **Model files end with `_model.php`** and handle data

**Relationship Examples:**
```
dashboard/index.php     ← Dashboard page view
Dashboard_model.php     ← Dashboard data processing
ramom.css              ← Dashboard styling
echarts.min.js         ← Dashboard charts
```

---

## Part 3: Common Modification Tasks

### 📊 Modifying Dashboard Elements and Charts

**Real Example: Our Recent Pie Chart Fix**

**Problem:** Charts not displaying on live website
**Root Cause:** CSS file reference error in header.php
**Solution Steps:**

1. **Identified the error** using F12 Console:
   ```
   Failed to load resource: passdrc.css 500 (Internal Server Error)
   ```

2. **Located the problem file:**
   ```
   application/views/layout/header.php line 33
   ```

3. **Found the incorrect reference:**
   ```php
   <!-- Wrong -->
   <link rel="stylesheet" href="<?php echo base_url('assets/css/passdrc.css?v=' . version_combine());?>">
   
   <!-- Correct -->
   <link rel="stylesheet" href="<?php echo base_url('assets/css/ramom.css?v=' . version_combine());?>">
   ```

4. **Made the fix and uploaded** the corrected file

**Key Lesson:** Always check file references match actual file names!

### 🎨 Changing Colors Throughout the Website

**Main Color File:** `assets/css/ramom.css` (1817 lines)

**Our Color Palette:**
- **Primary Green:** `#10b981` (Emerald 500)
- **Secondary Blue:** `#3b82f6` (Blue 500)
- **Dark Background:** `#0f172a` (Slate 900)

**How to Change Colors:**

1. **Open ramom.css** in your file manager
2. **Search for color codes** (e.g., `#10b981`)
3. **Replace with your new color**
4. **Save and upload** the file
5. **Clear browser cache** and test

**Example Color Changes:**
```css
/* Before */
.box-top-line.line-color-primary {
    border-color: #10b981;
}

/* After (changing to red) */
.box-top-line.line-color-primary {
    border-color: #dc2626;
}
```

### 👤 Modifying Profile Pages and UI Elements

**Profile-Related Files:**
- **CSS:** Lines 400-712 in `ramom.css` contain profile styles
- **Views:** Look in `application/views/` for profile templates

**Common Profile Modifications:**
```css
/* Profile image borders */
.quick_image .user-img-circle {
    border: 2px solid #10b981;  /* Change this color */
}

/* Profile header background */
.profile-head::before {
    background: linear-gradient(135deg, #10b981, #3b82f6);
}
```

### 💾 Safely Backing Up Files Before Changes

**Before ANY modification:**

1. **Download the original file** from Hostinger File Manager
2. **Save it with a backup name:**
   ```
   ramom.css → ramom.css.backup.2025-01-11
   header.php → header.php.backup.2025-01-11
   ```
3. **Keep backups organized** in a local folder
4. **Make your changes** to a copy first
5. **Test thoroughly** before uploading

### 🧪 Testing Changes: Local vs. Live Website

**Testing Workflow:**

1. **Make changes locally** (on your computer)
2. **Test with browser developer tools** first
3. **Upload to a test subdirectory** if possible
4. **Only upload to live site** after thorough testing

**Safe Testing Method:**
```
public_html/school/          ← Live website
public_html/school-test/     ← Test copy for experiments
```

---

## Part 4: Best Practices and Safety

### 💾 Always Backup Files Before Modification

**Critical Files to Always Backup:**
- `ramom.css` (main stylesheet)
- `application/views/layout/header.php` (site header)
- `application/views/dashboard/index.php` (dashboard)
- Any file you're about to modify

**Backup Naming Convention:**
```
filename.extension → filename.extension.backup.YYYY-MM-DD
ramom.css → ramom.css.backup.2025-01-11
```

### 🧹 Browser Cache Clearing Effectively

**Why Clear Cache:**
- Browsers store old versions of CSS/JS files
- Changes won't show until cache is cleared
- Old cached files can cause conflicts

**How to Clear Cache:**

**Complete Cache Clear (Recommended):**
1. **Press:** `Ctrl + Shift + Delete` (Windows) or `Cmd + Shift + Delete` (Mac)
2. **Select:** "All time" or "Everything"
3. **Check:** Cached images and files, Cookies, Site data
4. **Click:** "Clear data" or "Clear"

**Quick Cache Clear:**
- **Hard Refresh:** `Ctrl + F5` (Windows) or `Cmd + Shift + R` (Mac)
- **Force Reload:** `Ctrl + Shift + R` (Windows)

### 🕵️ When to Use Incognito/Private Browsing

**Use Incognito Mode When:**
- Testing changes for the first time
- Verifying cache-related issues
- Checking if problems are user-specific
- Testing without browser extensions interfering

**How to Open Incognito:**
- **Chrome:** `Ctrl + Shift + N`
- **Firefox:** `Ctrl + Shift + P`
- **Safari:** `Cmd + Shift + N`

### 🔄 How to Revert Changes if Something Breaks

**Emergency Revert Steps:**

1. **Don't Panic!** Most issues are fixable
2. **Upload your backup file** immediately
3. **Clear browser cache** completely
4. **Test the site** to confirm it's working
5. **Identify what went wrong** before trying again

**Common "Oops" Scenarios:**
```
Problem: "Website is completely white/broken"
Solution: Upload backup of header.php or ramom.css

Problem: "Charts disappeared"
Solution: Check Console for JavaScript errors, upload backup files

Problem: "Colors look wrong everywhere"
Solution: Upload backup of ramom.css
```

### 🔧 Basic Troubleshooting Workflow

**Step-by-Step Troubleshooting:**

1. **Identify the Problem**
   - What exactly is broken?
   - When did it start happening?
   - What was changed recently?

2. **Check Browser Console (F12)**
   - Look for red error messages
   - Note the file names mentioned in errors

3. **Check Network Tab**
   - Look for failed file loads (red entries)
   - Check if CSS/JS files are loading

4. **Compare with Backups**
   - What's different from the working version?
   - When was the last time it worked?

5. **Make Targeted Fixes**
   - Fix one thing at a time
   - Test after each change
   - Keep notes of what you tried

6. **Test Thoroughly**
   - Clear cache and test
   - Try different browsers
   - Test on mobile devices

### 📋 Quick Reference Checklist

**Before Making Changes:**
- [ ] Backup the files you'll modify
- [ ] Note current working state
- [ ] Have a rollback plan

**While Making Changes:**
- [ ] Make one change at a time
- [ ] Test each change immediately
- [ ] Use browser dev tools to preview

**After Making Changes:**
- [ ] Clear browser cache
- [ ] Test in incognito mode
- [ ] Test on different devices
- [ ] Document what you changed

**If Something Breaks:**
- [ ] Don't make more changes
- [ ] Check browser console for errors
- [ ] Upload backup files if needed
- [ ] Start troubleshooting systematically

---

## 🎯 Summary

This guide covers the essential skills for maintaining your school management system:

- **Browser debugging** helps you identify problems quickly
- **File structure knowledge** helps you find the right files to modify
- **Safe modification practices** prevent breaking your website
- **Systematic troubleshooting** gets you back on track when issues arise

Remember: **Always backup, test thoroughly, and make one change at a time!**

---

## Part 5: Advanced CSS File Detection and Debugging Mastery

*Master the art of identifying exactly which CSS file to modify for any visual element*

### 🎯 CSS File Detection Process

**The Systematic 4-Step Method:**

**Step 1: Inspect and Identify Classes**
1. **Right-click element** → Inspect
2. **Note all CSS classes** in the HTML (e.g., `class="fa fa-circle text-danger"`)
3. **Write them down:** `fa`, `fa-circle`, `text-danger`

**Step 2: Check Styles Panel Priority**
1. **Look at Styles panel** (right side of F12)
2. **Find the active rule** (not crossed out)
3. **Note the filename** next to the rule (e.g., `custom-style.css:4205`)

**Step 3: Verify File Source**
1. **Click the filename link** to open the source
2. **Confirm the exact line number**
3. **Check if rule has `!important`** (highest priority)

**Step 4: Cross-Reference with File Structure**
1. **Match filename** to your known file structure
2. **Verify file location** in your hosting directory
3. **Confirm this is the file to modify**

### 🔍 Reading Developer Tools Clues

**Visual Indicators in Styles Panel:**

**✅ Active Rules (What's Actually Applied):**
```css
.text-danger {                    ← Clear, not crossed out
    color: #10b981 !important;    ← This rule is active
}
```

**❌ Overridden Rules (Ignored):**
```css
.text-danger {                    ← Crossed out with strikethrough
    color: #d2322d !important;    ← This rule is overridden
}
```

**📁 File Reference Clues:**
```css
custom-style.css:4205    ← Filename : Line number
bootstrap.css:1360       ← Different file, different line
ramom.css:1091          ← Another file reference
```

**🎯 Priority Indicators:**
- **`!important`** = Highest priority
- **Inline styles** = Second highest
- **ID selectors** = High priority
- **Class selectors** = Medium priority
- **Element selectors** = Low priority

**📊 Inheritance Clues:**
```css
Inherited from body      ← Style comes from parent element
Computed                 ← Final calculated value
```

### 📋 File Priority and Hierarchy

**CSS Loading Order in Your School Management System:**

```
1. bootstrap.css         ← Framework base styles (loaded first)
2. fontawesome.css       ← Icon styles
3. ramom.css            ← Main custom styles (1817 lines)
4. custom-style.css     ← Additional custom styles (13,197 lines)
5. Inline styles        ← Highest priority (in HTML)
```

**Priority Rules (Last One Wins):**
```css
/* bootstrap.css - loaded first */
.text-danger { color: #a94442; }

/* custom-style.css - loaded last, WINS! */
.text-danger { color: #10b981 !important; }
```

**Why custom-style.css Overrides bootstrap.css:**
1. **Loading order:** custom-style.css loads after bootstrap.css
2. **Specificity:** Same specificity, but later = winner
3. **!important:** Forces priority regardless of order

### 🗺️ Tracing from HTML to CSS

**Systematic Tracing Method:**

**Example: `<i class="fa fa-circle text-danger">`**

**Step 1: Break Down Classes**
- `fa` = Font Awesome base class
- `fa-circle` = Font Awesome circle icon
- `text-danger` = Bootstrap/custom text color class

**Step 2: Predict File Locations**
- `fa*` classes → `fontawesome.css` or `all.css`
- `text-*` classes → `bootstrap.css` or `custom-style.css`
- Custom colors → `ramom.css` or `custom-style.css`

**Step 3: Check Styles Panel**
```css
.fa, .fas {                           ← fontawesome.css:123
    font-family: "Font Awesome 6 Free";
}

.fa-circle::before {                  ← fontawesome.css:1699
    content: "\f111";
}

.text-danger {                        ← custom-style.css:4205 ✅
    color: #10b981 !important;        ← This controls the color!
}
```

**Step 4: Identify the Winner**
- **Font shape:** `fontawesome.css` (creates the circle)
- **Font color:** `custom-style.css` (makes it green) ← **MODIFY THIS FILE**

### 🌳 File Structure Decision Tree

```
Visual Element Issue
│
├── Is it a color/background?
│   ├── Custom brand colors → ramom.css or custom-style.css
│   ├── Bootstrap colors (primary, danger, etc.) → custom-style.css
│   └── Default colors → bootstrap.css (don't modify)
│
├── Is it an icon?
│   ├── Font Awesome icons (fa-*) → fontawesome.css (don't modify)
│   ├── Custom icon colors → ramom.css or custom-style.css
│   └── Icon positioning → ramom.css or custom-style.css
│
├── Is it layout/spacing?
│   ├── Bootstrap grid/spacing → bootstrap.css (don't modify)
│   ├── Custom layouts → ramom.css
│   └── Page-specific layouts → custom-style.css
│
├── Is it typography?
│   ├── Font families → ramom.css or custom-style.css
│   ├── Text colors → custom-style.css
│   └── Font sizes → bootstrap.css or custom-style.css
│
└── Is it a component?
    ├── Dashboard widgets → ramom.css
    ├── Forms/buttons → custom-style.css
    └── Navigation → ramom.css or custom-style.css
```

### 🎯 Quick Decision Guide

**Modify ramom.css when:**
- Dashboard-specific styling
- Chart colors and layouts
- Profile page elements
- Navigation styling
- Widget appearances

**Modify custom-style.css when:**
- Bootstrap class overrides (text-*, btn-*, bg-*)
- Global color scheme changes
- Form styling
- General utility classes

**Never modify:**
- `bootstrap.css` (framework file)
- `fontawesome.css` (icon library)
- Files in `/vendor/` directory

### 🔍 Common CSS Class Patterns Recognition

**Pattern Recognition Guide:**

**Bootstrap Utility Classes:**
```css
.text-*        → custom-style.css (text-primary, text-danger, etc.)
.bg-*          → custom-style.css (bg-primary, bg-success, etc.)
.btn-*         → custom-style.css (btn-primary, btn-warning, etc.)
.d-*           → bootstrap.css (d-flex, d-none, etc.) - don't modify
.m-*, .p-*     → bootstrap.css (margins, padding) - don't modify
```

**Font Awesome Classes:**
```css
.fa, .fas, .far → fontawesome.css - don't modify
.fa-*          → fontawesome.css - don't modify
Custom fa colors → ramom.css or custom-style.css
```

**Custom Application Classes:**
```css
.dashboard-*   → ramom.css
.chart-*       → ramom.css
.profile-*     → ramom.css
.widget-*      → ramom.css
.panel-*       → ramom.css or custom-style.css
```

**Framework Component Classes:**
```css
.navbar-*      → bootstrap.css (structure) + ramom.css (styling)
.modal-*       → bootstrap.css (structure) + custom-style.css (styling)
.form-*        → bootstrap.css (structure) + custom-style.css (styling)
```

### 🚀 Expert-Level Debugging Workflow

**The Professional 6-Step Process:**

**1. Element Inspection**
```
Right-click → Inspect → Note all classes and IDs
```

**2. Style Analysis**
```
Check Styles panel → Identify active rules → Note file references
```

**3. File Prediction**
```
Use class patterns → Predict likely files → Cross-reference with hierarchy
```

**4. Source Verification**
```
Click file links → Verify line numbers → Confirm rule content
```

**5. Impact Assessment**
```
Check if rule affects other elements → Test change temporarily
```

**6. Safe Modification**
```
Backup file → Make change → Upload → Test → Document
```

### 📊 Real-World Example Walkthrough

**Scenario: Change button color from blue to green**

**HTML Found:** `<button class="btn btn-primary">Save</button>`

**Step 1: Inspect Classes**
- `btn` = Bootstrap button base
- `btn-primary` = Bootstrap primary button color

**Step 2: Check Styles Panel**
```css
.btn-primary {                    ← custom-style.css:6180
    background-color: #337ab7;    ← Current blue color
    border-color: #2e6da4;
}
```

**Step 3: File Decision**
- `btn-*` pattern → custom-style.css ✅
- Bootstrap override → custom-style.css ✅
- Color change → custom-style.css ✅

**Step 4: Make Change**
```css
.btn-primary {
    background-color: #10b981;    ← Change to green
    border-color: #0d9f73;        ← Darker green for border
}
```

**Result: All primary buttons site-wide turn green!**

### 🎯 Mastery Checklist

**You've mastered CSS file detection when you can:**

- [ ] **Instantly identify** which file contains a CSS rule by looking at class names
- [ ] **Read Styles panel** like a map, understanding priority and inheritance
- [ ] **Predict file locations** based on CSS class patterns
- [ ] **Trace any visual element** from HTML to the exact CSS line
- [ ] **Understand file hierarchy** and why certain rules override others
- [ ] **Make surgical changes** without breaking other elements
- [ ] **Debug complex styling issues** using systematic methodology

### 💡 Pro Tips for Mastery

**1. Learn Your File Signatures**
- `ramom.css` = Dashboard, widgets, charts (1,817 lines)
- `custom-style.css` = Bootstrap overrides, utilities (13,197 lines)
- `bootstrap.css` = Framework base (don't modify)

**2. Use Browser Tools Effectively**
- **Computed tab** shows final calculated values
- **Changes tab** tracks your temporary modifications
- **Coverage tab** shows which CSS is actually used

**3. Pattern Recognition Shortcuts**
- `text-*` = Almost always custom-style.css
- `fa-*` = Always fontawesome.css (don't modify colors here)
- `dashboard-*` = Always ramom.css
- `btn-*` = Usually custom-style.css for colors

**4. Safety First**
- Always backup before modifying
- Test changes temporarily first
- Understand the scope of your changes
- Document what you modified

---

*Last updated: January 2025*
*Based on successful pie chart debugging and color scheme implementation*
