<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_CivicInfo_VoterInfoSegmentResult extends Google_Model
{
  public $generatedMillis;
  protected $postalAddressType = 'Google_Service_CivicInfo_PostalAddress';
  protected $postalAddressDataType = '';
  protected $requestType = 'Google_Service_CivicInfo_VoterInfoRequest';
  protected $requestDataType = '';
  protected $responseType = 'Google_Service_CivicInfo_VoterInfoResponse';
  protected $responseDataType = '';

  public function setGeneratedMillis($generatedMillis)
  {
    $this->generatedMillis = $generatedMillis;
  }
  public function getGeneratedMillis()
  {
    return $this->generatedMillis;
  }
  public function setPostalAddress(Google_Service_CivicInfo_PostalAddress $postalAddress)
  {
    $this->postalAddress = $postalAddress;
  }
  public function getPostalAddress()
  {
    return $this->postalAddress;
  }
  public function setRequest(Google_Service_CivicInfo_VoterInfoRequest $request)
  {
    $this->request = $request;
  }
  public function getRequest()
  {
    return $this->request;
  }
  public function setResponse(Google_Service_CivicInfo_VoterInfoResponse $response)
  {
    $this->response = $response;
  }
  public function getResponse()
  {
    return $this->response;
  }
}
