<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Messaging\V1\BrandRegistration;

use Twilio\Options;
use Twilio\Values;

abstract class BrandVettingOptions
{
    /**
     * @param string $vettingId The unique ID of the vetting
     * @return CreateBrandVettingOptions Options builder
     */
    public static function create(
        
        string $vettingId = Values::NONE

    ): CreateBrandVettingOptions
    {
        return new CreateBrandVettingOptions(
            $vettingId
        );
    }


    /**
     * @param string $vettingProvider The third-party provider of the vettings to read
     * @return ReadBrandVettingOptions Options builder
     */
    public static function read(
        
        string $vettingProvider = Values::NONE

    ): ReadBrandVettingOptions
    {
        return new ReadBrandVettingOptions(
            $vettingProvider
        );
    }

}

class CreateBrandVettingOptions extends Options
    {
    /**
     * @param string $vettingId The unique ID of the vetting
     */
    public function __construct(
        
        string $vettingId = Values::NONE

    ) {
        $this->options['vettingId'] = $vettingId;
    }

    /**
     * The unique ID of the vetting
     *
     * @param string $vettingId The unique ID of the vetting
     * @return $this Fluent Builder
     */
    public function setVettingId(string $vettingId): self
    {
        $this->options['vettingId'] = $vettingId;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Messaging.V1.CreateBrandVettingOptions ' . $options . ']';
    }
}


class ReadBrandVettingOptions extends Options
    {
    /**
     * @param string $vettingProvider The third-party provider of the vettings to read
     */
    public function __construct(
        
        string $vettingProvider = Values::NONE

    ) {
        $this->options['vettingProvider'] = $vettingProvider;
    }

    /**
     * The third-party provider of the vettings to read
     *
     * @param string $vettingProvider The third-party provider of the vettings to read
     * @return $this Fluent Builder
     */
    public function setVettingProvider(string $vettingProvider): self
    {
        $this->options['vettingProvider'] = $vettingProvider;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Messaging.V1.ReadBrandVettingOptions ' . $options . ']';
    }
}

