<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Tracing_TimeEvent extends Google_Model
{
  protected $annotationType = 'Google_Service_Tracing_Annotation';
  protected $annotationDataType = '';
  public $localTime;
  protected $networkEventType = 'Google_Service_Tracing_NetworkEvent';
  protected $networkEventDataType = '';

  public function setAnnotation(Google_Service_Tracing_Annotation $annotation)
  {
    $this->annotation = $annotation;
  }
  public function getAnnotation()
  {
    return $this->annotation;
  }
  public function setLocalTime($localTime)
  {
    $this->localTime = $localTime;
  }
  public function getLocalTime()
  {
    return $this->localTime;
  }
  public function setNetworkEvent(Google_Service_Tracing_NetworkEvent $networkEvent)
  {
    $this->networkEvent = $networkEvent;
  }
  public function getNetworkEvent()
  {
    return $this->networkEvent;
  }
}
