{"name": "twilio/sdk", "type": "library", "description": "A PHP wrapper for Twilio's API", "keywords": ["twi<PERSON>", "sms", "api"], "homepage": "https://github.com/twilio/twilio-php", "license": "MIT", "authors": [{"name": "Twilio API Team", "email": "<EMAIL>"}], "require": {"php": ">=7.1.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "phpunit/phpunit": ">=7.0 < 10"}, "suggest": {"guzzlehttp/guzzle": "An HTTP client to execute the API requests"}, "autoload": {"psr-4": {"Twilio\\": "src/<PERSON>wi<PERSON>/"}}, "autoload-dev": {"psr-4": {"": "src/<PERSON>wi<PERSON>/", "Twilio\\Tests\\": "tests/Twilio/"}}, "config": {"lock": false}}