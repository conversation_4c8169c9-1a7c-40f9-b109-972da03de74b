<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Preview\Sync\Service\SyncMap;

use Twilio\Options;
use Twilio\Values;

abstract class SyncMapItemOptions
{

    /**
     * @param string $ifMatch The If-Match HTTP request header
     * @return DeleteSyncMapItemOptions Options builder
     */
    public static function delete(
        
        string $ifMatch = Values::NONE

    ): DeleteSyncMapItemOptions
    {
        return new DeleteSyncMapItemOptions(
            $ifMatch
        );
    }


    /**
     * @param string $order 
     * @param string $from 
     * @param string $bounds 
     * @return ReadSyncMapItemOptions Options builder
     */
    public static function read(
        
        string $order = Values::NONE,
        string $from = Values::NONE,
        string $bounds = Values::NONE

    ): ReadSyncMapItemOptions
    {
        return new ReadSyncMapItemOptions(
            $order,
            $from,
            $bounds
        );
    }

    /**
     * @param string $ifMatch The If-Match HTTP request header
     * @return UpdateSyncMapItemOptions Options builder
     */
    public static function update(
        
        string $ifMatch = Values::NONE

    ): UpdateSyncMapItemOptions
    {
        return new UpdateSyncMapItemOptions(
            $ifMatch
        );
    }

}


class DeleteSyncMapItemOptions extends Options
    {
    /**
     * @param string $ifMatch The If-Match HTTP request header
     */
    public function __construct(
        
        string $ifMatch = Values::NONE

    ) {
        $this->options['ifMatch'] = $ifMatch;
    }

    /**
     * The If-Match HTTP request header
     *
     * @param string $ifMatch The If-Match HTTP request header
     * @return $this Fluent Builder
     */
    public function setIfMatch(string $ifMatch): self
    {
        $this->options['ifMatch'] = $ifMatch;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Sync.DeleteSyncMapItemOptions ' . $options . ']';
    }
}


class ReadSyncMapItemOptions extends Options
    {
    /**
     * @param string $order 
     * @param string $from 
     * @param string $bounds 
     */
    public function __construct(
        
        string $order = Values::NONE,
        string $from = Values::NONE,
        string $bounds = Values::NONE

    ) {
        $this->options['order'] = $order;
        $this->options['from'] = $from;
        $this->options['bounds'] = $bounds;
    }

    /**
     * 
     *
     * @param string $order 
     * @return $this Fluent Builder
     */
    public function setOrder(string $order): self
    {
        $this->options['order'] = $order;
        return $this;
    }

    /**
     * 
     *
     * @param string $from 
     * @return $this Fluent Builder
     */
    public function setFrom(string $from): self
    {
        $this->options['from'] = $from;
        return $this;
    }

    /**
     * 
     *
     * @param string $bounds 
     * @return $this Fluent Builder
     */
    public function setBounds(string $bounds): self
    {
        $this->options['bounds'] = $bounds;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Sync.ReadSyncMapItemOptions ' . $options . ']';
    }
}

class UpdateSyncMapItemOptions extends Options
    {
    /**
     * @param string $ifMatch The If-Match HTTP request header
     */
    public function __construct(
        
        string $ifMatch = Values::NONE

    ) {
        $this->options['ifMatch'] = $ifMatch;
    }

    /**
     * The If-Match HTTP request header
     *
     * @param string $ifMatch The If-Match HTTP request header
     * @return $this Fluent Builder
     */
    public function setIfMatch(string $ifMatch): self
    {
        $this->options['ifMatch'] = $ifMatch;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Sync.UpdateSyncMapItemOptions ' . $options . ']';
    }
}

