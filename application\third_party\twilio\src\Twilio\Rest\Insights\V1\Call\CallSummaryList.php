<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Insights\V1\Call;

use Twilio\ListResource;
use Twilio\Version;


class CallSummaryList extends ListResource
    {
    /**
     * Construct the CallSummaryList
     *
     * @param Version $version Version that contains the resource
     * @param string $callSid The unique SID identifier of the Call.
     */
    public function __construct(
        Version $version,
        string $callSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'callSid' =>
            $callSid,
        
        ];
    }

    /**
     * Constructs a CallSummaryContext
     */
    public function getContext(
        
    ): CallSummaryContext
    {
        return new CallSummaryContext(
            $this->version,
            $this->solution['callSid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Insights.V1.CallSummaryList]';
    }
}
