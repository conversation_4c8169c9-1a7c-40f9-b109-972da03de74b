<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Intelligence
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Intelligence\V2;

use Twilio\Options;
use Twilio\Values;

abstract class ServiceOptions
{
    /**
     * @param bool $autoTranscribe Instructs the Speech Recognition service to automatically transcribe all recordings made on the account.
     * @param bool $dataLogging Data logging allows Twilio to improve the quality of the speech recognition through using customer data to refine its speech recognition models.
     * @param string $friendlyName A human readable description of this resource, up to 64 characters.
     * @param string $languageCode The default language code of the audio.
     * @param bool $autoRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts made on this service.
     * @param bool $mediaRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts media made on this service. The auto_redaction flag must be enabled, results in error otherwise.
     * @param string $webhookUrl The URL Twilio will request when executing the Webhook.
     * @param string $webhookHttpMethod
     * @return CreateServiceOptions Options builder
     */
    public static function create(
        
        bool $autoTranscribe = Values::BOOL_NONE,
        bool $dataLogging = Values::BOOL_NONE,
        string $friendlyName = Values::NONE,
        string $languageCode = Values::NONE,
        bool $autoRedaction = Values::BOOL_NONE,
        bool $mediaRedaction = Values::BOOL_NONE,
        string $webhookUrl = Values::NONE,
        string $webhookHttpMethod = Values::NONE

    ): CreateServiceOptions
    {
        return new CreateServiceOptions(
            $autoTranscribe,
            $dataLogging,
            $friendlyName,
            $languageCode,
            $autoRedaction,
            $mediaRedaction,
            $webhookUrl,
            $webhookHttpMethod
        );
    }




    /**
     * @param bool $autoTranscribe Instructs the Speech Recognition service to automatically transcribe all recordings made on the account.
     * @param bool $dataLogging Data logging allows Twilio to improve the quality of the speech recognition through using customer data to refine its speech recognition models.
     * @param string $friendlyName A human readable description of this resource, up to 64 characters.
     * @param string $languageCode The default language code of the audio.
     * @param string $uniqueName Provides a unique and addressable name to be assigned to this Service, assigned by the developer, to be optionally used in addition to SID.
     * @param bool $autoRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts made on this service.
     * @param bool $mediaRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts media made on this service. The auto_redaction flag must be enabled, results in error otherwise.
     * @param string $webhookUrl The URL Twilio will request when executing the Webhook.
     * @param string $webhookHttpMethod
     * @param string $ifMatch The If-Match HTTP request header
     * @return UpdateServiceOptions Options builder
     */
    public static function update(
        
        bool $autoTranscribe = Values::BOOL_NONE,
        bool $dataLogging = Values::BOOL_NONE,
        string $friendlyName = Values::NONE,
        string $languageCode = Values::NONE,
        string $uniqueName = Values::NONE,
        bool $autoRedaction = Values::BOOL_NONE,
        bool $mediaRedaction = Values::BOOL_NONE,
        string $webhookUrl = Values::NONE,
        string $webhookHttpMethod = Values::NONE,
        string $ifMatch = Values::NONE

    ): UpdateServiceOptions
    {
        return new UpdateServiceOptions(
            $autoTranscribe,
            $dataLogging,
            $friendlyName,
            $languageCode,
            $uniqueName,
            $autoRedaction,
            $mediaRedaction,
            $webhookUrl,
            $webhookHttpMethod,
            $ifMatch
        );
    }

}

class CreateServiceOptions extends Options
    {
    /**
     * @param bool $autoTranscribe Instructs the Speech Recognition service to automatically transcribe all recordings made on the account.
     * @param bool $dataLogging Data logging allows Twilio to improve the quality of the speech recognition through using customer data to refine its speech recognition models.
     * @param string $friendlyName A human readable description of this resource, up to 64 characters.
     * @param string $languageCode The default language code of the audio.
     * @param bool $autoRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts made on this service.
     * @param bool $mediaRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts media made on this service. The auto_redaction flag must be enabled, results in error otherwise.
     * @param string $webhookUrl The URL Twilio will request when executing the Webhook.
     * @param string $webhookHttpMethod
     */
    public function __construct(
        
        bool $autoTranscribe = Values::BOOL_NONE,
        bool $dataLogging = Values::BOOL_NONE,
        string $friendlyName = Values::NONE,
        string $languageCode = Values::NONE,
        bool $autoRedaction = Values::BOOL_NONE,
        bool $mediaRedaction = Values::BOOL_NONE,
        string $webhookUrl = Values::NONE,
        string $webhookHttpMethod = Values::NONE

    ) {
        $this->options['autoTranscribe'] = $autoTranscribe;
        $this->options['dataLogging'] = $dataLogging;
        $this->options['friendlyName'] = $friendlyName;
        $this->options['languageCode'] = $languageCode;
        $this->options['autoRedaction'] = $autoRedaction;
        $this->options['mediaRedaction'] = $mediaRedaction;
        $this->options['webhookUrl'] = $webhookUrl;
        $this->options['webhookHttpMethod'] = $webhookHttpMethod;
    }

    /**
     * Instructs the Speech Recognition service to automatically transcribe all recordings made on the account.
     *
     * @param bool $autoTranscribe Instructs the Speech Recognition service to automatically transcribe all recordings made on the account.
     * @return $this Fluent Builder
     */
    public function setAutoTranscribe(bool $autoTranscribe): self
    {
        $this->options['autoTranscribe'] = $autoTranscribe;
        return $this;
    }

    /**
     * Data logging allows Twilio to improve the quality of the speech recognition through using customer data to refine its speech recognition models.
     *
     * @param bool $dataLogging Data logging allows Twilio to improve the quality of the speech recognition through using customer data to refine its speech recognition models.
     * @return $this Fluent Builder
     */
    public function setDataLogging(bool $dataLogging): self
    {
        $this->options['dataLogging'] = $dataLogging;
        return $this;
    }

    /**
     * A human readable description of this resource, up to 64 characters.
     *
     * @param string $friendlyName A human readable description of this resource, up to 64 characters.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The default language code of the audio.
     *
     * @param string $languageCode The default language code of the audio.
     * @return $this Fluent Builder
     */
    public function setLanguageCode(string $languageCode): self
    {
        $this->options['languageCode'] = $languageCode;
        return $this;
    }

    /**
     * Instructs the Speech Recognition service to automatically redact PII from all transcripts made on this service.
     *
     * @param bool $autoRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts made on this service.
     * @return $this Fluent Builder
     */
    public function setAutoRedaction(bool $autoRedaction): self
    {
        $this->options['autoRedaction'] = $autoRedaction;
        return $this;
    }

    /**
     * Instructs the Speech Recognition service to automatically redact PII from all transcripts media made on this service. The auto_redaction flag must be enabled, results in error otherwise.
     *
     * @param bool $mediaRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts media made on this service. The auto_redaction flag must be enabled, results in error otherwise.
     * @return $this Fluent Builder
     */
    public function setMediaRedaction(bool $mediaRedaction): self
    {
        $this->options['mediaRedaction'] = $mediaRedaction;
        return $this;
    }

    /**
     * The URL Twilio will request when executing the Webhook.
     *
     * @param string $webhookUrl The URL Twilio will request when executing the Webhook.
     * @return $this Fluent Builder
     */
    public function setWebhookUrl(string $webhookUrl): self
    {
        $this->options['webhookUrl'] = $webhookUrl;
        return $this;
    }

    /**
     * @param string $webhookHttpMethod
     * @return $this Fluent Builder
     */
    public function setWebhookHttpMethod(string $webhookHttpMethod): self
    {
        $this->options['webhookHttpMethod'] = $webhookHttpMethod;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Intelligence.V2.CreateServiceOptions ' . $options . ']';
    }
}




class UpdateServiceOptions extends Options
    {
    /**
     * @param bool $autoTranscribe Instructs the Speech Recognition service to automatically transcribe all recordings made on the account.
     * @param bool $dataLogging Data logging allows Twilio to improve the quality of the speech recognition through using customer data to refine its speech recognition models.
     * @param string $friendlyName A human readable description of this resource, up to 64 characters.
     * @param string $languageCode The default language code of the audio.
     * @param string $uniqueName Provides a unique and addressable name to be assigned to this Service, assigned by the developer, to be optionally used in addition to SID.
     * @param bool $autoRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts made on this service.
     * @param bool $mediaRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts media made on this service. The auto_redaction flag must be enabled, results in error otherwise.
     * @param string $webhookUrl The URL Twilio will request when executing the Webhook.
     * @param string $webhookHttpMethod
     * @param string $ifMatch The If-Match HTTP request header
     */
    public function __construct(
        
        bool $autoTranscribe = Values::BOOL_NONE,
        bool $dataLogging = Values::BOOL_NONE,
        string $friendlyName = Values::NONE,
        string $languageCode = Values::NONE,
        string $uniqueName = Values::NONE,
        bool $autoRedaction = Values::BOOL_NONE,
        bool $mediaRedaction = Values::BOOL_NONE,
        string $webhookUrl = Values::NONE,
        string $webhookHttpMethod = Values::NONE,
        string $ifMatch = Values::NONE

    ) {
        $this->options['autoTranscribe'] = $autoTranscribe;
        $this->options['dataLogging'] = $dataLogging;
        $this->options['friendlyName'] = $friendlyName;
        $this->options['languageCode'] = $languageCode;
        $this->options['uniqueName'] = $uniqueName;
        $this->options['autoRedaction'] = $autoRedaction;
        $this->options['mediaRedaction'] = $mediaRedaction;
        $this->options['webhookUrl'] = $webhookUrl;
        $this->options['webhookHttpMethod'] = $webhookHttpMethod;
        $this->options['ifMatch'] = $ifMatch;
    }

    /**
     * Instructs the Speech Recognition service to automatically transcribe all recordings made on the account.
     *
     * @param bool $autoTranscribe Instructs the Speech Recognition service to automatically transcribe all recordings made on the account.
     * @return $this Fluent Builder
     */
    public function setAutoTranscribe(bool $autoTranscribe): self
    {
        $this->options['autoTranscribe'] = $autoTranscribe;
        return $this;
    }

    /**
     * Data logging allows Twilio to improve the quality of the speech recognition through using customer data to refine its speech recognition models.
     *
     * @param bool $dataLogging Data logging allows Twilio to improve the quality of the speech recognition through using customer data to refine its speech recognition models.
     * @return $this Fluent Builder
     */
    public function setDataLogging(bool $dataLogging): self
    {
        $this->options['dataLogging'] = $dataLogging;
        return $this;
    }

    /**
     * A human readable description of this resource, up to 64 characters.
     *
     * @param string $friendlyName A human readable description of this resource, up to 64 characters.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The default language code of the audio.
     *
     * @param string $languageCode The default language code of the audio.
     * @return $this Fluent Builder
     */
    public function setLanguageCode(string $languageCode): self
    {
        $this->options['languageCode'] = $languageCode;
        return $this;
    }

    /**
     * Provides a unique and addressable name to be assigned to this Service, assigned by the developer, to be optionally used in addition to SID.
     *
     * @param string $uniqueName Provides a unique and addressable name to be assigned to this Service, assigned by the developer, to be optionally used in addition to SID.
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * Instructs the Speech Recognition service to automatically redact PII from all transcripts made on this service.
     *
     * @param bool $autoRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts made on this service.
     * @return $this Fluent Builder
     */
    public function setAutoRedaction(bool $autoRedaction): self
    {
        $this->options['autoRedaction'] = $autoRedaction;
        return $this;
    }

    /**
     * Instructs the Speech Recognition service to automatically redact PII from all transcripts media made on this service. The auto_redaction flag must be enabled, results in error otherwise.
     *
     * @param bool $mediaRedaction Instructs the Speech Recognition service to automatically redact PII from all transcripts media made on this service. The auto_redaction flag must be enabled, results in error otherwise.
     * @return $this Fluent Builder
     */
    public function setMediaRedaction(bool $mediaRedaction): self
    {
        $this->options['mediaRedaction'] = $mediaRedaction;
        return $this;
    }

    /**
     * The URL Twilio will request when executing the Webhook.
     *
     * @param string $webhookUrl The URL Twilio will request when executing the Webhook.
     * @return $this Fluent Builder
     */
    public function setWebhookUrl(string $webhookUrl): self
    {
        $this->options['webhookUrl'] = $webhookUrl;
        return $this;
    }

    /**
     * @param string $webhookHttpMethod
     * @return $this Fluent Builder
     */
    public function setWebhookHttpMethod(string $webhookHttpMethod): self
    {
        $this->options['webhookHttpMethod'] = $webhookHttpMethod;
        return $this;
    }

    /**
     * The If-Match HTTP request header
     *
     * @param string $ifMatch The If-Match HTTP request header
     * @return $this Fluent Builder
     */
    public function setIfMatch(string $ifMatch): self
    {
        $this->options['ifMatch'] = $ifMatch;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Intelligence.V2.UpdateServiceOptions ' . $options . ']';
    }
}

