<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Supersim
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Supersim\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $sid
 * @property string|null $accountSid
 * @property string|null $iccid
 * @property string|null $simSid
 * @property string $status
 * @property string|null $eid
 * @property string|null $smdpPlusAddress
 * @property string|null $matchingId
 * @property string|null $activationCode
 * @property string|null $errorCode
 * @property string|null $errorMessage
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property string|null $url
 */
class EsimProfileInstance extends InstanceResource
{
    /**
     * Initialize the EsimProfileInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $sid The SID of the eSIM Profile resource to fetch.
     */
    public function __construct(Version $version, array $payload, string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'iccid' => Values::array_get($payload, 'iccid'),
            'simSid' => Values::array_get($payload, 'sim_sid'),
            'status' => Values::array_get($payload, 'status'),
            'eid' => Values::array_get($payload, 'eid'),
            'smdpPlusAddress' => Values::array_get($payload, 'smdp_plus_address'),
            'matchingId' => Values::array_get($payload, 'matching_id'),
            'activationCode' => Values::array_get($payload, 'activation_code'),
            'errorCode' => Values::array_get($payload, 'error_code'),
            'errorMessage' => Values::array_get($payload, 'error_message'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return EsimProfileContext Context for this EsimProfileInstance
     */
    protected function proxy(): EsimProfileContext
    {
        if (!$this->context) {
            $this->context = new EsimProfileContext(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the EsimProfileInstance
     *
     * @return EsimProfileInstance Fetched EsimProfileInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): EsimProfileInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Supersim.V1.EsimProfileInstance ' . \implode(' ', $context) . ']';
    }
}

