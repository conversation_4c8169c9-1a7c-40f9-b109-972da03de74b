{"version": 3, "sources": ["../../../js/i18n/defaults-th_TH.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,gBAAgB,CAAC,CAAC,qBAAqB,CAAC;AAC5C,IAAI,eAAe,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,CAAC;AAClD,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC;AAC7C,IAAI,cAAc,CAAC,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AACvG,IAAI,aAAa,CAAC,CAAC,eAAe,CAAC;AACnC,IAAI,eAAe,CAAC,CAAC,kBAAkB,CAAC;AACxC,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,EAAE,EAAE,CAAC;AACL,GAAG,MAAM,EAAE,CAAC", "file": "defaults-th_TH.js", "sourcesContent": ["(function ($) {\r\n  $.fn.selectpicker.defaults = {\r\n    noneSelectedText: 'ไม่ได้เลือกอะไรเลย',\r\n    noneResultsText: 'ไม่มีผลลัพธ์ที่ตรงกัน {0}',\r\n    countSelectedText: '{0} รายการที่เลือก',\r\n    maxOptionsText: ['เกินจำนวนที่กำหนด (สูงสุด {n} รายการ)', 'เกินจำนวนที่กำหนด (สูงสุด {n} กลุ่ม)'],\r\n    selectAllText: 'เลือกทั้งหมด',\r\n    deselectAllText: 'ไม่เลือกทั้งหมด',\r\n    multipleSeparator: ', '\r\n  };\r\n})(jQuery);\r\n"]}