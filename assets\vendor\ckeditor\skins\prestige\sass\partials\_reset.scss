/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/license
*/

/*
reset.css (part of editor.css)
================================
This file holds the "reset" requirements of CKEditor, as well as define the
default interface styles.
CKEditor includes two main "reset" class names in the DOM structure created for
editors:
	* .cke_reset: Intended to reset a specific element, but not its children.
	  Because of this, only styles that will not be inherited can be defined.
	* .cke_reset_all: Intended to reset not only the element holding it, but
	   also its child elements.
To understand why "reset" is needed, check the CKEditor Skin SDK:
https://docs.ckeditor.com/ckeditor4/docs/#!/guide/skin_sdk_reset
*/

/* Reset for single elements, not their children. */
.cke_reset
{
	/* Do not include inheritable rules here. */
	margin: 0;
	padding: 0;
	border: 0;
	background: transparent;
	text-decoration: none;
	width: auto;
	height: auto;
	vertical-align: baseline;
	box-sizing: content-box;
	position: static;
	transition: none;
}

/* Reset for elements and their children. */
.cke_reset_all, .cke_reset_all *,
.cke_reset_all a, .cke_reset_all textarea
{
	/* The following must be identical to .cke_reset. */
	margin: 0;
	padding: 0;
	border: 0;
	background: transparent;
	text-decoration: none;
	width: auto;
	height: auto;
	vertical-align: baseline;
	box-sizing: content-box;
	position: static;
	transition: none;

	/* These are rule inherited by all children elements. */
	border-collapse: collapse;
	font: normal normal normal 12px Arial,Helvetica,Tahoma,Verdana,Sans-Serif;
	color: #000;
	text-align: left;
	white-space: nowrap;
	cursor: auto;
	float: none;
}

.cke_reset_all .cke_rtl *
{
	text-align: right;
}

/* Defaults for some elements. */

.cke_reset_all iframe
{
	vertical-align: inherit;	/** For IE */
}

.cke_reset_all textarea
{
	white-space: pre-wrap;
}

.cke_reset_all textarea,
.cke_reset_all input[type="text"],
.cke_reset_all input[type="password"]
{
	cursor: text;
}

.cke_reset_all textarea[disabled],
.cke_reset_all input[type="text"][disabled],
.cke_reset_all input[type="password"][disabled]
{
	cursor: default;
}

.cke_reset_all fieldset
{
	padding: 10px;
	border: 2px groove #E0DFE3;
}

.cke_reset_all select
{
	box-sizing: border-box;
}

.cke_reset_all table
{
	table-layout: auto;
}