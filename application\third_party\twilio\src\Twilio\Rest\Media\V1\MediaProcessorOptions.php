<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Media
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Media\V1;

use Twilio\Options;
use Twilio\Values;

abstract class MediaProcessorOptions
{
    /**
     * @param array $extensionEnvironment User-defined environment variables for the Media Extension, represented as a JSON dictionary of key/value strings. See the documentation for the specific [Media Extension](/docs/live/api/media-extensions-overview) you are using for more information about whether you need to provide this.
     * @param string $statusCallback The URL to which <PERSON><PERSON><PERSON> will send asynchronous webhook requests for every MediaProcessor event. See [Status Callbacks](/docs/live/status-callbacks) for details.
     * @param string $statusCallbackMethod The HTTP method Twilio should use to call the `status_callback` URL. Can be `POST` or `GET` and the default is `POST`.
     * @param int $maxDuration The maximum time, in seconds, that the MediaProcessor can run before automatically ends. The default value is 300 seconds, and the maximum value is 90000 seconds. Once this maximum duration is reached, Twilio will end the MediaProcessor, regardless of whether media is still streaming.
     * @return CreateMediaProcessorOptions Options builder
     */
    public static function create(
        
        array $extensionEnvironment = Values::ARRAY_NONE,
        string $statusCallback = Values::NONE,
        string $statusCallbackMethod = Values::NONE,
        int $maxDuration = Values::INT_NONE

    ): CreateMediaProcessorOptions
    {
        return new CreateMediaProcessorOptions(
            $extensionEnvironment,
            $statusCallback,
            $statusCallbackMethod,
            $maxDuration
        );
    }


    /**
     * @param string $order The sort order of the list by `date_created`. Can be: `asc` (ascending) or `desc` (descending) with `desc` as the default.
     * @param string $status Status to filter by, with possible values `started`, `ended` or `failed`.
     * @return ReadMediaProcessorOptions Options builder
     */
    public static function read(
        
        string $order = Values::NONE,
        string $status = Values::NONE

    ): ReadMediaProcessorOptions
    {
        return new ReadMediaProcessorOptions(
            $order,
            $status
        );
    }


}

class CreateMediaProcessorOptions extends Options
    {
    /**
     * @param array $extensionEnvironment User-defined environment variables for the Media Extension, represented as a JSON dictionary of key/value strings. See the documentation for the specific [Media Extension](/docs/live/api/media-extensions-overview) you are using for more information about whether you need to provide this.
     * @param string $statusCallback The URL to which Twilio will send asynchronous webhook requests for every MediaProcessor event. See [Status Callbacks](/docs/live/status-callbacks) for details.
     * @param string $statusCallbackMethod The HTTP method Twilio should use to call the `status_callback` URL. Can be `POST` or `GET` and the default is `POST`.
     * @param int $maxDuration The maximum time, in seconds, that the MediaProcessor can run before automatically ends. The default value is 300 seconds, and the maximum value is 90000 seconds. Once this maximum duration is reached, Twilio will end the MediaProcessor, regardless of whether media is still streaming.
     */
    public function __construct(
        
        array $extensionEnvironment = Values::ARRAY_NONE,
        string $statusCallback = Values::NONE,
        string $statusCallbackMethod = Values::NONE,
        int $maxDuration = Values::INT_NONE

    ) {
        $this->options['extensionEnvironment'] = $extensionEnvironment;
        $this->options['statusCallback'] = $statusCallback;
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        $this->options['maxDuration'] = $maxDuration;
    }

    /**
     * User-defined environment variables for the Media Extension, represented as a JSON dictionary of key/value strings. See the documentation for the specific [Media Extension](/docs/live/api/media-extensions-overview) you are using for more information about whether you need to provide this.
     *
     * @param array $extensionEnvironment User-defined environment variables for the Media Extension, represented as a JSON dictionary of key/value strings. See the documentation for the specific [Media Extension](/docs/live/api/media-extensions-overview) you are using for more information about whether you need to provide this.
     * @return $this Fluent Builder
     */
    public function setExtensionEnvironment(array $extensionEnvironment): self
    {
        $this->options['extensionEnvironment'] = $extensionEnvironment;
        return $this;
    }

    /**
     * The URL to which Twilio will send asynchronous webhook requests for every MediaProcessor event. See [Status Callbacks](/docs/live/status-callbacks) for details.
     *
     * @param string $statusCallback The URL to which Twilio will send asynchronous webhook requests for every MediaProcessor event. See [Status Callbacks](/docs/live/status-callbacks) for details.
     * @return $this Fluent Builder
     */
    public function setStatusCallback(string $statusCallback): self
    {
        $this->options['statusCallback'] = $statusCallback;
        return $this;
    }

    /**
     * The HTTP method Twilio should use to call the `status_callback` URL. Can be `POST` or `GET` and the default is `POST`.
     *
     * @param string $statusCallbackMethod The HTTP method Twilio should use to call the `status_callback` URL. Can be `POST` or `GET` and the default is `POST`.
     * @return $this Fluent Builder
     */
    public function setStatusCallbackMethod(string $statusCallbackMethod): self
    {
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        return $this;
    }

    /**
     * The maximum time, in seconds, that the MediaProcessor can run before automatically ends. The default value is 300 seconds, and the maximum value is 90000 seconds. Once this maximum duration is reached, Twilio will end the MediaProcessor, regardless of whether media is still streaming.
     *
     * @param int $maxDuration The maximum time, in seconds, that the MediaProcessor can run before automatically ends. The default value is 300 seconds, and the maximum value is 90000 seconds. Once this maximum duration is reached, Twilio will end the MediaProcessor, regardless of whether media is still streaming.
     * @return $this Fluent Builder
     */
    public function setMaxDuration(int $maxDuration): self
    {
        $this->options['maxDuration'] = $maxDuration;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Media.V1.CreateMediaProcessorOptions ' . $options . ']';
    }
}


class ReadMediaProcessorOptions extends Options
    {
    /**
     * @param string $order The sort order of the list by `date_created`. Can be: `asc` (ascending) or `desc` (descending) with `desc` as the default.
     * @param string $status Status to filter by, with possible values `started`, `ended` or `failed`.
     */
    public function __construct(
        
        string $order = Values::NONE,
        string $status = Values::NONE

    ) {
        $this->options['order'] = $order;
        $this->options['status'] = $status;
    }

    /**
     * The sort order of the list by `date_created`. Can be: `asc` (ascending) or `desc` (descending) with `desc` as the default.
     *
     * @param string $order The sort order of the list by `date_created`. Can be: `asc` (ascending) or `desc` (descending) with `desc` as the default.
     * @return $this Fluent Builder
     */
    public function setOrder(string $order): self
    {
        $this->options['order'] = $order;
        return $this;
    }

    /**
     * Status to filter by, with possible values `started`, `ended` or `failed`.
     *
     * @param string $status Status to filter by, with possible values `started`, `ended` or `failed`.
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Media.V1.ReadMediaProcessorOptions ' . $options . ']';
    }
}


