<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Video\V1\Room;

use Twilio\Options;
use Twilio\Values;

abstract class ParticipantOptions
{

    /**
     * @param string $status Read only the participants with this status. Can be: `connected` or `disconnected`. For `in-progress` Rooms the default Status is `connected`, for `completed` Rooms only `disconnected` Participants are returned.
     * @param string $identity Read only the Participants with this [User](https://www.twilio.com/docs/chat/rest/user-resource) `identity` value.
     * @param \DateTime $dateCreatedAfter Read only Participants that started after this date in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#UTC) format.
     * @param \DateTime $dateCreatedBefore Read only Participants that started before this date in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#UTC) format.
     * @return ReadParticipantOptions Options builder
     */
    public static function read(
        
        string $status = Values::NONE,
        string $identity = Values::NONE,
        \DateTime $dateCreatedAfter = null,
        \DateTime $dateCreatedBefore = null

    ): ReadParticipantOptions
    {
        return new ReadParticipantOptions(
            $status,
            $identity,
            $dateCreatedAfter,
            $dateCreatedBefore
        );
    }

    /**
     * @param string $status
     * @return UpdateParticipantOptions Options builder
     */
    public static function update(
        
        string $status = Values::NONE

    ): UpdateParticipantOptions
    {
        return new UpdateParticipantOptions(
            $status
        );
    }

}


class ReadParticipantOptions extends Options
    {
    /**
     * @param string $status Read only the participants with this status. Can be: `connected` or `disconnected`. For `in-progress` Rooms the default Status is `connected`, for `completed` Rooms only `disconnected` Participants are returned.
     * @param string $identity Read only the Participants with this [User](https://www.twilio.com/docs/chat/rest/user-resource) `identity` value.
     * @param \DateTime $dateCreatedAfter Read only Participants that started after this date in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#UTC) format.
     * @param \DateTime $dateCreatedBefore Read only Participants that started before this date in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#UTC) format.
     */
    public function __construct(
        
        string $status = Values::NONE,
        string $identity = Values::NONE,
        \DateTime $dateCreatedAfter = null,
        \DateTime $dateCreatedBefore = null

    ) {
        $this->options['status'] = $status;
        $this->options['identity'] = $identity;
        $this->options['dateCreatedAfter'] = $dateCreatedAfter;
        $this->options['dateCreatedBefore'] = $dateCreatedBefore;
    }

    /**
     * Read only the participants with this status. Can be: `connected` or `disconnected`. For `in-progress` Rooms the default Status is `connected`, for `completed` Rooms only `disconnected` Participants are returned.
     *
     * @param string $status Read only the participants with this status. Can be: `connected` or `disconnected`. For `in-progress` Rooms the default Status is `connected`, for `completed` Rooms only `disconnected` Participants are returned.
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * Read only the Participants with this [User](https://www.twilio.com/docs/chat/rest/user-resource) `identity` value.
     *
     * @param string $identity Read only the Participants with this [User](https://www.twilio.com/docs/chat/rest/user-resource) `identity` value.
     * @return $this Fluent Builder
     */
    public function setIdentity(string $identity): self
    {
        $this->options['identity'] = $identity;
        return $this;
    }

    /**
     * Read only Participants that started after this date in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#UTC) format.
     *
     * @param \DateTime $dateCreatedAfter Read only Participants that started after this date in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#UTC) format.
     * @return $this Fluent Builder
     */
    public function setDateCreatedAfter(\DateTime $dateCreatedAfter): self
    {
        $this->options['dateCreatedAfter'] = $dateCreatedAfter;
        return $this;
    }

    /**
     * Read only Participants that started before this date in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#UTC) format.
     *
     * @param \DateTime $dateCreatedBefore Read only Participants that started before this date in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#UTC) format.
     * @return $this Fluent Builder
     */
    public function setDateCreatedBefore(\DateTime $dateCreatedBefore): self
    {
        $this->options['dateCreatedBefore'] = $dateCreatedBefore;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Video.V1.ReadParticipantOptions ' . $options . ']';
    }
}

class UpdateParticipantOptions extends Options
    {
    /**
     * @param string $status
     */
    public function __construct(
        
        string $status = Values::NONE

    ) {
        $this->options['status'] = $status;
    }

    /**
     * @param string $status
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Video.V1.UpdateParticipantOptions ' . $options . ']';
    }
}

