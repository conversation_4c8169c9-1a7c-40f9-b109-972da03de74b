<?php
/**
 * Admin Credentials Checker - Bypass CodeIgniter Security
 * Upload this to: /domains/passdrc.com/public_html/school/
 * Access via: https://school.passdrc.com/admin_check.php
 */

// Bypass CodeIgniter's security check
define('BASEPATH', 'system/');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Admin Login Credentials Finder</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";

try {
    // Direct database connection (bypass CodeIgniter)
    $hostname = 'localhost';
    $username = 'u467814674_schooladmin';
    $password = 'n*qy@1=Tg';
    $database = 'u467814674_schooldatabase';
    
    echo "<h2>📊 Database Connection</h2>";
    echo "<p>✅ <strong>Connecting to:</strong> $database</p>";
    
    // Connect to database
    $connection = new mysqli($hostname, $username, $password, $database);
    
    if ($connection->connect_error) {
        die("<p>❌ <strong>Connection failed:</strong> " . $connection->connect_error . "</p>");
    }
    
    echo "<p>✅ <strong>Database connected successfully!</strong></p>";
    
    echo "<h2>👤 Admin Login Credentials</h2>";
    
    // Check if login table exists
    $result = $connection->query("SHOW TABLES LIKE 'login'");
    if ($result->num_rows == 0) {
        echo "<p>❌ <strong>Login table not found!</strong></p>";
        
        // Check what tables exist
        echo "<h3>📋 Available Tables:</h3>";
        $tables = $connection->query("SHOW TABLES");
        if ($tables) {
            echo "<ul>";
            while ($table = $tables->fetch_array()) {
                echo "<li>" . $table[0] . "</li>";
            }
            echo "</ul>";
        }
        
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffc107; border-radius: 5px;'>";
        echo "<h3>🔧 System Not Installed</h3>";
        echo "<p>The login table doesn't exist, which means the system hasn't been installed yet.</p>";
        echo "<p><strong>You need to run the installation process first:</strong></p>";
        echo "<ol>";
        echo "<li>Go to: <a href='index.php/install'>https://school.passdrc.com/index.php/install</a></li>";
        echo "<li>Follow the installation wizard</li>";
        echo "<li>Create your admin account during installation</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<p>✅ <strong>Login table found</strong></p>";
        
        // Get admin users (role = 1 is typically admin/superadmin)
        $query = "SELECT l.id, l.username, l.role, l.active, s.name, s.email 
                  FROM login l 
                  LEFT JOIN staff s ON l.user_id = s.id 
                  WHERE l.role = 1 
                  ORDER BY l.id ASC";
        
        $result = $connection->query($query);
        
        if ($result && $result->num_rows > 0) {
            echo "<div style='background: #e8f5e8; padding: 15px; margin: 20px 0; border: 1px solid #4CAF50; border-radius: 5px;'>";
            echo "<h3>🎯 Found Admin Accounts:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 10px;'>ID</th>";
            echo "<th style='padding: 10px;'>Username</th>";
            echo "<th style='padding: 10px;'>Name</th>";
            echo "<th style='padding: 10px;'>Email</th>";
            echo "<th style='padding: 10px;'>Status</th>";
            echo "</tr>";
            
            while ($row = $result->fetch_assoc()) {
                $status = $row['active'] == 1 ? '✅ Active' : '❌ Inactive';
                
                echo "<tr>";
                echo "<td style='padding: 10px; text-align: center;'>" . $row['id'] . "</td>";
                echo "<td style='padding: 10px;'><strong>" . htmlspecialchars($row['username']) . "</strong></td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($row['name'] ?? 'N/A') . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($row['email'] ?? 'N/A') . "</td>";
                echo "<td style='padding: 10px;'>" . $status . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
            
            echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border: 1px solid #28a745; border-radius: 5px;'>";
            echo "<h3>🔑 How to Login:</h3>";
            echo "<ol>";
            echo "<li><strong>Go to:</strong> <a href='index.php' target='_blank'>https://school.passdrc.com/</a></li>";
            echo "<li><strong>Use the username</strong> shown in the table above</li>";
            echo "<li><strong>Password:</strong> The password you set during installation (you need to remember this)</li>";
            echo "</ol>";
            
            echo "<h4>🤔 Forgot Your Password?</h4>";
            echo "<p>If you forgot your password, I can help you reset it. The password is hashed in the database for security.</p>";
            echo "</div>";
            
        } else {
            echo "<p>❌ <strong>No admin accounts found in login table!</strong></p>";
            
            // Check if there are any users at all
            $allUsers = $connection->query("SELECT COUNT(*) as count FROM login");
            if ($allUsers) {
                $count = $allUsers->fetch_assoc()['count'];
                echo "<p>📊 <strong>Total users in login table:</strong> $count</p>";
                
                if ($count == 0) {
                    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffc107; border-radius: 5px;'>";
                    echo "<h3>🔧 Empty Login Table</h3>";
                    echo "<p>The login table exists but is empty. This suggests:</p>";
                    echo "<ol>";
                    echo "<li>Installation was not completed properly</li>";
                    echo "<li>You need to run the installation process</li>";
                    echo "</ol>";
                    echo "<p><strong>Try accessing:</strong> <a href='index.php/install'>https://school.passdrc.com/index.php/install</a></p>";
                    echo "</div>";
                }
            }
        }
    }
    
    // Check installation status
    echo "<h2>⚙️ Installation Status</h2>";
    $result = $connection->query("SHOW TABLES LIKE 'global_settings'");
    if ($result->num_rows > 0) {
        $settings = $connection->query("SELECT * FROM global_settings LIMIT 1");
        if ($settings && $settings->num_rows > 0) {
            $setting = $settings->fetch_assoc();
            echo "<p>✅ <strong>System appears to be installed</strong></p>";
            echo "<p><strong>School Name:</strong> " . htmlspecialchars($setting['school_name'] ?? 'Not set') . "</p>";
        } else {
            echo "<p>⚠️ <strong>Global settings table exists but is empty</strong></p>";
        }
    } else {
        echo "<p>❌ <strong>Global settings table not found</strong></p>";
        echo "<p>🔧 <strong>System needs installation</strong></p>";
    }
    
    $connection->close();
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>📋 Troubleshooting</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;'>";
echo "<h3>If you can't find your credentials:</h3>";
echo "<ol>";
echo "<li><strong>Try default credentials:</strong> admin / admin123</li>";
echo "<li><strong>Run installation:</strong> <a href='index.php/install'>https://school.passdrc.com/index.php/install</a></li>";
echo "<li><strong>Reset password:</strong> I can help you reset it in the database</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🚀 Next Steps</h2>";
echo "<ul>";
echo "<li>If admin accounts were found above, use those credentials to login</li>";
echo "<li>If no accounts found, run the installation process</li>";
echo "<li>If you need password reset, let me know and I'll help</li>";
echo "</ul>";
?>
