<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Supersim
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Supersim\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class FleetContext extends InstanceContext
    {
    /**
     * Initialize the FleetContext
     *
     * @param Version $version Version that contains the resource
     * @param string $sid The SID of the Fleet resource to fetch.
     */
    public function __construct(
        Version $version,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'sid' =>
            $sid,
        ];

        $this->uri = '/Fleets/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Fetch the FleetInstance
     *
     * @return FleetInstance Fetched FleetInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): FleetInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new FleetInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Update the FleetInstance
     *
     * @param array|Options $options Optional Arguments
     * @return FleetInstance Updated FleetInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): FleetInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'UniqueName' =>
                $options['uniqueName'],
            'NetworkAccessProfile' =>
                $options['networkAccessProfile'],
            'IpCommandsUrl' =>
                $options['ipCommandsUrl'],
            'IpCommandsMethod' =>
                $options['ipCommandsMethod'],
            'SmsCommandsUrl' =>
                $options['smsCommandsUrl'],
            'SmsCommandsMethod' =>
                $options['smsCommandsMethod'],
            'DataLimit' =>
                $options['dataLimit'],
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new FleetInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Supersim.V1.FleetContext ' . \implode(' ', $context) . ']';
    }
}
