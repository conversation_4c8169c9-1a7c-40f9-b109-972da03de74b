<?php
/**
 * Email Configuration Setup Script for Hostinger - CSRF Fixed Version
 * This script helps set up proper email configuration for the school management system
 *
 * Instructions:
 * 1. Place this file in the root directory of your school management system
 * 2. Access it via browser: http://yourdomain.com/setup_email_config.php
 * 3. Configure your email settings for Hostinger
 * 4. Remove this file after configuration is complete
 */

// Temporarily disable CSRF protection for this configuration script
$_SERVER['REQUEST_URI_ORIGINAL'] = $_SERVER['REQUEST_URI'] ?? '';
$_SERVER['REQUEST_URI'] = '/setup_email_config/'; // This will be excluded from CSRF

// Include CodeIgniter bootstrap
require_once('index.php');

// Get CodeIgniter instance
$CI =& get_instance();

// Restore original REQUEST_URI
$_SERVER['REQUEST_URI'] = $_SERVER['REQUEST_URI_ORIGINAL'];

// Handle form submission
if ($_POST && isset($_POST['setup_email'])) {
    try {
        $branch_id = 1; // Default branch ID - adjust if needed
        
        // Prepare email configuration data
        $email_config = array(
            'branch_id' => $branch_id,
            'email' => $_POST['system_email'],
            'protocol' => $_POST['protocol'],
            'smtp_host' => $_POST['smtp_host'],
            'smtp_port' => $_POST['smtp_port'],
            'smtp_user' => $_POST['smtp_user'],
            'smtp_pass' => $_POST['smtp_pass'],
            'smtp_encryption' => $_POST['smtp_encryption'],
            'smtp_auth' => 1 // Always enable SMTP auth for Hostinger
        );
        
        // Check if configuration already exists
        $existing = $CI->db->get_where('email_config', array('branch_id' => $branch_id))->row();
        
        if ($existing) {
            // Update existing configuration
            $CI->db->where('branch_id', $branch_id);
            $CI->db->update('email_config', $email_config);
            $message = "Email configuration updated successfully!";
        } else {
            // Insert new configuration
            $CI->db->insert('email_config', $email_config);
            $message = "Email configuration created successfully!";
        }
        
        $success = true;
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        $success = false;
    }
}

// Handle email template setup
if ($_POST && isset($_POST['setup_template'])) {
    try {
        $branch_id = 1; // Default branch ID - adjust if needed
        
        // Check if forgot password template exists
        $existing_template = $CI->db->where(array('template_id' => 2, 'branch_id' => $branch_id))->get('email_templates_details')->row();
        
        $template_data = array(
            'template_id' => 2,
            'branch_id' => $branch_id,
            'notified' => 1,
            'subject' => 'Réinitialisation de mot de passe - {institute_name}',
            'template_body' => '
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 24px;">Réinitialisation de mot de passe</h1>
    </div>
    
    <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <p style="font-size: 16px; color: #333; margin-bottom: 20px;">Bonjour <strong>{name}</strong>,</p>
        
        <p style="font-size: 14px; color: #666; line-height: 1.6;">
            Vous avez demandé la réinitialisation de votre mot de passe pour votre compte sur <strong>{institute_name}</strong>.
        </p>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0; font-size: 14px; color: #666;">
                <strong>Nom d\'utilisateur:</strong> {username}<br>
                <strong>Email:</strong> {email}
            </p>
        </div>
        
        <p style="font-size: 14px; color: #666; line-height: 1.6;">
            Cliquez sur le bouton ci-dessous pour réinitialiser votre mot de passe:
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{reset_url}" style="background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
                Réinitialiser mon mot de passe
            </a>
        </div>
        
        <p style="font-size: 12px; color: #999; line-height: 1.6;">
            Si vous n\'avez pas demandé cette réinitialisation, ignorez simplement cet email. Votre mot de passe restera inchangé.
        </p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
        
        <p style="font-size: 12px; color: #999; text-align: center;">
            © 2025 {institute_name} - Système de gestion scolaire PASS-DRC
        </p>
    </div>
</div>'
        );
        
        if ($existing_template) {
            // Update existing template
            $CI->db->where(array('template_id' => 2, 'branch_id' => $branch_id));
            $CI->db->update('email_templates_details', $template_data);
            $template_message = "Email template updated successfully!";
        } else {
            // Insert new template
            $CI->db->insert('email_templates_details', $template_data);
            $template_message = "Email template created successfully!";
        }
        
        $template_success = true;
        
    } catch (Exception $e) {
        $template_error = "Error: " . $e->getMessage();
        $template_success = false;
    }
}

// Get current configuration
$branch_id = 1;
$current_config = $CI->db->get_where('email_config', array('branch_id' => $branch_id))->row();
$current_template = $CI->db->where(array('template_id' => 2, 'branch_id' => $branch_id))->get('email_templates_details')->row();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Email Configuration Setup - PASS-DRC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .current-config { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Email Configuration Setup - PASS-DRC</h1>
        <p><strong>Note:</strong> This script helps configure email settings for your school management system on Hostinger. Remove it after setup.</p>
        
        <?php if (isset($success) && $success): ?>
            <div class="section success">
                <strong>✓ Success:</strong> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php elseif (isset($error)): ?>
            <div class="section error">
                <strong>❌ Error:</strong> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($template_success) && $template_success): ?>
            <div class="section success">
                <strong>✓ Template Success:</strong> <?php echo htmlspecialchars($template_message); ?>
            </div>
        <?php elseif (isset($template_error)): ?>
            <div class="section error">
                <strong>❌ Template Error:</strong> <?php echo htmlspecialchars($template_error); ?>
            </div>
        <?php endif; ?>
        
        <!-- Current Configuration Display -->
        <?php if ($current_config): ?>
            <div class="section">
                <h3>📋 Current Email Configuration</h3>
                <div class="current-config">
                    <strong>System Email:</strong> <?php echo htmlspecialchars($current_config->email); ?><br>
                    <strong>Protocol:</strong> <?php echo htmlspecialchars($current_config->protocol); ?><br>
                    <?php if ($current_config->protocol == 'smtp'): ?>
                        <strong>SMTP Host:</strong> <?php echo htmlspecialchars($current_config->smtp_host); ?><br>
                        <strong>SMTP Port:</strong> <?php echo htmlspecialchars($current_config->smtp_port); ?><br>
                        <strong>SMTP Encryption:</strong> <?php echo htmlspecialchars($current_config->smtp_encryption); ?><br>
                        <strong>SMTP Username:</strong> <?php echo htmlspecialchars($current_config->smtp_user); ?><br>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Email Configuration Form -->
        <div class="section">
            <h3>⚙️ Email Configuration</h3>
            <form method="post">
                <div class="form-group">
                    <label for="system_email">System Email Address *</label>
                    <input type="email" name="system_email" id="system_email" 
                           value="<?php echo $current_config ? htmlspecialchars($current_config->email) : '<EMAIL>'; ?>" 
                           placeholder="<EMAIL>" required>
                    <small>This email will be used as the sender for all outgoing emails</small>
                </div>
                
                <div class="form-group">
                    <label for="protocol">Email Protocol *</label>
                    <select name="protocol" id="protocol" required>
                        <option value="smtp" <?php echo ($current_config && $current_config->protocol == 'smtp') ? 'selected' : 'selected'; ?>>SMTP (Recommended for Hostinger)</option>
                        <option value="mail" <?php echo ($current_config && $current_config->protocol == 'mail') ? 'selected' : ''; ?>>PHP Mail</option>
                    </select>
                </div>
                
                <div id="smtp-settings">
                    <div class="form-group">
                        <label for="smtp_host">SMTP Host *</label>
                        <input type="text" name="smtp_host" id="smtp_host" 
                               value="<?php echo $current_config ? htmlspecialchars($current_config->smtp_host) : 'mail.passdrc.com'; ?>" 
                               placeholder="mail.yourdomain.com">
                        <small>For Hostinger: mail.yourdomain.com or smtp.hostinger.com</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_port">SMTP Port *</label>
                        <select name="smtp_port" id="smtp_port">
                            <option value="587" <?php echo ($current_config && $current_config->smtp_port == '587') ? 'selected' : 'selected'; ?>>587 (TLS - Recommended)</option>
                            <option value="465" <?php echo ($current_config && $current_config->smtp_port == '465') ? 'selected' : ''; ?>>465 (SSL)</option>
                            <option value="25" <?php echo ($current_config && $current_config->smtp_port == '25') ? 'selected' : ''; ?>>25 (No encryption)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_encryption">SMTP Encryption *</label>
                        <select name="smtp_encryption" id="smtp_encryption">
                            <option value="tls" <?php echo ($current_config && $current_config->smtp_encryption == 'tls') ? 'selected' : 'selected'; ?>>TLS (Recommended)</option>
                            <option value="ssl" <?php echo ($current_config && $current_config->smtp_encryption == 'ssl') ? 'selected' : ''; ?>>SSL</option>
                            <option value="" <?php echo ($current_config && empty($current_config->smtp_encryption)) ? 'selected' : ''; ?>>None</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_user">SMTP Username *</label>
                        <input type="email" name="smtp_user" id="smtp_user" 
                               value="<?php echo $current_config ? htmlspecialchars($current_config->smtp_user) : '<EMAIL>'; ?>" 
                               placeholder="<EMAIL>">
                        <small>Usually your full email address</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_pass">SMTP Password *</label>
                        <input type="password" name="smtp_pass" id="smtp_pass" 
                               value="<?php echo $current_config ? htmlspecialchars($current_config->smtp_pass) : ''; ?>" 
                               placeholder="Your email password">
                        <small>Your email account password</small>
                    </div>
                </div>
                
                <button type="submit" name="setup_email" class="btn">Save Email Configuration</button>
            </form>
        </div>
        
        <!-- Email Template Setup -->
        <div class="section">
            <h3>📝 Email Template Setup</h3>
            <p>Set up the forgot password email template:</p>
            
            <?php if ($current_template): ?>
                <div class="current-config">
                    <strong>Template Status:</strong> <?php echo $current_template->notified ? 'Enabled' : 'Disabled'; ?><br>
                    <strong>Subject:</strong> <?php echo htmlspecialchars($current_template->subject); ?><br>
                </div>
            <?php else: ?>
                <div class="warning">
                    <strong>⚠️ Warning:</strong> Forgot password email template is not configured.
                </div>
            <?php endif; ?>
            
            <form method="post">
                <button type="submit" name="setup_template" class="btn btn-success">
                    <?php echo $current_template ? 'Update' : 'Create'; ?> Email Template
                </button>
            </form>
        </div>
        
        <div class="section warning">
            <h3>⚠️ Security & Next Steps</h3>
            <ol>
                <li><strong>Delete this file</strong> after configuration to prevent security risks</li>
                <li>Test email sending using the diagnostic script: <code>email_diagnostic.php</code></li>
                <li>Test password recovery: <a href="<?php echo base_url('authentication/forgot'); ?>" target="_blank">Forgot Password Page</a></li>
                <li>Check email logs in your Hostinger control panel if emails aren't delivered</li>
                <li>Ensure your domain has proper SPF/DKIM records for better deliverability</li>
            </ol>
        </div>
    </div>
    
    <script>
        // Show/hide SMTP settings based on protocol selection
        document.getElementById('protocol').addEventListener('change', function() {
            const smtpSettings = document.getElementById('smtp-settings');
            if (this.value === 'smtp') {
                smtpSettings.style.display = 'block';
            } else {
                smtpSettings.style.display = 'none';
            }
        });
        
        // Update encryption based on port selection
        document.getElementById('smtp_port').addEventListener('change', function() {
            const encryptionSelect = document.getElementById('smtp_encryption');
            if (this.value === '587') {
                encryptionSelect.value = 'tls';
            } else if (this.value === '465') {
                encryptionSelect.value = 'ssl';
            } else if (this.value === '25') {
                encryptionSelect.value = '';
            }
        });
    </script>
</body>
</html>
