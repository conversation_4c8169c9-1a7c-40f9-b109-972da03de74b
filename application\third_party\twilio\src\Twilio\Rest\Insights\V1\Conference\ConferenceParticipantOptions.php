<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Insights\V1\Conference;

use Twilio\Options;
use Twilio\Values;

abstract class ConferenceParticipantOptions
{
    /**
     * @param string $events Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc.
     * @param string $metrics Object. Contains participant call quality metrics.
     * @return FetchConferenceParticipantOptions Options builder
     */
    public static function fetch(
        
        string $events = Values::NONE,
        string $metrics = Values::NONE

    ): FetchConferenceParticipantOptions
    {
        return new FetchConferenceParticipantOptions(
            $events,
            $metrics
        );
    }

    /**
     * @param string $participantSid The unique SID identifier of the Participant.
     * @param string $label User-specified label for a participant.
     * @param string $events Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc.
     * @return ReadConferenceParticipantOptions Options builder
     */
    public static function read(
        
        string $participantSid = Values::NONE,
        string $label = Values::NONE,
        string $events = Values::NONE

    ): ReadConferenceParticipantOptions
    {
        return new ReadConferenceParticipantOptions(
            $participantSid,
            $label,
            $events
        );
    }

}

class FetchConferenceParticipantOptions extends Options
    {
    /**
     * @param string $events Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc.
     * @param string $metrics Object. Contains participant call quality metrics.
     */
    public function __construct(
        
        string $events = Values::NONE,
        string $metrics = Values::NONE

    ) {
        $this->options['events'] = $events;
        $this->options['metrics'] = $metrics;
    }

    /**
     * Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc.
     *
     * @param string $events Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc.
     * @return $this Fluent Builder
     */
    public function setEvents(string $events): self
    {
        $this->options['events'] = $events;
        return $this;
    }

    /**
     * Object. Contains participant call quality metrics.
     *
     * @param string $metrics Object. Contains participant call quality metrics.
     * @return $this Fluent Builder
     */
    public function setMetrics(string $metrics): self
    {
        $this->options['metrics'] = $metrics;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Insights.V1.FetchConferenceParticipantOptions ' . $options . ']';
    }
}

class ReadConferenceParticipantOptions extends Options
    {
    /**
     * @param string $participantSid The unique SID identifier of the Participant.
     * @param string $label User-specified label for a participant.
     * @param string $events Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc.
     */
    public function __construct(
        
        string $participantSid = Values::NONE,
        string $label = Values::NONE,
        string $events = Values::NONE

    ) {
        $this->options['participantSid'] = $participantSid;
        $this->options['label'] = $label;
        $this->options['events'] = $events;
    }

    /**
     * The unique SID identifier of the Participant.
     *
     * @param string $participantSid The unique SID identifier of the Participant.
     * @return $this Fluent Builder
     */
    public function setParticipantSid(string $participantSid): self
    {
        $this->options['participantSid'] = $participantSid;
        return $this;
    }

    /**
     * User-specified label for a participant.
     *
     * @param string $label User-specified label for a participant.
     * @return $this Fluent Builder
     */
    public function setLabel(string $label): self
    {
        $this->options['label'] = $label;
        return $this;
    }

    /**
     * Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc.
     *
     * @param string $events Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc.
     * @return $this Fluent Builder
     */
    public function setEvents(string $events): self
    {
        $this->options['events'] = $events;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Insights.V1.ReadConferenceParticipantOptions ' . $options . ']';
    }
}

