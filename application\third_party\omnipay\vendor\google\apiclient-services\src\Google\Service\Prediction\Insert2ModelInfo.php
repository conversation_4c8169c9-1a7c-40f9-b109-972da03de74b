<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Prediction_Insert2ModelInfo extends Google_Model
{
  public $classWeightedAccuracy;
  public $classificationAccuracy;
  public $meanSquaredError;
  public $modelType;
  public $numberInstances;
  public $numberLabels;

  public function setClassWeightedAccuracy($classWeightedAccuracy)
  {
    $this->classWeightedAccuracy = $classWeightedAccuracy;
  }
  public function getClassWeightedAccuracy()
  {
    return $this->classWeightedAccuracy;
  }
  public function setClassificationAccuracy($classificationAccuracy)
  {
    $this->classificationAccuracy = $classificationAccuracy;
  }
  public function getClassificationAccuracy()
  {
    return $this->classificationAccuracy;
  }
  public function setMeanSquaredError($meanSquaredError)
  {
    $this->meanSquaredError = $meanSquaredError;
  }
  public function getMeanSquaredError()
  {
    return $this->meanSquaredError;
  }
  public function setModelType($modelType)
  {
    $this->modelType = $modelType;
  }
  public function getModelType()
  {
    return $this->modelType;
  }
  public function setNumberInstances($numberInstances)
  {
    $this->numberInstances = $numberInstances;
  }
  public function getNumberInstances()
  {
    return $this->numberInstances;
  }
  public function setNumberLabels($numberLabels)
  {
    $this->numberLabels = $numberLabels;
  }
  public function getNumberLabels()
  {
    return $this->numberLabels;
  }
}
