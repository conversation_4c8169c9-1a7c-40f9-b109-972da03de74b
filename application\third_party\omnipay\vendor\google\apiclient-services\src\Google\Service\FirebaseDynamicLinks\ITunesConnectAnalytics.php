<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_FirebaseDynamicLinks_ITunesConnectAnalytics extends Google_Model
{
  public $at;
  public $ct;
  public $mt;
  public $pt;

  public function setAt($at)
  {
    $this->at = $at;
  }
  public function getAt()
  {
    return $this->at;
  }
  public function setCt($ct)
  {
    $this->ct = $ct;
  }
  public function getCt()
  {
    return $this->ct;
  }
  public function setMt($mt)
  {
    $this->mt = $mt;
  }
  public function getMt()
  {
    return $this->mt;
  }
  public function setPt($pt)
  {
    $this->pt = $pt;
  }
  public function getPt()
  {
    return $this->pt;
  }
}
