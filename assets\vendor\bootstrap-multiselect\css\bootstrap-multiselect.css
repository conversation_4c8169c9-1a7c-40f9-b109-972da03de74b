span.multiselect-native-select {
    position: relative
}

span.multiselect-native-select select {
    border: 0!important;
    clip: rect(0 0 0 0)!important;
    height: 1px!important;
    margin: -1px -1px -1px -3px!important;
    overflow: hidden!important;
    padding: 0!important;
    position: absolute!important;
    width: 1px!important;
    left: 50%;
    top: 30px
}

.multiselect-container {
    position: absolute;
    list-style-type: none;
    margin: 0;
    padding: 0;
	width: 100%
}

.multiselect-container .input-group {
    margin: 5px
}

.multiselect-container .multiselect-reset .input-group {
    width: 93%
}

.multiselect-container>li {
    padding: 0
}

.multiselect-container>li>a.multiselect-all label {
    font-weight: 700
}

.multiselect-container>li.multiselect-group label {
    margin: 0;
    padding: 3px 20px;
    height: 100%;
    font-weight: 700
}

.multiselect-container>li.multiselect-group-clickable label {
    cursor: pointer
}

.multiselect-container>li>a {
    padding: 0
}

.multiselect-container>li>a>label {
    margin: 0;
    height: 100%;
    cursor: pointer;
    font-weight: 400;
    padding: 7px 20px 7px 40px
}

.multiselect-container>li>a>label.checkbox,
.multiselect-container>li>a>label.radio {
    margin: 1px 0;
}

.multiselect-container>li>a>label>input[type=checkbox] {
    margin-bottom: 5px
}

.btn-group>.btn-group:nth-child(2)>.multiselect.btn {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px
}

.form-inline .multiselect-container label.checkbox,
.form-inline .multiselect-container label.radio {
    padding: 3px 20px 3px 40px
}

.form-inline .multiselect-container li a label.checkbox input[type=checkbox],
.form-inline .multiselect-container li a label.radio input[type=radio] {
    margin-left: -20px;
    margin-right: 0
}

.cus-multiselect {
	text-align: left !important;
}

.cus-multiselect .caret {
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -2px;
    border-color: #999 transparent transparent;
    border-style: solid;
    border-width: 4px 4px 0;
}

.multiselect-native-select .btn-group.open .caret {
    border-color: transparent transparent #999;
    border-width: 0 4px 4px;
}

html.dark .dropdown-menu > .active > a, 
html.dark .dropdown-menu > .active > a:focus {
  background-color: #ff685c;
}