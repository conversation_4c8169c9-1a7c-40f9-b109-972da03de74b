HTTP/1.1 200 OK
Server: nginx
Date: Tue, 26 Feb 2013 16:11:12 GMT
Content-Type: application/json;charset=utf-8
Connection: keep-alive
Access-Control-Max-Age: 300
Access-Control-Allow-Credentials: true
Cache-Control: no-cache, no-store

{
  "object": "customer",
  "created": 1361895072,
  "id": "cus_1MZSEtqSghKx99",
  "livemode": false,
  "description": "fdsa",
  "default_card": "card_15WhVwIobxWFFmzdQ3QBSwNi",
  "active_card": {
    "object": "card",
    "last4": "4242",
    "type": "Visa",
    "exp_month": 9,
    "exp_year": 2019,
    "fingerprint": "dfB0t0avO0bWr9eY",
    "country": "US",
    "name": "fdjsk fdjksl",
    "address_line1": "",
    "address_line2": "",
    "address_city": "",
    "address_state": "",
    "address_zip": "",
    "address_country": "",
    "cvc_check": "pass",
    "address_line1_check": "pass",
    "address_zip_check": "pass"
  },
  "email": null,
  "delinquent": false,
  "subscription": null,
  "discount": null,
  "account_balance": 0
}
