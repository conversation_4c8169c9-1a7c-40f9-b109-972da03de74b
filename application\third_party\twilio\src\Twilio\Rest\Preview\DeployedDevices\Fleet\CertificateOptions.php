<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Preview\DeployedDevices\Fleet;

use Twilio\Options;
use Twilio\Values;

abstract class CertificateOptions
{
    /**
     * @param string $friendlyName Provides a human readable descriptive text for this Certificate credential, up to 256 characters long.
     * @param string $deviceSid Provides the unique string identifier of an existing Device to become authenticated with this Certificate credential.
     * @return CreateCertificateOptions Options builder
     */
    public static function create(
        
        string $friendlyName = Values::NONE,
        string $deviceSid = Values::NONE

    ): CreateCertificateOptions
    {
        return new CreateCertificateOptions(
            $friendlyName,
            $deviceSid
        );
    }



    /**
     * @param string $deviceSid Filters the resulting list of Certificates by a unique string identifier of an authenticated Device.
     * @return ReadCertificateOptions Options builder
     */
    public static function read(
        
        string $deviceSid = Values::NONE

    ): ReadCertificateOptions
    {
        return new ReadCertificateOptions(
            $deviceSid
        );
    }

    /**
     * @param string $friendlyName Provides a human readable descriptive text for this Certificate credential, up to 256 characters long.
     * @param string $deviceSid Provides the unique string identifier of an existing Device to become authenticated with this Certificate credential.
     * @return UpdateCertificateOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $deviceSid = Values::NONE

    ): UpdateCertificateOptions
    {
        return new UpdateCertificateOptions(
            $friendlyName,
            $deviceSid
        );
    }

}

class CreateCertificateOptions extends Options
    {
    /**
     * @param string $friendlyName Provides a human readable descriptive text for this Certificate credential, up to 256 characters long.
     * @param string $deviceSid Provides the unique string identifier of an existing Device to become authenticated with this Certificate credential.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $deviceSid = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['deviceSid'] = $deviceSid;
    }

    /**
     * Provides a human readable descriptive text for this Certificate credential, up to 256 characters long.
     *
     * @param string $friendlyName Provides a human readable descriptive text for this Certificate credential, up to 256 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provides the unique string identifier of an existing Device to become authenticated with this Certificate credential.
     *
     * @param string $deviceSid Provides the unique string identifier of an existing Device to become authenticated with this Certificate credential.
     * @return $this Fluent Builder
     */
    public function setDeviceSid(string $deviceSid): self
    {
        $this->options['deviceSid'] = $deviceSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.DeployedDevices.CreateCertificateOptions ' . $options . ']';
    }
}



class ReadCertificateOptions extends Options
    {
    /**
     * @param string $deviceSid Filters the resulting list of Certificates by a unique string identifier of an authenticated Device.
     */
    public function __construct(
        
        string $deviceSid = Values::NONE

    ) {
        $this->options['deviceSid'] = $deviceSid;
    }

    /**
     * Filters the resulting list of Certificates by a unique string identifier of an authenticated Device.
     *
     * @param string $deviceSid Filters the resulting list of Certificates by a unique string identifier of an authenticated Device.
     * @return $this Fluent Builder
     */
    public function setDeviceSid(string $deviceSid): self
    {
        $this->options['deviceSid'] = $deviceSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.DeployedDevices.ReadCertificateOptions ' . $options . ']';
    }
}

class UpdateCertificateOptions extends Options
    {
    /**
     * @param string $friendlyName Provides a human readable descriptive text for this Certificate credential, up to 256 characters long.
     * @param string $deviceSid Provides the unique string identifier of an existing Device to become authenticated with this Certificate credential.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $deviceSid = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['deviceSid'] = $deviceSid;
    }

    /**
     * Provides a human readable descriptive text for this Certificate credential, up to 256 characters long.
     *
     * @param string $friendlyName Provides a human readable descriptive text for this Certificate credential, up to 256 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provides the unique string identifier of an existing Device to become authenticated with this Certificate credential.
     *
     * @param string $deviceSid Provides the unique string identifier of an existing Device to become authenticated with this Certificate credential.
     * @return $this Fluent Builder
     */
    public function setDeviceSid(string $deviceSid): self
    {
        $this->options['deviceSid'] = $deviceSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.DeployedDevices.UpdateCertificateOptions ' . $options . ']';
    }
}

