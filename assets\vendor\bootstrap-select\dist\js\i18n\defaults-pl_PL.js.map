{"version": 3, "sources": ["../../../js/i18n/defaults-pl_PL.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;AAC5C,IAAI,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;AACtD,IAAI,iBAAiB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC/C,IAAI,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,OAAO,IAAI,CAAC;AAC7H,IAAI,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;AACxC,IAAI,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;AAC1C,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,EAAE,EAAE,CAAC;AACL,GAAG,MAAM,EAAE,CAAC", "file": "defaults-pl_PL.js", "sourcesContent": ["(function ($) {\r\n  $.fn.selectpicker.defaults = {\r\n    noneSelectedText: 'Nic nie zaznaczono',\r\n    noneResultsText: 'Brak wyników wyszukiwania {0}',\r\n    countSelectedText: 'Zaznaczono {0} z {1}',\r\n    maxOptionsText: ['Osiągnięto limit ({n} {var} max)', 'Limit grupy osiągnięty ({n} {var} max)', ['elementy', 'element']],\r\n    selectAllText: 'Zaznacz wszystkie',\r\n    deselectAllText: 'Odznacz wszystkie',\r\n    multipleSeparator: ', '\r\n  };\r\n})(jQuery);\r\n"]}