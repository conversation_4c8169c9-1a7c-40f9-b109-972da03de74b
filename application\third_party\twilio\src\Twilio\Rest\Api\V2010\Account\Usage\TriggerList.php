<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account\Usage;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Stream;
use Twilio\Values;
use Twilio\Version;


class TriggerList extends ListResource
    {
    /**
     * Construct the TriggerList
     *
     * @param Version $version Version that contains the resource
     * @param string $accountSid The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that will create the resource.
     */
    public function __construct(
        Version $version,
        string $accountSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'accountSid' =>
            $accountSid,
        
        ];

        $this->uri = '/Accounts/' . \rawurlencode($accountSid)
        .'/Usage/Triggers.json';
    }

    /**
     * Create the TriggerInstance
     *
     * @param string $callbackUrl The URL we should call using `callback_method` when the trigger fires.
     * @param string $triggerValue The usage value at which the trigger should fire.  For convenience, you can use an offset value such as `+30` to specify a trigger_value that is 30 units more than the current usage value. Be sure to urlencode a `+` as `%2B`.
     * @param string $usageCategory
     * @param array|Options $options Optional Arguments
     * @return TriggerInstance Created TriggerInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(string $callbackUrl, string $triggerValue, string $usageCategory, array $options = []): TriggerInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'CallbackUrl' =>
                $callbackUrl,
            'TriggerValue' =>
                $triggerValue,
            'UsageCategory' =>
                $usageCategory,
            'CallbackMethod' =>
                $options['callbackMethod'],
            'FriendlyName' =>
                $options['friendlyName'],
            'Recurring' =>
                $options['recurring'],
            'TriggerBy' =>
                $options['triggerBy'],
        ]);

        $payload = $this->version->create('POST', $this->uri, [], $data);

        return new TriggerInstance(
            $this->version,
            $payload,
            $this->solution['accountSid']
        );
    }


    /**
     * Reads TriggerInstance records from the API as a list.
     * Unlike stream(), this operation is eager and will load `limit` records into
     * memory before returning.
     *
     * @param array|Options $options Optional Arguments
     * @param int $limit Upper limit for the number of records to return. read()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, read()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return TriggerInstance[] Array of results
     */
    public function read(array $options = [], int $limit = null, $pageSize = null): array
    {
        return \iterator_to_array($this->stream($options, $limit, $pageSize), false);
    }

    /**
     * Streams TriggerInstance records from the API as a generator stream.
     * This operation lazily loads records as efficiently as possible until the
     * limit
     * is reached.
     * The results are returned as a generator, so this operation is memory
     * efficient.
     *
     * @param array|Options $options Optional Arguments
     * @param int $limit Upper limit for the number of records to return. stream()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, stream()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return Stream stream of results
     */
    public function stream(array $options = [], int $limit = null, $pageSize = null): Stream
    {
        $limits = $this->version->readLimits($limit, $pageSize);

        $page = $this->page($options, $limits['pageSize']);

        return $this->version->stream($page, $limits['limit'], $limits['pageLimit']);
    }

    /**
     * Retrieve a single page of TriggerInstance records from the API.
     * Request is executed immediately
     *
     * @param mixed $pageSize Number of records to return, defaults to 50
     * @param string $pageToken PageToken provided by the API
     * @param mixed $pageNumber Page Number, this value is simply for client state
     * @return TriggerPage Page of TriggerInstance
     */
    public function page(
        array $options = [],
        $pageSize = Values::NONE,
        string $pageToken = Values::NONE,
        $pageNumber = Values::NONE
    ): TriggerPage
    {
        $options = new Values($options);

        $params = Values::of([
            'Recurring' =>
                $options['recurring'],
            'TriggerBy' =>
                $options['triggerBy'],
            'UsageCategory' =>
                $options['usageCategory'],
            'PageToken' => $pageToken,
            'Page' => $pageNumber,
            'PageSize' => $pageSize,
        ]);

        $response = $this->version->page('GET', $this->uri, $params);

        return new TriggerPage($this->version, $response, $this->solution);
    }

    /**
     * Retrieve a specific page of TriggerInstance records from the API.
     * Request is executed immediately
     *
     * @param string $targetUrl API-generated URL for the requested results page
     * @return TriggerPage Page of TriggerInstance
     */
    public function getPage(string $targetUrl): TriggerPage
    {
        $response = $this->version->getDomain()->getClient()->request(
            'GET',
            $targetUrl
        );

        return new TriggerPage($this->version, $response, $this->solution);
    }


    /**
     * Constructs a TriggerContext
     *
     * @param string $sid The Twilio-provided string that uniquely identifies the UsageTrigger resource to delete.
     */
    public function getContext(
        string $sid
        
    ): TriggerContext
    {
        return new TriggerContext(
            $this->version,
            $this->solution['accountSid'],
            $sid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Api.V2010.TriggerList]';
    }
}
