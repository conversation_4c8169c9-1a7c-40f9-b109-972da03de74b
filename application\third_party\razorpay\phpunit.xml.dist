<?xml version="1.0" encoding="UTF-8"?>
<!-- http://www.phpunit.de/manual/current/en/appendixes.configuration.html -->
<phpunit
         backupGlobals="false"
         backupStaticAttributes="false"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false"
         bootstrap="tests/bootstrap.php">
  <coverage>
    <include>
      <directory>./src</directory>
    </include>
    <exclude/>
  </coverage>
  <php>
    <!-- copy this file to phpunit.xml and replace with your API key to run tests -->
    <!-- Also uncomment the following two lines-->
    <!--<server name="KEY_ID" value="" />
        <server name="KEY_SECRET" value="" />
        -->
  </php>
  <testsuites>
    <testsuite name="default">
      <directory>./non_composer_tests/</directory>
    </testsuite>
    <testsuite name="default">
      <directory>./tests</directory>
    </testsuite>
  </testsuites>
</phpunit>
