<?php
/**
 * Immediate Email Delivery Fix
 * Based on your test results, this will switch to PHP mail() which is working
 * 
 * Instructions:
 * 1. Upload this file to your root directory
 * 2. Access it via browser to apply the fix
 * 3. Test email delivery immediately
 * 4. Delete this file after use
 */

// Direct database connection
$db_config = array(
    'hostname' => 'localhost',
    'username' => 'u467814674_schooladmin',
    'password' => 'n*qy@1=Tg',
    'database' => 'u467814674_schooldatabase'
);

$fix_applied = false;
$fix_message = '';
$test_result = '';

// Apply the fix automatically
if (!isset($_GET['applied'])) {
    try {
        $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
        
        if ($mysqli->connect_error) {
            throw new Exception("Database connection failed: " . $mysqli->connect_error);
        }
        
        // Switch to PHP mail() since it's working
        $update_query = "UPDATE email_config SET 
            protocol = 'mail',
            email = '<EMAIL>'
            WHERE branch_id = 1";
        
        if ($mysqli->query($update_query)) {
            $fix_applied = true;
            $fix_message = "Successfully switched to PHP mail() method - this should work based on your test results!";
        } else {
            throw new Exception("Update failed: " . $mysqli->error);
        }
        
        $mysqli->close();
        
        // Redirect to avoid reapplying fix
        header("Location: " . $_SERVER['PHP_SELF'] . "?applied=1");
        exit;
        
    } catch (Exception $e) {
        $fix_message = "Error applying fix: " . $e->getMessage();
    }
}

// Test email sending after fix
if ($_POST && isset($_POST['test_email'])) {
    $test_email = $_POST['test_email'];
    
    // Test using PHP mail (which should now be configured)
    $subject = "PASS-DRC Password Recovery Test - Fixed Configuration";
    $message = '
    <html>
    <head><title>Password Recovery Test</title></head>
    <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0;">Email Delivery Fixed!</h1>
        </div>
        
        <div style="background: white; padding: 30px; border: 1px solid #ddd; border-radius: 0 0 10px 10px;">
            <h2 style="color: #333;">🎉 Success!</h2>
            <p>Your PASS-DRC email system is now working correctly!</p>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p style="margin: 0;"><strong>Configuration Details:</strong></p>
                <ul>
                    <li>Method: PHP mail() function</li>
                    <li>Sender: <EMAIL></li>
                    <li>Time: ' . date('Y-m-d H:i:s') . '</li>
                    <li>Server: ' . $_SERVER['SERVER_NAME'] . '</li>
                </ul>
            </div>
            
            <p><strong>What this means:</strong></p>
            <ul>
                <li>✅ Password recovery emails will now be delivered</li>
                <li>✅ Users can reset their passwords successfully</li>
                <li>✅ Email system is optimized for Hostinger hosting</li>
            </ul>
            
            <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;">
                <p style="margin: 0; color: #155724;"><strong>Next Steps:</strong></p>
                <ol style="color: #155724;">
                    <li>Test the password recovery page</li>
                    <li>Verify users receive reset emails</li>
                    <li>Delete diagnostic files for security</li>
                </ol>
            </div>
            
            <hr style="margin: 20px 0;">
            <p style="font-size: 12px; color: #666; text-align: center;">
                PASS-DRC School Management System - Email Delivery Test
            </p>
        </div>
    </body>
    </html>';
    
    $headers = array(
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: PASS-DRC System <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'X-Mailer: PHP/' . phpversion()
    );
    
    $result = mail($test_email, $subject, $message, implode("\r\n", $headers));
    
    if ($result) {
        $test_result = "✅ Test email sent successfully! Check your inbox (and spam folder).";
    } else {
        $test_result = "❌ Test email failed. There may be additional server restrictions.";
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Email Delivery Fix Applied - PASS-DRC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .form-group { margin: 15px 0; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Email Delivery Fix Applied</h1>
        
        <?php if (isset($_GET['applied'])): ?>
            <div class="section success">
                <h2>✅ Configuration Updated Successfully!</h2>
                <p><strong>Change Applied:</strong> Switched from SMTP to PHP mail() method</p>
                <p><strong>Reason:</strong> Your diagnostic test showed PHP mail() is working on your Hostinger server</p>
                <p><strong>Result:</strong> Email delivery should now work correctly</p>
            </div>
        <?php endif; ?>
        
        <?php if ($test_result): ?>
            <div class="section <?php echo strpos($test_result, '✅') !== false ? 'success' : 'error'; ?>">
                <h3>Test Result</h3>
                <p><?php echo $test_result; ?></p>
            </div>
        <?php endif; ?>
        
        <!-- Test Email Form -->
        <div class="section">
            <h2>📧 Final Email Delivery Test</h2>
            <p>Test the fixed configuration by sending an email:</p>
            
            <form method="post">
                <div class="form-group">
                    <label for="test_email"><strong>Your Email Address:</strong></label>
                    <input type="email" name="test_email" id="test_email" 
                           placeholder="<EMAIL>" required>
                </div>
                <button type="submit" class="btn btn-success">Send Test Email</button>
            </form>
        </div>
        
        <!-- Current Configuration -->
        <div class="section info">
            <h2>📋 Current Email Configuration</h2>
            <?php
            try {
                $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
                $result = $mysqli->query("SELECT * FROM email_config WHERE branch_id = 1");
                if ($result && $result->num_rows > 0) {
                    $config = $result->fetch_assoc();
                    echo "<ul>";
                    echo "<li><strong>Protocol:</strong> " . htmlspecialchars($config['protocol']) . "</li>";
                    echo "<li><strong>System Email:</strong> " . htmlspecialchars($config['email']) . "</li>";
                    if ($config['protocol'] == 'smtp') {
                        echo "<li><strong>SMTP Host:</strong> " . htmlspecialchars($config['smtp_host']) . "</li>";
                        echo "<li><strong>SMTP Port:</strong> " . htmlspecialchars($config['smtp_port']) . "</li>";
                    }
                    echo "</ul>";
                }
                $mysqli->close();
            } catch (Exception $e) {
                echo "<p>Could not retrieve configuration: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            ?>
        </div>
        
        <!-- Next Steps -->
        <div class="section success">
            <h2>🎯 Next Steps</h2>
            <ol>
                <li><strong>Test the email above</strong> - You should receive it within 5 minutes</li>
                <li><strong>Test password recovery:</strong> 
                    <a href="authentication/forgot" target="_blank" style="color: #007bff;">Password Recovery Page</a>
                </li>
                <li><strong>Verify users can reset passwords</strong> successfully</li>
                <li><strong>Clean up diagnostic files:</strong>
                    <ul>
                        <li>Delete: <code>email_delivery_troubleshoot.php</code></li>
                        <li>Delete: <code>fix_email_delivery_now.php</code> (this file)</li>
                        <li>Delete: <code>standalone_email_test.php</code></li>
                        <li>Delete: <code>simple_email_setup.php</code></li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <!-- Why This Fix Works -->
        <div class="section info">
            <h2>💡 Why This Fix Works</h2>
            <p><strong>Your diagnostic results showed:</strong></p>
            <ul>
                <li>✅ PHP mail() function: <strong>Working</strong></li>
                <li>❌ SMTP connection: <strong>Issues with mail.passdrc.com</strong></li>
                <li>✅ Email account exists: <strong><EMAIL> created</strong></li>
            </ul>
            
            <p><strong>The solution:</strong></p>
            <ul>
                <li>Switch from SMTP to PHP mail() method</li>
                <li>PHP mail() uses the server's built-in mail system</li>
                <li>More reliable on Hostinger shared hosting</li>
                <li>No SMTP authentication issues</li>
            </ul>
        </div>
        
        <!-- Troubleshooting -->
        <div class="section warning">
            <h2>🔧 If Email Still Doesn't Work</h2>
            <p>If the test email above fails, try these additional steps:</p>
            
            <h3>1. Check Email Account Status</h3>
            <ul>
                <li>Login to Hostinger control panel</li>
                <li>Verify <code><EMAIL></code> is active</li>
                <li>Check email account quota isn't full</li>
            </ul>
            
            <h3>2. Add DNS Records</h3>
            <ul>
                <li>Add SPF record: <code>v=spf1 include:_spf.hostinger.com ~all</code></li>
                <li>Enable DKIM in Hostinger DNS settings</li>
            </ul>
            
            <h3>3. Contact Hostinger Support</h3>
            <ul>
                <li>Mention: "PHP mail() function not delivering emails"</li>
                <li>Ask about: Email sending restrictions on your plan</li>
                <li>Request: Email server configuration check</li>
            </ul>
        </div>
        
        <div class="section error">
            <h2>🗑️ Security Cleanup</h2>
            <p><strong>Important:</strong> Delete all diagnostic files after testing!</p>
            <p>These files contain sensitive information and should not remain on your server:</p>
            <ul>
                <li><code>email_delivery_troubleshoot.php</code></li>
                <li><code>fix_email_delivery_now.php</code></li>
                <li><code>standalone_email_test.php</code></li>
                <li><code>simple_email_setup.php</code></li>
            </ul>
        </div>
    </div>
</body>
</html>
