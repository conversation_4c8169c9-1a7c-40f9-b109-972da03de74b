<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Logging_LogEntryOperation extends Google_Model
{
  public $first;
  public $id;
  public $last;
  public $producer;

  public function setFirst($first)
  {
    $this->first = $first;
  }
  public function getFirst()
  {
    return $this->first;
  }
  public function setId($id)
  {
    $this->id = $id;
  }
  public function getId()
  {
    return $this->id;
  }
  public function setLast($last)
  {
    $this->last = $last;
  }
  public function getLast()
  {
    return $this->last;
  }
  public function setProducer($producer)
  {
    $this->producer = $producer;
  }
  public function getProducer()
  {
    return $this->producer;
  }
}
