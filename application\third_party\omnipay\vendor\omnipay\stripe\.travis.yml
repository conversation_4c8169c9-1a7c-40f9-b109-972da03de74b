language: php

php:
  - 5.6
  - 7.0
  - 7.1
  - 7.2

# This triggers builds to run on the new TravisCI infrastructure.
# See: http://docs.travis-ci.com/user/workers/container-based-infrastructure/
sudo: false

## Cache composer
cache:
  directories:
    - $HOME/.composer/cache

env:
  global:
    - setup=basic

matrix:
  include:
    - php: 5.6
      env: setup=lowest

install:
  - if [[ $setup = 'basic' ]]; then travis_retry composer install --prefer-dist --no-interaction; fi
  - if [[ $setup = 'lowest' ]]; then travis_retry composer update --prefer-dist --no-interaction --prefer-lowest --prefer-stable; fi

script: vendor/bin/phpcs --standard=PSR2 src && vendor/bin/phpunit --coverage-text
