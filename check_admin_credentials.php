<?php
/**
 * Check Admin Login Credentials in Hostinger Database
 * Upload this to: /domains/passdrc.com/public_html/school/
 * Access via: https://school.passdrc.com/check_admin_credentials.php
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Admin Login Credentials Checker</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";

try {
    // Load database configuration
    include 'application/config/database.php';
    
    $hostname = $db['default']['hostname'];
    $username = $db['default']['username'];
    $password = $db['default']['password'];
    $database = $db['default']['database'];
    
    echo "<h2>📊 Database Connection</h2>";
    echo "<p>✅ <strong>Connecting to:</strong> $database</p>";
    
    // Connect to database
    $connection = new mysqli($hostname, $username, $password, $database);
    
    if ($connection->connect_error) {
        die("<p>❌ <strong>Connection failed:</strong> " . $connection->connect_error . "</p>");
    }
    
    echo "<p>✅ <strong>Database connected successfully!</strong></p>";
    
    echo "<h2>👤 Admin Login Credentials</h2>";
    
    // Check if login table exists
    $result = $connection->query("SHOW TABLES LIKE 'login'");
    if ($result->num_rows == 0) {
        echo "<p>❌ <strong>Login table not found!</strong> System may not be installed yet.</p>";
        echo "<p>🔧 <strong>Solution:</strong> You may need to run the installation process first.</p>";
    } else {
        echo "<p>✅ <strong>Login table found</strong></p>";
        
        // Get admin users (role = 1 is typically admin/superadmin)
        $query = "SELECT l.username, l.role, l.active, s.name, s.email 
                  FROM login l 
                  LEFT JOIN staff s ON l.user_id = s.id 
                  WHERE l.role = 1 
                  ORDER BY l.id ASC";
        
        $result = $connection->query($query);
        
        if ($result && $result->num_rows > 0) {
            echo "<h3>🎯 Found Admin Accounts:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 10px;'>Username</th>";
            echo "<th style='padding: 10px;'>Name</th>";
            echo "<th style='padding: 10px;'>Email</th>";
            echo "<th style='padding: 10px;'>Role</th>";
            echo "<th style='padding: 10px;'>Status</th>";
            echo "</tr>";
            
            while ($row = $result->fetch_assoc()) {
                $status = $row['active'] == 1 ? '✅ Active' : '❌ Inactive';
                $roleText = $row['role'] == 1 ? 'Super Admin' : 'Role ' . $row['role'];
                
                echo "<tr>";
                echo "<td style='padding: 10px;'><strong>" . htmlspecialchars($row['username']) . "</strong></td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($row['name'] ?? 'N/A') . "</td>";
                echo "<td style='padding: 10px;'>" . htmlspecialchars($row['email'] ?? 'N/A') . "</td>";
                echo "<td style='padding: 10px;'>" . $roleText . "</td>";
                echo "<td style='padding: 10px;'>" . $status . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<div style='background: #e8f5e8; padding: 15px; margin: 20px 0; border: 1px solid #4CAF50; border-radius: 5px;'>";
            echo "<h3>🔑 How to Login:</h3>";
            echo "<ol>";
            echo "<li><strong>Go to:</strong> <a href='index.php'>https://school.passdrc.com/</a></li>";
            echo "<li><strong>Use the username</strong> shown in the table above</li>";
            echo "<li><strong>Password:</strong> You need to remember the password you set during installation</li>";
            echo "</ol>";
            echo "</div>";
            
        } else {
            echo "<p>❌ <strong>No admin accounts found!</strong></p>";
            echo "<p>🔧 <strong>This means the system hasn't been properly installed yet.</strong></p>";
        }
    }
    
    // Check if system is installed
    echo "<h2>⚙️ Installation Status</h2>";
    $result = $connection->query("SHOW TABLES LIKE 'global_settings'");
    if ($result->num_rows > 0) {
        $settings = $connection->query("SELECT * FROM global_settings LIMIT 1");
        if ($settings && $settings->num_rows > 0) {
            $setting = $settings->fetch_assoc();
            echo "<p>✅ <strong>System is installed</strong></p>";
            echo "<p><strong>School Name:</strong> " . htmlspecialchars($setting['school_name'] ?? 'Not set') . "</p>";
        }
    } else {
        echo "<p>❌ <strong>System not installed</strong> - Run installation first</p>";
    }
    
    $connection->close();
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>📋 Default Credentials (if system not installed)</h2>";
echo "<p>If no admin accounts were found above, you can try these default credentials from the documentation:</p>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffc107; border-radius: 5px;'>";
echo "<p><strong>Username:</strong> admin</p>";
echo "<p><strong>Password:</strong> admin123</p>";
echo "<p><em>Note: These only work if the system uses default test data</em></p>";
echo "</div>";

echo "<h2>🚀 Next Steps</h2>";
echo "<ul>";
echo "<li>If admin accounts were found above, use those credentials to login</li>";
echo "<li>If no accounts found, you may need to run the installation process</li>";
echo "<li>If you forgot your password, you can reset it through the database</li>";
echo "</ul>";
?>
