<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Sync
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Sync\V1\Service;

use Twilio\Options;
use Twilio\Values;

abstract class DocumentOptions
{
    /**
     * @param string $uniqueName An application-defined string that uniquely identifies the Sync Document
     * @param array $data A JSON string that represents an arbitrary, schema-less object that the Sync Document stores. Can be up to 16 KiB in length.
     * @param int $ttl How long, [in seconds](https://www.twilio.com/docs/sync/limits#sync-payload-limits), before the Sync Document expires and is deleted (the Sync Document's time-to-live).
     * @return CreateDocumentOptions Options builder
     */
    public static function create(
        
        string $uniqueName = Values::NONE,
        array $data = Values::ARRAY_NONE,
        int $ttl = Values::INT_NONE

    ): CreateDocumentOptions
    {
        return new CreateDocumentOptions(
            $uniqueName,
            $data,
            $ttl
        );
    }




    /**
     * @param array $data A JSON string that represents an arbitrary, schema-less object that the Sync Document stores. Can be up to 16 KiB in length.
     * @param int $ttl How long, [in seconds](https://www.twilio.com/docs/sync/limits#sync-payload-limits), before the Sync Document expires and is deleted (time-to-live).
     * @param string $ifMatch The If-Match HTTP request header
     * @return UpdateDocumentOptions Options builder
     */
    public static function update(
        
        array $data = Values::ARRAY_NONE,
        int $ttl = Values::INT_NONE,
        string $ifMatch = Values::NONE

    ): UpdateDocumentOptions
    {
        return new UpdateDocumentOptions(
            $data,
            $ttl,
            $ifMatch
        );
    }

}

class CreateDocumentOptions extends Options
    {
    /**
     * @param string $uniqueName An application-defined string that uniquely identifies the Sync Document
     * @param array $data A JSON string that represents an arbitrary, schema-less object that the Sync Document stores. Can be up to 16 KiB in length.
     * @param int $ttl How long, [in seconds](https://www.twilio.com/docs/sync/limits#sync-payload-limits), before the Sync Document expires and is deleted (the Sync Document's time-to-live).
     */
    public function __construct(
        
        string $uniqueName = Values::NONE,
        array $data = Values::ARRAY_NONE,
        int $ttl = Values::INT_NONE

    ) {
        $this->options['uniqueName'] = $uniqueName;
        $this->options['data'] = $data;
        $this->options['ttl'] = $ttl;
    }

    /**
     * An application-defined string that uniquely identifies the Sync Document
     *
     * @param string $uniqueName An application-defined string that uniquely identifies the Sync Document
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * A JSON string that represents an arbitrary, schema-less object that the Sync Document stores. Can be up to 16 KiB in length.
     *
     * @param array $data A JSON string that represents an arbitrary, schema-less object that the Sync Document stores. Can be up to 16 KiB in length.
     * @return $this Fluent Builder
     */
    public function setData(array $data): self
    {
        $this->options['data'] = $data;
        return $this;
    }

    /**
     * How long, [in seconds](https://www.twilio.com/docs/sync/limits#sync-payload-limits), before the Sync Document expires and is deleted (the Sync Document's time-to-live).
     *
     * @param int $ttl How long, [in seconds](https://www.twilio.com/docs/sync/limits#sync-payload-limits), before the Sync Document expires and is deleted (the Sync Document's time-to-live).
     * @return $this Fluent Builder
     */
    public function setTtl(int $ttl): self
    {
        $this->options['ttl'] = $ttl;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Sync.V1.CreateDocumentOptions ' . $options . ']';
    }
}




class UpdateDocumentOptions extends Options
    {
    /**
     * @param array $data A JSON string that represents an arbitrary, schema-less object that the Sync Document stores. Can be up to 16 KiB in length.
     * @param int $ttl How long, [in seconds](https://www.twilio.com/docs/sync/limits#sync-payload-limits), before the Sync Document expires and is deleted (time-to-live).
     * @param string $ifMatch The If-Match HTTP request header
     */
    public function __construct(
        
        array $data = Values::ARRAY_NONE,
        int $ttl = Values::INT_NONE,
        string $ifMatch = Values::NONE

    ) {
        $this->options['data'] = $data;
        $this->options['ttl'] = $ttl;
        $this->options['ifMatch'] = $ifMatch;
    }

    /**
     * A JSON string that represents an arbitrary, schema-less object that the Sync Document stores. Can be up to 16 KiB in length.
     *
     * @param array $data A JSON string that represents an arbitrary, schema-less object that the Sync Document stores. Can be up to 16 KiB in length.
     * @return $this Fluent Builder
     */
    public function setData(array $data): self
    {
        $this->options['data'] = $data;
        return $this;
    }

    /**
     * How long, [in seconds](https://www.twilio.com/docs/sync/limits#sync-payload-limits), before the Sync Document expires and is deleted (time-to-live).
     *
     * @param int $ttl How long, [in seconds](https://www.twilio.com/docs/sync/limits#sync-payload-limits), before the Sync Document expires and is deleted (time-to-live).
     * @return $this Fluent Builder
     */
    public function setTtl(int $ttl): self
    {
        $this->options['ttl'] = $ttl;
        return $this;
    }

    /**
     * The If-Match HTTP request header
     *
     * @param string $ifMatch The If-Match HTTP request header
     * @return $this Fluent Builder
     */
    public function setIfMatch(string $ifMatch): self
    {
        $this->options['ifMatch'] = $ifMatch;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Sync.V1.UpdateDocumentOptions ' . $options . ']';
    }
}

