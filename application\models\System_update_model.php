<?php
defined('BASEPATH') or exit('No direct script access allowed');

class System_update_model extends MY_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->load->library('user_agent');
    }

    public function get_update_info()
    {
        // Purchase code verification removed for development - return mock update info
        return json_encode([
            'status' => true,
            'latest_version' => '7.0.0',
            'current_version' => $this->get_current_db_version(),
            'message' => 'Development mode - updates disabled'
        ]);
    }

    public function getPurchaseCode()
    {
        // Purchase code functionality removed for development
        return array(
            'purchase_username' => "development",
            'purchase_code' => "dev-mode",
        );
    }

    public function get_current_db_version()
    {
        $this->db->limit(1);
        return $this->db->get('migrations')->row()->version;
    }

    public function getIP()
    {
        $client = @$_SERVER['HTTP_CLIENT_IP'];
        $forward = @$_SERVER['HTTP_X_FORWARDED_FOR'];
        $remote = $_SERVER['REMOTE_ADDR'];
        if (filter_var($client, FILTER_VALIDATE_IP)) {
            $ip = $client;
        } elseif (filter_var($forward, FILTER_VALIDATE_IP)) {
            $ip = $forward;
        } else {
            $ip = ($remote == "::1" ? "127.0.0.1" : $remote);
        }
        return $ip;
    }

    public function is_connected($host = 'www.google.com')
    {
        $connected = @fsockopen($host, 80);
        //website, port  (try 80 or 443)
        if ($connected) {
            $is_conn = true; //action when connected
            fclose($connected);
        } else {
            $is_conn = false; //action in connection failure
        }
        return $is_conn;
    }

    public function upgrade_database_silent()
    {
        $this->load->config('migration');
        $this->load->library('migration', array(
            'migration_enabled' => true,
            'migration_type' => $this->config->item('migration_type'),
            'migration_table' => $this->config->item('migration_table'),
            'migration_auto_latest' => $this->config->item('migration_auto_latest'),
            'migration_version' => $this->config->item('migration_version'),
            'migration_path' => $this->config->item('migration_path'),
        ));
        if ($this->migration->current() === false) {
            return array(
                'success' => 0,
                'message' => $this->migration->error_string(),
            );
        } else {
            return array(
                'success' => 1,
            );
        }
    }

    public function addonChk()
    {
        $this->db->select('prefix,version');
        $addons = $this->db->get('addon')->result();
        $data = [];
        $result = "";
        if (!empty($addons)) {
            foreach ($addons as $key => $value) {
                $data[] = $value->prefix ."-". $value->version; 
            }
        }
        if (is_array($data) && !empty($data)) {
            $result = implode("||", $data);
        }
        return $result;
    }
}
