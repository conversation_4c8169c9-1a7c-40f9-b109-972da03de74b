/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/license
*/

/*
dialog.css
============

This file styles dialogs and all widgets available inside of it (tabs, buttons,
fields, etc.).

Dialogs are a complex system because they're very flexible. The CKEditor API
makes it easy to create and customize dialogs by code, by making use of several
different widgets inside its contents.

All dialogs share a main dialog strucuture, which can be visually represented
as follows:

+-- .cke_dialog -------------------------------------------------+
| +-- .cke_dialog_body ----------------------------------------+ |
| | +-- .cke_dialog_title --+ +-- .cke_dialog_close_button --+ | |
| | |                       | |                              | | |
| | +-----------------------+ +------------------------------+ | |
| | +-- .cke_dialog_tabs ------------------------------------+ | |
| | |                                                        | | |
| | +--------------------------------------------------------+ | |
| | +-- .cke_dialog_contents --------------------------------+ | |
| | | +-- .cke_dialog_contents_body -----------------------+ | | |
| | | |                                                    | | | |
| | | +----------------------------------------------------+ | | |
| | | +-- .cke_dialog_footer ------------------------------+ | | |
| | | |                                                    | | | |
| | | +----------------------------------------------------+ | | |
| | +--------------------------------------------------------+ | |
| +------------------------------------------------------------+ |
+----------------------------------------------------------------+

Comments in this file will give more details about each of the above blocks.
*/
@import 'partials/variables';

/* The outer container of the dialog. */
.cke_dialog
{
	/* Mandatory: Because the dialog.css file is loaded on demand, we avoid
		showing an unstyled dialog by hidding it. Here, we restore its visibility. */
	visibility: visible;
}

/* The inner boundary container. */
.cke_dialog_body
{
	z-index: 1;
	background: $color-dialog;
}

/* Due to our reset we have to recover the styles of some elements. */
.cke_dialog strong
{
	font-weight: bold;
}

/* The dialog title. */
.cke_dialog_title
{
	font-weight: bold;
	font-size: 12px;
	cursor: move;
	position: relative;

	color: $color-label;

	border-bottom: 1px solid $color-border;
	padding: 12px 19px 12px 12px;

	background: $color-dialog-title;
	letter-spacing: 0.3px;
}

.cke_dialog_spinner
{
	border-radius: 50%;

	width: 12px;
	height: 12px;
	overflow: hidden;

	text-indent: -9999em;

	border: 2px solid rgba(102, 102, 102, 0.2);
	border-left-color: rgba(102, 102, 102, 1);

	-webkit-animation: dialog_spinner 1s infinite linear;
	animation: dialog_spinner 1s infinite linear;
}

/* A GIF fallback for IE8 and IE9 which does not support CSS animations. */
.cke_browser_ie8 .cke_dialog_spinner,
.cke_browser_ie9 .cke_dialog_spinner
{
	background: url(images/spinner.gif) center top no-repeat;
	width: 16px;
	height: 16px;
	border: 0;
}

@-webkit-keyframes dialog_spinner
{
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes dialog_spinner
{
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

/* The outer part of the dialog contents, which contains the contents body
   and the footer. */
.cke_dialog_contents
{
	background-color: $color-dialog;
	overflow: auto;
	padding: 15px 10px 5px 10px;
	margin-top: 43px;
	border-top: 1px solid $color-border;
	color: $color-text;
}

/* The contents body part, which will hold all elements available in the dialog. */
.cke_dialog_contents_body
{
	overflow: auto;
	padding: 9px 10px 5px 10px;
	margin-top: 22px;
}

/* The dialog footer, which usually contains the "Ok" and "Cancel" buttons as
   well as a resize handler. */
.cke_dialog_footer
{
	text-align: right;
	position: relative;
	border-top: 1px solid $color-border;
	background: $color-dialog-title;
}

.cke_rtl .cke_dialog_footer
{
	text-align: left;
}

.cke_hc .cke_dialog_footer
{
	outline: none;
	border-top: 1px solid $color-border;
}

.cke_dialog .cke_resizer
{
	margin-top: 22px;
}

.cke_dialog .cke_resizer_rtl
{
	margin-left: 5px;
}

.cke_dialog .cke_resizer_ltr
{
	margin-right: 5px;
}

/*
Dialog tabs
-------------

Tabs are presented on some of the dialogs to make it possible to have its
contents split on different groups, visible one after the other.

The main element that holds the tabs can be made hidden, in case of no tabs
available.

The following is the visual representation of the tabs block:

+-- .cke_dialog_tabs ------------------------------------+
|  +-- .cke_dialog_tab --+ +-- .cke_dialog_tab --+ ...   |
|  |                     | |                     |       |
|  +---------------------+ +---------------------+       |
+--------------------------------------------------------+

The .cke_dialog_tab_selected class is appended to the active tab.
*/

/* The main tabs container. */
.cke_dialog_tabs
{
	height: 33px;
	display: inline-block;
	margin: 9px 0 0;
	position: absolute;
	z-index: 2;
	left: 11px;
}

.cke_rtl .cke_dialog_tabs
{
	left: auto;
	right: 11px;
}

/* A single tab (an <a> element). */
a.cke_dialog_tab
{
	height: 25px;
	padding: 4px 8px;
	display: inline-block;
	cursor: pointer;
	line-height: 26px;
	outline: none;
	color: #484848;
	border: 1px solid $color-border;

	border-radius: 3px 3px 0 0;

	background: $color-ui;
	min-width: 90px;
	text-align: center;

	margin-left: -1px; /* Use negative margin to create 1px border between tabs. */
	letter-spacing: 0.3px;
}

/* A hover state of a regular inactive tab. */
a.cke_dialog_tab:hover
{
	border: 1px solid $color-border;
	background-color: $color-dialog;
}

a.cke_dialog_tab:focus
{
	border: 1px solid $color-border;
	border-bottom-color: #139ff7;
	padding: 3px 7px;
	position: relative; /* Puts hovered tabs on top so both left and right borders are visible.*/
	z-index: 1;
}

/* Single selected tab. */
a.cke_dialog_tab_selected
{
	border: 1px solid $color-border;
	background: $color-dialog-title;
	color: $color-label;
	border-bottom-color: $color-label;
	cursor: default;
	filter: none;
}

/* A hover state for selected tab. */
a.cke_dialog_tab_selected:hover,
a.cke_dialog_tab_selected:focus
{
	border-bottom-color: $color-label;
}

.cke_hc a.cke_dialog_tab:hover,
.cke_hc a.cke_dialog_tab:focus,
.cke_hc a.cke_dialog_tab_selected
{
	border: 3px solid;
	padding: 2px 6px;
}

a.cke_dialog_tab_disabled
{
	background-color: $color-dialog;
	color: rgba(0,0,0,0.2);
	cursor: default;
}

/* The .cke_single_page class is appended to the dialog outer element in case
   of dialogs that has no tabs. */
.cke_single_page .cke_dialog_tabs
{
	display: none;
}

.cke_single_page .cke_dialog_contents
{
	padding-top: 5px;
	margin-top: 0;
	border-top: none;
}

/* The close button at the top of the dialog. */
a.cke_dialog_close_button
{
	background-image: url(images/close.png);
	background-repeat: no-repeat;
	background-position: 50%;
	position: absolute;
	cursor: pointer;
	text-align: center;
	height: 16px;
	width: 16px;
	top: 11px;
	z-index: 5;

	opacity: 0.7;
	filter: alpha(opacity = 70);
}

.cke_rtl .cke_dialog_close_button
{
	left: 12px;
}

.cke_ltr .cke_dialog_close_button
{
	right: 12px;
}

.cke_hc a.cke_dialog_close_button {
	background-image: none;
}

.cke_hidpi a.cke_dialog_close_button
{
	background-image: url(images/hidpi/close.png);
	background-size: 16px;
}

a.cke_dialog_close_button:hover
{
	opacity: 1;
	filter: alpha(opacity = 100);
}

a.cke_dialog_close_button span
{
	display: none;
}

.cke_hc a.cke_dialog_close_button span
{
	display: inline;
	cursor: pointer;
	font-weight: bold;
	position: relative;
	top: 3px;
}

/*
Dialog UI Elements
--------------------

The remaining styles define the UI elements that can be used inside dialog
contents.

Most of the UI elements on dialogs contain a textual label. All of them share
the same labelling structure, having the label text inside an element with
.cke_dialog_ui_labeled_label and the element specific part inside the
.cke_dialog_ui_labeled_content class.
*/

/* If an element is supposed to be disabled, the .cke_disabled class is
   appended to it. */
div.cke_disabled .cke_dialog_ui_labeled_content div *
{
	background-color: #ddd;
	cursor: default;
}

/*
Horizontal-Box and Vertical-Box
---------------------------------

There are basic layou element used by the editor to properly align elements in
the dialog. They're basically tables that have each cell filled by UI elements.

The following is the visual representation of a H-Box:

+-- .cke_dialog_ui_hbox --------------------------------------------------------------------------------+
|  +-- .cke_dialog_ui_hbox_first --+ +-- .cke_dialog_ui_hbox_child --+ +-- .cke_dialog_ui_hbox_last --+ |
|  +                               + +                               + +                              + |
|  +-------------------------------+ +-------------------------------+ +------------------------------+ |
+-------------------------------------------------------------------------------------------------------+

It is possible to have nested V/H-Boxes.
*/

.cke_dialog_ui_vbox table,
.cke_dialog_ui_hbox table
{
	margin: auto;
}

.cke_dialog_ui_vbox_child
{
	padding: 5px 0px;
}

.cke_dialog_ui_hbox
{
	width: 100%;
	margin-top: 12px;
}

.cke_dialog_ui_hbox_first,
.cke_dialog_ui_hbox_child,
.cke_dialog_ui_hbox_last
{
	vertical-align: top;
}

.cke_ltr .cke_dialog_ui_hbox_first,
.cke_ltr .cke_dialog_ui_hbox_child
{
	padding-right: 10px;
}

.cke_rtl .cke_dialog_ui_hbox_first,
.cke_rtl .cke_dialog_ui_hbox_child
{
	padding-left: 10px;
}

.cke_ltr .cke_dialog_footer_buttons .cke_dialog_ui_hbox_first,
.cke_ltr .cke_dialog_footer_buttons .cke_dialog_ui_hbox_child
{
	padding-right: 5px;
}

.cke_rtl .cke_dialog_footer_buttons .cke_dialog_ui_hbox_first,
.cke_rtl .cke_dialog_footer_buttons .cke_dialog_ui_hbox_child
{
	padding-left: 5px;
	padding-right: 0;
}

/* Applies to all labeled dialog fields */
.cke_hc div.cke_dialog_ui_input_text,
.cke_hc div.cke_dialog_ui_input_password,
.cke_hc div.cke_dialog_ui_input_textarea,
.cke_hc div.cke_dialog_ui_input_select,
.cke_hc div.cke_dialog_ui_input_file
{
	border: 1px solid;
}

/*
Text Input
------------

The basic text field to input text.

+-- .cke_dialog_ui_text --------------------------+
|  +-- .cke_dialog_ui_labeled_label ------------+ |
|  |                                            | |
|  +--------------------------------------------+ |
|  +-- .cke_dialog_ui_labeled_content ----------+ |
|  | +-- div.cke_dialog_ui_input_text --------+ | |
|  | | +-- input.cke_dialog_ui_input_text --+ | | |
|  | | |                                    | | | |
|  | | +------------------------------------+ | | |
|  | +----------------------------------------+ | |
|  +--------------------------------------------+ |
+-------------------------------------------------+
*/

/*
Textarea
----------

The textarea field to input larger text.

+-- .cke_dialog_ui_textarea --------------------------+
|  +-- .cke_dialog_ui_labeled_label ----------------+ |
|  |                                                | |
|  +------------------------------------------------+ |
|  +-- .cke_dialog_ui_labeled_content --------------+ |
|  | +-- div.cke_dialog_ui_input_textarea --------+ | |
|  | | +-- input.cke_dialog_ui_input_textarea --+ | | |
|  | | |                                        | | | |
|  | | +----------------------------------------+ | | |
|  | +--------------------------------------------+ | |
|  +------------------------------------------------+ |
+-----------------------------------------------------+
*/

textarea.cke_dialog_ui_input_textarea
{
	overflow: auto;
	resize: none;
}

input.cke_dialog_ui_input_text,
input.cke_dialog_ui_input_password,
textarea.cke_dialog_ui_input_textarea,
select.cke_dialog_ui_input_select
{
	background-color: $color-dialog-title !important;
	border: 1px solid rgba(0,0,0,0.2) !important;
	color: $color-text;
	padding: 4px 6px;
	outline: none;
	width: 100%;
	*width: 95%;

	box-sizing: border-box;

	border-radius: 2px;

	min-height: 28px;
	margin-left: 1px;
}

input.cke_dialog_ui_input_text:hover,
input.cke_dialog_ui_input_password:hover,
textarea.cke_dialog_ui_input_textarea:hover
{
	border: 1px solid #aeb3b9;
}

input.cke_dialog_ui_input_text:focus,
input.cke_dialog_ui_input_password:focus,
textarea.cke_dialog_ui_input_textarea:focus,
select.cke_dialog_ui_input_select:focus
{
	outline: none;
	border: 2px solid #139ff7;
}

input.cke_dialog_ui_input_text:focus
{
	padding-left: 5px;
}

textarea.cke_dialog_ui_input_textarea:focus
{
	padding: 3px 5px;
}

select.cke_dialog_ui_input_select:focus
{
	margin: 0;
	width: 100% !important;
}

input.cke_dialog_ui_checkbox_input,
input.cke_dialog_ui_radio_input
{
	margin-left: 1px;
	margin-right: 2px;
}

input.cke_dialog_ui_checkbox_input:focus,
input.cke_dialog_ui_checkbox_input:active,
input.cke_dialog_ui_radio_input:focus,
input.cke_dialog_ui_radio_input:active
{
	border: none;
	outline: 2px solid #139ff7;
}

/*
Button
--------

The buttons used in the dialog footer or inside the contents.

+-- a.cke_dialog_ui_button -----------+
|  +-- span.cke_dialog_ui_button --+  |
|  |                               |  |
|  +-------------------------------+  |
+-------------------------------------+
*/

/* The outer part of the button. */
a.cke_dialog_ui_button
{
	display: inline-block;
	*display: inline;
	*zoom: 1;

	padding: 4px 1px;
	margin: 0;

	text-align: center;
	color: #484848;
	vertical-align: middle;
	cursor: pointer;

	border: 1px solid #bcbcbc;
	border-radius: 2px;

	background: #f8f8f8;
	letter-spacing: 0.3px;
	line-height: 18px;
	box-sizing: border-box;

}

.cke_hc a.cke_dialog_ui_button {
	border-width: 3px;
}


span.cke_dialog_ui_button
{
	padding: 0 10px;
	cursor: pointer;
}

a.cke_dialog_ui_button:hover
{
	background: #fff;
}

/* 	:focus/:active styles for dialog buttons: regular and footer. */
a.cke_dialog_ui_button:focus,
a.cke_dialog_ui_button:active
{
	border: 2px solid #139ff7;
	outline: none;
	padding: 3px 0;
}

.cke_hc a.cke_dialog_ui_button:hover,
.cke_hc a.cke_dialog_ui_button:focus,
.cke_hc a.cke_dialog_ui_button:active
{
	border: 3px solid;
}

/* The inner part of the button (both in dialog tabs and dialog footer). */
.cke_dialog_footer_buttons a.cke_dialog_ui_button span
{
	color: inherit;
	font-size: 12px;
	font-weight: bold;
	padding: 0 12px;
}

/* Special class appended to the Ok button. */
a.cke_dialog_ui_button_ok
{
	color: #fff;
	background: #09863E;
	border: 1px solid #09863E;
}

.cke_hc a.cke_dialog_ui_button {
	border: 3px solid #bcbcbc;
}


a.cke_dialog_ui_button_ok:hover
{
	background: #53AA78;
	border-color: #53AA78;
}

a.cke_dialog_ui_button_ok:focus
{
	box-shadow: inset 0 0 0 2px #FFF;
}

a.cke_dialog_ui_button_ok:focus,
a.cke_dialog_ui_button_ok:active
{
	border-color: #139ff7;
}

.cke_hc a.cke_dialog_ui_button_ok:hover,
.cke_hc a.cke_dialog_ui_button_ok:focus,
.cke_hc a.cke_dialog_ui_button_ok:active
{
	border-color: #484848;
}

a.cke_dialog_ui_button_ok.cke_disabled
{
	background: #d1d1d1;
	border-color: #d1d1d1;
	cursor: default;
}

a.cke_dialog_ui_button_ok.cke_disabled span
{
	cursor: default;
}

/* A special container that holds the footer buttons. */
.cke_dialog_footer_buttons
{
	display: inline-table;
	margin: 5px;
	width: auto;
	position: relative;
	vertical-align: middle;
}

/* Styles for other dialog element types. */
div.cke_dialog_ui_input_select
{
	display: table;
}

select.cke_dialog_ui_input_select
{
	height: 28px;
	line-height: 28px;

	padding: 3px 3px 3px 6px;
	outline: none;
	border-radius: 2px;
	margin: 0 1px;
	box-sizing: border-box;
	width: calc(100% - 2px) !important;
}

.cke_dialog_ui_input_file
{
	width: 100%;
	height: 25px;
}

.cke_hc .cke_dialog_ui_labeled_content input:focus,
.cke_hc .cke_dialog_ui_labeled_content select:focus,
.cke_hc .cke_dialog_ui_labeled_content textarea:focus
{
	outline: 1px dotted;
}

.cke_dialog_ui_labeled_label
{
	margin-left: 1px;
}

/*
 * Some utility CSS classes for dialog authors.
 */
.cke_dialog .cke_dark_background
{
	/* In moono-lisa .cke_dark_background in a dialog is transparent.
		Done this way to be consistent in all plugins. */
	background-color: transparent;
}

.cke_dialog .cke_light_background
{
	background-color: #EBEBEB;
}

.cke_dialog .cke_centered
{
	text-align: center;
}

.cke_dialog a.cke_btn_reset
{
	float: right;
	background: url(images/refresh.png) top left no-repeat;
	width: 16px;
	height: 16px;
	border: 1px none;
	font-size: 1px;
}

.cke_hidpi .cke_dialog a.cke_btn_reset
{
	background-size: 16px;
	background-image: url(images/hidpi/refresh.png);
}

.cke_rtl .cke_dialog a.cke_btn_reset
{
	float: left;
}

.cke_dialog a.cke_btn_locked,
.cke_dialog a.cke_btn_unlocked
{
	float: left;
	width: 16px;
	height: 16px;
	background-repeat: no-repeat;
	border: none 1px;
	font-size: 1px;
}

.cke_dialog a.cke_btn_locked,
.cke_dialog a.cke_btn_unlocked,
.cke_dialog a.cke_btn_reset
{
	margin: 2px;
}

.cke_dialog a.cke_btn_locked
{
	background-image: url(images/lock.png);
}

.cke_dialog a.cke_btn_unlocked
{
	background-image: url(images/lock-open.png);
}

.cke_rtl .cke_dialog a.cke_btn_locked,
.cke_rtl .cke_dialog a.cke_btn_unlocked
{
	float: right;
}

.cke_hidpi .cke_dialog a.cke_btn_unlocked,
.cke_hidpi .cke_dialog a.cke_btn_locked
{
	background-size: 16px;
}

.cke_hidpi .cke_dialog a.cke_btn_locked
{
	background-image: url(images/hidpi/lock.png);
}

.cke_hidpi .cke_dialog a.cke_btn_unlocked
{
	background-image: url(images/hidpi/lock-open.png);
}

.cke_dialog a.cke_btn_locked .cke_icon
{
	display: none;
}

.cke_dialog a.cke_btn_over,
.cke_dialog a.cke_btn_locked:hover,
.cke_dialog a.cke_btn_locked:focus,
.cke_dialog a.cke_btn_locked:active,
.cke_dialog a.cke_btn_unlocked:hover,
.cke_dialog a.cke_btn_unlocked:focus,
.cke_dialog a.cke_btn_unlocked:active,
.cke_dialog a.cke_btn_reset:hover,
.cke_dialog a.cke_btn_reset:focus,
.cke_dialog a.cke_btn_reset:active
{
	cursor: pointer;
	outline: none;
	margin: 0;
	border: 2px solid #139FF7;
}

.cke_dialog fieldset
{
	border: 1px solid #bcbcbc;
}

.cke_dialog fieldset legend
{
	padding: 0 6px;
}

.cke_dialog_ui_checkbox,
.cke_dialog fieldset .cke_dialog_ui_vbox .cke_dialog_ui_checkbox {
	display: inline-block;
}

.cke_dialog fieldset .cke_dialog_ui_vbox .cke_dialog_ui_checkbox {
	padding-top: 5px;
}

.cke_dialog_ui_checkbox .cke_dialog_ui_checkbox_input,
.cke_dialog_ui_checkbox .cke_dialog_ui_checkbox_input + label,
.cke_dialog fieldset .cke_dialog_ui_vbox .cke_dialog_ui_checkbox .cke_dialog_ui_checkbox_input,
.cke_dialog fieldset .cke_dialog_ui_vbox .cke_dialog_ui_checkbox .cke_dialog_ui_checkbox_input + label {
	vertical-align: middle;
}

/*
The rest of the file contains style used on several common plugins. There is a
tendency that these will be moved to the plugins code in the future.
*/
.cke_dialog .ImagePreviewBox
{
	border: 1px ridge #bcbcbc;
	overflow: scroll;
	height: 200px;
	width: 300px;
	padding: 2px;
	background-color: white;
}

.cke_dialog .ImagePreviewBox table td
{
	white-space: normal;
}

.cke_dialog  .ImagePreviewLoader
{
	position: absolute;
	white-space: normal;
	overflow: hidden;
	height: 160px;
	width: 230px;
	margin: 2px;
	padding: 2px;
	opacity: 0.9;
	filter: alpha(opacity = 90);

	background-color: #e4e4e4;
}

.cke_dialog .FlashPreviewBox
{
	white-space: normal;
	border: 1px solid #bcbcbc;
	overflow: auto;
	height: 160px;
	width: 390px;
	padding: 2px;
	background-color: white;
}

.cke_dialog .cke_pastetext
{
	width: 346px;
	height: 170px;
}

.cke_dialog .cke_pastetext textarea
{
	width: 340px;
	height: 170px;
	resize: none;
}

.cke_dialog iframe.cke_pasteframe
{
	width: 346px;
	height: 130px;
	background-color: white;
	border: 1px solid #aeb3b9;
	border-radius: 3px;
}

.cke_dialog .cke_hand
{
	cursor: pointer;
}

.cke_disabled
{
	color: #a0a0a0;
}

.cke_dialog_body .cke_label
{
	display: none;
}

.cke_dialog_body label
{
	color: $color-label;
	display: inline;
	cursor: default;
	letter-spacing: 0.3px;
}

.cke_dialog_body label + .cke_dialog_ui_labeled_content
{
	margin-top: 2px;
}

.cke_dialog_contents_body .cke_dialog_ui_text,
.cke_dialog_contents_body .cke_dialog_ui_select,
/* Find Dialog */
.cke_dialog_contents_body .cke_dialog_ui_hbox_last > a.cke_dialog_ui_button
{
	margin-top: 4px;
}

a.cke_smile
{
	overflow: hidden;
	display: block;
	text-align: center;
	padding: 0.3em 0;
}

a.cke_smile img
{
	vertical-align: middle;
}

a.cke_specialchar
{
	cursor: inherit;
	display: block;
	height: 1.25em;
	padding: 0.2em 0.3em;
	text-align: center;
}

a.cke_smile,
a.cke_specialchar
{
	border: 2px solid transparent;
}

a.cke_smile:hover,
a.cke_smile:focus,
a.cke_smile:active,
a.cke_specialchar:hover,
a.cke_specialchar:focus,
a.cke_specialchar:active
{
	background: #fff;
	outline: 0;
}

a.cke_smile:hover,
a.cke_specialchar:hover
{
	border-color: #888;
}

a.cke_smile:focus,
a.cke_smile:active,
a.cke_specialchar:focus,
a.cke_specialchar:active
{
	border-color: #139FF7;
}

/**
 * Styles specific to "cellProperties" dialog.
 */

.cke_dialog_contents a.colorChooser
{
	display: block;
	margin-top: 6px;
	margin-left: 10px;
	width: 80px;
}

.cke_rtl .cke_dialog_contents a.colorChooser
{
	margin-right: 10px;
}

.cke_iframe_shim
{
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	z-index: -1;
	filter: alpha(opacity = 0);
	width: 100%;
	height: 100%;
}

.cke_dialog_contents_body .cke_accessibility_legend
{
	margin: 2px 7px 2px 2px;
}

.cke_dialog_contents_body .cke_accessibility_legend:focus,
.cke_dialog_contents_body .cke_accessibility_legend:active
{
	outline: none;
	border: 2px solid #139FF7;
	margin: 0 5px 0 0;
}

.cke_dialog_contents_body input[type=file]:focus,
.cke_dialog_contents_body input[type=file]:active
{
	border: 2px solid #139FF7;
}

/* Specific dialog styles. Overridden in css to not break plugins compatibility with other skins. */
.cke_dialog_find_fieldset
{
	margin-top: 10px !important;
}

.cke_dialog_image_ratiolock
{
	margin-top: 52px !important;
}

.cke_dialog_forms_select_order label.cke_dialog_ui_labeled_label
{
	margin-left: 0;
}

.cke_dialog_forms_select_order div.cke_dialog_ui_input_select
{
	width: 100%;
}

.cke_dialog_forms_select_order_txtsize .cke_dialog_ui_hbox_last
{
	padding-top: 4px;
}

.cke_dialog_image_url .cke_dialog_ui_hbox_last,
.cke_dialog_flash_url .cke_dialog_ui_hbox_last
{
	vertical-align: bottom;
}

a.cke_dialog_ui_button.cke_dialog_image_browse
{
	margin-top: 10px;
}

/* Templates plugin. */
.cke_dialog_contents_body .cke_tpl_list
{
	border: #bcbcbc 1px solid;
	margin: 1px;
}

.cke_dialog_contents_body .cke_tpl_list:focus,
.cke_dialog_contents_body .cke_tpl_list:active
{
	outline: none;
	margin: 0;
	border: 2px solid #139FF7;
}

.cke_dialog_contents_body .cke_tpl_list a:focus,
.cke_dialog_contents_body .cke_tpl_list a:active
{
	outline: none;
}

.cke_dialog_contents_body .cke_tpl_list a:focus .cke_tpl_item,
.cke_dialog_contents_body .cke_tpl_list a:active .cke_tpl_item
{
	border: 2px solid #139FF7;
	padding: 6px;
}

.cke_about_container {
	color: $color-text !important;
}