<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Chat
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Chat\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ServiceOptions
{




    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $defaultServiceRoleSid The service role assigned to users when they are added to the service. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     * @param string $defaultChannelRoleSid The channel role assigned to users when they are added to a channel. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     * @param string $defaultChannelCreatorRoleSid The channel role assigned to a channel creator when they join a new channel. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     * @param bool $readStatusEnabled Whether to enable the [Message Consumption Horizon](https://www.twilio.com/docs/chat/consumption-horizon) feature. The default is `true`.
     * @param bool $reachabilityEnabled Whether to enable the [Reachability Indicator](https://www.twilio.com/docs/chat/reachability-indicator) for this Service instance. The default is `false`.
     * @param int $typingIndicatorTimeout How long in seconds after a `started typing` event until clients should assume that user is no longer typing, even if no `ended typing` message was received.  The default is 5 seconds.
     * @param int $consumptionReportInterval DEPRECATED. The interval in seconds between consumption reports submission batches from client endpoints.
     * @param bool $notificationsNewMessageEnabled Whether to send a notification when a new message is added to a channel. Can be: `true` or `false` and the default is `false`.
     * @param string $notificationsNewMessageTemplate The template to use to create the notification text displayed when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
     * @param bool $notificationsAddedToChannelEnabled Whether to send a notification when a member is added to a channel. Can be: `true` or `false` and the default is `false`.
     * @param string $notificationsAddedToChannelTemplate The template to use to create the notification text displayed when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
     * @param bool $notificationsRemovedFromChannelEnabled Whether to send a notification to a user when they are removed from a channel. Can be: `true` or `false` and the default is `false`.
     * @param string $notificationsRemovedFromChannelTemplate The template to use to create the notification text displayed to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
     * @param bool $notificationsInvitedToChannelEnabled Whether to send a notification when a user is invited to a channel. Can be: `true` or `false` and the default is `false`.
     * @param string $notificationsInvitedToChannelTemplate The template to use to create the notification text displayed when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
     * @param string $preWebhookUrl The URL for pre-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/api/chat/webhooks) for more details.
     * @param string $postWebhookUrl The URL for post-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/api/chat/webhooks) for more details.
     * @param string $webhookMethod The HTTP method to use for calls to the `pre_webhook_url` and `post_webhook_url` webhooks.  Can be: `POST` or `GET` and the default is `POST`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
     * @param string[] $webhookFilters The list of WebHook events that are enabled for this Service instance. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
     * @param string $webhooksOnMessageSendUrl The URL of the webhook to call in response to the `on_message_send` event using the `webhooks.on_message_send.method` HTTP method.
     * @param string $webhooksOnMessageSendMethod The HTTP method to use when calling the `webhooks.on_message_send.url`.
     * @param string $webhooksOnMessageUpdateUrl The URL of the webhook to call in response to the `on_message_update` event using the `webhooks.on_message_update.method` HTTP method.
     * @param string $webhooksOnMessageUpdateMethod The HTTP method to use when calling the `webhooks.on_message_update.url`.
     * @param string $webhooksOnMessageRemoveUrl The URL of the webhook to call in response to the `on_message_remove` event using the `webhooks.on_message_remove.method` HTTP method.
     * @param string $webhooksOnMessageRemoveMethod The HTTP method to use when calling the `webhooks.on_message_remove.url`.
     * @param string $webhooksOnChannelAddUrl The URL of the webhook to call in response to the `on_channel_add` event using the `webhooks.on_channel_add.method` HTTP method.
     * @param string $webhooksOnChannelAddMethod The HTTP method to use when calling the `webhooks.on_channel_add.url`.
     * @param string $webhooksOnChannelDestroyUrl The URL of the webhook to call in response to the `on_channel_destroy` event using the `webhooks.on_channel_destroy.method` HTTP method.
     * @param string $webhooksOnChannelDestroyMethod The HTTP method to use when calling the `webhooks.on_channel_destroy.url`.
     * @param string $webhooksOnChannelUpdateUrl The URL of the webhook to call in response to the `on_channel_update` event using the `webhooks.on_channel_update.method` HTTP method.
     * @param string $webhooksOnChannelUpdateMethod The HTTP method to use when calling the `webhooks.on_channel_update.url`.
     * @param string $webhooksOnMemberAddUrl The URL of the webhook to call in response to the `on_member_add` event using the `webhooks.on_member_add.method` HTTP method.
     * @param string $webhooksOnMemberAddMethod The HTTP method to use when calling the `webhooks.on_member_add.url`.
     * @param string $webhooksOnMemberRemoveUrl The URL of the webhook to call in response to the `on_member_remove` event using the `webhooks.on_member_remove.method` HTTP method.
     * @param string $webhooksOnMemberRemoveMethod The HTTP method to use when calling the `webhooks.on_member_remove.url`.
     * @param string $webhooksOnMessageSentUrl The URL of the webhook to call in response to the `on_message_sent` event using the `webhooks.on_message_sent.method` HTTP method.
     * @param string $webhooksOnMessageSentMethod The URL of the webhook to call in response to the `on_message_sent` event`.
     * @param string $webhooksOnMessageUpdatedUrl The URL of the webhook to call in response to the `on_message_updated` event using the `webhooks.on_message_updated.method` HTTP method.
     * @param string $webhooksOnMessageUpdatedMethod The HTTP method to use when calling the `webhooks.on_message_updated.url`.
     * @param string $webhooksOnMessageRemovedUrl The URL of the webhook to call in response to the `on_message_removed` event using the `webhooks.on_message_removed.method` HTTP method.
     * @param string $webhooksOnMessageRemovedMethod The HTTP method to use when calling the `webhooks.on_message_removed.url`.
     * @param string $webhooksOnChannelAddedUrl The URL of the webhook to call in response to the `on_channel_added` event using the `webhooks.on_channel_added.method` HTTP method.
     * @param string $webhooksOnChannelAddedMethod The URL of the webhook to call in response to the `on_channel_added` event`.
     * @param string $webhooksOnChannelDestroyedUrl The URL of the webhook to call in response to the `on_channel_added` event using the `webhooks.on_channel_destroyed.method` HTTP method.
     * @param string $webhooksOnChannelDestroyedMethod The HTTP method to use when calling the `webhooks.on_channel_destroyed.url`.
     * @param string $webhooksOnChannelUpdatedUrl The URL of the webhook to call in response to the `on_channel_updated` event using the `webhooks.on_channel_updated.method` HTTP method.
     * @param string $webhooksOnChannelUpdatedMethod The HTTP method to use when calling the `webhooks.on_channel_updated.url`.
     * @param string $webhooksOnMemberAddedUrl The URL of the webhook to call in response to the `on_channel_updated` event using the `webhooks.on_channel_updated.method` HTTP method.
     * @param string $webhooksOnMemberAddedMethod The HTTP method to use when calling the `webhooks.on_channel_updated.url`.
     * @param string $webhooksOnMemberRemovedUrl The URL of the webhook to call in response to the `on_member_removed` event using the `webhooks.on_member_removed.method` HTTP method.
     * @param string $webhooksOnMemberRemovedMethod The HTTP method to use when calling the `webhooks.on_member_removed.url`.
     * @param int $limitsChannelMembers The maximum number of Members that can be added to Channels within this Service. Can be up to 1,000.
     * @param int $limitsUserChannels The maximum number of Channels Users can be a Member of within this Service. Can be up to 1,000.
     * @return UpdateServiceOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $defaultServiceRoleSid = Values::NONE,
        string $defaultChannelRoleSid = Values::NONE,
        string $defaultChannelCreatorRoleSid = Values::NONE,
        bool $readStatusEnabled = Values::BOOL_NONE,
        bool $reachabilityEnabled = Values::BOOL_NONE,
        int $typingIndicatorTimeout = Values::INT_NONE,
        int $consumptionReportInterval = Values::INT_NONE,
        bool $notificationsNewMessageEnabled = Values::BOOL_NONE,
        string $notificationsNewMessageTemplate = Values::NONE,
        bool $notificationsAddedToChannelEnabled = Values::BOOL_NONE,
        string $notificationsAddedToChannelTemplate = Values::NONE,
        bool $notificationsRemovedFromChannelEnabled = Values::BOOL_NONE,
        string $notificationsRemovedFromChannelTemplate = Values::NONE,
        bool $notificationsInvitedToChannelEnabled = Values::BOOL_NONE,
        string $notificationsInvitedToChannelTemplate = Values::NONE,
        string $preWebhookUrl = Values::NONE,
        string $postWebhookUrl = Values::NONE,
        string $webhookMethod = Values::NONE,
        array $webhookFilters = Values::ARRAY_NONE,
        string $webhooksOnMessageSendUrl = Values::NONE,
        string $webhooksOnMessageSendMethod = Values::NONE,
        string $webhooksOnMessageUpdateUrl = Values::NONE,
        string $webhooksOnMessageUpdateMethod = Values::NONE,
        string $webhooksOnMessageRemoveUrl = Values::NONE,
        string $webhooksOnMessageRemoveMethod = Values::NONE,
        string $webhooksOnChannelAddUrl = Values::NONE,
        string $webhooksOnChannelAddMethod = Values::NONE,
        string $webhooksOnChannelDestroyUrl = Values::NONE,
        string $webhooksOnChannelDestroyMethod = Values::NONE,
        string $webhooksOnChannelUpdateUrl = Values::NONE,
        string $webhooksOnChannelUpdateMethod = Values::NONE,
        string $webhooksOnMemberAddUrl = Values::NONE,
        string $webhooksOnMemberAddMethod = Values::NONE,
        string $webhooksOnMemberRemoveUrl = Values::NONE,
        string $webhooksOnMemberRemoveMethod = Values::NONE,
        string $webhooksOnMessageSentUrl = Values::NONE,
        string $webhooksOnMessageSentMethod = Values::NONE,
        string $webhooksOnMessageUpdatedUrl = Values::NONE,
        string $webhooksOnMessageUpdatedMethod = Values::NONE,
        string $webhooksOnMessageRemovedUrl = Values::NONE,
        string $webhooksOnMessageRemovedMethod = Values::NONE,
        string $webhooksOnChannelAddedUrl = Values::NONE,
        string $webhooksOnChannelAddedMethod = Values::NONE,
        string $webhooksOnChannelDestroyedUrl = Values::NONE,
        string $webhooksOnChannelDestroyedMethod = Values::NONE,
        string $webhooksOnChannelUpdatedUrl = Values::NONE,
        string $webhooksOnChannelUpdatedMethod = Values::NONE,
        string $webhooksOnMemberAddedUrl = Values::NONE,
        string $webhooksOnMemberAddedMethod = Values::NONE,
        string $webhooksOnMemberRemovedUrl = Values::NONE,
        string $webhooksOnMemberRemovedMethod = Values::NONE,
        int $limitsChannelMembers = Values::INT_NONE,
        int $limitsUserChannels = Values::INT_NONE

    ): UpdateServiceOptions
    {
        return new UpdateServiceOptions(
            $friendlyName,
            $defaultServiceRoleSid,
            $defaultChannelRoleSid,
            $defaultChannelCreatorRoleSid,
            $readStatusEnabled,
            $reachabilityEnabled,
            $typingIndicatorTimeout,
            $consumptionReportInterval,
            $notificationsNewMessageEnabled,
            $notificationsNewMessageTemplate,
            $notificationsAddedToChannelEnabled,
            $notificationsAddedToChannelTemplate,
            $notificationsRemovedFromChannelEnabled,
            $notificationsRemovedFromChannelTemplate,
            $notificationsInvitedToChannelEnabled,
            $notificationsInvitedToChannelTemplate,
            $preWebhookUrl,
            $postWebhookUrl,
            $webhookMethod,
            $webhookFilters,
            $webhooksOnMessageSendUrl,
            $webhooksOnMessageSendMethod,
            $webhooksOnMessageUpdateUrl,
            $webhooksOnMessageUpdateMethod,
            $webhooksOnMessageRemoveUrl,
            $webhooksOnMessageRemoveMethod,
            $webhooksOnChannelAddUrl,
            $webhooksOnChannelAddMethod,
            $webhooksOnChannelDestroyUrl,
            $webhooksOnChannelDestroyMethod,
            $webhooksOnChannelUpdateUrl,
            $webhooksOnChannelUpdateMethod,
            $webhooksOnMemberAddUrl,
            $webhooksOnMemberAddMethod,
            $webhooksOnMemberRemoveUrl,
            $webhooksOnMemberRemoveMethod,
            $webhooksOnMessageSentUrl,
            $webhooksOnMessageSentMethod,
            $webhooksOnMessageUpdatedUrl,
            $webhooksOnMessageUpdatedMethod,
            $webhooksOnMessageRemovedUrl,
            $webhooksOnMessageRemovedMethod,
            $webhooksOnChannelAddedUrl,
            $webhooksOnChannelAddedMethod,
            $webhooksOnChannelDestroyedUrl,
            $webhooksOnChannelDestroyedMethod,
            $webhooksOnChannelUpdatedUrl,
            $webhooksOnChannelUpdatedMethod,
            $webhooksOnMemberAddedUrl,
            $webhooksOnMemberAddedMethod,
            $webhooksOnMemberRemovedUrl,
            $webhooksOnMemberRemovedMethod,
            $limitsChannelMembers,
            $limitsUserChannels
        );
    }

}





class UpdateServiceOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $defaultServiceRoleSid The service role assigned to users when they are added to the service. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     * @param string $defaultChannelRoleSid The channel role assigned to users when they are added to a channel. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     * @param string $defaultChannelCreatorRoleSid The channel role assigned to a channel creator when they join a new channel. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     * @param bool $readStatusEnabled Whether to enable the [Message Consumption Horizon](https://www.twilio.com/docs/chat/consumption-horizon) feature. The default is `true`.
     * @param bool $reachabilityEnabled Whether to enable the [Reachability Indicator](https://www.twilio.com/docs/chat/reachability-indicator) for this Service instance. The default is `false`.
     * @param int $typingIndicatorTimeout How long in seconds after a `started typing` event until clients should assume that user is no longer typing, even if no `ended typing` message was received.  The default is 5 seconds.
     * @param int $consumptionReportInterval DEPRECATED. The interval in seconds between consumption reports submission batches from client endpoints.
     * @param bool $notificationsNewMessageEnabled Whether to send a notification when a new message is added to a channel. Can be: `true` or `false` and the default is `false`.
     * @param string $notificationsNewMessageTemplate The template to use to create the notification text displayed when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
     * @param bool $notificationsAddedToChannelEnabled Whether to send a notification when a member is added to a channel. Can be: `true` or `false` and the default is `false`.
     * @param string $notificationsAddedToChannelTemplate The template to use to create the notification text displayed when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
     * @param bool $notificationsRemovedFromChannelEnabled Whether to send a notification to a user when they are removed from a channel. Can be: `true` or `false` and the default is `false`.
     * @param string $notificationsRemovedFromChannelTemplate The template to use to create the notification text displayed to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
     * @param bool $notificationsInvitedToChannelEnabled Whether to send a notification when a user is invited to a channel. Can be: `true` or `false` and the default is `false`.
     * @param string $notificationsInvitedToChannelTemplate The template to use to create the notification text displayed when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
     * @param string $preWebhookUrl The URL for pre-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/api/chat/webhooks) for more details.
     * @param string $postWebhookUrl The URL for post-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/api/chat/webhooks) for more details.
     * @param string $webhookMethod The HTTP method to use for calls to the `pre_webhook_url` and `post_webhook_url` webhooks.  Can be: `POST` or `GET` and the default is `POST`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
     * @param string[] $webhookFilters The list of WebHook events that are enabled for this Service instance. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
     * @param string $webhooksOnMessageSendUrl The URL of the webhook to call in response to the `on_message_send` event using the `webhooks.on_message_send.method` HTTP method.
     * @param string $webhooksOnMessageSendMethod The HTTP method to use when calling the `webhooks.on_message_send.url`.
     * @param string $webhooksOnMessageUpdateUrl The URL of the webhook to call in response to the `on_message_update` event using the `webhooks.on_message_update.method` HTTP method.
     * @param string $webhooksOnMessageUpdateMethod The HTTP method to use when calling the `webhooks.on_message_update.url`.
     * @param string $webhooksOnMessageRemoveUrl The URL of the webhook to call in response to the `on_message_remove` event using the `webhooks.on_message_remove.method` HTTP method.
     * @param string $webhooksOnMessageRemoveMethod The HTTP method to use when calling the `webhooks.on_message_remove.url`.
     * @param string $webhooksOnChannelAddUrl The URL of the webhook to call in response to the `on_channel_add` event using the `webhooks.on_channel_add.method` HTTP method.
     * @param string $webhooksOnChannelAddMethod The HTTP method to use when calling the `webhooks.on_channel_add.url`.
     * @param string $webhooksOnChannelDestroyUrl The URL of the webhook to call in response to the `on_channel_destroy` event using the `webhooks.on_channel_destroy.method` HTTP method.
     * @param string $webhooksOnChannelDestroyMethod The HTTP method to use when calling the `webhooks.on_channel_destroy.url`.
     * @param string $webhooksOnChannelUpdateUrl The URL of the webhook to call in response to the `on_channel_update` event using the `webhooks.on_channel_update.method` HTTP method.
     * @param string $webhooksOnChannelUpdateMethod The HTTP method to use when calling the `webhooks.on_channel_update.url`.
     * @param string $webhooksOnMemberAddUrl The URL of the webhook to call in response to the `on_member_add` event using the `webhooks.on_member_add.method` HTTP method.
     * @param string $webhooksOnMemberAddMethod The HTTP method to use when calling the `webhooks.on_member_add.url`.
     * @param string $webhooksOnMemberRemoveUrl The URL of the webhook to call in response to the `on_member_remove` event using the `webhooks.on_member_remove.method` HTTP method.
     * @param string $webhooksOnMemberRemoveMethod The HTTP method to use when calling the `webhooks.on_member_remove.url`.
     * @param string $webhooksOnMessageSentUrl The URL of the webhook to call in response to the `on_message_sent` event using the `webhooks.on_message_sent.method` HTTP method.
     * @param string $webhooksOnMessageSentMethod The URL of the webhook to call in response to the `on_message_sent` event`.
     * @param string $webhooksOnMessageUpdatedUrl The URL of the webhook to call in response to the `on_message_updated` event using the `webhooks.on_message_updated.method` HTTP method.
     * @param string $webhooksOnMessageUpdatedMethod The HTTP method to use when calling the `webhooks.on_message_updated.url`.
     * @param string $webhooksOnMessageRemovedUrl The URL of the webhook to call in response to the `on_message_removed` event using the `webhooks.on_message_removed.method` HTTP method.
     * @param string $webhooksOnMessageRemovedMethod The HTTP method to use when calling the `webhooks.on_message_removed.url`.
     * @param string $webhooksOnChannelAddedUrl The URL of the webhook to call in response to the `on_channel_added` event using the `webhooks.on_channel_added.method` HTTP method.
     * @param string $webhooksOnChannelAddedMethod The URL of the webhook to call in response to the `on_channel_added` event`.
     * @param string $webhooksOnChannelDestroyedUrl The URL of the webhook to call in response to the `on_channel_added` event using the `webhooks.on_channel_destroyed.method` HTTP method.
     * @param string $webhooksOnChannelDestroyedMethod The HTTP method to use when calling the `webhooks.on_channel_destroyed.url`.
     * @param string $webhooksOnChannelUpdatedUrl The URL of the webhook to call in response to the `on_channel_updated` event using the `webhooks.on_channel_updated.method` HTTP method.
     * @param string $webhooksOnChannelUpdatedMethod The HTTP method to use when calling the `webhooks.on_channel_updated.url`.
     * @param string $webhooksOnMemberAddedUrl The URL of the webhook to call in response to the `on_channel_updated` event using the `webhooks.on_channel_updated.method` HTTP method.
     * @param string $webhooksOnMemberAddedMethod The HTTP method to use when calling the `webhooks.on_channel_updated.url`.
     * @param string $webhooksOnMemberRemovedUrl The URL of the webhook to call in response to the `on_member_removed` event using the `webhooks.on_member_removed.method` HTTP method.
     * @param string $webhooksOnMemberRemovedMethod The HTTP method to use when calling the `webhooks.on_member_removed.url`.
     * @param int $limitsChannelMembers The maximum number of Members that can be added to Channels within this Service. Can be up to 1,000.
     * @param int $limitsUserChannels The maximum number of Channels Users can be a Member of within this Service. Can be up to 1,000.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $defaultServiceRoleSid = Values::NONE,
        string $defaultChannelRoleSid = Values::NONE,
        string $defaultChannelCreatorRoleSid = Values::NONE,
        bool $readStatusEnabled = Values::BOOL_NONE,
        bool $reachabilityEnabled = Values::BOOL_NONE,
        int $typingIndicatorTimeout = Values::INT_NONE,
        int $consumptionReportInterval = Values::INT_NONE,
        bool $notificationsNewMessageEnabled = Values::BOOL_NONE,
        string $notificationsNewMessageTemplate = Values::NONE,
        bool $notificationsAddedToChannelEnabled = Values::BOOL_NONE,
        string $notificationsAddedToChannelTemplate = Values::NONE,
        bool $notificationsRemovedFromChannelEnabled = Values::BOOL_NONE,
        string $notificationsRemovedFromChannelTemplate = Values::NONE,
        bool $notificationsInvitedToChannelEnabled = Values::BOOL_NONE,
        string $notificationsInvitedToChannelTemplate = Values::NONE,
        string $preWebhookUrl = Values::NONE,
        string $postWebhookUrl = Values::NONE,
        string $webhookMethod = Values::NONE,
        array $webhookFilters = Values::ARRAY_NONE,
        string $webhooksOnMessageSendUrl = Values::NONE,
        string $webhooksOnMessageSendMethod = Values::NONE,
        string $webhooksOnMessageUpdateUrl = Values::NONE,
        string $webhooksOnMessageUpdateMethod = Values::NONE,
        string $webhooksOnMessageRemoveUrl = Values::NONE,
        string $webhooksOnMessageRemoveMethod = Values::NONE,
        string $webhooksOnChannelAddUrl = Values::NONE,
        string $webhooksOnChannelAddMethod = Values::NONE,
        string $webhooksOnChannelDestroyUrl = Values::NONE,
        string $webhooksOnChannelDestroyMethod = Values::NONE,
        string $webhooksOnChannelUpdateUrl = Values::NONE,
        string $webhooksOnChannelUpdateMethod = Values::NONE,
        string $webhooksOnMemberAddUrl = Values::NONE,
        string $webhooksOnMemberAddMethod = Values::NONE,
        string $webhooksOnMemberRemoveUrl = Values::NONE,
        string $webhooksOnMemberRemoveMethod = Values::NONE,
        string $webhooksOnMessageSentUrl = Values::NONE,
        string $webhooksOnMessageSentMethod = Values::NONE,
        string $webhooksOnMessageUpdatedUrl = Values::NONE,
        string $webhooksOnMessageUpdatedMethod = Values::NONE,
        string $webhooksOnMessageRemovedUrl = Values::NONE,
        string $webhooksOnMessageRemovedMethod = Values::NONE,
        string $webhooksOnChannelAddedUrl = Values::NONE,
        string $webhooksOnChannelAddedMethod = Values::NONE,
        string $webhooksOnChannelDestroyedUrl = Values::NONE,
        string $webhooksOnChannelDestroyedMethod = Values::NONE,
        string $webhooksOnChannelUpdatedUrl = Values::NONE,
        string $webhooksOnChannelUpdatedMethod = Values::NONE,
        string $webhooksOnMemberAddedUrl = Values::NONE,
        string $webhooksOnMemberAddedMethod = Values::NONE,
        string $webhooksOnMemberRemovedUrl = Values::NONE,
        string $webhooksOnMemberRemovedMethod = Values::NONE,
        int $limitsChannelMembers = Values::INT_NONE,
        int $limitsUserChannels = Values::INT_NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['defaultServiceRoleSid'] = $defaultServiceRoleSid;
        $this->options['defaultChannelRoleSid'] = $defaultChannelRoleSid;
        $this->options['defaultChannelCreatorRoleSid'] = $defaultChannelCreatorRoleSid;
        $this->options['readStatusEnabled'] = $readStatusEnabled;
        $this->options['reachabilityEnabled'] = $reachabilityEnabled;
        $this->options['typingIndicatorTimeout'] = $typingIndicatorTimeout;
        $this->options['consumptionReportInterval'] = $consumptionReportInterval;
        $this->options['notificationsNewMessageEnabled'] = $notificationsNewMessageEnabled;
        $this->options['notificationsNewMessageTemplate'] = $notificationsNewMessageTemplate;
        $this->options['notificationsAddedToChannelEnabled'] = $notificationsAddedToChannelEnabled;
        $this->options['notificationsAddedToChannelTemplate'] = $notificationsAddedToChannelTemplate;
        $this->options['notificationsRemovedFromChannelEnabled'] = $notificationsRemovedFromChannelEnabled;
        $this->options['notificationsRemovedFromChannelTemplate'] = $notificationsRemovedFromChannelTemplate;
        $this->options['notificationsInvitedToChannelEnabled'] = $notificationsInvitedToChannelEnabled;
        $this->options['notificationsInvitedToChannelTemplate'] = $notificationsInvitedToChannelTemplate;
        $this->options['preWebhookUrl'] = $preWebhookUrl;
        $this->options['postWebhookUrl'] = $postWebhookUrl;
        $this->options['webhookMethod'] = $webhookMethod;
        $this->options['webhookFilters'] = $webhookFilters;
        $this->options['webhooksOnMessageSendUrl'] = $webhooksOnMessageSendUrl;
        $this->options['webhooksOnMessageSendMethod'] = $webhooksOnMessageSendMethod;
        $this->options['webhooksOnMessageUpdateUrl'] = $webhooksOnMessageUpdateUrl;
        $this->options['webhooksOnMessageUpdateMethod'] = $webhooksOnMessageUpdateMethod;
        $this->options['webhooksOnMessageRemoveUrl'] = $webhooksOnMessageRemoveUrl;
        $this->options['webhooksOnMessageRemoveMethod'] = $webhooksOnMessageRemoveMethod;
        $this->options['webhooksOnChannelAddUrl'] = $webhooksOnChannelAddUrl;
        $this->options['webhooksOnChannelAddMethod'] = $webhooksOnChannelAddMethod;
        $this->options['webhooksOnChannelDestroyUrl'] = $webhooksOnChannelDestroyUrl;
        $this->options['webhooksOnChannelDestroyMethod'] = $webhooksOnChannelDestroyMethod;
        $this->options['webhooksOnChannelUpdateUrl'] = $webhooksOnChannelUpdateUrl;
        $this->options['webhooksOnChannelUpdateMethod'] = $webhooksOnChannelUpdateMethod;
        $this->options['webhooksOnMemberAddUrl'] = $webhooksOnMemberAddUrl;
        $this->options['webhooksOnMemberAddMethod'] = $webhooksOnMemberAddMethod;
        $this->options['webhooksOnMemberRemoveUrl'] = $webhooksOnMemberRemoveUrl;
        $this->options['webhooksOnMemberRemoveMethod'] = $webhooksOnMemberRemoveMethod;
        $this->options['webhooksOnMessageSentUrl'] = $webhooksOnMessageSentUrl;
        $this->options['webhooksOnMessageSentMethod'] = $webhooksOnMessageSentMethod;
        $this->options['webhooksOnMessageUpdatedUrl'] = $webhooksOnMessageUpdatedUrl;
        $this->options['webhooksOnMessageUpdatedMethod'] = $webhooksOnMessageUpdatedMethod;
        $this->options['webhooksOnMessageRemovedUrl'] = $webhooksOnMessageRemovedUrl;
        $this->options['webhooksOnMessageRemovedMethod'] = $webhooksOnMessageRemovedMethod;
        $this->options['webhooksOnChannelAddedUrl'] = $webhooksOnChannelAddedUrl;
        $this->options['webhooksOnChannelAddedMethod'] = $webhooksOnChannelAddedMethod;
        $this->options['webhooksOnChannelDestroyedUrl'] = $webhooksOnChannelDestroyedUrl;
        $this->options['webhooksOnChannelDestroyedMethod'] = $webhooksOnChannelDestroyedMethod;
        $this->options['webhooksOnChannelUpdatedUrl'] = $webhooksOnChannelUpdatedUrl;
        $this->options['webhooksOnChannelUpdatedMethod'] = $webhooksOnChannelUpdatedMethod;
        $this->options['webhooksOnMemberAddedUrl'] = $webhooksOnMemberAddedUrl;
        $this->options['webhooksOnMemberAddedMethod'] = $webhooksOnMemberAddedMethod;
        $this->options['webhooksOnMemberRemovedUrl'] = $webhooksOnMemberRemovedUrl;
        $this->options['webhooksOnMemberRemovedMethod'] = $webhooksOnMemberRemovedMethod;
        $this->options['limitsChannelMembers'] = $limitsChannelMembers;
        $this->options['limitsUserChannels'] = $limitsUserChannels;
    }

    /**
     * A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The service role assigned to users when they are added to the service. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     *
     * @param string $defaultServiceRoleSid The service role assigned to users when they are added to the service. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     * @return $this Fluent Builder
     */
    public function setDefaultServiceRoleSid(string $defaultServiceRoleSid): self
    {
        $this->options['defaultServiceRoleSid'] = $defaultServiceRoleSid;
        return $this;
    }

    /**
     * The channel role assigned to users when they are added to a channel. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     *
     * @param string $defaultChannelRoleSid The channel role assigned to users when they are added to a channel. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     * @return $this Fluent Builder
     */
    public function setDefaultChannelRoleSid(string $defaultChannelRoleSid): self
    {
        $this->options['defaultChannelRoleSid'] = $defaultChannelRoleSid;
        return $this;
    }

    /**
     * The channel role assigned to a channel creator when they join a new channel. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     *
     * @param string $defaultChannelCreatorRoleSid The channel role assigned to a channel creator when they join a new channel. See the [Roles endpoint](https://www.twilio.com/docs/chat/api/roles) for more details.
     * @return $this Fluent Builder
     */
    public function setDefaultChannelCreatorRoleSid(string $defaultChannelCreatorRoleSid): self
    {
        $this->options['defaultChannelCreatorRoleSid'] = $defaultChannelCreatorRoleSid;
        return $this;
    }

    /**
     * Whether to enable the [Message Consumption Horizon](https://www.twilio.com/docs/chat/consumption-horizon) feature. The default is `true`.
     *
     * @param bool $readStatusEnabled Whether to enable the [Message Consumption Horizon](https://www.twilio.com/docs/chat/consumption-horizon) feature. The default is `true`.
     * @return $this Fluent Builder
     */
    public function setReadStatusEnabled(bool $readStatusEnabled): self
    {
        $this->options['readStatusEnabled'] = $readStatusEnabled;
        return $this;
    }

    /**
     * Whether to enable the [Reachability Indicator](https://www.twilio.com/docs/chat/reachability-indicator) for this Service instance. The default is `false`.
     *
     * @param bool $reachabilityEnabled Whether to enable the [Reachability Indicator](https://www.twilio.com/docs/chat/reachability-indicator) for this Service instance. The default is `false`.
     * @return $this Fluent Builder
     */
    public function setReachabilityEnabled(bool $reachabilityEnabled): self
    {
        $this->options['reachabilityEnabled'] = $reachabilityEnabled;
        return $this;
    }

    /**
     * How long in seconds after a `started typing` event until clients should assume that user is no longer typing, even if no `ended typing` message was received.  The default is 5 seconds.
     *
     * @param int $typingIndicatorTimeout How long in seconds after a `started typing` event until clients should assume that user is no longer typing, even if no `ended typing` message was received.  The default is 5 seconds.
     * @return $this Fluent Builder
     */
    public function setTypingIndicatorTimeout(int $typingIndicatorTimeout): self
    {
        $this->options['typingIndicatorTimeout'] = $typingIndicatorTimeout;
        return $this;
    }

    /**
     * DEPRECATED. The interval in seconds between consumption reports submission batches from client endpoints.
     *
     * @param int $consumptionReportInterval DEPRECATED. The interval in seconds between consumption reports submission batches from client endpoints.
     * @return $this Fluent Builder
     */
    public function setConsumptionReportInterval(int $consumptionReportInterval): self
    {
        $this->options['consumptionReportInterval'] = $consumptionReportInterval;
        return $this;
    }

    /**
     * Whether to send a notification when a new message is added to a channel. Can be: `true` or `false` and the default is `false`.
     *
     * @param bool $notificationsNewMessageEnabled Whether to send a notification when a new message is added to a channel. Can be: `true` or `false` and the default is `false`.
     * @return $this Fluent Builder
     */
    public function setNotificationsNewMessageEnabled(bool $notificationsNewMessageEnabled): self
    {
        $this->options['notificationsNewMessageEnabled'] = $notificationsNewMessageEnabled;
        return $this;
    }

    /**
     * The template to use to create the notification text displayed when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
     *
     * @param string $notificationsNewMessageTemplate The template to use to create the notification text displayed when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setNotificationsNewMessageTemplate(string $notificationsNewMessageTemplate): self
    {
        $this->options['notificationsNewMessageTemplate'] = $notificationsNewMessageTemplate;
        return $this;
    }

    /**
     * Whether to send a notification when a member is added to a channel. Can be: `true` or `false` and the default is `false`.
     *
     * @param bool $notificationsAddedToChannelEnabled Whether to send a notification when a member is added to a channel. Can be: `true` or `false` and the default is `false`.
     * @return $this Fluent Builder
     */
    public function setNotificationsAddedToChannelEnabled(bool $notificationsAddedToChannelEnabled): self
    {
        $this->options['notificationsAddedToChannelEnabled'] = $notificationsAddedToChannelEnabled;
        return $this;
    }

    /**
     * The template to use to create the notification text displayed when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
     *
     * @param string $notificationsAddedToChannelTemplate The template to use to create the notification text displayed when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setNotificationsAddedToChannelTemplate(string $notificationsAddedToChannelTemplate): self
    {
        $this->options['notificationsAddedToChannelTemplate'] = $notificationsAddedToChannelTemplate;
        return $this;
    }

    /**
     * Whether to send a notification to a user when they are removed from a channel. Can be: `true` or `false` and the default is `false`.
     *
     * @param bool $notificationsRemovedFromChannelEnabled Whether to send a notification to a user when they are removed from a channel. Can be: `true` or `false` and the default is `false`.
     * @return $this Fluent Builder
     */
    public function setNotificationsRemovedFromChannelEnabled(bool $notificationsRemovedFromChannelEnabled): self
    {
        $this->options['notificationsRemovedFromChannelEnabled'] = $notificationsRemovedFromChannelEnabled;
        return $this;
    }

    /**
     * The template to use to create the notification text displayed to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
     *
     * @param string $notificationsRemovedFromChannelTemplate The template to use to create the notification text displayed to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setNotificationsRemovedFromChannelTemplate(string $notificationsRemovedFromChannelTemplate): self
    {
        $this->options['notificationsRemovedFromChannelTemplate'] = $notificationsRemovedFromChannelTemplate;
        return $this;
    }

    /**
     * Whether to send a notification when a user is invited to a channel. Can be: `true` or `false` and the default is `false`.
     *
     * @param bool $notificationsInvitedToChannelEnabled Whether to send a notification when a user is invited to a channel. Can be: `true` or `false` and the default is `false`.
     * @return $this Fluent Builder
     */
    public function setNotificationsInvitedToChannelEnabled(bool $notificationsInvitedToChannelEnabled): self
    {
        $this->options['notificationsInvitedToChannelEnabled'] = $notificationsInvitedToChannelEnabled;
        return $this;
    }

    /**
     * The template to use to create the notification text displayed when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
     *
     * @param string $notificationsInvitedToChannelTemplate The template to use to create the notification text displayed when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setNotificationsInvitedToChannelTemplate(string $notificationsInvitedToChannelTemplate): self
    {
        $this->options['notificationsInvitedToChannelTemplate'] = $notificationsInvitedToChannelTemplate;
        return $this;
    }

    /**
     * The URL for pre-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/api/chat/webhooks) for more details.
     *
     * @param string $preWebhookUrl The URL for pre-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/api/chat/webhooks) for more details.
     * @return $this Fluent Builder
     */
    public function setPreWebhookUrl(string $preWebhookUrl): self
    {
        $this->options['preWebhookUrl'] = $preWebhookUrl;
        return $this;
    }

    /**
     * The URL for post-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/api/chat/webhooks) for more details.
     *
     * @param string $postWebhookUrl The URL for post-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/api/chat/webhooks) for more details.
     * @return $this Fluent Builder
     */
    public function setPostWebhookUrl(string $postWebhookUrl): self
    {
        $this->options['postWebhookUrl'] = $postWebhookUrl;
        return $this;
    }

    /**
     * The HTTP method to use for calls to the `pre_webhook_url` and `post_webhook_url` webhooks.  Can be: `POST` or `GET` and the default is `POST`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
     *
     * @param string $webhookMethod The HTTP method to use for calls to the `pre_webhook_url` and `post_webhook_url` webhooks.  Can be: `POST` or `GET` and the default is `POST`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
     * @return $this Fluent Builder
     */
    public function setWebhookMethod(string $webhookMethod): self
    {
        $this->options['webhookMethod'] = $webhookMethod;
        return $this;
    }

    /**
     * The list of WebHook events that are enabled for this Service instance. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
     *
     * @param string[] $webhookFilters The list of WebHook events that are enabled for this Service instance. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
     * @return $this Fluent Builder
     */
    public function setWebhookFilters(array $webhookFilters): self
    {
        $this->options['webhookFilters'] = $webhookFilters;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_message_send` event using the `webhooks.on_message_send.method` HTTP method.
     *
     * @param string $webhooksOnMessageSendUrl The URL of the webhook to call in response to the `on_message_send` event using the `webhooks.on_message_send.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageSendUrl(string $webhooksOnMessageSendUrl): self
    {
        $this->options['webhooksOnMessageSendUrl'] = $webhooksOnMessageSendUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_message_send.url`.
     *
     * @param string $webhooksOnMessageSendMethod The HTTP method to use when calling the `webhooks.on_message_send.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageSendMethod(string $webhooksOnMessageSendMethod): self
    {
        $this->options['webhooksOnMessageSendMethod'] = $webhooksOnMessageSendMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_message_update` event using the `webhooks.on_message_update.method` HTTP method.
     *
     * @param string $webhooksOnMessageUpdateUrl The URL of the webhook to call in response to the `on_message_update` event using the `webhooks.on_message_update.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageUpdateUrl(string $webhooksOnMessageUpdateUrl): self
    {
        $this->options['webhooksOnMessageUpdateUrl'] = $webhooksOnMessageUpdateUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_message_update.url`.
     *
     * @param string $webhooksOnMessageUpdateMethod The HTTP method to use when calling the `webhooks.on_message_update.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageUpdateMethod(string $webhooksOnMessageUpdateMethod): self
    {
        $this->options['webhooksOnMessageUpdateMethod'] = $webhooksOnMessageUpdateMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_message_remove` event using the `webhooks.on_message_remove.method` HTTP method.
     *
     * @param string $webhooksOnMessageRemoveUrl The URL of the webhook to call in response to the `on_message_remove` event using the `webhooks.on_message_remove.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageRemoveUrl(string $webhooksOnMessageRemoveUrl): self
    {
        $this->options['webhooksOnMessageRemoveUrl'] = $webhooksOnMessageRemoveUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_message_remove.url`.
     *
     * @param string $webhooksOnMessageRemoveMethod The HTTP method to use when calling the `webhooks.on_message_remove.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageRemoveMethod(string $webhooksOnMessageRemoveMethod): self
    {
        $this->options['webhooksOnMessageRemoveMethod'] = $webhooksOnMessageRemoveMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_channel_add` event using the `webhooks.on_channel_add.method` HTTP method.
     *
     * @param string $webhooksOnChannelAddUrl The URL of the webhook to call in response to the `on_channel_add` event using the `webhooks.on_channel_add.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelAddUrl(string $webhooksOnChannelAddUrl): self
    {
        $this->options['webhooksOnChannelAddUrl'] = $webhooksOnChannelAddUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_channel_add.url`.
     *
     * @param string $webhooksOnChannelAddMethod The HTTP method to use when calling the `webhooks.on_channel_add.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelAddMethod(string $webhooksOnChannelAddMethod): self
    {
        $this->options['webhooksOnChannelAddMethod'] = $webhooksOnChannelAddMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_channel_destroy` event using the `webhooks.on_channel_destroy.method` HTTP method.
     *
     * @param string $webhooksOnChannelDestroyUrl The URL of the webhook to call in response to the `on_channel_destroy` event using the `webhooks.on_channel_destroy.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelDestroyUrl(string $webhooksOnChannelDestroyUrl): self
    {
        $this->options['webhooksOnChannelDestroyUrl'] = $webhooksOnChannelDestroyUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_channel_destroy.url`.
     *
     * @param string $webhooksOnChannelDestroyMethod The HTTP method to use when calling the `webhooks.on_channel_destroy.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelDestroyMethod(string $webhooksOnChannelDestroyMethod): self
    {
        $this->options['webhooksOnChannelDestroyMethod'] = $webhooksOnChannelDestroyMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_channel_update` event using the `webhooks.on_channel_update.method` HTTP method.
     *
     * @param string $webhooksOnChannelUpdateUrl The URL of the webhook to call in response to the `on_channel_update` event using the `webhooks.on_channel_update.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelUpdateUrl(string $webhooksOnChannelUpdateUrl): self
    {
        $this->options['webhooksOnChannelUpdateUrl'] = $webhooksOnChannelUpdateUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_channel_update.url`.
     *
     * @param string $webhooksOnChannelUpdateMethod The HTTP method to use when calling the `webhooks.on_channel_update.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelUpdateMethod(string $webhooksOnChannelUpdateMethod): self
    {
        $this->options['webhooksOnChannelUpdateMethod'] = $webhooksOnChannelUpdateMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_member_add` event using the `webhooks.on_member_add.method` HTTP method.
     *
     * @param string $webhooksOnMemberAddUrl The URL of the webhook to call in response to the `on_member_add` event using the `webhooks.on_member_add.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberAddUrl(string $webhooksOnMemberAddUrl): self
    {
        $this->options['webhooksOnMemberAddUrl'] = $webhooksOnMemberAddUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_member_add.url`.
     *
     * @param string $webhooksOnMemberAddMethod The HTTP method to use when calling the `webhooks.on_member_add.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberAddMethod(string $webhooksOnMemberAddMethod): self
    {
        $this->options['webhooksOnMemberAddMethod'] = $webhooksOnMemberAddMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_member_remove` event using the `webhooks.on_member_remove.method` HTTP method.
     *
     * @param string $webhooksOnMemberRemoveUrl The URL of the webhook to call in response to the `on_member_remove` event using the `webhooks.on_member_remove.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberRemoveUrl(string $webhooksOnMemberRemoveUrl): self
    {
        $this->options['webhooksOnMemberRemoveUrl'] = $webhooksOnMemberRemoveUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_member_remove.url`.
     *
     * @param string $webhooksOnMemberRemoveMethod The HTTP method to use when calling the `webhooks.on_member_remove.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberRemoveMethod(string $webhooksOnMemberRemoveMethod): self
    {
        $this->options['webhooksOnMemberRemoveMethod'] = $webhooksOnMemberRemoveMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_message_sent` event using the `webhooks.on_message_sent.method` HTTP method.
     *
     * @param string $webhooksOnMessageSentUrl The URL of the webhook to call in response to the `on_message_sent` event using the `webhooks.on_message_sent.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageSentUrl(string $webhooksOnMessageSentUrl): self
    {
        $this->options['webhooksOnMessageSentUrl'] = $webhooksOnMessageSentUrl;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_message_sent` event`.
     *
     * @param string $webhooksOnMessageSentMethod The URL of the webhook to call in response to the `on_message_sent` event`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageSentMethod(string $webhooksOnMessageSentMethod): self
    {
        $this->options['webhooksOnMessageSentMethod'] = $webhooksOnMessageSentMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_message_updated` event using the `webhooks.on_message_updated.method` HTTP method.
     *
     * @param string $webhooksOnMessageUpdatedUrl The URL of the webhook to call in response to the `on_message_updated` event using the `webhooks.on_message_updated.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageUpdatedUrl(string $webhooksOnMessageUpdatedUrl): self
    {
        $this->options['webhooksOnMessageUpdatedUrl'] = $webhooksOnMessageUpdatedUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_message_updated.url`.
     *
     * @param string $webhooksOnMessageUpdatedMethod The HTTP method to use when calling the `webhooks.on_message_updated.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageUpdatedMethod(string $webhooksOnMessageUpdatedMethod): self
    {
        $this->options['webhooksOnMessageUpdatedMethod'] = $webhooksOnMessageUpdatedMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_message_removed` event using the `webhooks.on_message_removed.method` HTTP method.
     *
     * @param string $webhooksOnMessageRemovedUrl The URL of the webhook to call in response to the `on_message_removed` event using the `webhooks.on_message_removed.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageRemovedUrl(string $webhooksOnMessageRemovedUrl): self
    {
        $this->options['webhooksOnMessageRemovedUrl'] = $webhooksOnMessageRemovedUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_message_removed.url`.
     *
     * @param string $webhooksOnMessageRemovedMethod The HTTP method to use when calling the `webhooks.on_message_removed.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageRemovedMethod(string $webhooksOnMessageRemovedMethod): self
    {
        $this->options['webhooksOnMessageRemovedMethod'] = $webhooksOnMessageRemovedMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_channel_added` event using the `webhooks.on_channel_added.method` HTTP method.
     *
     * @param string $webhooksOnChannelAddedUrl The URL of the webhook to call in response to the `on_channel_added` event using the `webhooks.on_channel_added.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelAddedUrl(string $webhooksOnChannelAddedUrl): self
    {
        $this->options['webhooksOnChannelAddedUrl'] = $webhooksOnChannelAddedUrl;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_channel_added` event`.
     *
     * @param string $webhooksOnChannelAddedMethod The URL of the webhook to call in response to the `on_channel_added` event`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelAddedMethod(string $webhooksOnChannelAddedMethod): self
    {
        $this->options['webhooksOnChannelAddedMethod'] = $webhooksOnChannelAddedMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_channel_added` event using the `webhooks.on_channel_destroyed.method` HTTP method.
     *
     * @param string $webhooksOnChannelDestroyedUrl The URL of the webhook to call in response to the `on_channel_added` event using the `webhooks.on_channel_destroyed.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelDestroyedUrl(string $webhooksOnChannelDestroyedUrl): self
    {
        $this->options['webhooksOnChannelDestroyedUrl'] = $webhooksOnChannelDestroyedUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_channel_destroyed.url`.
     *
     * @param string $webhooksOnChannelDestroyedMethod The HTTP method to use when calling the `webhooks.on_channel_destroyed.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelDestroyedMethod(string $webhooksOnChannelDestroyedMethod): self
    {
        $this->options['webhooksOnChannelDestroyedMethod'] = $webhooksOnChannelDestroyedMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_channel_updated` event using the `webhooks.on_channel_updated.method` HTTP method.
     *
     * @param string $webhooksOnChannelUpdatedUrl The URL of the webhook to call in response to the `on_channel_updated` event using the `webhooks.on_channel_updated.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelUpdatedUrl(string $webhooksOnChannelUpdatedUrl): self
    {
        $this->options['webhooksOnChannelUpdatedUrl'] = $webhooksOnChannelUpdatedUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_channel_updated.url`.
     *
     * @param string $webhooksOnChannelUpdatedMethod The HTTP method to use when calling the `webhooks.on_channel_updated.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelUpdatedMethod(string $webhooksOnChannelUpdatedMethod): self
    {
        $this->options['webhooksOnChannelUpdatedMethod'] = $webhooksOnChannelUpdatedMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_channel_updated` event using the `webhooks.on_channel_updated.method` HTTP method.
     *
     * @param string $webhooksOnMemberAddedUrl The URL of the webhook to call in response to the `on_channel_updated` event using the `webhooks.on_channel_updated.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberAddedUrl(string $webhooksOnMemberAddedUrl): self
    {
        $this->options['webhooksOnMemberAddedUrl'] = $webhooksOnMemberAddedUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_channel_updated.url`.
     *
     * @param string $webhooksOnMemberAddedMethod The HTTP method to use when calling the `webhooks.on_channel_updated.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberAddedMethod(string $webhooksOnMemberAddedMethod): self
    {
        $this->options['webhooksOnMemberAddedMethod'] = $webhooksOnMemberAddedMethod;
        return $this;
    }

    /**
     * The URL of the webhook to call in response to the `on_member_removed` event using the `webhooks.on_member_removed.method` HTTP method.
     *
     * @param string $webhooksOnMemberRemovedUrl The URL of the webhook to call in response to the `on_member_removed` event using the `webhooks.on_member_removed.method` HTTP method.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberRemovedUrl(string $webhooksOnMemberRemovedUrl): self
    {
        $this->options['webhooksOnMemberRemovedUrl'] = $webhooksOnMemberRemovedUrl;
        return $this;
    }

    /**
     * The HTTP method to use when calling the `webhooks.on_member_removed.url`.
     *
     * @param string $webhooksOnMemberRemovedMethod The HTTP method to use when calling the `webhooks.on_member_removed.url`.
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberRemovedMethod(string $webhooksOnMemberRemovedMethod): self
    {
        $this->options['webhooksOnMemberRemovedMethod'] = $webhooksOnMemberRemovedMethod;
        return $this;
    }

    /**
     * The maximum number of Members that can be added to Channels within this Service. Can be up to 1,000.
     *
     * @param int $limitsChannelMembers The maximum number of Members that can be added to Channels within this Service. Can be up to 1,000.
     * @return $this Fluent Builder
     */
    public function setLimitsChannelMembers(int $limitsChannelMembers): self
    {
        $this->options['limitsChannelMembers'] = $limitsChannelMembers;
        return $this;
    }

    /**
     * The maximum number of Channels Users can be a Member of within this Service. Can be up to 1,000.
     *
     * @param int $limitsUserChannels The maximum number of Channels Users can be a Member of within this Service. Can be up to 1,000.
     * @return $this Fluent Builder
     */
    public function setLimitsUserChannels(int $limitsUserChannels): self
    {
        $this->options['limitsUserChannels'] = $limitsUserChannels;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Chat.V1.UpdateServiceOptions ' . $options . ']';
    }
}

