<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use Twilio\Options;
use Twilio\Values;

abstract class FeedbackSummaryOptions
{
    /**
     * @param bool $includeSubaccounts Whether to also include Feedback resources from all subaccounts. `true` includes feedback from all subaccounts and `false`, the default, includes feedback from only the specified account.
     * @param string $statusCallback The URL that we will request when the feedback summary is complete.
     * @param string $statusCallbackMethod The HTTP method (`GET` or `POST`) we use to make the request to the `StatusCallback` URL.
     * @return CreateFeedbackSummaryOptions Options builder
     */
    public static function create(
        
        bool $includeSubaccounts = Values::BOOL_NONE,
        string $statusCallback = Values::NONE,
        string $statusCallbackMethod = Values::NONE

    ): CreateFeedbackSummaryOptions
    {
        return new CreateFeedbackSummaryOptions(
            $includeSubaccounts,
            $statusCallback,
            $statusCallbackMethod
        );
    }



}

class CreateFeedbackSummaryOptions extends Options
    {
    /**
     * @param bool $includeSubaccounts Whether to also include Feedback resources from all subaccounts. `true` includes feedback from all subaccounts and `false`, the default, includes feedback from only the specified account.
     * @param string $statusCallback The URL that we will request when the feedback summary is complete.
     * @param string $statusCallbackMethod The HTTP method (`GET` or `POST`) we use to make the request to the `StatusCallback` URL.
     */
    public function __construct(
        
        bool $includeSubaccounts = Values::BOOL_NONE,
        string $statusCallback = Values::NONE,
        string $statusCallbackMethod = Values::NONE

    ) {
        $this->options['includeSubaccounts'] = $includeSubaccounts;
        $this->options['statusCallback'] = $statusCallback;
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
    }

    /**
     * Whether to also include Feedback resources from all subaccounts. `true` includes feedback from all subaccounts and `false`, the default, includes feedback from only the specified account.
     *
     * @param bool $includeSubaccounts Whether to also include Feedback resources from all subaccounts. `true` includes feedback from all subaccounts and `false`, the default, includes feedback from only the specified account.
     * @return $this Fluent Builder
     */
    public function setIncludeSubaccounts(bool $includeSubaccounts): self
    {
        $this->options['includeSubaccounts'] = $includeSubaccounts;
        return $this;
    }

    /**
     * The URL that we will request when the feedback summary is complete.
     *
     * @param string $statusCallback The URL that we will request when the feedback summary is complete.
     * @return $this Fluent Builder
     */
    public function setStatusCallback(string $statusCallback): self
    {
        $this->options['statusCallback'] = $statusCallback;
        return $this;
    }

    /**
     * The HTTP method (`GET` or `POST`) we use to make the request to the `StatusCallback` URL.
     *
     * @param string $statusCallbackMethod The HTTP method (`GET` or `POST`) we use to make the request to the `StatusCallback` URL.
     * @return $this Fluent Builder
     */
    public function setStatusCallbackMethod(string $statusCallbackMethod): self
    {
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.CreateFeedbackSummaryOptions ' . $options . ']';
    }
}



