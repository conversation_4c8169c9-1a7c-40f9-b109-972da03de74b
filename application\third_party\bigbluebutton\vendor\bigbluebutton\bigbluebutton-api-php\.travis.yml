language: php

php:
  - 5.4
  - 5.5
  - 5.6
  - 7.0
  - 7.1
  - 7.2
  - 7.3
  - hhvm
  - nightly

matrix:
  fast_finish: true
  allow_failures:
    - php: 5.4
    - php: 5.5
    - php: 5.6
    - php: hhvm
    - php: nightly

env:
  global:
    - BBB_SECRET=8cd8ef52e8e101574e400365b55e11a6
    - BBB_SERVER_BASE_URL=https://test-install.blindsidenetworks.com/bigbluebutton/

before_script:
  - wget http://getcomposer.org/composer.phar
  - php composer.phar install --dev --no-interaction

script:
  - mkdir -p build/logs
  - if [ "$TRAVIS_PHP_VERSION" != "nightly" ]; then vendor/bin/phpunit --coverage-clover build/logs/clover.xml; fi
  - if [ "$TRAVIS_PHP_VERSION" == "nightly" ]; then vendor/bin/phpunit; fi

after_script:
  - if [ "$TRAVIS_PHP_VERSION" == "7.0" ] || [ "$TRAVIS_PHP_VERSION" == "7.1" ] || [ "$TRAVIS_PHP_VERSION" == "7.2" ]; then php vendor/bin/php-coveralls -v; fi
  - if [ "$TRAVIS_PHP_VERSION" == "7.0" ] || [ "$TRAVIS_PHP_VERSION" == "7.1" ] || [ "$TRAVIS_PHP_VERSION" == "7.2" ]; then wget https://scrutinizer-ci.com/ocular.phar; fi
  - if [ "$TRAVIS_PHP_VERSION" == "7.0" ] || [ "$TRAVIS_PHP_VERSION" == "7.1" ] || [ "$TRAVIS_PHP_VERSION" == "7.2" ]; then php ocular.phar code-coverage:upload --format=php-clover build/logs/clover.xml; fi
