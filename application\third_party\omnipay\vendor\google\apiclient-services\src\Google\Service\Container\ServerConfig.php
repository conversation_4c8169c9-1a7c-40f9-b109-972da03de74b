<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Container_ServerConfig extends Google_Collection
{
  protected $collection_key = 'validNodeVersions';
  public $defaultClusterVersion;
  public $defaultImageType;
  public $validImageTypes;
  public $validMasterVersions;
  public $validNodeVersions;

  public function setDefaultClusterVersion($defaultClusterVersion)
  {
    $this->defaultClusterVersion = $defaultClusterVersion;
  }
  public function getDefaultClusterVersion()
  {
    return $this->defaultClusterVersion;
  }
  public function setDefaultImageType($defaultImageType)
  {
    $this->defaultImageType = $defaultImageType;
  }
  public function getDefaultImageType()
  {
    return $this->defaultImageType;
  }
  public function setValidImageTypes($validImageTypes)
  {
    $this->validImageTypes = $validImageTypes;
  }
  public function getValidImageTypes()
  {
    return $this->validImageTypes;
  }
  public function setValidMasterVersions($validMasterVersions)
  {
    $this->validMasterVersions = $validMasterVersions;
  }
  public function getValidMasterVersions()
  {
    return $this->validMasterVersions;
  }
  public function setValidNodeVersions($validNodeVersions)
  {
    $this->validNodeVersions = $validNodeVersions;
  }
  public function getValidNodeVersions()
  {
    return $this->validNodeVersions;
  }
}
