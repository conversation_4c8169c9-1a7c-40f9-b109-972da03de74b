<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_AndroidEnterprise_AppRestrictionsSchemaRestrictionRestrictionValue extends Google_Collection
{
  protected $collection_key = 'valueMultiselect';
  public $type;
  public $valueBool;
  public $valueInteger;
  public $valueMultiselect;
  public $valueString;

  public function setType($type)
  {
    $this->type = $type;
  }
  public function getType()
  {
    return $this->type;
  }
  public function setValueBool($valueBool)
  {
    $this->valueBool = $valueBool;
  }
  public function getValueBool()
  {
    return $this->valueBool;
  }
  public function setValueInteger($valueInteger)
  {
    $this->valueInteger = $valueInteger;
  }
  public function getValueInteger()
  {
    return $this->valueInteger;
  }
  public function setValueMultiselect($valueMultiselect)
  {
    $this->valueMultiselect = $valueMultiselect;
  }
  public function getValueMultiselect()
  {
    return $this->valueMultiselect;
  }
  public function setValueString($valueString)
  {
    $this->valueString = $valueString;
  }
  public function getValueString()
  {
    return $this->valueString;
  }
}
