HTTP/1.1 201 Created
Server: Apache-Coyote/1.1
PROXY_SERVER_INFO: host=slcsbjava2.slc.paypal.com;threadId=1534
Paypal-Debug-Id: 98cbd3ab19dfe
SERVER_INFO: paymentsplatformserv:v1.payments.payment&CalThreadId=129&TopLevelTxnStartTime=146fc9074ec&Host=slcsbjm3.slc.paypal.com&pid=15797
CORRELATION-ID: 98cbd3ab19dfe
Content-Language: *
Date: Thu, 03 Jul 2014 14:11:10 GMT
Content-Type: application/json

{
    "id": "PAY-0WB587530N302915SKXWVCSQ",
    "create_time": "2015-09-07T08:56:42Z",
    "update_time": "2015-09-07T08:56:42Z",
    "state": "created",
    "intent": "sale",
    "payer": {
        "payment_method": "paypal",
        "payer_info": {
            "shipping_address": []
        }
    },
    "transactions": [
        {
            "amount": {
                "total": "10.00",
                "currency": "AUD",
                "details": {
                    "subtotal": "10.00"
                }
            },
            "description": "This is a test purchase transaction.",
            "related_resources": []
        }
    ],
    "links": [
        {
            "href": "https:\/\/api.sandbox.paypal.com\/v1\/payments\/payment\/PAY-0WB587530N302915SKXWVCSQ",
            "rel": "self",
            "method": "GET"
        },
        {
            "href": "https:\/\/www.sandbox.paypal.com\/cgi-bin\/webscr?cmd=_express-checkout&token=EC-3DR71034MD528800J",
            "rel": "approval_url",
            "method": "REDIRECT"
        },
        {
            "href": "https:\/\/api.sandbox.paypal.com\/v1\/payments\/payment\/PAY-0WB587530N302915SKXWVCSQ\/execute",
            "rel": "execute",
            "method": "POST"
        }
    ]
}
