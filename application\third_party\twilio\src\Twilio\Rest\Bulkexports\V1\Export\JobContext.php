<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Bulkexports
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Bulkexports\V1\Export;

use Twilio\Exceptions\TwilioException;
use Twilio\Version;
use Twilio\InstanceContext;


class JobContext extends InstanceContext
    {
    /**
     * Initialize the JobContext
     *
     * @param Version $version Version that contains the resource
     * @param string $jobSid The unique string that that we created to identify the Bulk Export job
     */
    public function __construct(
        Version $version,
        $jobSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'jobSid' =>
            $jobSid,
        ];

        $this->uri = '/Exports/Jobs/' . \rawurlencode($jobSid)
        .'';
    }

    /**
     * Delete the JobInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the JobInstance
     *
     * @return JobInstance Fetched JobInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): JobInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new JobInstance(
            $this->version,
            $payload,
            $this->solution['jobSid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Bulkexports.V1.JobContext ' . \implode(' ', $context) . ']';
    }
}
