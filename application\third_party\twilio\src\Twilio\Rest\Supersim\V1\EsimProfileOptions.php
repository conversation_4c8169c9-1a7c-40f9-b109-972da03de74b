<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Supersim
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Supersim\V1;

use Twilio\Options;
use Twilio\Values;

abstract class EsimProfileOptions
{
    /**
     * @param string $callbackUrl The URL we should call using the `callback_method` when the status of the eSIM Profile changes. At this stage of the eSIM Profile pilot, the a request to the URL will only be called when the ESimProfile resource changes from `reserving` to `available`.
     * @param string $callbackMethod The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is POST.
     * @param bool $generateMatchingId When set to `true`, a value for `Eid` does not need to be provided. Instead, when the eSIM profile is reserved, a matching ID will be generated and returned via the `matching_id` property. This identifies the specific eSIM profile that can be used by any capable device to claim and download the profile.
     * @param string $eid Identifier of the eUICC that will claim the eSIM Profile.
     * @return CreateEsimProfileOptions Options builder
     */
    public static function create(
        
        string $callbackUrl = Values::NONE,
        string $callbackMethod = Values::NONE,
        bool $generateMatchingId = Values::BOOL_NONE,
        string $eid = Values::NONE

    ): CreateEsimProfileOptions
    {
        return new CreateEsimProfileOptions(
            $callbackUrl,
            $callbackMethod,
            $generateMatchingId,
            $eid
        );
    }


    /**
     * @param string $eid List the eSIM Profiles that have been associated with an EId.
     * @param string $simSid Find the eSIM Profile resource related to a [Sim](https://www.twilio.com/docs/wireless/api/sim-resource) resource by providing the SIM SID. Will always return an array with either 1 or 0 records.
     * @param string $status List the eSIM Profiles that are in a given status.
     * @return ReadEsimProfileOptions Options builder
     */
    public static function read(
        
        string $eid = Values::NONE,
        string $simSid = Values::NONE,
        string $status = Values::NONE

    ): ReadEsimProfileOptions
    {
        return new ReadEsimProfileOptions(
            $eid,
            $simSid,
            $status
        );
    }

}

class CreateEsimProfileOptions extends Options
    {
    /**
     * @param string $callbackUrl The URL we should call using the `callback_method` when the status of the eSIM Profile changes. At this stage of the eSIM Profile pilot, the a request to the URL will only be called when the ESimProfile resource changes from `reserving` to `available`.
     * @param string $callbackMethod The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is POST.
     * @param bool $generateMatchingId When set to `true`, a value for `Eid` does not need to be provided. Instead, when the eSIM profile is reserved, a matching ID will be generated and returned via the `matching_id` property. This identifies the specific eSIM profile that can be used by any capable device to claim and download the profile.
     * @param string $eid Identifier of the eUICC that will claim the eSIM Profile.
     */
    public function __construct(
        
        string $callbackUrl = Values::NONE,
        string $callbackMethod = Values::NONE,
        bool $generateMatchingId = Values::BOOL_NONE,
        string $eid = Values::NONE

    ) {
        $this->options['callbackUrl'] = $callbackUrl;
        $this->options['callbackMethod'] = $callbackMethod;
        $this->options['generateMatchingId'] = $generateMatchingId;
        $this->options['eid'] = $eid;
    }

    /**
     * The URL we should call using the `callback_method` when the status of the eSIM Profile changes. At this stage of the eSIM Profile pilot, the a request to the URL will only be called when the ESimProfile resource changes from `reserving` to `available`.
     *
     * @param string $callbackUrl The URL we should call using the `callback_method` when the status of the eSIM Profile changes. At this stage of the eSIM Profile pilot, the a request to the URL will only be called when the ESimProfile resource changes from `reserving` to `available`.
     * @return $this Fluent Builder
     */
    public function setCallbackUrl(string $callbackUrl): self
    {
        $this->options['callbackUrl'] = $callbackUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is POST.
     *
     * @param string $callbackMethod The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is POST.
     * @return $this Fluent Builder
     */
    public function setCallbackMethod(string $callbackMethod): self
    {
        $this->options['callbackMethod'] = $callbackMethod;
        return $this;
    }

    /**
     * When set to `true`, a value for `Eid` does not need to be provided. Instead, when the eSIM profile is reserved, a matching ID will be generated and returned via the `matching_id` property. This identifies the specific eSIM profile that can be used by any capable device to claim and download the profile.
     *
     * @param bool $generateMatchingId When set to `true`, a value for `Eid` does not need to be provided. Instead, when the eSIM profile is reserved, a matching ID will be generated and returned via the `matching_id` property. This identifies the specific eSIM profile that can be used by any capable device to claim and download the profile.
     * @return $this Fluent Builder
     */
    public function setGenerateMatchingId(bool $generateMatchingId): self
    {
        $this->options['generateMatchingId'] = $generateMatchingId;
        return $this;
    }

    /**
     * Identifier of the eUICC that will claim the eSIM Profile.
     *
     * @param string $eid Identifier of the eUICC that will claim the eSIM Profile.
     * @return $this Fluent Builder
     */
    public function setEid(string $eid): self
    {
        $this->options['eid'] = $eid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Supersim.V1.CreateEsimProfileOptions ' . $options . ']';
    }
}


class ReadEsimProfileOptions extends Options
    {
    /**
     * @param string $eid List the eSIM Profiles that have been associated with an EId.
     * @param string $simSid Find the eSIM Profile resource related to a [Sim](https://www.twilio.com/docs/wireless/api/sim-resource) resource by providing the SIM SID. Will always return an array with either 1 or 0 records.
     * @param string $status List the eSIM Profiles that are in a given status.
     */
    public function __construct(
        
        string $eid = Values::NONE,
        string $simSid = Values::NONE,
        string $status = Values::NONE

    ) {
        $this->options['eid'] = $eid;
        $this->options['simSid'] = $simSid;
        $this->options['status'] = $status;
    }

    /**
     * List the eSIM Profiles that have been associated with an EId.
     *
     * @param string $eid List the eSIM Profiles that have been associated with an EId.
     * @return $this Fluent Builder
     */
    public function setEid(string $eid): self
    {
        $this->options['eid'] = $eid;
        return $this;
    }

    /**
     * Find the eSIM Profile resource related to a [Sim](https://www.twilio.com/docs/wireless/api/sim-resource) resource by providing the SIM SID. Will always return an array with either 1 or 0 records.
     *
     * @param string $simSid Find the eSIM Profile resource related to a [Sim](https://www.twilio.com/docs/wireless/api/sim-resource) resource by providing the SIM SID. Will always return an array with either 1 or 0 records.
     * @return $this Fluent Builder
     */
    public function setSimSid(string $simSid): self
    {
        $this->options['simSid'] = $simSid;
        return $this;
    }

    /**
     * List the eSIM Profiles that are in a given status.
     *
     * @param string $status List the eSIM Profiles that are in a given status.
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Supersim.V1.ReadEsimProfileOptions ' . $options . ']';
    }
}

