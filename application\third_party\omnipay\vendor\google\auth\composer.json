{"name": "google/auth", "type": "library", "description": "Google Auth Library for PHP", "keywords": ["google", "oauth2", "authentication"], "homepage": "http://github.com/google/google-auth-library-php", "license": "Apache-2.0", "require": {"php": ">=5.4", "firebase/php-jwt": "~2.0|~3.0|~4.0", "guzzlehttp/guzzle": "~5.3|~6.0", "guzzlehttp/psr7": "~1.2", "psr/http-message": "^1.0", "psr/cache": "^1.0"}, "require-dev": {"phpunit/phpunit": "3.7.*", "friendsofphp/php-cs-fixer": "^1.11"}, "autoload": {"classmap": ["src/"], "psr-4": {"Google\\Auth\\": "src"}}}