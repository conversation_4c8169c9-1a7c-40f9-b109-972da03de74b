# 📁 Complete Hostinger Upload Guide - Exact File Locations

## 🎯 Overview
This guide shows you **exactly where** to upload each file on your Hostinger server for the password recovery email fix.

**Your Hostinger Structure:**
- Domain: `passdrc.com`
- School System Path: `/public_html/school/`
- Full URL: `https://passdrc.com/public_html/school/`

---

## 📋 Files to Upload - Complete List

### **CORE SYSTEM FILES (Required - Overwrite Existing)**

#### **File 1: Authentication_model.php**
```
Local Path: C:\My Software Projects\schoolhostinger\application\models\Authentication_model.php

Hostinger Path: /domains/passdrc.com/public_html/school/application/models/Authentication_model.php

Upload Method: Overwrite existing file
```

#### **File 2: Email_model.php**
```
Local Path: C:\My Software Projects\schoolhostinger\application\models\Email_model.php

Hostinger Path: /domains/passdrc.com/public_html/school/application/models/Email_model.php

Upload Method: Overwrite existing file
```

#### **File 3: Mailer.php**
```
Local Path: C:\My Software Projects\schoolhostinger\application\libraries\Mailer.php

Hostinger Path: /domains/passdrc.com/public_html/school/application/libraries/Mailer.php

Upload Method: Overwrite existing file
```

#### **File 4: config.php**
```
Local Path: C:\My Software Projects\schoolhostinger\application\config\config.php

Hostinger Path: /domains/passdrc.com/public_html/school/application/config/config.php

Upload Method: Overwrite existing file
⚠️ IMPORTANT: This contains CSRF fixes
```

### **SETUP & DIAGNOSTIC TOOLS (Temporary)**

#### **File 5: fix_csrf_hostinger.php**
```
Local Path: C:\My Software Projects\schoolhostinger\fix_csrf_hostinger.php

Hostinger Path: /domains/passdrc.com/public_html/school/fix_csrf_hostinger.php

Upload Method: New file in root directory
Access URL: https://passdrc.com/public_html/school/fix_csrf_hostinger.php
```

#### **File 6: simple_email_setup.php**
```
Local Path: C:\My Software Projects\schoolhostinger\simple_email_setup.php

Hostinger Path: /domains/passdrc.com/public_html/school/simple_email_setup.php

Upload Method: New file in root directory
Access URL: https://passdrc.com/public_html/school/simple_email_setup.php
```

#### **File 7: setup_email_config.php**
```
Local Path: C:\My Software Projects\schoolhostinger\setup_email_config.php

Hostinger Path: /domains/passdrc.com/public_html/school/setup_email_config.php

Upload Method: New file in root directory
Access URL: https://passdrc.com/public_html/school/setup_email_config.php
```

#### **File 8: email_diagnostic.php**
```
Local Path: C:\My Software Projects\schoolhostinger\email_diagnostic.php

Hostinger Path: /domains/passdrc.com/public_html/school/email_diagnostic.php

Upload Method: New file in root directory
Access URL: https://passdrc.com/public_html/school/email_diagnostic.php
```

---

## 🚀 Step-by-Step Upload Process

### **Method 1: Hostinger File Manager (Recommended)**

#### **Step 1: Login to Hostinger**
1. Go to: https://hpanel.hostinger.com/
2. Login with your Hostinger credentials
3. Select your hosting plan

#### **Step 2: Open File Manager**
1. Click **"File Manager"** in the hosting section
2. Navigate to: `domains/passdrc.com/public_html/school/`

#### **Step 3: Upload Core System Files**

**Upload Authentication_model.php:**
1. Navigate to: `application/models/`
2. Right-click → **Upload**
3. Select: `Authentication_model.php` from your local project
4. **Confirm overwrite** when prompted

**Upload Email_model.php:**
1. Stay in: `application/models/`
2. Right-click → **Upload**
3. Select: `Email_model.php` from your local project
4. **Confirm overwrite** when prompted

**Upload Mailer.php:**
1. Navigate to: `application/libraries/`
2. Right-click → **Upload**
3. Select: `Mailer.php` from your local project
4. **Confirm overwrite** when prompted

**Upload config.php:**
1. Navigate to: `application/config/`
2. Right-click → **Upload**
3. Select: `config.php` from your local project
4. **Confirm overwrite** when prompted

#### **Step 4: Upload Setup Tools**

**Upload Setup Scripts:**
1. Navigate back to root: `public_html/school/`
2. Right-click → **Upload**
3. Select all 4 setup files:
   - `fix_csrf_hostinger.php`
   - `simple_email_setup.php`
   - `setup_email_config.php`
   - `email_diagnostic.php`

### **Method 2: FTP Client (FileZilla, WinSCP)**

#### **Step 1: Connect to Hostinger FTP**
```
Host: ftp.passdrc.com (or your FTP server from Hostinger panel)
Username: [Your FTP username from Hostinger]
Password: [Your FTP password from Hostinger]
Port: 21
```

#### **Step 2: Navigate and Upload**
```
Remote Directory Structure:
/domains/passdrc.com/public_html/school/
├── application/
│   ├── config/
│   │   └── config.php ← Upload here (overwrite)
│   ├── libraries/
│   │   └── Mailer.php ← Upload here (overwrite)
│   └── models/
│       ├── Authentication_model.php ← Upload here (overwrite)
│       └── Email_model.php ← Upload here (overwrite)
├── fix_csrf_hostinger.php ← Upload here (new)
├── simple_email_setup.php ← Upload here (new)
├── setup_email_config.php ← Upload here (new)
└── email_diagnostic.php ← Upload here (new)
```

---

## 🔍 Verification After Upload

### **Check File Permissions**
Ensure uploaded files have correct permissions:
- **Files:** 644 (readable by web server)
- **Directories:** 755 (if you created any new directories)

### **Test Access URLs**
After upload, these URLs should be accessible:

**Setup Tools:**
- https://passdrc.com/public_html/school/fix_csrf_hostinger.php
- https://passdrc.com/public_html/school/simple_email_setup.php
- https://passdrc.com/public_html/school/setup_email_config.php
- https://passdrc.com/public_html/school/email_diagnostic.php

**Main System:**
- https://passdrc.com/public_html/school/authentication/forgot (password recovery)
- https://passdrc.com/public_html/school/ (main system)

---

## ⚡ Quick Upload Checklist

**Before Upload:**
- [ ] Backup current files (download existing files first)
- [ ] Ensure you have all 8 files ready locally
- [ ] Verify Hostinger login credentials

**During Upload:**
- [ ] Upload 4 core system files (overwrite existing)
- [ ] Upload 4 setup tools (new files in root)
- [ ] Check file permissions after upload
- [ ] Verify no upload errors occurred

**After Upload:**
- [ ] Test setup tool URLs are accessible
- [ ] Run CSRF fix: `fix_csrf_hostinger.php`
- [ ] Configure email: `simple_email_setup.php`
- [ ] Test system: password recovery page
- [ ] Delete setup files after configuration

---

## 🆘 Troubleshooting Upload Issues

### **Common Upload Problems:**

**"File not found" errors:**
- Check exact file paths match the guide
- Ensure you're in the correct directory
- Verify file names are exactly as specified

**"Permission denied" errors:**
- Check FTP credentials are correct
- Ensure you have write permissions
- Try using Hostinger File Manager instead of FTP

**"Upload failed" errors:**
- Check file size limits (should be fine for these small files)
- Try uploading files one at a time
- Clear browser cache and try again

**"Cannot overwrite" errors:**
- Ensure you have permission to modify existing files
- Try deleting the old file first, then upload new one
- Check if files are currently in use by the system

### **File Size Reference:**
All files are small and should upload quickly:
- Core system files: ~10-50KB each
- Setup tools: ~15-30KB each
- Total upload size: <500KB

---

## 🔒 Security Notes

**After Configuration:**
1. **Delete setup files** immediately after use:
   - `fix_csrf_hostinger.php`
   - `simple_email_setup.php`
   - `setup_email_config.php`
   - `email_diagnostic.php`

2. **Keep core system files** - these are permanent fixes:
   - `Authentication_model.php`
   - `Email_model.php`
   - `Mailer.php`
   - `config.php`

**The core system files contain the permanent fixes for password recovery and email delivery, while the setup tools are temporary helpers for configuration.**
