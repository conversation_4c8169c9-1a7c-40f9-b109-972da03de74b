<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Autopilot
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Autopilot\V1;

use Twilio\Options;
use Twilio\Values;

abstract class AssistantOptions
{
    /**
     * @param string $friendlyName A descriptive string that you create to describe the new resource. It is not unique and can be up to 255 characters long.
     * @param bool $logQueries Whether queries should be logged and kept after training. Can be: `true` or `false` and defaults to `true`. If `true`, queries are stored for 30 days, and then deleted. If `false`, no queries are stored.
     * @param string $uniqueName An application-defined string that uniquely identifies the new resource. It can be used as an alternative to the `sid` in the URL path to address the resource. The first 64 characters must be unique.
     * @param string $callbackUrl Reserved.
     * @param string $callbackEvents Reserved.
     * @param array $styleSheet The JSON string that defines the Assistant's [style sheet](https://www.twilio.com/docs/autopilot/api/assistant/stylesheet)
     * @param array $defaults A JSON object that defines the Assistant's [default tasks](https://www.twilio.com/docs/autopilot/api/assistant/defaults) for various scenarios, including initiation actions and fallback tasks.
     * @return CreateAssistantOptions Options builder
     */
    public static function create(
        
        string $friendlyName = Values::NONE,
        bool $logQueries = Values::BOOL_NONE,
        string $uniqueName = Values::NONE,
        string $callbackUrl = Values::NONE,
        string $callbackEvents = Values::NONE,
        array $styleSheet = Values::ARRAY_NONE,
        array $defaults = Values::ARRAY_NONE

    ): CreateAssistantOptions
    {
        return new CreateAssistantOptions(
            $friendlyName,
            $logQueries,
            $uniqueName,
            $callbackUrl,
            $callbackEvents,
            $styleSheet,
            $defaults
        );
    }




    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.
     * @param bool $logQueries Whether queries should be logged and kept after training. Can be: `true` or `false` and defaults to `true`. If `true`, queries are stored for 30 days, and then deleted. If `false`, no queries are stored.
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used as an alternative to the `sid` in the URL path to address the resource. The first 64 characters must be unique.
     * @param string $callbackUrl Reserved.
     * @param string $callbackEvents Reserved.
     * @param array $styleSheet The JSON string that defines the Assistant's [style sheet](https://www.twilio.com/docs/autopilot/api/assistant/stylesheet)
     * @param array $defaults A JSON object that defines the Assistant's [default tasks](https://www.twilio.com/docs/autopilot/api/assistant/defaults) for various scenarios, including initiation actions and fallback tasks.
     * @param string $developmentStage A string describing the state of the assistant.
     * @return UpdateAssistantOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        bool $logQueries = Values::BOOL_NONE,
        string $uniqueName = Values::NONE,
        string $callbackUrl = Values::NONE,
        string $callbackEvents = Values::NONE,
        array $styleSheet = Values::ARRAY_NONE,
        array $defaults = Values::ARRAY_NONE,
        string $developmentStage = Values::NONE

    ): UpdateAssistantOptions
    {
        return new UpdateAssistantOptions(
            $friendlyName,
            $logQueries,
            $uniqueName,
            $callbackUrl,
            $callbackEvents,
            $styleSheet,
            $defaults,
            $developmentStage
        );
    }

}

class CreateAssistantOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the new resource. It is not unique and can be up to 255 characters long.
     * @param bool $logQueries Whether queries should be logged and kept after training. Can be: `true` or `false` and defaults to `true`. If `true`, queries are stored for 30 days, and then deleted. If `false`, no queries are stored.
     * @param string $uniqueName An application-defined string that uniquely identifies the new resource. It can be used as an alternative to the `sid` in the URL path to address the resource. The first 64 characters must be unique.
     * @param string $callbackUrl Reserved.
     * @param string $callbackEvents Reserved.
     * @param array $styleSheet The JSON string that defines the Assistant's [style sheet](https://www.twilio.com/docs/autopilot/api/assistant/stylesheet)
     * @param array $defaults A JSON object that defines the Assistant's [default tasks](https://www.twilio.com/docs/autopilot/api/assistant/defaults) for various scenarios, including initiation actions and fallback tasks.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        bool $logQueries = Values::BOOL_NONE,
        string $uniqueName = Values::NONE,
        string $callbackUrl = Values::NONE,
        string $callbackEvents = Values::NONE,
        array $styleSheet = Values::ARRAY_NONE,
        array $defaults = Values::ARRAY_NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['logQueries'] = $logQueries;
        $this->options['uniqueName'] = $uniqueName;
        $this->options['callbackUrl'] = $callbackUrl;
        $this->options['callbackEvents'] = $callbackEvents;
        $this->options['styleSheet'] = $styleSheet;
        $this->options['defaults'] = $defaults;
    }

    /**
     * A descriptive string that you create to describe the new resource. It is not unique and can be up to 255 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the new resource. It is not unique and can be up to 255 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Whether queries should be logged and kept after training. Can be: `true` or `false` and defaults to `true`. If `true`, queries are stored for 30 days, and then deleted. If `false`, no queries are stored.
     *
     * @param bool $logQueries Whether queries should be logged and kept after training. Can be: `true` or `false` and defaults to `true`. If `true`, queries are stored for 30 days, and then deleted. If `false`, no queries are stored.
     * @return $this Fluent Builder
     */
    public function setLogQueries(bool $logQueries): self
    {
        $this->options['logQueries'] = $logQueries;
        return $this;
    }

    /**
     * An application-defined string that uniquely identifies the new resource. It can be used as an alternative to the `sid` in the URL path to address the resource. The first 64 characters must be unique.
     *
     * @param string $uniqueName An application-defined string that uniquely identifies the new resource. It can be used as an alternative to the `sid` in the URL path to address the resource. The first 64 characters must be unique.
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * Reserved.
     *
     * @param string $callbackUrl Reserved.
     * @return $this Fluent Builder
     */
    public function setCallbackUrl(string $callbackUrl): self
    {
        $this->options['callbackUrl'] = $callbackUrl;
        return $this;
    }

    /**
     * Reserved.
     *
     * @param string $callbackEvents Reserved.
     * @return $this Fluent Builder
     */
    public function setCallbackEvents(string $callbackEvents): self
    {
        $this->options['callbackEvents'] = $callbackEvents;
        return $this;
    }

    /**
     * The JSON string that defines the Assistant's [style sheet](https://www.twilio.com/docs/autopilot/api/assistant/stylesheet)
     *
     * @param array $styleSheet The JSON string that defines the Assistant's [style sheet](https://www.twilio.com/docs/autopilot/api/assistant/stylesheet)
     * @return $this Fluent Builder
     */
    public function setStyleSheet(array $styleSheet): self
    {
        $this->options['styleSheet'] = $styleSheet;
        return $this;
    }

    /**
     * A JSON object that defines the Assistant's [default tasks](https://www.twilio.com/docs/autopilot/api/assistant/defaults) for various scenarios, including initiation actions and fallback tasks.
     *
     * @param array $defaults A JSON object that defines the Assistant's [default tasks](https://www.twilio.com/docs/autopilot/api/assistant/defaults) for various scenarios, including initiation actions and fallback tasks.
     * @return $this Fluent Builder
     */
    public function setDefaults(array $defaults): self
    {
        $this->options['defaults'] = $defaults;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Autopilot.V1.CreateAssistantOptions ' . $options . ']';
    }
}




class UpdateAssistantOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.
     * @param bool $logQueries Whether queries should be logged and kept after training. Can be: `true` or `false` and defaults to `true`. If `true`, queries are stored for 30 days, and then deleted. If `false`, no queries are stored.
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used as an alternative to the `sid` in the URL path to address the resource. The first 64 characters must be unique.
     * @param string $callbackUrl Reserved.
     * @param string $callbackEvents Reserved.
     * @param array $styleSheet The JSON string that defines the Assistant's [style sheet](https://www.twilio.com/docs/autopilot/api/assistant/stylesheet)
     * @param array $defaults A JSON object that defines the Assistant's [default tasks](https://www.twilio.com/docs/autopilot/api/assistant/defaults) for various scenarios, including initiation actions and fallback tasks.
     * @param string $developmentStage A string describing the state of the assistant.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        bool $logQueries = Values::BOOL_NONE,
        string $uniqueName = Values::NONE,
        string $callbackUrl = Values::NONE,
        string $callbackEvents = Values::NONE,
        array $styleSheet = Values::ARRAY_NONE,
        array $defaults = Values::ARRAY_NONE,
        string $developmentStage = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['logQueries'] = $logQueries;
        $this->options['uniqueName'] = $uniqueName;
        $this->options['callbackUrl'] = $callbackUrl;
        $this->options['callbackEvents'] = $callbackEvents;
        $this->options['styleSheet'] = $styleSheet;
        $this->options['defaults'] = $defaults;
        $this->options['developmentStage'] = $developmentStage;
    }

    /**
     * A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Whether queries should be logged and kept after training. Can be: `true` or `false` and defaults to `true`. If `true`, queries are stored for 30 days, and then deleted. If `false`, no queries are stored.
     *
     * @param bool $logQueries Whether queries should be logged and kept after training. Can be: `true` or `false` and defaults to `true`. If `true`, queries are stored for 30 days, and then deleted. If `false`, no queries are stored.
     * @return $this Fluent Builder
     */
    public function setLogQueries(bool $logQueries): self
    {
        $this->options['logQueries'] = $logQueries;
        return $this;
    }

    /**
     * An application-defined string that uniquely identifies the resource. It can be used as an alternative to the `sid` in the URL path to address the resource. The first 64 characters must be unique.
     *
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used as an alternative to the `sid` in the URL path to address the resource. The first 64 characters must be unique.
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * Reserved.
     *
     * @param string $callbackUrl Reserved.
     * @return $this Fluent Builder
     */
    public function setCallbackUrl(string $callbackUrl): self
    {
        $this->options['callbackUrl'] = $callbackUrl;
        return $this;
    }

    /**
     * Reserved.
     *
     * @param string $callbackEvents Reserved.
     * @return $this Fluent Builder
     */
    public function setCallbackEvents(string $callbackEvents): self
    {
        $this->options['callbackEvents'] = $callbackEvents;
        return $this;
    }

    /**
     * The JSON string that defines the Assistant's [style sheet](https://www.twilio.com/docs/autopilot/api/assistant/stylesheet)
     *
     * @param array $styleSheet The JSON string that defines the Assistant's [style sheet](https://www.twilio.com/docs/autopilot/api/assistant/stylesheet)
     * @return $this Fluent Builder
     */
    public function setStyleSheet(array $styleSheet): self
    {
        $this->options['styleSheet'] = $styleSheet;
        return $this;
    }

    /**
     * A JSON object that defines the Assistant's [default tasks](https://www.twilio.com/docs/autopilot/api/assistant/defaults) for various scenarios, including initiation actions and fallback tasks.
     *
     * @param array $defaults A JSON object that defines the Assistant's [default tasks](https://www.twilio.com/docs/autopilot/api/assistant/defaults) for various scenarios, including initiation actions and fallback tasks.
     * @return $this Fluent Builder
     */
    public function setDefaults(array $defaults): self
    {
        $this->options['defaults'] = $defaults;
        return $this;
    }

    /**
     * A string describing the state of the assistant.
     *
     * @param string $developmentStage A string describing the state of the assistant.
     * @return $this Fluent Builder
     */
    public function setDevelopmentStage(string $developmentStage): self
    {
        $this->options['developmentStage'] = $developmentStage;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Autopilot.V1.UpdateAssistantOptions ' . $options . ']';
    }
}

