<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Dfareporting_ChangeLog extends Google_Model
{
  public $accountId;
  public $action;
  public $changeTime;
  public $fieldName;
  public $id;
  public $kind;
  public $newValue;
  public $objectId;
  public $objectType;
  public $oldValue;
  public $subaccountId;
  public $transactionId;
  public $userProfileId;
  public $userProfileName;

  public function setAccountId($accountId)
  {
    $this->accountId = $accountId;
  }
  public function getAccountId()
  {
    return $this->accountId;
  }
  public function setAction($action)
  {
    $this->action = $action;
  }
  public function getAction()
  {
    return $this->action;
  }
  public function setChangeTime($changeTime)
  {
    $this->changeTime = $changeTime;
  }
  public function getChangeTime()
  {
    return $this->changeTime;
  }
  public function setFieldName($fieldName)
  {
    $this->fieldName = $fieldName;
  }
  public function getFieldName()
  {
    return $this->fieldName;
  }
  public function setId($id)
  {
    $this->id = $id;
  }
  public function getId()
  {
    return $this->id;
  }
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  public function getKind()
  {
    return $this->kind;
  }
  public function setNewValue($newValue)
  {
    $this->newValue = $newValue;
  }
  public function getNewValue()
  {
    return $this->newValue;
  }
  public function setObjectId($objectId)
  {
    $this->objectId = $objectId;
  }
  public function getObjectId()
  {
    return $this->objectId;
  }
  public function setObjectType($objectType)
  {
    $this->objectType = $objectType;
  }
  public function getObjectType()
  {
    return $this->objectType;
  }
  public function setOldValue($oldValue)
  {
    $this->oldValue = $oldValue;
  }
  public function getOldValue()
  {
    return $this->oldValue;
  }
  public function setSubaccountId($subaccountId)
  {
    $this->subaccountId = $subaccountId;
  }
  public function getSubaccountId()
  {
    return $this->subaccountId;
  }
  public function setTransactionId($transactionId)
  {
    $this->transactionId = $transactionId;
  }
  public function getTransactionId()
  {
    return $this->transactionId;
  }
  public function setUserProfileId($userProfileId)
  {
    $this->userProfileId = $userProfileId;
  }
  public function getUserProfileId()
  {
    return $this->userProfileId;
  }
  public function setUserProfileName($userProfileName)
  {
    $this->userProfileName = $userProfileName;
  }
  public function getUserProfileName()
  {
    return $this->userProfileName;
  }
}
