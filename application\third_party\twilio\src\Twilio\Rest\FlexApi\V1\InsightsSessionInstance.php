<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\FlexApi\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;


/**
 * @property string|null $workspaceId
 * @property string|null $sessionExpiry
 * @property string|null $sessionId
 * @property string|null $baseUrl
 * @property string|null $url
 */
class InsightsSessionInstance extends InstanceResource
{
    /**
     * Initialize the InsightsSessionInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     */
    public function __construct(Version $version, array $payload)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'workspaceId' => Values::array_get($payload, 'workspace_id'),
            'sessionExpiry' => Values::array_get($payload, 'session_expiry'),
            'sessionId' => Values::array_get($payload, 'session_id'),
            'baseUrl' => Values::array_get($payload, 'base_url'),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = [];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return InsightsSessionContext Context for this InsightsSessionInstance
     */
    protected function proxy(): InsightsSessionContext
    {
        if (!$this->context) {
            $this->context = new InsightsSessionContext(
                $this->version
            );
        }

        return $this->context;
    }

    /**
     * Create the InsightsSessionInstance
     *
     * @param array|Options $options Optional Arguments
     * @return InsightsSessionInstance Created InsightsSessionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(array $options = []): InsightsSessionInstance
    {

        return $this->proxy()->create($options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.FlexApi.V1.InsightsSessionInstance ' . \implode(' ', $context) . ']';
    }
}

