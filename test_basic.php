<?php
/**
 * Basic PHP Test for Hostinger
 * Upload this to: /domains/passdrc.com/public_html/school/
 * Access via: https://school.passdrc.com/test_basic.php
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>🔍 Basic PHP Test - Hostinger</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";

// Test 1: PHP Info
echo "<h2>✅ PHP Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";

// Test 2: File System
echo "<h2>📁 File System Check</h2>";
$files_to_check = [
    'index.php',
    '.htaccess',
    '.user.ini',
    'application',
    'application/config',
    'application/config/config.php',
    'application/config/database.php',
    'system',
    'system/core',
    'system/core/CodeIgniter.php'
];

foreach ($files_to_check as $file) {
    $exists = file_exists($file);
    $readable = $exists ? is_readable($file) : false;
    echo "<p><strong>$file:</strong> ";
    echo $exists ? "✅ Exists" : "❌ Missing";
    echo $readable ? " | ✅ Readable" : " | ❌ Not Readable";
    echo "</p>";
}

// Test 3: PHP Extensions
echo "<h2>🔌 PHP Extensions</h2>";
$required_extensions = ['mysqli', 'mbstring', 'json', 'openssl', 'curl'];
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "<p><strong>$ext:</strong> " . ($loaded ? "✅ Loaded" : "❌ Missing") . "</p>";
}

// Test 4: Memory and Limits
echo "<h2>⚙️ PHP Configuration</h2>";
echo "<p><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . "</p>";
echo "<p><strong>Upload Max Size:</strong> " . ini_get('upload_max_filesize') . "</p>";
echo "<p><strong>Post Max Size:</strong> " . ini_get('post_max_size') . "</p>";

echo "<hr>";
echo "<p><strong>✅ If you see this message, basic PHP is working!</strong></p>";
echo "<p><em>The issue is likely in CodeIgniter configuration or database connection.</em></p>";
?>
