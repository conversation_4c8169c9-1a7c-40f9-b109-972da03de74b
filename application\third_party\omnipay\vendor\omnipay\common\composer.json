{"name": "omnipay/common", "type": "library", "description": "Common components for Omnipay payment processing library", "keywords": ["gateway", "merchant", "omnipay", "pay", "payment", "purchase"], "homepage": "https://github.com/thephpleague/omnipay-common", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Del"}, {"name": "Omnipay Contributors", "homepage": "https://github.com/thephpleague/omnipay-common/contributors"}], "autoload": {"psr-4": {"Omnipay\\Common\\": "src/Common"}, "classmap": ["src/Omnipay.php"]}, "autoload-dev": {"psr-4": {"Omnipay\\Common\\": "tests/Common"}, "classmap": ["tests/OmnipayTest.php"]}, "require": {"php": "^7.2|^8", "php-http/client-implementation": "^1", "php-http/message": "^1.5", "php-http/message-factory": "^1.1", "php-http/discovery": "^1.14", "symfony/http-foundation": "^2.1|^3|^4|^5|^6", "moneyphp/money": "^3.1|^4.0.3"}, "require-dev": {"omnipay/tests": "^4.1", "php-http/mock-client": "^1", "php-http/guzzle7-adapter": "^1", "squizlabs/php_codesniffer": "^3.5"}, "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "suggest": {"league/omnipay": "The default Omnipay package provides a default HTTP Adapter."}, "scripts": {"test": "phpunit", "check-style": "phpcs -p --standard=PSR2 src/", "fix-style": "phpcbf -p --standard=PSR2 src/"}, "minimum-stability": "dev", "prefer-stable": true}