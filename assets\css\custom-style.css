html,
body {
	background: #f8fafc;
	width: 100%;
}

html {
	font-size: 10px;
	overflow-x: hidden;
	overflow-y: scroll;
}

body {
	color: #64748b;
	font-family: 'Signika', sans-serif;
	line-height: 22px;
	margin: 0;
	font-size: 14px;
}

a {
	color: #CCC;
}

a:hover, a:focus {
	color: #34d399; /* Updated to Emerald 400 */
}

a:active {
	color: #059669;
}

/* Layout Base - Main Wrapper */
.body {
	min-height: 100vh;
	width: 100%;
}

/* Layout Base - Header */
.header {
	height: 60px;
	left: 0;
	position: absolute;
	right: 0;
	top: 0;
}

/* Header Logo And Quick Menu */
.header .logo-env {
	float: left;
	height: 59px;
	width: 300px;
	left: 0;
	right: 0;
	top: 0;
	z-index: 99;
	min-width: 200px !important;
	background: linear-gradient(to right, #063944 0%, #313131 100%);
}

html.sidebar-light:not(.dark) .header .logo-env {
	background: linear-gradient(to right, rgb(130, 225, 234) 0%, #fff 100%);
}

/* Layout Base - Shortcut Menu */
.header .menu-icon-grid {
    width: 220px;
    padding: 0 8px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.header .menu-icon-grid > a {
    display: inline-flex;
    width: 96px;
    flex-direction: column;
    align-items: center;
    padding: 12px 12px;
    color: #3b82f6; /* Updated to Blue 500 for menu icon grid */
    border-radius: 4px;
    text-align: center;
    font-size: 13px;
}

html.dark .header .menu-icon-grid > a {
    color: #3b82f6; /* Updated to Blue 500 for menu icon grid in dark mode */
}

.header .menu-icon-grid > a i {
    font-size: 25px;
    margin-bottom: 4px;
}

.header .menu-icon-grid > a:hover {
    background: #34d399; /* Updated to Emerald 400 */
    color: #fff;
}

html.dark .header .menu-icon-grid > a:hover {
    color: #fff;
    background: #34d399; /* Updated to Emerald 400 */
}

/* Layout Base - Inner Wrapper */
.inner-wrapper {
	display: table;
	min-height: 100vh;
	padding-top: 60px;
	table-layout: fixed;
	overflow: hidden;
	width: 100%;
}

/* Layout Base - Content Body */
.content-body {
	display: table-cell;
	padding: 18px;
	position: relative;
	vertical-align: top;
}

html.fixed .content-body {
	-webkit-transition: margin-left .25s ease-in-out;
	-moz-transition: margin-left .25s ease-in-out;
	-o-transition: margin-left .25s ease-in-out;
	transition: margin-left .25s ease-in-out;
}

.content-body > .row + .row {
	padding-top: 10px;
}

/* Layout Base - Page Header */
.page-header {
	background: #1e293b;
	border-bottom: none;
	box-shadow: 1px 2px 4px 5px #CCCCCC;
	height: 50px;
	margin: -18px -18px 18px -18px;
	padding: 0;
	-webkit-transition: left .25s ease-in-out;
	-moz-transition: left .25s ease-in-out;
	-o-transition: left .25s ease-in-out;
	transition: left .25s ease-in-out;
}

/* Layout Base - Sidebar Left */
.sidebar-left {
	color: #cbd5e1;
	display: table-cell;
	position: relative;
	vertical-align: top;
	width: 300px;
	z-index: 1010;
	-webkit-transition: width .25s ease-in-out;
	-moz-transition: width .25s ease-in-out;
	-o-transition: width .25s ease-in-out;
	transition: width .25s ease-in-out;
}

.sidebar-left .nav-main a span {
	opacity: 1;
	visibility: visible;
	-webkit-transition: .2s;
	-moz-transition: .2s;
	-o-transition: .2s;
	transition: .2s;
}

/* Layout Base - Sidebar Left Opened ( Larger than mobile ) */
@media only screen and (min-width: 768px) {
	html.sidebar-left-collapsed .sidebar-left {
		width: 73px;
	}
}
/* Layout Base - Sidebar Right */
.sidebar-right {
	background: #0f172a;
	bottom: 0;
	margin-right: -300px;
	min-height: 100vh;
	position: fixed;
	right: 0;
	top: 0;
	width: 300px;
}

/* Layout Base - Sidebar Right Opened ( Larger than mobile ) */
@media only screen and (min-width: 768px) {
	html.sidebar-right-opened .header {
		margin-left: -300px;
		margin-right: 300px;
	}

	html.sidebar-right-opened .inner-wrapper {
		margin-left: -300px;
	}

	html.sidebar-right-opened .sidebar-right {
		margin-right: 0;
	}

	html.sidebar-right-opened.has-top-menu .inner-wrapper {
		margin-left: 0;
		padding-right: 300px;
	}
}
/* Layout Base - Sidebar Right Opened - Has Top Horizontal Menu ( Ipad ) */
@media only screen and (min-width: 768px) and (max-width: 991px) {
	html.sidebar-right-opened.has-top-menu .content-body {
		right: 300px;
	}
}
/* Layout Base - Flexbox supported */
@media only screen and (min-width: 768px) {
	html.flexbox .inner-wrapper,
	html.flexboxlegacy .inner-wrapper {
		display: -webkit-box;
		display: -moz-box;
		display: box;
		display: -webkit-flex;
		display: -moz-flex;
		display: -ms-flexbox;
		display: flex;
	}

	html.flexbox .sidebar-left,
	html.flexbox .content-body,
	html.flexboxlegacy .sidebar-left,
	html.flexboxlegacy .content-body {
		display: block;
		-webkit-flex-shrink: 0;
		-moz-flex-shrink: 0;
		flex-shrink: 0;
		-ms-flex-negative: 0;
	}

	html.flexbox .content-body,
	html.flexboxlegacy .content-body {
		-webkit-box-flex: 2;
		-moz-box-flex: 2;
		box-flex: 2;
		-webkit-flex: 2;
		-moz-flex: 2;
		-ms-flex: 2;
		flex: 2;
		min-width: 1px;
	}
}
/* Layout Fixed */
@media only screen and (min-width: 768px) {
	/* Layout Fixed - Reseting Styles */
	html.fixed .inner-wrapper,
	html.fixed .sidebar-left,
	html.fixed .content-body {
		display: block;
	}

	/* Layout Fixed - Header */
	html.fixed .header {
		position: fixed;
		z-index: 1020;
	}

	/* Layout Fixed - Inner Wrapper */
	html.fixed .inner-wrapper {
		padding-top: 110px;
	}

	/* Layout Fixed - Content Body */
	html.fixed .content-body {
		margin-left: 300px;
	}

	html.fixed .content-body.has-toolbar {
		padding-top: 92px;
	}

	html.fixed.has-top-menu .content-body {
		margin-left: 0;
	}

	/* Layout Fixed - Page header */
	html.fixed .page-header {
		left: 300px;
		margin: 0;
		position: fixed;
		right: 0;
		top: 60px;
	}

	html.fixed.has-top-menu .page-header {
		left: 0;
	}

	/* Layout Fixed - Sidebar Left */
	html.fixed .sidebar-left {
		bottom: 0;
		left: 0;
		padding-bottom: 50px;
		position: fixed;
		top: 60px;
	}

	html.fixed .sidebar-left .nano-content {
		padding-bottom: 50px;
	}

	/* Layout Fixed - Sidebar Left Collapsed */
	html.fixed.sidebar-left-collapsed .page-header {
		left: 73px;
	}

	html.fixed.sidebar-left-collapsed .content-body {
		margin-left: 73px;
	}

	/* Layout Fixed - Sidebar Right Opened */
	html.fixed.sidebar-right-opened .page-header {
		left: 0;
		margin-right: 300px;
	}

	html.fixed.sidebar-right-opened .sidebar-left {
		left: -300px;
	}

	/* Layout Fixed - Sidebar Left Collapsed & Sidebar Right Opened */
	html.fixed.sidebar-left-collapsed.sidebar-right-opened .page-header {
		left: -300px;
	}
}

@media only screen and (max-width: 767px) {
	html,
	body {
		background: #ecedf0;
	}

	html.mobile-device .sidebar-left,
	html.mobile-device .sidebar-right {
		overflow-y: scroll;
		overflow-x: hidden;
		-webkit-overflow-scrolling: touch;
	}

	body {
		min-height: 100vh;
	}

	.inner-wrapper,
	.sidebar-left,
	.content-body {
		display: block;
	}

	.body {
		min-height: 0;
		overflow: visible;
	}

	.header {
		background: none;
		border: none;
		height: auto;
		position: static;
	}

	.header .logo-env {
		position: fixed;
		width: 100%;
		height: 60px;
	}

	.header .header-right {
		background: #FFF;
		float: none !important;
		height: 60px;
		margin-top: 60px;
		width: 100%;
		display: block;
	}

	.inner-wrapper {
		min-height: 0;
		padding-top: 0;
	}

	.content-body {
		padding: 0 15px 15px;
	}

	.page-header {
		margin: 0 -15px 20px;
	}

	.sidebar-left {
		bottom: 0;
		left: -100%;
		min-height: 0;
		min-width: 100%;
		min-width: 100vw;
		padding-top: 60px;
		padding-bottom: 50px;
		position: fixed;
		overflow: hidden;
		top: 0;
		z-index: 98 !important;
	}

	.sidebar-right {
		bottom: 0;
		left: auto;
		right: -100%;
		min-height: 0;
		margin-right: 0;
		min-width: 100%;
		min-width: 100vw;
		top: 0;
		z-index: 100;
	}

	html.csstransforms .sidebar-left,
	html.csstransforms .sidebar-right {
		/* performs better but native android browser
		has problems with translate and percentage
		@include transition-property(transform);
		*/
		-webkit-transition-property: margin;
		-moz-transition-property: margin;
		transition-property: margin;
		-webkit-transition-duration: 0.25s;
		-moz-transition-duration: 0.25s;
		transition-duration: 0.25s;
		-webkit-transition-timing-function: ease-out;
		-moz-transition-timing-function: ease-out;
		transition-timing-function: ease-out;
		-webkit-transition-delay: 0;
		-moz-transition-delay: 0;
		transition-delay: 0;
	}

	html.csstransforms .sidebar-left {
		/* performs better but native android browser
		has problems with translate and percentage
		@include transform( translateX(0) );
		*/
		margin-left: -25px;
	}

	html.csstransforms .sidebar-right {
		/* performs better but native android browser
		has problems with translate and percentage
		@include transform( translateX(0) );
		*/
		margin-right: -25px;
	}

	/* If desktop is seeing mobile res, fix scrollbars */
	html.no-mobile-device body {
		min-height: 0;
	}

	html.no-mobile-device .body {
		min-height: 100vh;
		overflow: hidden;
	}

	html.no-mobile-device .inner-wrapper {
		overflow-y: auto;
	}

	html.no-mobile-device.sidebar-left-opened, html.no-mobile-device.sidebar-left-opened body, html.no-mobile-device.sidebar-right-opened, html.no-mobile-device.sidebar-right-opened body {
		overflow: hidden;
	}

	/* Layout Mobile - Sidebar Left Opened */
	html.sidebar-left-opened.no-csstransforms .sidebar-left {
		left: 0;
	}

	html.sidebar-left-opened.csstransforms .sidebar-left {
		/* performs better but native android browser
		has problems with translate and percentage
		@include transform( translateX(100%) );
		*/
		margin-left: 100%;
	}

	/* Layout Mobile - Sidebar Right Opened */
	html.sidebar-right-opened.no-csstransforms .sidebar-right {
		right: 0;
	}

	html.sidebar-right-opened.csstransforms .sidebar-right {
		/* performs better but native android browser
		has problems with translate and percentage
		@include transform( translateX(-100%) );
		*/
		margin-right: 100%;
	}

	/* Layout Mobile - Sidebar Left Collapsed & Sidebar Right Opened */
	html.sidebar-left-collapsed.sidebar-right-opened .sidebar-left {
		margin-left: -300px;
	}
}
/* iOS10 Content Width Fix */
@media (min-width: 768px) {
	html.mobile-device.flexbox .content-body {
		width: calc(100vw - 230px) !important;
	}
}
/* Content With Menu - Boxed Layout Fixing Spacement on Bottom */
@media only screen and (min-width: 1200px) {
	html.boxed .content-with-menu {
		margin-bottom: -40px;
	}
}
/* Content With Menu - Container */
@media only screen and (min-width: 768px) {
	.content-with-menu-container {
		display: table;
		table-layout: fixed;
		width: 100%;
	}
}
/* Content With Menu - Menu Faux Column for Scroll and Boxed Layouts */
@media only screen and (min-width: 768px) {
	html.scroll .content-with-menu:before,
	html.boxed .content-with-menu:before {
		bottom: -47px;
		content: '';
		display: block;
		left: 0;
		position: absolute;
		top: 54px;
		width: 300px;
	}

	html.scroll .content-with-menu:after,
	html.boxed .content-with-menu:after {
		bottom: -46px;
		content: '';
		display: block;
		left: -1px;
		position: absolute;
		top: 54px;
		width: 1px;
		z-index: 3;
	}

	html.boxed .content-with-menu:before {
		bottom: 0;
	}

	html.boxed .content-with-menu:after {
		bottom: 2px;
	}
}

.content-with-menu {
	margin: -20px -15px 0;
}

/* Content With Menu - Responsive */
@media only screen and (max-width: 767px) {
	.content-with-menu {
		clear: both;
	}

	.inner-body {
		padding: 40px 15px 0;
	}
}
/* Content With Menu - Menu and Body */
@media only screen and (min-width: 768px) {
	.content-with-menu {
		border-top: 110px solid transparent;
		margin: -150px -40px -53px -40px;
		min-height: 100vh;
	}

	.inner-menu {
		display: table-cell;
		vertical-align: top;
	}

	.inner-body {
		display: table-cell;
		vertical-align: top;
		padding: 40px;
	}

	.inner-toolbar {
		height: 52px;
		overflow: hidden;
	}

	.content-with-menu-has-toolbar .inner-menu-toggle {
		border-radius: 0;
	}

	.content-with-menu-has-toolbar .inner-toolbar {
		padding-left: 140px;
	}
}
/* Content With Menu - Flexbox supported */
@media only screen and (min-width: 768px) {
	html.flexbox .content-with-menu-container,
	html.flexboxlegacy .content-with-menu-container {
		display: -webkit-box;
		display: -moz-box;
		display: box;
		display: -webkit-flex;
		display: -moz-flex;
		display: -ms-flexbox;
		display: flex;
	}

	html.flexbox .inner-menu,
	html.flexbox .inner-body,
	html.flexboxlegacy .inner-menu,
	html.flexboxlegacy .inner-body {
		display: block;
		-webkit-flex-shrink: 0;
		-moz-flex-shrink: 0;
		flex-shrink: 0;
		-ms-flex-negative: 0;
	}

	html.flexbox .inner-body,
	html.flexboxlegacy .inner-body {
		-webkit-box-flex: 2;
		-moz-box-flex: 2;
		box-flex: 2;
		-webkit-flex: 2;
		-moz-flex: 2;
		-ms-flex: 2;
		flex: 2;
	}
}
/* Content With Menu + Layout Fixed */
@media only screen and (min-width: 768px) {
	html.fixed .content-with-menu-container,
	html.fixed .inner-menu,
	html.fixed .inner-body {
		display: block;
	}

	html.fixed .content-with-menu-container {
		position: relative;
	}

	html.fixed .inner-menu-toggle {
		position: absolute;
		top: 114px;
		border-radius: 0 0 5px 0;
		width: 140px;
		z-index: 1002;
	}

	html.fixed .inner-menu {
		bottom: 0;
		display: block;
		left: 300px;
		position: fixed;
		margin: 0;
		top: 114px;
		width: 300px;
		padding: 35px;
		z-index: 1002;
	}

	html.fixed .inner-menu-content {
		display: block;
	}

	html.fixed .inner-body {
		margin-left: 300px;
		border-top: 113px solid transparent;
		margin-top: -110px;
		min-height: 100vh;
		position: relative;
	}

	html.fixed .content-with-menu-has-toolbar .inner-body {
		border-top-width: 165px;
	}
}
/* Content With Menu + Layout Scroll & Boxed */
@media only screen and (min-width: 768px) {
	html.scroll .inner-menu,
	html.scroll .inner-body,
	html.boxed .inner-menu,
	html.boxed .inner-body {
		display: block;
	}

	html.scroll .content-with-menu-container,
	html.boxed .content-with-menu-container {
		position: relative;
	}

	html.scroll .inner-menu-toggle,
	html.boxed .inner-menu-toggle {
		position: absolute;
		top: 0;
		border-radius: 0 0 5px 0;
		width: 140px;
		z-index: 3;
	}

	html.scroll .inner-menu,
	html.boxed .inner-menu {
		display: block;
		position: relative;
		margin: 0;
		width: 300px;
		padding: 35px;
	}

	html.scroll .inner-menu-content,
	html.boxed .inner-menu-content {
		display: block;
	}

	html.scroll .inner-body,
	html.boxed .inner-body {
		margin-left: 0;
		min-height: 100vh;
		position: relative;
	}

	html.scroll.flexbox .content-with-menu-container, html.scroll.flexboxlegacy .content-with-menu-container,
	html.boxed.flexbox .content-with-menu-container,
	html.boxed.flexboxlegacy .content-with-menu-container {
		display: -webkit-box;
		display: -moz-box;
		display: box;
		display: -webkit-flex;
		display: -moz-flex;
		display: -ms-flexbox;
		display: flex;
	}
}
/* Content With Menu + Layout Fixed + Sidebar Left Collapsed */
@media only screen and (min-width: 768px) {
	html.fixed.sidebar-left-collapsed .inner-menu,
	html.fixed.sidebar-left-collapsed .inner-menu-toggle,
	html.fixed.sidebar-left-collapsed .inner-toolbar {
		left: 73px;
	}

	html.fixed.sidebar-left-collapsed.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-collapsed.inner-menu-opened .inner-toolbar {
		left: 373px;
	}
}
/* Content With Menu + Layout Fixed + Sidebar Right Opened */
@media only screen and (min-width: 768px) {
	html.fixed.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-right-opened .inner-menu-toggle,
	html.fixed.sidebar-right-opened .inner-toolbar {
		left: 0px;
	}

	html.fixed.sidebar-right-opened .inner-toolbar {
		margin-right: 300px;
	}

	html.fixed.sidebar-right-opened.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-right-opened.inner-menu-opened .inner-toolbar {
		left: -300px;
	}
}
/* Content With Menu + Layout Fixed + Sidebar Left Collapsed + Sidebar Right Opened */
@media only screen and (min-width: 768px) {
	html.fixed.sidebar-left-collapsed.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-left-collapsed.sidebar-right-opened .inner-menu-toggle,
	html.fixed.sidebar-left-collapsed.sidebar-right-opened .inner-toolbar {
		left: -227px;
	}

	html.fixed.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .inner-toolbar {
		left: -527px;
	}
}
/* Resolution gt 767 and lt 1366 - Hide Inner Menu */
@media only screen and (min-width: 768px) and (max-width: 1365px) {
	html.fixed .inner-menu,
	html.scroll .inner-menu,
	html.boxed .inner-menu {
		display: none;
	}

	html.fixed .inner-menu-toggle,
	html.scroll .inner-menu-toggle,
	html.boxed .inner-menu-toggle {
		display: block;
	}

	html.fixed .inner-body,
	html.scroll .inner-body,
	html.boxed .inner-body {
		margin-left: 0;
	}

	html.fixed .content-with-menu-has-toolbar .inner-toolbar,
	html.scroll .content-with-menu-has-toolbar .inner-toolbar,
	html.boxed .content-with-menu-has-toolbar .inner-toolbar {
		padding-left: 140px;
	}

	html.fixed.inner-menu-opened .inner-menu,
	html.scroll.inner-menu-opened .inner-menu,
	html.boxed.inner-menu-opened .inner-menu {
		display: block;
	}

	html.fixed.inner-menu-opened .inner-menu-toggle,
	html.scroll.inner-menu-opened .inner-menu-toggle,
	html.boxed.inner-menu-opened .inner-menu-toggle {
		display: none;
	}

	html.fixed.inner-menu-opened .inner-body,
	html.scroll.inner-menu-opened .inner-body,
	html.boxed.inner-menu-opened .inner-body {
		margin-right: -300px;
	}

	html.fixed.inner-menu-opened .content-with-menu-has-toolbar .inner-toolbar,
	html.scroll.inner-menu-opened .content-with-menu-has-toolbar .inner-toolbar,
	html.boxed.inner-menu-opened .content-with-menu-has-toolbar .inner-toolbar {
		padding-left: 0;
	}

	html.fixed.inner-menu-opened .inner-body {
		margin-left: 300px;
	}

	html.scroll .content-with-menu:before,
	html.boxed .content-with-menu:before {
		display: none;
	}

	html.scroll.inner-menu-opened:before,
	html.boxed.inner-menu-opened:before {
		display: block;
	}
}
/* Resolution gt 1366 - Show Inner Menu */
@media only screen and (min-width: 1366px) {
	html.fixed .inner-menu,
	html.scroll .inner-menu,
	html.boxed .inner-menu {
		display: block;
	}

	html.fixed .inner-menu-toggle,
	html.fixed .inner-menu-toggle-inside,
	html.scroll .inner-menu-toggle,
	html.scroll .inner-menu-toggle-inside,
	html.boxed .inner-menu-toggle,
	html.boxed .inner-menu-toggle-inside {
		display: none;
	}

	html.fixed .inner-body,
	html.scroll .inner-body,
	html.boxed .inner-body {
		margin-right: 0;
	}

	html.fixed .content-with-menu-has-toolbar .inner-toolbar,
	html.scroll .content-with-menu-has-toolbar .inner-toolbar,
	html.boxed .content-with-menu-has-toolbar .inner-toolbar {
		padding-left: 0;
	}

	html.fixed.inner-menu-opened .inner-body {
		margin-left: 300px;
	}

	html.fixed .content-with-menu .inner-toolbar,
	html.fixed.inner-menu-opened .content-with-menu .inner-toolbar {
		left: 600px;
	}

	html.fixed .inner-menu-toggle,
	html.fixed .inner-menu,
	html.fixed.inner-menu-opened .inner-menu-toggle,
	html.fixed.inner-menu-opened .inner-menu {
		left: 300px;
	}

	html.fixed.sidebar-right-opened .content-with-menu .inner-toolbar {
		left: 300px;
	}

	html.fixed.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-right-opened .inner-menu-toggle {
		left: 0px;
	}

	html.fixed.sidebar-left-collapsed .content-with-menu .inner-toolbar,
	html.fixed.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .content-with-menu .inner-toolbar,
	html.fixed.sidebar-left-collapsed.inner-menu-opened .content-with-menu .inner-toolbar {
		left: 373px;
	}

	html.fixed.sidebar-left-collapsed .inner-menu-toggle,
	html.fixed.sidebar-left-collapsed .inner-menu,
	html.fixed.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .inner-menu,
	html.fixed.sidebar-left-collapsed.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-collapsed.inner-menu-opened .inner-menu {
		left: 73px;
	}

	html.fixed.sidebar-left-collapsed.sidebar-right-opened .content-with-menu .inner-toolbar {
		left: 73px;
	}

	html.fixed.sidebar-left-collapsed.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-left-collapsed.sidebar-right-opened .inner-menu-toggle {
		left: -227px;
	}
}
/* Fix IE Scrollbar Overlaying content */
@-ms-viewport {
	width: auto !important;
}
/* ------------------------------------------------------------------------------------------------------------------------------------------
BOOTSTRAP EXTEND
------------------------------------------------------------------------------------------------------------------------------------------ */
/* Add New Grid Tier FOR NON BOXED LAYOUT */
html.scroll,
html.fixed {
	/* UNDO original bootrap LG helper classes*/
	/* Helper classes for XL */;
}

@media (min-width: 1600px) {
	html.scroll .container,
	html.fixed .container {
		width: 1570px;
	}
}

html.scroll .col-xl-1, html.scroll .col-xl-2, html.scroll .col-xl-3, html.scroll .col-xl-4, html.scroll .col-xl-5, html.scroll .col-xl-6, html.scroll .col-xl-7, html.scroll .col-xl-8, html.scroll .col-xl-9, html.scroll .col-xl-10, html.scroll .col-xl-11, html.scroll .col-xl-12,
html.fixed .col-xl-1,
html.fixed .col-xl-2,
html.fixed .col-xl-3,
html.fixed .col-xl-4,
html.fixed .col-xl-5,
html.fixed .col-xl-6,
html.fixed .col-xl-7,
html.fixed .col-xl-8,
html.fixed .col-xl-9,
html.fixed .col-xl-10,
html.fixed .col-xl-11,
html.fixed .col-xl-12 {
	position: relative;
	min-height: 1px;
	padding-right: 15px;
	padding-left: 15px;
}

@media (min-width: 1600px) {
	html.scroll .col-xl-1, html.scroll .col-xl-2, html.scroll .col-xl-3, html.scroll .col-xl-4, html.scroll .col-xl-5, html.scroll .col-xl-6, html.scroll .col-xl-7, html.scroll .col-xl-8, html.scroll .col-xl-9, html.scroll .col-xl-10, html.scroll .col-xl-11, html.scroll .col-xl-12,
	html.fixed .col-xl-1,
	html.fixed .col-xl-2,
	html.fixed .col-xl-3,
	html.fixed .col-xl-4,
	html.fixed .col-xl-5,
	html.fixed .col-xl-6,
	html.fixed .col-xl-7,
	html.fixed .col-xl-8,
	html.fixed .col-xl-9,
	html.fixed .col-xl-10,
	html.fixed .col-xl-11,
	html.fixed .col-xl-12 {
		float: left;
	}

	html.scroll .col-xl-12,
	html.fixed .col-xl-12 {
		width: 100%;
	}

	html.scroll .col-xl-11,
	html.fixed .col-xl-11 {
		width: 91.66666667%;
	}

	html.scroll .col-xl-10,
	html.fixed .col-xl-10 {
		width: 83.33333333%;
	}

	html.scroll .col-xl-9,
	html.fixed .col-xl-9 {
		width: 75%;
	}

	html.scroll .col-xl-8,
	html.fixed .col-xl-8 {
		width: 66.66666667%;
	}

	html.scroll .col-xl-7,
	html.fixed .col-xl-7 {
		width: 58.33333333%;
	}

	html.scroll .col-xl-6,
	html.fixed .col-xl-6 {
		width: 50%;
	}

	html.scroll .col-xl-5,
	html.fixed .col-xl-5 {
		width: 41.66666667%;
	}

	html.scroll .col-xl-4,
	html.fixed .col-xl-4 {
		width: 33.33333333%;
	}

	html.scroll .col-xl-3,
	html.fixed .col-xl-3 {
		width: 25%;
	}

	html.scroll .col-xl-2,
	html.fixed .col-xl-2 {
		width: 16.66666667%;
	}

	html.scroll .col-xl-1,
	html.fixed .col-xl-1 {
		width: 8.33333333%;
	}

	html.scroll .col-xl-pull-12,
	html.fixed .col-xl-pull-12 {
		right: 100%;
	}

	html.scroll .col-xl-pull-11,
	html.fixed .col-xl-pull-11 {
		right: 91.66666667%;
	}

	html.scroll .col-xl-pull-10,
	html.fixed .col-xl-pull-10 {
		right: 83.33333333%;
	}

	html.scroll .col-xl-pull-9,
	html.fixed .col-xl-pull-9 {
		right: 75%;
	}

	html.scroll .col-xl-pull-8,
	html.fixed .col-xl-pull-8 {
		right: 66.66666667%;
	}

	html.scroll .col-xl-pull-7,
	html.fixed .col-xl-pull-7 {
		right: 58.33333333%;
	}

	html.scroll .col-xl-pull-6,
	html.fixed .col-xl-pull-6 {
		right: 50%;
	}

	html.scroll .col-xl-pull-5,
	html.fixed .col-xl-pull-5 {
		right: 41.66666667%;
	}

	html.scroll .col-xl-pull-4,
	html.fixed .col-xl-pull-4 {
		right: 33.33333333%;
	}

	html.scroll .col-xl-pull-3,
	html.fixed .col-xl-pull-3 {
		right: 25%;
	}

	html.scroll .col-xl-pull-2,
	html.fixed .col-xl-pull-2 {
		right: 16.66666667%;
	}

	html.scroll .col-xl-pull-1,
	html.fixed .col-xl-pull-1 {
		right: 8.33333333%;
	}

	html.scroll .col-xl-pull-0,
	html.fixed .col-xl-pull-0 {
		right: auto;
	}

	html.scroll .col-xl-push-12,
	html.fixed .col-xl-push-12 {
		left: 100%;
	}

	html.scroll .col-xl-push-11,
	html.fixed .col-xl-push-11 {
		left: 91.66666667%;
	}

	html.scroll .col-xl-push-10,
	html.fixed .col-xl-push-10 {
		left: 83.33333333%;
	}

	html.scroll .col-xl-push-9,
	html.fixed .col-xl-push-9 {
		left: 75%;
	}

	html.scroll .col-xl-push-8,
	html.fixed .col-xl-push-8 {
		left: 66.66666667%;
	}

	html.scroll .col-xl-push-7,
	html.fixed .col-xl-push-7 {
		left: 58.33333333%;
	}

	html.scroll .col-xl-push-6,
	html.fixed .col-xl-push-6 {
		left: 50%;
	}

	html.scroll .col-xl-push-5,
	html.fixed .col-xl-push-5 {
		left: 41.66666667%;
	}

	html.scroll .col-xl-push-4,
	html.fixed .col-xl-push-4 {
		left: 33.33333333%;
	}

	html.scroll .col-xl-push-3,
	html.fixed .col-xl-push-3 {
		left: 25%;
	}

	html.scroll .col-xl-push-2,
	html.fixed .col-xl-push-2 {
		left: 16.66666667%;
	}

	html.scroll .col-xl-push-1,
	html.fixed .col-xl-push-1 {
		left: 8.33333333%;
	}

	html.scroll .col-xl-push-0,
	html.fixed .col-xl-push-0 {
		left: auto;
	}

	html.scroll .col-xl-offset-12,
	html.fixed .col-xl-offset-12 {
		margin-left: 100%;
	}

	html.scroll .col-xl-offset-11,
	html.fixed .col-xl-offset-11 {
		margin-left: 91.66666667%;
	}

	html.scroll .col-xl-offset-10,
	html.fixed .col-xl-offset-10 {
		margin-left: 83.33333333%;
	}

	html.scroll .col-xl-offset-9,
	html.fixed .col-xl-offset-9 {
		margin-left: 75%;
	}

	html.scroll .col-xl-offset-8,
	html.fixed .col-xl-offset-8 {
		margin-left: 66.66666667%;
	}

	html.scroll .col-xl-offset-7,
	html.fixed .col-xl-offset-7 {
		margin-left: 58.33333333%;
	}

	html.scroll .col-xl-offset-6,
	html.fixed .col-xl-offset-6 {
		margin-left: 50%;
	}

	html.scroll .col-xl-offset-5,
	html.fixed .col-xl-offset-5 {
		margin-left: 41.66666667%;
	}

	html.scroll .col-xl-offset-4,
	html.fixed .col-xl-offset-4 {
		margin-left: 33.33333333%;
	}

	html.scroll .col-xl-offset-3,
	html.fixed .col-xl-offset-3 {
		margin-left: 25%;
	}

	html.scroll .col-xl-offset-2,
	html.fixed .col-xl-offset-2 {
		margin-left: 16.66666667%;
	}

	html.scroll .col-xl-offset-1,
	html.fixed .col-xl-offset-1 {
		margin-left: 8.33333333%;
	}

	html.scroll .col-xl-offset-0,
	html.fixed .col-xl-offset-0 {
		margin-left: 0;
	}
}

html.scroll .visible-xl,
html.fixed .visible-xl {
	display: none !important;
}

html.scroll .visible-xl-block,
html.scroll .visible-xl-inline,
html.scroll .visible-xl-inline-block,
html.fixed .visible-xl-block,
html.fixed .visible-xl-inline,
html.fixed .visible-xl-inline-block {
	display: none !important;
}

@media (min-width: 1200px) and (max-width: 1599px) {
	html.scroll .visible-lg,
	html.fixed .visible-lg {
		display: block !important;
	}

	html.scroll table.visible-lg,
	html.fixed table.visible-lg {
		display: table;
	}

	html.scroll tr.visible-lg,
	html.fixed tr.visible-lg {
		display: table-row !important;
	}

	html.scroll th.visible-lg,
	html.scroll td.visible-lg,
	html.fixed th.visible-lg,
	html.fixed td.visible-lg {
		display: table-cell !important;
	}

	html.scroll .visible-lg-block,
	html.fixed .visible-lg-block {
		display: block !important;
	}

	html.scroll .visible-lg-inline,
	html.fixed .visible-lg-inline {
		display: inline !important;
	}

	html.scroll .visible-lg-inline-block,
	html.fixed .visible-lg-inline-block {
		display: inline-block !important;
	}

	html.scroll .hidden-lg,
	html.fixed .hidden-lg {
		display: none !important;
	}
}

@media (min-width: 1600px) {
	html.scroll .visible-lg-block,
	html.fixed .visible-lg-block {
		display: none !important;
	}

	html.scroll .visible-lg-inline,
	html.fixed .visible-lg-inline {
		display: none !important;
	}

	html.scroll .visible-lg-inline-block,
	html.fixed .visible-lg-inline-block {
		display: none !important;
	}
}

@media (min-width: 1600px) {
	html.scroll .visible-xl,
	html.fixed .visible-xl {
		display: block !important;
	}

	html.scroll table.visible-xl,
	html.fixed table.visible-xl {
		display: table;
	}

	html.scroll tr.visible-xl,
	html.fixed tr.visible-xl {
		display: table-row !important;
	}

	html.scroll th.visible-xl,
	html.scroll td.visible-xl,
	html.fixed th.visible-xl,
	html.fixed td.visible-xl {
		display: table-cell !important;
	}

	html.scroll .visible-xl-block,
	html.fixed .visible-xl-block {
		display: block !important;
	}

	html.scroll .visible-xl-inline,
	html.fixed .visible-xl-inline {
		display: inline !important;
	}

	html.scroll .visible-xl-inline-block,
	html.fixed .visible-xl-inline-block {
		display: inline-block !important;
	}

	html.scroll .hidden-xl,
	html.fixed .hidden-xl {
		display: none !important;
	}
}

@media screen and (max-width: 991px) {
	.table-responsive {
		width: 100%;
		margin-bottom: 15px;
		overflow-x: auto;
		overflow-y: hidden;
		-webkit-overflow-scrolling: touch;
		-ms-overflow-style: -ms-autohiding-scrollbar;
		border: 1px solid #ddd;
	}

	.table-responsive > .table {
		margin-bottom: 0;
	}

	.table-responsive > .table > thead > tr > th,
	.table-responsive > .table > tbody > tr > th,
	.table-responsive > .table > tfoot > tr > th,
	.table-responsive > .table > thead > tr > td,
	.table-responsive > .table > tbody > tr > td,
	.table-responsive > .table > tfoot > tr > td {
		white-space: nowrap;
	}

	.table-responsive > .table-bordered {
		border: 0;
	}

	.table-responsive > .table-bordered > thead > tr > th:first-child,
	.table-responsive > .table-bordered > tbody > tr > th:first-child,
	.table-responsive > .table-bordered > tfoot > tr > th:first-child,
	.table-responsive > .table-bordered > thead > tr > td:first-child,
	.table-responsive > .table-bordered > tbody > tr > td:first-child,
	.table-responsive > .table-bordered > tfoot > tr > td:first-child {
		border-left: 0;
	}

	.table-responsive > .table-bordered > thead > tr > th:last-child,
	.table-responsive > .table-bordered > tbody > tr > th:last-child,
	.table-responsive > .table-bordered > tfoot > tr > th:last-child,
	.table-responsive > .table-bordered > thead > tr > td:last-child,
	.table-responsive > .table-bordered > tbody > tr > td:last-child,
	.table-responsive > .table-bordered > tfoot > tr > td:last-child {
		border-right: 0;
	}

	.table-responsive > .table-bordered > tbody > tr:last-child > th,
	.table-responsive > .table-bordered > tfoot > tr:last-child > th,
	.table-responsive > .table-bordered > tbody > tr:last-child > td,
	.table-responsive > .table-bordered > tfoot > tr:last-child > td {
		border-bottom: 0;
	}
}
/* Fix img-thumbnail - IE10 and below */
.img-thumbnail {
	width: auto \9;
}

/* Header */
.header {
	background: #FFF;
	border-bottom: 1px solid #E9E9E6;
	z-index: 1000;
}

.header .logo {
	display: block;
	text-align: center;
	margin-top: 10px;
}

.header .logo img {
	color: transparent;
}

.header .separator {
	background-color: #F6F6F6;
	height: 20px;
	margin: 0 16px 0;
	width: 2px;
}

.header .search {
	width: 220px;
}

.header .toggle-sidebar-left {
	border-radius: 4px;
	color: #4c4e51;
	height: 30px;
	line-height: 30px;
	position: absolute;
	right: 15px;
	text-align: center;
	top: 14px;
	width: 30px;
	border: 1px solid #e4e4e4;
}

html.dark .header .toggle-sidebar-left {
	color: #fff;
	border-color: #404142;
}

.header-right,
.header-left {
	height: 59px;
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
}

.header-right {
	float: right;
}

.header-left {
	float: left;
}

/* Header Mobile */
@media only screen and (max-width: 767px) {
	.header .logo-env {
		background-color: #F6F6F6;
		border-bottom: 1px solid #E9E9E6;
		border-top: 3px solid #EDEDED;
	}

	.header .logo-env .logo {
		text-align: left;
		margin-left: 10px;
	}

	.header .search,
	.header .separator {
		display: none;
	}
}
/* Header Dark */
html.dark .header,
html.header-dark .header {
	background: #313131;
	border-bottom-color: #404142;
}

@media only screen and (max-width: 767px) {
	html.dark .header .logo-env,
	html.header-dark .header .logo-env {
		background: #313131;
		border-bottom-color: #404142;
		border-top-color: #313131;
	}

	html.dark .header .header-right,
	html.header-dark .header .header-right {
		background: #313131;
	}
}

html.dark .header .separator,
html.header-dark .header .separator {
	background-color: #404142;
}

html.dark .header .input-search input, html.dark .header .input-search input:focus,
html.header-dark .header .input-search input,
html.header-dark .header .input-search input:focus {
	background: #383838;
	border-color: #505254;
	color: #FFF;
}

html.dark .header .input-search .input-group-btn .btn-default,
html.header-dark .header .input-search .input-group-btn .btn-default {
	background: transparent;
	color: #C3C3C3;
}

@media only screen and (min-width: 768px) {
	html.header-fixed .header {
		border-radius: 0;
		border-top-color: transparent;
		left: 0;
		position: fixed;
		right: 0;
		top: -3px;
		z-index: 2000;
		margin: 0;
	}

	html.header-fixed .inner-wrapper {
		padding-top: 0;
		margin-top: 60px;
	}
}
/* Header Nav Menu */
.header.header-nav-menu {
	/* Header Nav Main */
	/* Header Nav Main Mobile */;
}

@media only screen and (min-width: 768px) {
	.header.header-nav-menu .logo {
		position: relative;
		padding: 0 20px 0 5px;
	}

	.header.header-nav-menu .logo:after {
		content: '';
		display: block;
		position: absolute;
		top: -13px;
		right: 0;
		height: 60px;
		border-right: 1px solid #E9E9E6;
	}
}

@media (min-width: 992px) {
	.header.header-nav-menu .header-nav-main {
		float: right;
		margin: 8px 0 0;
		min-height: 45px;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li:first-child {
		margin-left: 10px;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li > a {
		display: inline-block;
		border-radius: 4px;
		font-size: 12px;
		font-style: normal;
		font-weight: 700;
		line-height: 20px;
		padding: 10px;
		text-transform: uppercase;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li > a:focus {
		background: transparent;
		color: #CCC;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li > a.dropdown-toggle .fa-caret-down {
		display: none;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li > a.dropdown-toggle:after {
		border-color: #CCC transparent transparent transparent;
		border-style: solid;
		border-width: 4px;
		content: " ";
		float: right;
		margin-top: 7px;
		margin-left: 4px;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.open > a, .header.header-nav-menu .header-nav-main nav > ul > li:hover > a, .header.header-nav-menu .header-nav-main nav > ul > li.active > a {
		background: #CCC;
		color: #FFF;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown .dropdown-menu {
		top: -10000px;
		display: block;
		opacity: 0;
		left: auto;
		border-radius: 0 4px 4px;
		border: 0;
		border-top: 5px solid #CCC;
		box-shadow: 0 20px 45px rgba(0, 0, 0, 0.08);
		margin: -3px 0 0 0;
		min-width: 200px;
		padding: 5px;
		text-align: left;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown .dropdown-menu li a {
		border-bottom: 1px solid #f4f4f4;
		color: #777;
		font-size: 0.9em;
		font-weight: 400;
		padding: 8px 20px 8px 8px;
		position: relative;
		text-transform: none;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown .dropdown-menu li.dropdown-submenu {
		position: relative;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown .dropdown-menu li.dropdown-submenu > a .fa-caret-down {
		display: none;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown .dropdown-menu li.dropdown-submenu > a:after {
		border-color: transparent transparent transparent #CCC;
		border-style: solid;
		border-width: 4px 0 4px 4px;
		content: " ";
		position: absolute;
		top: 50%;
		right: 10px;
		-webkit-transform: translateY(-50%);
		-moz-transform: translateY(-50%);
		-ms-transform: translateY(-50%);
		-o-transform: translateY(-50%);
		transform: translateY(-50%);
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown .dropdown-menu li.dropdown-submenu > .dropdown-menu {
		display: block;
		margin-top: -10px;
		margin-left: -1px;
		border-radius: 4px;
		opacity: 0;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown .dropdown-menu li.dropdown-submenu:hover > .dropdown-menu {
		top: 0;
		opacity: 1;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown .dropdown-menu li:last-child a {
		border-bottom: 0;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown.open li.dropdown-submenu > .dropdown-menu, .header.header-nav-menu .header-nav-main nav > ul > li.dropdown:hover li.dropdown-submenu > .dropdown-menu {
		left: 100%;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown.open > a, .header.header-nav-menu .header-nav-main nav > ul > li.dropdown:hover > a {
		padding-bottom: 15px;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown.open > .dropdown-menu, .header.header-nav-menu .header-nav-main nav > ul > li.dropdown:hover > .dropdown-menu {
		top: auto;
		display: block;
		opacity: 1;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-reverse .dropdown-menu li a {
		padding-right: 8px;
		padding-left: 20px;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-reverse .dropdown-menu li.dropdown-submenu > a:after {
		border-width: 4px 4px 4px 0;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega {
		position: static;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega > .dropdown-menu {
		border-radius: 4px;
		left: 15px;
		right: 15px;
		width: auto;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-content {
		padding: 20px 30px;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-title {
		color: #333333;
		display: block;
		font-size: 1em;
		font-weight: 600;
		margin-top: 20px;
		padding-bottom: 5px;
		text-transform: uppercase;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-title:first-child {
		margin-top: 0;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-nav {
		list-style: none;
		padding: 0;
		margin: 0;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-nav > li > a {
		border: 0 none;
		border-radius: 4px;
		color: #777;
		display: block;
		font-size: 0.9em;
		font-weight: normal;
		margin: 0 0 0 -8px;
		padding: 3px 8px;
		text-shadow: none;
		text-transform: none;
		text-decoration: none;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-nav > li:hover > a {
		background: #f4f4f4;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-nav .mega-sub-nav-toggle {
		width: 20px;
		text-align: center;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-nav .mega-sub-nav-toggle:before {
		content: "\f0d8";
		font-family: 'Font Awesome 5 Free';
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-nav .mega-sub-nav-toggle.toggled:before {
		content: "\f0d7";
		font-family: 'Font Awesome 5 Free';
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega .dropdown-mega-sub-nav .dropdown-mega-sub-nav {
		padding-left: 15px;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-square nav > ul > li > a {
		border-radius: 0;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-square nav > ul > li.dropdown .dropdown-menu {
		margin-top: 0;
		border-radius: 0;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-square nav > ul > li.dropdown .dropdown-menu li.dropdown-submenu > .dropdown-menu {
		border-radius: 0;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-square nav > ul > li.dropdown-mega > .dropdown-menu {
		border-radius: 0;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-square nav > ul > li.dropdown-mega .dropdown-mega-sub-nav > li > a {
		border-radius: 0;
	}

	.header.header-nav-menu .header-nav-main .dropdown-reverse a > .thumb-info-preview {
		transform: translate3d(20px, 0, 0);
		right: 100%;
		left: auto;
		padding-left: 0;
		margin-right: 10px;
	}

	.header.header-nav-menu .header-nav-main .dropdown-reverse a:hover > .thumb-info-preview {
		transform: translate3d(0, 0, 0);
	}

	.header.header-nav-menu .header-nav {
		float: left;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown {
		margin-bottom: -9px;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li > a, .header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li:hover > a {
		background: transparent;
		color: #444;
		padding: 65px 13px 24px;
		margin: 0;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li > a.dropdown-toggle:after {
		border-color: #444 transparent transparent transparent;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li.dropdown li a {
		border-bottom-color: #2a2a2a;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li.dropdown .dropdown-menu {
		background: #1e1e1e;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li.dropdown .dropdown-menu > li > a {
		color: #969696;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li.dropdown .dropdown-menu > li > a:hover, .header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li.dropdown .dropdown-menu > li > a:focus {
		background: #282828;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li.dropdown.dropdown-mega .dropdown-mega-sub-title {
		color: #ababab;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown nav > ul > li.dropdown.dropdown-mega .dropdown-mega-sub-nav > li:hover > a {
		background: #282828;
	}

	.header.header-nav-menu .header-nav.header-nav-dark-dropdown .header-social-icons {
		margin-top: 70px;
	}

	.header.header-nav-menu .header-nav {
		display: block !important;
	}

	.header.header-nav-menu .header-nav-main {
		display: block !important;
		height: auto !important;
	}

	.header.header-nav-menu .header-nav-bar {
		background: #F4F4F4;
		padding: 0 10px 5px;
		margin-bottom: 0;
	}

	.header.header-nav-menu .header-nav-bar .header-nav-main {
		float: left;
		margin-bottom: 0;
	}
}

@media (min-width: 992px) {
	.header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li > a {
		color: #FFF;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li > a.dropdown-toggle:after {
		border-color: #FFF transparent transparent transparent;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li.open > a, .header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li:hover > a {
		background: #FFF;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li.open > a.dropdown-toggle:after, .header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li:hover > a.dropdown-toggle:after {
		border-color: #CCC transparent transparent transparent;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li.open > .dropdown-menu, .header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li:hover > .dropdown-menu {
		border-top-color: #FFF;
		box-shadow: 0 20px 25px rgba(0, 0, 0, 0.05);
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li.open > .dropdown-menu .dropdown-submenu:hover > .dropdown-menu, .header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li:hover > .dropdown-menu .dropdown-submenu:hover > .dropdown-menu {
		border-top-color: #FFF;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li.active > a {
		background: #FFF;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-light nav > ul > li.active > a.dropdown-toggle:after {
		border-color: #CCC transparent transparent transparent;
	}

	.header.header-nav-menu .header-nav-main.header-nav-main-light .dropdown-menu > li > a:hover, .header.header-nav-menu .header-nav-main.header-nav-main-light .dropdown-menu > li > a:focus {
		background: #f5f5f5;
	}
}

@media (min-width: 992px) {
	.header.header-nav-menu .header-nav-main-effect-1 nav > ul > li.dropdown .dropdown-menu li a, .header.header-nav-menu .header-nav-main-effect-1 nav > ul > li.dropdown .dropdown-mega-sub-nav li a {
		-webkit-transition: -webkit-transform 0.2s ease-out;
		-moz-transition: -moz-transform 0.2s ease-out;
		transition: transform 0.2s ease-out;
		transform: translate3d(0, -5px, 0);
	}

	.header.header-nav-menu .header-nav-main-effect-1 nav > ul > li.dropdown:hover > .dropdown-menu li a, .header.header-nav-menu .header-nav-main-effect-1 nav > ul > li.dropdown:hover .dropdown-mega-sub-nav li a {
		transform: translate3d(0, 0, 0);
	}

	.header.header-nav-menu .header-nav-main-effect-1 nav > ul > li.dropdown .dropdown-menu {
		-webkit-transition: -webkit-transform 0.2s ease-out;
		-moz-transition: -moz-transform 0.2s ease-out;
		transition: transform 0.2s ease-out;
		transform: translate3d(0, -5px, 0);
	}

	.header.header-nav-menu .header-nav-main-effect-1 nav > ul > li.dropdown:hover > .dropdown-menu {
		transform: translate3d(0, 0, 0);
	}
}

@media (min-width: 992px) {
	.header.header-nav-menu .header-nav-main-effect-2 nav > ul > li.dropdown .dropdown-menu {
		-webkit-transition: -webkit-transform 0.2s ease-out, opacity 0.2s ease-out;
		-moz-transition: -moz-transform 0.2s ease-out, opacity 0.2s ease-out;
		transition: transform 0.2s ease-out, opacity 0.2s ease-out;
		transform: translate3d(0, -5px, 0);
		opacity: 0;
	}

	.header.header-nav-menu .header-nav-main-effect-2 nav > ul > li.dropdown:hover > .dropdown-menu {
		transform: translate3d(0, -1px, 0);
		opacity: 1;
	}
}

@media (min-width: 992px) {
	.header.header-nav-menu .header-nav-main-effect-3 nav > ul > li.dropdown .dropdown-menu {
		-webkit-transition: -webkit-transform 0.2s ease-out;
		-moz-transition: -moz-transform 0.2s ease-out;
		transition: transform 0.2s ease-out;
		transform: translate3d(0, 10px, 0);
	}

	.header.header-nav-menu .header-nav-main-effect-3 nav > ul > li.dropdown:hover > .dropdown-menu {
		transform: translate3d(0, 0, 0);
	}
}

@media (min-width: 992px) {
	.header.header-nav-menu .header-nav-main-sub-effect-1 nav > ul > li.dropdown .dropdown-menu li.dropdown-submenu > .dropdown-menu {
		-webkit-transition: -webkit-transform 0.2s ease-out, opacity 0.2s ease-out;
		-moz-transition: -moz-transform 0.2s ease-out, opacity 0.2s ease-out;
		transition: transform 0.2s ease-out, opacity 0.2s ease-out;
		transform: translate3d(-20px, 0, 0);
		opacity: 0;
	}

	.header.header-nav-menu .header-nav-main-sub-effect-1 nav > ul > li.dropdown .dropdown-menu li.dropdown-submenu:hover > .dropdown-menu {
		transform: translate3d(0, 0, 0);
		opacity: 1;
	}

	.header.header-nav-menu .header-nav-main-sub-effect-1 nav > ul > li.dropdown.dropdown-reverse .dropdown-menu li.dropdown-submenu > .dropdown-menu {
		-webkit-transition: -webkit-transform 0.2s ease-out, opacity 0.2s ease-out;
		-moz-transition: -moz-transform 0.2s ease-out, opacity 0.2s ease-out;
		transition: transform 0.2s ease-out, opacity 0.2s ease-out;
		transform: translate3d(20px, 0, 0);
		left: auto;
		right: 100%;
		opacity: 0;
	}

	.header.header-nav-menu .header-nav-main-sub-effect-1 nav > ul > li.dropdown.dropdown-reverse .dropdown-menu li.dropdown-submenu:hover > .dropdown-menu {
		transform: translate3d(0, 0, 0);
		opacity: 1;
	}
}

@media (max-width: 991px) {
	.header.header-nav-menu .header-nav {
		clear: both;
		float: none;
	}

	.header.header-nav-menu .header-nav-main {
		background: #FFF;
		padding: 10px;
		max-height: 350px;
		overflow-x: hidden;
		overflow-y: auto;
	}
}

@media (max-width: 991px) and (min-width: 768px) {
	.header.header-nav-menu .header-nav-main {
		position: relative;
		top: 12px;
	}
}

@media (max-width: 991px) {
	.header.header-nav-menu .header-nav-main.collapsing {
		overflow: hidden;
	}

	.header.header-nav-menu .header-nav-main nav {
		margin: 0 0 6px;
	}

	.header.header-nav-menu .header-nav-main nav > ul li {
		border-bottom: 1px solid #e8e8e8;
		clear: both;
		display: block;
		float: none;
		margin: 0;
		padding: 0;
		position: relative;
	}

	.header.header-nav-menu .header-nav-main nav > ul li a {
		font-size: 13px;
		font-style: normal;
		line-height: 20px;
		padding: 7px 8px;
		margin: 1px 0;
		border-radius: 4px;
	}

	.header.header-nav-menu .header-nav-main nav > ul li a .fa-caret-down {
		line-height: 35px;
		min-height: 38px;
		min-width: 30px;
		position: absolute;
		right: 5px;
		text-align: center;
		top: 0;
	}

	.header.header-nav-menu .header-nav-main nav > ul li.dropdown .dropdown-menu {
		background: transparent;
		padding: 0;
		margin: 0;
		font-size: 13px;
		box-shadow: none;
		border-radius: 0;
		border: 0;
		clear: both;
		display: none;
		float: none;
		position: static;
		border-top: 0 !important;
	}

	.header.header-nav-menu .header-nav-main nav > ul li.dropdown .dropdown-menu li.dropdown-submenu.opened > .dropdown-menu {
		display: block;
		margin-left: 20px;
	}

	.header.header-nav-menu .header-nav-main nav > ul li.dropdown.opened > .dropdown-menu {
		display: block;
		margin-left: 20px;
	}

	.header.header-nav-menu .header-nav-main nav > ul li.dropdown-mega .dropdown-mega-sub-title {
		margin-top: 10px;
		display: block;
	}

	.header.header-nav-menu .header-nav-main nav > ul li.dropdown-mega .dropdown-mega-sub-nav {
		margin: 0 0 0 20px;
		padding: 0;
		list-style: none;
	}

	.header.header-nav-menu .header-nav-main nav > ul li.dropdown-mega .dropdown-mega-sub-nav > li > a {
		display: block;
		text-decoration: none;
		color: #333;
	}

	.header.header-nav-menu .header-nav-main nav > ul li:last-child {
		border-bottom: 0;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li > a {
		text-transform: uppercase;
		font-weight: 700;
		margin-top: 1px;
		margin-bottom: 1px;
	}

	.header.header-nav-menu .header-nav-main nav > ul > li.active > a, .header.header-nav-menu .header-nav-main nav > ul > li.active > a:focus, .header.header-nav-menu .header-nav-main nav > ul > li.active > a:hover {
		color: #FFF;
	}

	.header.header-nav-menu .header-nav-main nav .not-included {
		margin: 0;
	}

	.header.header-nav-menu .header-nav-main a > .thumb-info-preview {
		display: none !important;
	}

	.header.header-nav-menu .header-btn-collapse-nav {
		outline: 0;
		float: right;
		margin-top: 10px;
		margin-right: 15px;
	}

	.header.header-nav-menu .header-btn-collapse-nav:hover, .header.header-nav-menu .header-btn-collapse-nav:focus {
		color: #FFF;
	}

	.header.header-nav-menu .header-nav-bar {
		margin: 0 auto;
	}

	.header.header-nav-menu .header-nav-bar .header-btn-collapse-nav {
		margin-top: 14px;
	}

	.header.header-nav-menu.header-transparent .header-nav-main {
		padding: 10px;
		margin-bottom: 10px;
		background: #FFF;
		border-radius: 4px;
	}

	.header.header-nav-menu.header-semi-transparent .header-nav-main {
		padding: 10px;
		margin-bottom: 10px;
		background: #FFF;
		border-radius: 4px;
	}

	.header.header-nav-menu.header-semi-transparent-light .header-nav-main {
		padding: 10px;
		margin-bottom: 10px;
		background: #FFF;
		border-radius: 4px;
	}
}

.header.header-nav-menu .header-nav-main nav > ul > li:not(.dropdown-mega).active ul.dropdown-menu li:hover > a {
	background-color: #f4f4f4;
}

.header.header-nav-menu .header-nav-main nav > ul > li:not(.dropdown-mega).active ul.dropdown-menu li a {
	background: transparent;
}

.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega.active ul.dropdown-mega-sub-nav li:hover a {
	background-color: #f4f4f4;
}

.header.header-nav-menu .header-nav-main nav > ul > li.dropdown-mega.active ul.dropdown-mega-sub-nav li a {
	background: transparent;
}

.header.header-nav-menu .not-included {
	color: #b7b7b7;
	display: block;
	font-size: 0.8em;
	font-style: normal;
	margin: -4px 0;
	padding: 0;
}

.header.header-nav-menu .tip {
	display: inline-block;
	padding: 0 5px;
	background: #171717;
	color: #FFF;
	text-shadow: none;
	border-radius: 3px;
	margin-left: 8px;
	position: relative;
	text-transform: uppercase;
	font-size: 10px;
	font-weight: bold;
}

.header.header-nav-menu .tip:before {
	right: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(23, 23, 23, 0);
	border-right-color: #171717;
	border-width: 5px;
	margin-top: -5px;
}

.header.header-nav-menu .tip.skin {
	color: #171717;
}

.header.header-nav-menu .search-toggle {
	color: #CCC;
}

.header.header-nav-menu .search-toggle:focus, .header.header-nav-menu .search-toggle:active {
	box-shadow: none;
}

@media (min-width: 992px) {
	.header.header-nav-menu.header-nav-stripe {
		height: initial;
		border-bottom: 0;
	}

	.header.header-nav-menu.header-nav-stripe nav > ul > li > a, .header.header-nav-menu.header-nav-stripe nav > ul > li:hover > a {
		background: transparent;
		padding: 18px 13px 19px;
		margin: 0;
	}

	.header.header-nav-menu.header-nav-stripe nav > ul > li > a {
		color: #444;
	}

	.header.header-nav-menu.header-nav-stripe nav > ul > li > a.dropdown-toggle:after {
		border-color: #444 transparent transparent transparent;
	}

	.header.header-nav-menu.header-nav-stripe nav > ul > li:hover > a {
		color: #FFF;
	}

	.header.header-nav-menu.header-nav-stripe nav > ul > li.dropdown:hover > a, .header.header-nav-menu.header-nav-stripe nav > ul > li.dropdown.open > a {
		padding-bottom: 19px;
	}

	.header.header-nav-menu.header-nav-top-line {
		height: initial;
		border-bottom: 0;
	}

	.header.header-nav-menu.header-nav-top-line nav > ul > li > a, .header.header-nav-menu.header-nav-top-line nav > ul > li:hover > a {
		background: transparent !important;
		color: #444;
		padding: 18px 13px 19px;
		margin: 0;
	}

	.header.header-nav-menu.header-nav-top-line nav > ul > li > a:before, .header.header-nav-menu.header-nav-top-line nav > ul > li:hover > a:before {
		content: "";
		position: absolute;
		width: 100%;
		height: 5px;
		top: -5px;
		left: -5px;
		opacity: 0;
		background: #CCC;
	}

	.header.header-nav-menu.header-nav-top-line nav > ul > li.active > a, .header.header-nav-menu.header-nav-top-line nav > ul > li:hover > a {
		color: #CCC;
	}

	.header.header-nav-menu.header-nav-top-line nav > ul > li.active > a:before, .header.header-nav-menu.header-nav-top-line nav > ul > li:hover > a:before {
		opacity: 1;
	}

	.header.header-nav-menu.header-nav-top-line nav > ul > li.active > a.dropdown-toggle:after, .header.header-nav-menu.header-nav-top-line nav > ul > li:hover > a.dropdown-toggle:after {
		border-color: #CCC transparent transparent transparent;
	}

	.header.header-nav-menu.header-nav-top-line nav > ul > li > a.dropdown-toggle:after {
		border-color: #444 transparent transparent transparent;
	}

	.header.header-nav-menu.header-nav-top-line nav > ul > li.dropdown:hover > a, .header.header-nav-menu.header-nav-top-line nav > ul > li.dropdown.open > a {
		padding-bottom: 19px;
	}

	.header.header-nav-menu.header-nav-stripe .header-nav-main, .header.header-nav-menu.header-nav-top-line .header-nav-main {
		margin-top: 0;
	}
}

@media only screen and (max-width: 1199px) {
	.header.header-nav-menu .separator {
		margin: 0px 14px 0;
	}
}

@media only screen and (min-width: 768px) and (max-width: 1199px) {
	.header.header-nav-menu .search {
		position: absolute;
		top: 50px;
		left: -66px;
	}

	.header.header-nav-menu .search.active {
		display: block !important;
	}

	.header.header-nav-menu .search:before {
		content: '';
		display: block;
		position: absolute;
		top: -7px;
		left: 50%;
		width: 0;
		height: 0;
		border-left: 7px solid transparent;
		border-right: 7px solid transparent;
		border-bottom: 7px solid #CCC;
		-webkit-transform: translateX(-50%);
		-moz-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		-o-transform: translateX(-50%);
		transform: translateX(-50%);
	}
}

@media only screen and (min-width: 992px) {
	.header.header-nav-menu .header-right {
		position: relative;
	}
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
	.header.header-nav-menu .header-right {
		position: absolute;
		top: 0;
		right: 60px;
	}
}

.sidebar-left {
	z-index: 999;
}

.sidebar-left .sidebar-header {
	background: #1e293b;
	position: relative;
	color: #e2e8f0;
	height: 50px;
}

.sidebar-left .sidebar-header .sidebar-title {
	color: #465162;
	padding: 15px;
	font-size: 1.3rem;
}

.sidebar-left hr.separator {
	background: none;
	margin: 20px 10px 20px;
}

@media only screen and (max-width: 767px) {
	.sidebar-left {
		background: #1e293b;
	}
}

html.mobile-device .sidebar-left {
	background: #1e293b;
}

/* Unstyle nano for non fixed layouts */
@media only screen and (min-width: 768px) {
	html.scroll .sidebar-left,
	html.boxed .sidebar-left,
	html.sidebar-left-big-icons .sidebar-left {
		min-height: 100vh;
	}

	html.scroll .sidebar-left .sidebar-header,
	html.boxed .sidebar-left .sidebar-header,
	html.sidebar-left-big-icons .sidebar-left .sidebar-header {
		margin-bottom: -3px;
	}

	html.scroll .sidebar-left .nano,
	html.boxed .sidebar-left .nano,
	html.sidebar-left-big-icons .sidebar-left .nano {
		position: static;
		overflow: visible;
		width: 100%;
	}

	html.scroll .sidebar-left .nano .nano-content,
	html.boxed .sidebar-left .nano .nano-content,
	html.sidebar-left-big-icons .sidebar-left .nano .nano-content {
		margin-right: 0 !important;
		position: relative;
		overflow: visible;
		margin-top: 3px;
	}

	html.scroll .sidebar-left .nano .nano-pane,
	html.boxed .sidebar-left .nano .nano-pane,
	html.sidebar-left-big-icons .sidebar-left .nano .nano-pane {
		display: none !important;
	}

	html.boxed .sidebar-left .nano > .nano-content,
	html.scroll .sidebar-left .nano > .nano-content,
	html.sidebar-left-big-icons .sidebar-left .nano > .nano-content {
		overflow: visible !important;
	}

	html.boxed .sidebar-left .nano {
		padding-bottom: 10px;
	}

	html.scroll .sidebar-left .nano,
	html.sidebar-left-big-icons .sidebar-left .nano {
		padding-bottom: 10px;
	}
}

@media only screen and (min-width: 768px) {
	html.sidebar-left-collapsed .sidebar-left .nano {
		background: #313131;
		box-shadow: -5px 0 0 #2F3139 inset;
	}

	html.sidebar-left-collapsed .sidebar-left .nav-main > li > a {
		overflow: hidden;
		text-overflow: clip;
	}

	html.sidebar-left-collapsed .sidebar-left .nav-main li.nav-parent a:after {
		display: none;
	}

	html.sidebar-left-collapsed .sidebar-left .nav-main li.nav-parent > ul.nav-children {
		display: none;
	}

	html.sidebar-left-collapsed .sidebar-left .nav-main a span {
		opacity: 0;
		visibility: hidden;
	}

	html.sidebar-left-collapsed .sidebar-left .sidebar-widget,
	html.sidebar-left-collapsed .sidebar-left .separator {
		display: none;
	}

	html.sidebar-left-collapsed .sidebar-left .nano:hover {
		width: 230px;
	}

	html.sidebar-left-collapsed .sidebar-left .nano:hover .nav-main .nav-expanded > ul.nav-children {
		display: block;
	}

	html.sidebar-left-collapsed .sidebar-left .nano:hover .nav-main li.nav-parent a:after {
		display: inline-block;
	}

	html.sidebar-left-collapsed .sidebar-left .nano:hover .nav-main li a span {
		opacity: 1;
		visibility: visible;
	}

	html.sidebar-left-collapsed .sidebar-left .nano:hover .sidebar-widget,
	html.sidebar-left-collapsed .sidebar-left .nano:hover .separator {
		display: block;
	}

	html.sidebar-left-collapsed.sidebar-left-opened .sidebar-left .nano {
		width: 300px;
	}

	html.sidebar-left-collapsed.sidebar-left-opened .sidebar-left .nano .nav-main .nav-expanded > ul.nav-children {
		display: block;
	}

	html.sidebar-left-collapsed.sidebar-left-opened .sidebar-left .nano .nav-main li.nav-parent a:after {
		display: inline-block;
	}

	html.sidebar-left-collapsed.sidebar-left-opened .sidebar-left .nano .nav-main li a span {
		visibility: visible;
	}

	html.sidebar-left-collapsed.sidebar-left-opened .sidebar-left .nano .sidebar-widget,
	html.sidebar-left-collapsed.sidebar-left-opened .sidebar-left .nano .separator {
		display: block;
	}
}

html.sidebar-light:not(.dark) .sidebar-left .sidebar-header .sidebar-title {
	background: #FFF;
}

html.sidebar-light:not(.dark) .sidebar-left .nano {
	box-shadow: -5px 0 0 #f6f6f6 inset;
	background: #FFF;
}

html.sidebar-light:not(.dark).sidebar-left-collapsed .sidebar-left .nano {
	box-shadow: -5px 0 0 #f6f6f6 inset;
	background: #FFF;
}

@media only screen and (max-width: 767px) {
	html.sidebar-light .sidebar-left {
		background: #FFF;
	}
}

html.mobile-device.sidebar-light .sidebar-left {
	background: #FFF;
}

@media only screen and (min-width: 768px) {
	html.sidebar-left-big-icons .sidebar-left, html.sidebar-left-big-icons .logo-env {
		width: 152px;
	}
	
	html.sidebar-left-big-icons .sidebar-left .nano {
		box-shadow: none !important;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main {
		margin-right: 0;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li:hover > ul.nav-children {
		display: block;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li:hover > a {
		background: #303030;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li:last-child > a {
		border-top: 1px solid #303030;
		border-bottom: 1px solid #303030;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li.nav-active > a {
		background: #303030;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li > a {
		text-align: center;
		padding: 12px 10px;
		border-top: 1px solid #303030;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li > a:after {
		content: none;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li > a i {
		margin-right: 0;
		font-size: 2.8rem;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li > a span {
		display: block;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li > a span.label {
		position: absolute;
		top: 2px;
		left: 60%;
		-webkit-transform: translateX(-50%);
		-moz-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		-o-transform: translateX(-50%);
		transform: translateX(-50%);
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li > a .not-included {
		display: block;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li ul.nav-children {
		position: absolute;
		top: 0;
		left: 100%;
		min-width: 210px;
		border-left: 3px solid #2f3139;
		background: #303030;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li ul.nav-children li:hover > ul.nav-children {
		display: block;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li ul.nav-children li:hover > a {
		color: #FFF;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li ul.nav-children li:hover > a:hover {
		background: transparent;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li ul.nav-children li a {
		padding: 6px 15px;
		overflow: visible;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li ul.nav-children li.nav-parent > a {
		padding-right: 30px;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li ul.nav-children li.nav-parent > a:after {
		content: '\f105';
		padding: 6px 10px;
		right: 5px;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main > li ul.nav-children ul.nav-children {
		padding: 10px 0;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main li.nav-parent:hover > a:before {
		content: '';
		display: block;
		position: absolute;
		top: 0;
		right: -3px;
		bottom: 0;
		border-right: 4px solid #303030;
		z-index: 1;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main li.nav-parent.nav-expanded > ul.nav-children {
		display: none;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .nav-main li.nav-parent.nav-expanded:hover > ul.nav-children {
		display: block;
	}

	html.sidebar-left-big-icons .sidebar-left .nano .sidebar-widget {
		display: none;
	}

	html.sidebar-left-big-icons.sidebar-left-collapsed .sidebar-left {
		width: 55px;
	}

	html.sidebar-left-big-icons.sidebar-left-collapsed .sidebar-left .nano:hover {
		width: 55px;
	}

	html.sidebar-left-big-icons.sidebar-left-collapsed .sidebar-left .nano:hover .sidebar-widget {
		display: none;
	}

	html.sidebar-left-big-icons.sidebar-left-collapsed .sidebar-left .nano .nav-main > li > a {
		overflow: visible;
	}

	html.sidebar-left-big-icons.sidebar-left-collapsed .sidebar-left .nano .nav-main > li > a span {
		display: none;
	}

	html.sidebar-left-big-icons.sidebar-left-collapsed .sidebar-left .nano .nav-main > li > a > i {
		font-size: 2.1rem;
	}

	html.sidebar-left-big-icons.sidebar-light .sidebar-left .nano .nav-main > li:hover > a {
		background: #fafafa;
	}

	html.sidebar-left-big-icons.sidebar-light .sidebar-left .nano .nav-main > li:last-child > a {
		border-top: 1px solid #fafafa;
		border-bottom: 1px solid #fafafa;
	}

	html.sidebar-left-big-icons.sidebar-light .sidebar-left .nano .nav-main > li.nav-active > a {
		background: #fafafa;
	}

	html.sidebar-left-big-icons.sidebar-light .sidebar-left .nano .nav-main > li > a {
		border-top: 1px solid #fafafa;
	}

	html.sidebar-left-big-icons.sidebar-light .sidebar-left .nano .nav-main > li ul.nav-children {
		border-left: 3px solid #F1F1F1;
		background: #fafafa;
	}

	html.sidebar-left-big-icons.sidebar-light .sidebar-left .nano .nav-main > li ul.nav-children li:hover > a {
		color: #000;
	}

	html.sidebar-left-big-icons.sidebar-light .sidebar-left .nano .nav-main > li ul.nav-children li:hover > a:hover {
		background: transparent;
	}

	html.sidebar-left-big-icons.sidebar-light .sidebar-left .nano .nav-main li.nav-parent:hover > a:before {
		border-right: 4px solid #fafafa;
	}
}

@media only screen and (max-width: 767px) {
	/* Layout Mobile - Sidebar Left Collapsed & Sidebar Right Opened */
	html.sidebar-left-sm.sidebar-left-collapsed.sidebar-right-opened .sidebar-left {
		margin-left: -230px;
	}
}

@media only screen and (min-width: 768px) {
	/* Layout Base - Sidebar Left */
	html.sidebar-left-sm .sidebar-left, html.sidebar-left-sm .logo-env {
		width: 230px;
		font-size: 1.2rem;
	}

	html.sidebar-left-sm .sidebar-left ul.nav-main li i {
		font-size: 1.6rem;
	}

	html.sidebar-left-sm .sidebar-left ul.nav-main li a {
		font-size: 1.34rem;
		font-weight: 600;
	}

	html.sidebar-left-sm .sidebar-left .sidebar-widget .widget-header h6 {
		font-size: 1.2rem;
	}

	html.sidebar-left-sm.sidebar-left-collapsed.fixed .sidebar-left .nano:hover {
		-webkit-transition: width .25s ease-in-out;
		-moz-transition: width .25s ease-in-out;
		-o-transition: width .25s ease-in-out;
		transition: width .25s ease-in-out;
		width: 230px;
	}

	/* Layout Base - Sidebar Left Opened ( Larger than mobile ) */
	html.sidebar-left-sm.sidebar-left-collapsed .sidebar-left {
		width: 73px;
	}

	/* Layout Fixed - Content Body */
	html.fixed.sidebar-left-sm .content-body {
		margin-left: 230px;
	}

	/* Layout Fixed - Page header */
	html.fixed.sidebar-left-sm .page-header {
		left: 230px;
	}

	/* Layout Fixed - Sidebar Right Opened */
	html.fixed.sidebar-left-sm.sidebar-right-opened .page-header {
		left: 0;
	}

	html.fixed.sidebar-left-sm.sidebar-right-opened .sidebar-left {
		left: -230px;
	}

	/* Layout Fixed - Sidebar Left Collapsed */
	html.fixed.sidebar-left-collapsed .page-header {
		left: 73px;
	}

	html.fixed.sidebar-left-collapsed .content-body {
		margin-left: 73px;
	}

	/* Layout Fixed - Sidebar Left Collapsed & Sidebar Right Opened */
	html.fixed.sidebar-left-sm.sidebar-left-collapsed.sidebar-right-opened .page-header {
		left: -230px;
	}

	/* Content With Menu + Layout Fixed */
	html.fixed.sidebar-left-sm .inner-menu {
		left: 230px;
	}

	/* Content With Menu + Layout Fixed + Sidebar Left Collapsed */
	html.fixed.sidebar-left-sm.sidebar-left-collapsed .inner-menu,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed .inner-menu-toggle,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed .inner-toolbar {
		left: 73px;
	}

	html.fixed.sidebar-left-sm.sidebar-left-collapsed.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed.inner-menu-opened .inner-toolbar {
		left: 373px;
	}

	/* Content With Menu + Layout Fixed + Sidebar Right Opened */
	html.fixed.sidebar-left-sm.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-left-sm.sidebar-right-opened .inner-menu-toggle,
	html.fixed.sidebar-left-sm.sidebar-right-opened .inner-toolbar {
		left: -50px;
	}

	html.fixed.sidebar-left-sm.sidebar-right-opened.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-sm.sidebar-right-opened.inner-menu-opened .inner-toolbar {
		left: -350px;
	}

	/* Content With Menu - Toolbar + Layout Fixed */
	html.fixed.sidebar-left-sm.inner-menu-opened {
		left: 550px;
	}

	html.fixed.sidebar-left-sm .inner-menu-toggle {
		left: 230px;
	}
}
/* Resolution gt 1366 - Show Inner Menu */
@media only screen and (min-width: 1366px) {
	html.fixed.sidebar-left-sm .content-with-menu .inner-toolbar,
	html.fixed.sidebar-left-sm.inner-menu-opened .content-with-menu .inner-toolbar {
		left: 550px;
	}

	html.fixed.sidebar-left-sm .inner-menu-toggle,
	html.fixed.sidebar-left-sm .inner-menu,
	html.fixed.sidebar-left-sm.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-sm.inner-menu-opened .inner-menu {
		left: 230px;
	}

	html.fixed.sidebar-left-sm.sidebar-right-opened .content-with-menu .inner-toolbar {
		left: 230px;
	}

	html.fixed.sidebar-left-sm.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-left-sm.sidebar-right-opened .inner-menu-toggle {
		left: -50px;
	}

	html.fixed.sidebar-left-sm.sidebar-left-collapsed .content-with-menu .inner-toolbar,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .content-with-menu .inner-toolbar,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed.inner-menu-opened .content-with-menu .inner-toolbar {
		left: 373px;
	}

	html.fixed.sidebar-left-sm.sidebar-left-collapsed .inner-menu-toggle,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed .inner-menu,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .inner-menu,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed.inner-menu-opened .inner-menu {
		left: 73px;
	}

	html.fixed.sidebar-left-sm.sidebar-left-collapsed.sidebar-right-opened .content-with-menu .inner-toolbar {
		left: 73px;
	}

	html.fixed.sidebar-left-sm.sidebar-left-collapsed.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-left-sm.sidebar-left-collapsed.sidebar-right-opened .inner-menu-toggle {
		left: -227px;
	}
}

@media only screen and (max-width: 767px) {
	/* Layout Mobile - Sidebar Left Collapsed & Sidebar Right Opened */
	html.sidebar-left-xs.sidebar-left-collapsed.sidebar-right-opened .sidebar-left {
		margin-left: -200px;
	}
}

@media only screen and (min-width: 768px) {
	/* Layout Base - Sidebar Left */
	html.sidebar-left-xs .sidebar-left, html.sidebar-left-xs .logo-env {
		width: 200px;
		font-size: 1.1rem;
	}

	html.sidebar-left-xs .sidebar-left ul.nav-main li i {
		font-size: 1.4rem;
	}

	html.sidebar-left-xs .sidebar-left ul.nav-main li a {
		font-size: 1.1rem;
	}

	html.sidebar-left-xs .sidebar-left .sidebar-widget .widget-header h6 {
		font-size: 1.1rem;
	}

	html.sidebar-left-xs.sidebar-left-collapsed.fixed .sidebar-left .nano:hover {
		width: 200px;
	}

	/* Layout Base - Sidebar Left Opened ( Larger than mobile ) */
	html.sidebar-left-xs.sidebar-left-collapsed .sidebar-left {
		width: 73px;
	}

	/* Layout Fixed - Content Body */
	html.fixed.sidebar-left-xs .content-body {
		margin-left: 200px;
	}

	/* Layout Fixed - Page header */
	html.fixed.sidebar-left-xs .page-header {
		left: 200px;
	}

	/* Layout Fixed - Sidebar Right Opened */
	html.fixed.sidebar-left-xs.sidebar-right-opened .page-header {
		left: 0;
	}

	html.fixed.sidebar-left-xs.sidebar-right-opened .sidebar-left {
		left: -200px;
	}

	/* Layout Fixed - Sidebar Left Collapsed */
	html.fixed.sidebar-left-collapsed .page-header {
		left: 73px;
	}

	html.fixed.sidebar-left-collapsed .content-body {
		margin-left: 73px;
	}

	/* Layout Fixed - Sidebar Left Collapsed & Sidebar Right Opened */
	html.fixed.sidebar-left-xs.sidebar-left-collapsed.sidebar-right-opened .page-header {
		left: -200px;
	}

	/* Content With Menu + Layout Fixed */
	html.fixed.sidebar-left-xs .inner-menu {
		left: 200px;
	}

	/* Content With Menu + Layout Fixed + Sidebar Left Collapsed */
	html.fixed.sidebar-left-xs.sidebar-left-collapsed .inner-menu,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed .inner-menu-toggle,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed .inner-toolbar {
		left: 73px;
	}

	html.fixed.sidebar-left-xs.sidebar-left-collapsed.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed.inner-menu-opened .inner-toolbar {
		left: 373px;
	}

	/* Content With Menu + Layout Fixed + Sidebar Right Opened */
	html.fixed.sidebar-left-xs.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-left-xs.sidebar-right-opened .inner-menu-toggle,
	html.fixed.sidebar-left-xs.sidebar-right-opened .inner-toolbar {
		left: -100px;
	}

	html.fixed.sidebar-left-xs.sidebar-right-opened.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-xs.sidebar-right-opened.inner-menu-opened .inner-toolbar {
		left: -400px;
	}

	/* Content With Menu - Toolbar + Layout Fixed */
	html.fixed.sidebar-left-xs.inner-menu-opened {
		left: 500px;
	}

	html.fixed.sidebar-left-xs .inner-menu-toggle {
		left: 200px;
	}
}
/* Resolution gt 1366 - Show Inner Menu */
@media only screen and (min-width: 1366px) {
	html.fixed.sidebar-left-xs .content-with-menu .inner-toolbar,
	html.fixed.sidebar-left-xs.inner-menu-opened .content-with-menu .inner-toolbar {
		left: 500px;
	}

	html.fixed.sidebar-left-xs .inner-menu-toggle,
	html.fixed.sidebar-left-xs .inner-menu,
	html.fixed.sidebar-left-xs.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-xs.inner-menu-opened .inner-menu {
		left: 200px;
	}

	html.fixed.sidebar-left-xs.sidebar-right-opened .content-with-menu .inner-toolbar {
		left: 200px;
	}

	html.fixed.sidebar-left-xs.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-left-xs.sidebar-right-opened .inner-menu-toggle {
		left: -100px;
	}

	html.fixed.sidebar-left-xs.sidebar-left-collapsed .content-with-menu .inner-toolbar,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .content-with-menu .inner-toolbar,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed.inner-menu-opened .content-with-menu .inner-toolbar {
		left: 373px;
	}

	html.fixed.sidebar-left-xs.sidebar-left-collapsed .inner-menu-toggle,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed .inner-menu,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed.sidebar-right-opened.inner-menu-opened .inner-menu,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed.inner-menu-opened .inner-menu-toggle,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed.inner-menu-opened .inner-menu {
		left: 73px;
	}

	html.fixed.sidebar-left-xs.sidebar-left-collapsed.sidebar-right-opened .content-with-menu .inner-toolbar {
		left: 73px;
	}

	html.fixed.sidebar-left-xs.sidebar-left-collapsed.sidebar-right-opened .inner-menu,
	html.fixed.sidebar-left-xs.sidebar-left-collapsed.sidebar-right-opened .inner-menu-toggle {
		left: -227px;
	}
}
/* Sidebar Right */
.sidebar-right {
	z-index: 1010;
}

.sidebar-right .sidebar-right-wrapper {
	padding: 20px;
}

.sidebar-right h6 {
	margin: 0;
	color: #e2e8f0;
	text-transform: uppercase;
	font-size: 1.2rem;
	font-weight: 600;
}

.sidebar-right .mobile-close {
	background: #000;
	color: #cbd5e1;
	left: 0;
	line-height: 50px;
	padding-left: 20px;
	position: relative;
	overflow: hidden;
	width: 100%;
	text-align: left;
	text-decoration: none;
}

.sidebar-right .mobile-close i {
	margin-left: 5px;
	vertical-align: middle;
}

.sidebar-right .mobile-close:after {
	box-shadow: 0 0px 3px 0 rgba(255, 255, 255, 0.7);
	bottom: -1px;
	content: '';
	display: block;
	height: 1px;
	left: 0;
	position: absolute;
	right: 0;
}

/* If desktop is seeing mobile res, fix scrollbars */
@media only screen and (max-width: 767px) {
	html.no-mobile-device.custom-scroll .sidebar-right .nano > .nano-content {
		overflow: scroll;
		overflow-x: hidden;
	}
}
/* Content With Menu - Menu Faux Column for Scroll and Boxed Layouts Colors */
@media only screen and (min-width: 768px) {
	html.scroll .content-with-menu:before,
	html.boxed .content-with-menu:before {
		background: #313131;
	}

	html.scroll .content-with-menu:after,
	html.boxed .content-with-menu:after {
		background: #000;
		box-shadow: 0px 0 4px 2px rgba(0, 0, 0, 0.5);
	}
}
/* Unstyle nano for non fixed layouts */
html.scroll .inner-menu .nano, html.scroll.no-overflowscrolling.custom-scroll .inner-menu .nano,
html.boxed .inner-menu .nano,
html.boxed.no-overflowscrolling.custom-scroll .inner-menu .nano {
	position: static;
	height: auto;
	overflow: visible;
	width: auto;
}

html.scroll .inner-menu .nano > .nano-content, html.scroll.no-overflowscrolling.custom-scroll .inner-menu .nano > .nano-content,
html.boxed .inner-menu .nano > .nano-content,
html.boxed.no-overflowscrolling.custom-scroll .inner-menu .nano > .nano-content {
	position: static;
	overflow: visible;
}

@media only screen and (max-width: 767px) {
	html.fixed .inner-menu .nano {
		position: static;
		height: auto;
		overflow: visible;
		width: auto;
	}

	html.fixed .inner-menu .nano .nano-content {
		margin-right: 0;
		position: static;
		overflow: visible;
	}
}
/* Fix padding when fixed */
@media only screen and (min-width: 768px) {
	html.fixed .inner-menu {
		padding: 0;
	}

	html.fixed .inner-menu .nano-content {
		padding: 35px;
	}

	html.fixed .inner-menu .nano-content:after {
		display: block;
		content: '';
		height: 35px;
	}
}
/* Content With Menu - Inner Menu Style */
.inner-menu {
	background: #313131;
	border-right: 1px solid #242830;
	color: #abb4be;
	padding: 0;
	margin: 0;
}

.inner-menu .title {
	color: #465162;
	font-weight: 600;
	margin: 10px 0;
	padding: 0;
	text-transform: uppercase;
}

.inner-menu hr.separator {
	background-image: -webkit-linear-gradient(left, transparent, rgba(0, 0, 0, 0.4), transparent);
	background-image: -moz-linear-gradient(left, transparent, rgba(0, 0, 0, 0.4), transparent);
	background-image: -ms-linear-gradient(left, transparent, rgba(0, 0, 0, 0.4), transparent);
	background-image: -o-linear-gradient(left, transparent, rgba(0, 0, 0, 0.4), transparent);
	margin: 20px -35px 20px;
}

.inner-menu a,
.inner-menu a:hover {
	color: #abb4be;
}

.inner-menu a.menu-item {
	color: #abb4be;
	display: block;
	margin: 0 -35px 0 -35px;
	padding: 10px 50px 10px 50px;
	text-decoration: none;
}

.inner-menu a.menu-item:hover {
	background: #303030;
	color: #abb4be;
	text-decoration: none;
}

.inner-menu a.menu-item.active {
	background: #383838;
}

.inner-menu a.menu-item .label {
	font-weight: normal;
	font-size: 10px;
	font-size: 1rem;
	padding: .3em .7em .4em;
	margin: .2em -1em 0 0;
}

html.sidebar-light:not(.dark) .inner-menu {
	background: #FFF;
	border-right-color: #e2e3e6;
	color: #777;
}

html.sidebar-light:not(.dark) .inner-menu .title {
	color: #465162;
}

html.sidebar-light:not(.dark) .inner-menu hr.separator {
	background-image: -webkit-linear-gradient(left, transparent, rgba(0, 0, 0, 0.1), transparent);
	background-image: -moz-linear-gradient(left, transparent, rgba(0, 0, 0, 0.1), transparent);
	background-image: -ms-linear-gradient(left, transparent, rgba(0, 0, 0, 0.1), transparent);
	background-image: -o-linear-gradient(left, transparent, rgba(0, 0, 0, 0.1), transparent);
}

html.sidebar-light:not(.dark) .inner-menu a.menu-item {
	color: #777;
}

html.sidebar-light:not(.dark) .inner-menu a.menu-item:hover {
	background: #e2e3e6;
	color: #777;
}

html.sidebar-light:not(.dark) .inner-menu a.menu-item.active {
	background: #e2e3e6;
}

/* Content With Menu - Toggle */
.inner-menu-toggle,
.inner-menu .inner-menu-toggle-inside {
	background: #000;
	color: #999;
	left: 0;
	line-height: 52px;
	position: relative;
	overflow: hidden;
	text-align: left;
	text-decoration: none;
}

.inner-menu-toggle:after,
.inner-menu .inner-menu-toggle-inside:after {
	box-shadow: 0 0px 3px 0 rgba(255, 255, 255, 0.7);
	bottom: -1px;
	content: '';
	display: block;
	height: 1px;
	left: 0;
	position: absolute;
	right: 0;
}

.inner-menu-toggle a,
.inner-menu .inner-menu-toggle-inside a {
	display: block;
	padding-left: 20px;
	text-decoration: none;
}

.inner-menu-toggle a i,
.inner-menu .inner-menu-toggle-inside a i {
	vertical-align: middle;
}

.inner-menu-toggle .inner-menu-collapse,
.inner-menu .inner-menu-toggle-inside .inner-menu-collapse {
	display: none;
}

html.sidebar-light:not(.dark) .inner-menu-toggle,
html.sidebar-light:not(.dark) .inner-menu .inner-menu-toggle-inside {
	background: #E2E3E6;
	color: #777;
}

html.sidebar-light:not(.dark) .inner-menu-toggle > a,
html.sidebar-light:not(.dark) .inner-menu .inner-menu-toggle-inside > a {
	color: #777;
}

.inner-menu-toggle a i {
	margin-left: 5px;
}

.inner-menu-toggle-inside {
	margin: -35px -35px 15px -35px;
}

.inner-menu-toggle-inside .inner-menu-collapse i {
	margin-right: 5px;
}

.inner-menu-toggle-inside .inner-menu-expand i {
	margin-left: 5px;
}

/* Content With Menu - Toggle - Outside */
.inner-menu-toggle {
	display: none;
}

/* Content With Menu - Inner Menu Content */
.inner-menu-content {
	display: none;
}

html.inner-menu-opened .inner-menu .inner-menu-toggle-inside .inner-menu-collapse {
	display: block;
}

html.inner-menu-opened .inner-menu-expand {
	display: none;
}

html.inner-menu-opened .inner-menu-content {
	display: block;
}

/* Content With Menu - Responsive */
@media only screen and (max-width: 767px) {
	.inner-menu .hidden-xs-inline {
		display: none;
	}

	.inner-menu .inner-menu-content {
		padding: 20px;
	}

	.inner-menu-toggle-inside {
		margin: 0;
	}
}
/* Content With Menu - Toolbar + Layout Fixed */
@media only screen and (min-width: 768px) {
	html.fixed.inner-menu-opened {
		left: 600px;
	}

	html.fixed .inner-menu-toggle {
		position: fixed;
		left: 300px;
	}
}

html.dark .inner-menu-toggle:after,
html.dark .inner-menu .inner-menu-toggle-inside:after {
	box-shadow: none;
}

ul.nav-main {
	margin-right: 5px;
}

ul.nav-main > li > a {
	padding: 9px 22px;
}

ul.nav-main > li > a:hover, ul.nav-main > li > a:focus {
	background-color: rgba(62,70,75,0.301);
}

ul.nav-main > li.nav-active > a {
	box-shadow: 2px 0 0 #CCC inset;
}

ul.nav-main > li.nav-active > a:hover {
	color: #abb4be;
}

ul.nav-main > li.nav-active > i {
	color: #CCC;
}

ul.nav-main > li.nav-expanded > a {
	background: rgba(62,70,75,0.301);
}

ul.nav-main li a {
	font-size: 1.3rem;
	color: #0e0e0e;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-weight: 500;
}

html.dark ul.nav-main li a {
	color: #e2e8f0;
}

ul.nav-main li a i {
	color: #34d399; /* Updated to Emerald 400 */
}

html.dark ul.nav-main li a i {
	color: #34d399; /* Updated to Emerald 400 */
}

ul.nav-main li a span.label {
	font-weight: normal;
	font-size: 1rem;
	padding: .3em .7em .4em;
	margin: .4em -1em 0 0;
}

ul.nav-main li a .not-included {
	font-style: normal;
	color: #505b67;
	display: inline-block;
	padding: 0 0 0 6px;
}

ul.nav-main li span {
	vertical-align: middle;
}

ul.nav-main li i {
	font-size: 1.8rem;
	width: 1.1em;
	margin-right: 0.5em;
	text-align: center;
	vertical-align: middle;
}

ul.nav-main li .nav-children span i {
	margin-right: 0.1em;
}

ul.nav-main li.nav-parent {
	position: relative;
}

ul.nav-main li.nav-parent > a {
	cursor: pointer;
}

ul.nav-main li.nav-parent > a:after {
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
	content: '\f067';
	color: #abb4be;
	position: absolute;
	right: 0;
	top: 0;
	padding: 14px 15px;
	font-size: .90rem;
}

ul.nav-main li.nav-parent.nav-expanded > a:after {
	content: '\f068';
}

ul.nav-main li.nav-parent.nav-expanded > ul.nav-children {
	display: block;
}

ul.nav-main li .nav-children {
	background: #2d2d2d;
	box-shadow: 0px -3px 3px -3px rgba(33, 31, 31, 0.7) inset;
	display: none;
	padding: 10px 0;
}

ul.nav-main li .nav-children li a {
	padding: 6px 15px 6px 34px;
	overflow: hidden;
}

ul.nav-main li .nav-children li a:hover, ul.nav-main li .nav-children li a:focus {
	background: #2d2d2d;
}

ul.nav-main li .nav-children li a:after {
	padding: 6px 25px;
}

ul.nav-main li .nav-children .nav-children {
	box-shadow: none;
	padding: 0;
}

ul.nav-main li .nav-children .nav-children li a {
	padding: 6px 15px 6px 50px;
}

ul.nav-main li .nav-children .nav-children .nav-children li a {
	padding: 6px 15px 6px 97px;
}

/* Sidebar Light - Menu */
html.sidebar-light:not(.dark) ul.nav-main {
	margin-top: 3px;
}

html.sidebar-light:not(.dark) ul.nav-main > li > a:hover, html.sidebar-light:not(.dark) ul.nav-main > li > a:focus {
	background-color: #fafafa;
}

html.sidebar-light:not(.dark) ul.nav-main > li.nav-expanded > a {
	background: #fafafa;
}

html.sidebar-light:not(.dark) ul.nav-main li .nav-children {
	background: #F6F6F6;
	box-shadow: 0 -3px 3px -3px rgba(0, 0, 0, 0.1) inset;
}

html.sidebar-light:not(.dark) ul.nav-main li .nav-children li a:hover, html.sidebar-light:not(.dark) ul.nav-main li .nav-children li a:focus {
	background: #fff;
}

/* Page Header */
.page-header {
	z-index: 998;
}

.page-header h2 {
	color: #FFF;
	border-bottom: 4px solid #CCC;
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	display: inline-block;
	float: left;
	height: 50px;
	font-size: 2rem;
	letter-spacing: normal;
	line-height: 50px;
	margin: 0 0 0 -1px;
	padding: 0 22px 0 10px;
}

.page-header .right-wrapper {
	float: right;
}

.page-header .breadcrumbs {
	display: inline-block;
	font-size: 0;
	line-height: 50px;
	margin: 0;
	padding: 0;
}

.page-header .breadcrumbs li {
	color: #e2e8f0;
	display: inline-block;
	font-weight: 300;
}

.page-header .breadcrumbs li:after {
	content: '/';
	display: inline-block;
	font-size: 1.4rem;
	margin: 0 10px;
	vertical-align: middle;
}

.page-header .breadcrumbs li:last-child:after {
	display: none;
}

.page-header .breadcrumbs .fa-home {
	font-size: 2rem;
}

.page-header .breadcrumbs i {
	vertical-align: middle;
}

.page-header .breadcrumbs a,
.page-header .breadcrumbs span {
	color: #e2e8f0;
	display: inline-block;
	font-size: 1.4rem;
	line-height: 20px;
	vertical-align: middle;
}

/* Header Dark - Page Header */
html.dark .page-header {
	box-shadow: 1px 2px 4px 3px #2d2d2d;
}

/* Sidebar Light- Page Header */
html.sidebar-light:not(.dark) .page-header {
	border-left-color: #e6e6e6;
	background: #fff;
	box-shadow: 1px 2px 4px 4px rgba(204, 204, 204, 0.55);
}

html.sidebar-light:not(.dark) .page-header h2 {
	color: #333;
}

html.sidebar-light:not(.dark) .page-header .breadcrumbs a,
html.sidebar-light:not(.dark) .page-header .breadcrumbs span {
	color: #333;
}

html.sidebar-light:not(.dark) .page-header .sidebar-right-toggle i {
	color: #333;
}

html.sidebar-right-opened .page-header .sidebar-right-toggle i:before {
	content: "\f054";
}

/* Page Header - Mobile */
@media only screen and (max-width: 767px) {
	.page-header {
		padding-right: 80px;
	}

	.page-header .breadcrumbs {
		display: none;
	}

	.page-header h2 {
		font-size: 16px;
		padding: 0 15px 0;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		max-width: 100%;
	}

	.page-header .sidebar-right-toggle {
		position: absolute;
		right: 0;
		top: 0;
	}
}
/* Headings */
h1,
h2,
h3,
.h1,
.h2,
.h3 {
	letter-spacing: -1px;
}

h1,
.h1 {
	font-size: 3.6rem;
}

h2,
.h2 {
	font-size: 3rem;
}

h3,
.h3 {
	font-size: 2.4rem;
}

h4,
.h4 {
	font-size: 1.8rem;
}

h5,
.h5 {
	font-size: 1.4rem;
}

h6,
.h6 {
	font-size: 1.2rem;
	letter-spacing: 0;
}

/* Alternative Font Style */
.alternative-font {
	color: #CCC;
	font-family: "Shadows Into Light", cursive;
	font-size: 1.6em;
}

/* Shadow Style 1 */
.shadow-style-1 {
	box-shadow: 10px 10px 74px -15px rgba(74, 74, 74, 0.1);
	-webkit-transition: ease box-shadow 300ms;
	-moz-transition: ease box-shadow 300ms;
	transition: ease box-shadow 300ms;
}

.shadow-style-1:hover {
	box-shadow: 10px 10px 74px -15px rgba(74, 74, 74, 0.4);
}

/* Shadow Style 2 */
.shadow-style-2 {
	box-shadow: 10px 10px 74px -15px rgba(74, 74, 74, 0.4);
}

/* Drop Caps */
p.drop-caps:first-child:first-letter {
	float: left;
	font-size: 75px;
	line-height: 60px;
	padding: 4px;
	margin-right: 5px;
	margin-top: 5px;
	font-family: Georgia;
	color: #171717;
}

p.drop-caps.secondary:first-child:first-letter {
	background-color: #171717;
	color: #FFF;
	padding: 6px;
	margin-right: 5px;
	border-radius: 4px;
}

p.drop-caps.colored:first-child:first-letter {
	color: #CCC;
}

p.drop-caps.colored.secondary:first-child:first-letter {
	background-color: #CCC;
	color: #FFF;
}

/* Blockquote */
blockquote {
	font-size: 1em;
}

/* Hightlight */
.highlight {
	background-color: #CCC;
	color: #FFF;
	padding: 3px 6px;
}

/* Divider Line */
hr {
	border: 0;
	height: 1px;
	background-image: -webkit-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);
	background-image: -moz-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);
	background-image: -ms-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);
	background-image: -o-linear-gradient(left, transparent, rgba(0, 0, 0, 0.2), transparent);
	margin: 22px 0 22px 0;
}

hr.short {
	margin: 11px 0 11px 0;
}

hr.tall {
	margin: 44px 0 44px 0;
}

hr.taller {
	margin: 66px 0 66px 0;
}

hr.light {
	background-image: -webkit-linear-gradient(left, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
	background-image: -moz-linear-gradient(left, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
	background-image: -ms-linear-gradient(left, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
	background-image: -o-linear-gradient(left, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
}

hr.dotted {
	height: 0;
	border-bottom: 1px dotted #ddd;
}

hr.solid {
	height: 0;
	border-bottom: 1px solid #ddd;
}

/* Buttons Icon */
.btn-icon i {
	margin-right: 10px;
}

.btn-icon-right i {
	margin-right: 0;
	margin-left: 10px;
}

/* Form Elements */
input {
	outline: none;
}

label {
	font-weight: normal;
}

textarea {
	resize: vertical;
}

textarea[data-toggle=autosize] {
	-webkit-transition: height 0.15s ease-in;
	-moz-transition: height 0.15s ease-in;
	transition: height 0.15s ease-in;
}

select {
	border: 1px solid #E5E7E9;
	border-radius: 6px;
	height: 46px;
	padding: 12px;
	outline: none;
}

/* Forms Validations */
label.valid {
	display: inline-block;
	text-indent: -9999px;
}

label.error {
	color: #C10000;
	font-size: 0.9em;
	margin-top: -5px;
	padding: 0;
}

span.error{
	color: #B94A48;
	font-size: 0.9em;
	padding: 0;
}

span.error p{
	margin: 2px 0 2px !important;
}

/* Miscellaneous */
body a, body a:focus, body a:hover, body a:active, body a:visited {
	outline: none !important;
	text-decoration: none;
}

.center {
	text-align: center;
}

.popover .btn {
	margin-right: 5px;
}

.popover .btn:last-child {
	margin-right: 0;
}

ul,
ol {
	margin-bottom: 0;
	padding-left: 27px;
}

blockquote.primary {
	border-color: #CCC;
}

blockquote.success {
	border-color: #47a447;
}

blockquote.warning {
	border-color: #ed9c28;
}

blockquote.danger {
	border-color: #d2322d;
}

blockquote.info {
	border-color: #5bc0de;
}

blockquote.dark {
	border-color: #171717;
}

.well.primary {
	background: #CCC;
	border-color: #b3b3b3;
	color: #FFF;
}

.well.success {
	background: #34d399;
	border-color: #34d399; /* Updated to Emerald 400 */
	color: #FFF;
}

.well.warning {
	background: #60a5fa;
	border-color: #3b82f6;
	color: #FFF;
}

.well.danger {
	background: #d2322d;
	border-color: #a82824;
	color: #FFF;
}

.well.info {
	background: #2dd4bf;
	border-color: #14b8a6;
	color: #FFF;
}

.well.dark {
	background: #0f172a;
	border-color: #1e293b;
	color: #FFF;
}

/* Arrows */
.arrow {
	background: transparent url(../images/arrows.png) no-repeat 0 0;
	width: 47px;
	height: 120px;
	display: inline-block;
	position: relative;
}

.arrow.arrow-light {
	background-image: url(../images/arrows-dark.png);
}

.arrow.vtl {
	background-position: 0 0;
	width: 47px;
	height: 96px;
}

.arrow.vtr {
	background-position: -101px 0;
	width: 47px;
	height: 96px;
}

.arrow.vbl {
	background-position: 0 -144px;
	width: 47px;
	height: 96px;
}

.arrow.vbr {
	background-position: -101px -144px;
	width: 47px;
	height: 96px;
}

.arrow.hlt {
	background-position: -209px 0;
	width: 120px;
	height: 47px;
}

.arrow.hlb {
	background-position: -209px -101px;
	width: 120px;
	height: 47px;
}

.arrow.hrt {
	background-position: -353px 0;
	width: 120px;
	height: 47px;
}

.arrow.hrb {
	background-position: -353px -101px;
	width: 120px;
	height: 47px;
}

.img-thumbnail {
	border-radius: 8px;
	position: relative;
}

.img-thumbnail .zoom {
	display: block;
	position: absolute;
	right: 8px;
	bottom: 8px;
	height: 30px;
	width: 30px;
	padding: 6px;
	font-size: 14px;
	line-height: 18px;
	background: #CCC;
	border-radius: 100%;
	color: #FFF;
	text-align: center;
}

.img-thumbnail .zoom i {
	position: relative;
	top: -1px;
	left: -1px;
}

/* Thumbnail Gallery */
.thumbnail-gallery {
	list-style: none;
	margin: 10px 0;
	padding: 0;
}

.thumbnail-gallery .img-thumbnail,
.thumbnail-gallery .thumbnail {
	margin: 10px 10px 0 0;
}

/* Navs */
ul.nav-list.primary > li {
	margin: 0;
	padding: 0;
}

ul.nav-list.primary > li:last-child a {
	border-bottom: transparent !important;
}

ul.nav-list.primary > li a {
	-webkit-transition: all 0.3s;
	-moz-transition: all 0.3s;
	transition: all 0.3s;
	background-position: 9px 16px;
	background-repeat: no-repeat;
	border-bottom: 1px solid #EDEDDE;
	padding: 8px 20px;
}

.changelog h4 {
	display: inline-block;
	color: #000;
	font-size: 1em;
	font-weight: 600;
}

.changelog .release-date {
	color: #94a3b8;
	font-size: 0.9em;
}

.changelog .label {
	display: inline-block;
	min-width: 100px;
}

.scrollable {
	overflow: hidden;
	position: relative;
	width: 100%;
}

.scrollable .scrollable-content {
	bottom: 0;
	left: 0;
	overflow: hidden;
	position: absolute;
	right: 0;
	top: 0;
	padding: 0 18px 0 0;
	overflow-x: hidden;
	overflow-y: scroll;
	outline: none;
}

.scrollable .scrollable-content::-webkit-scrollbar {
	visibility: hidden;
}

.scrollable .scrollable-pane {
	bottom: 0;
	opacity: 0.01;
	position: absolute;
	right: 5px;
	top: 0;
	transition: all 0.2s ease 0s;
	width: 4px;
}

.scrollable .scrollable-slider {
	border-radius: 5px;
	background: none repeat scroll 0 0 #CCC;
	margin: 0;
	position: relative;
	transition: opacity 0.2s ease 0s;
	opacity: 0;
}

html.dark .scrollable .scrollable-slider {
	background-color: #34d399; /* Updated to Emerald 400 */
}

.scrollable.scrollable-padding .scrollable-content {
	padding: 10px 24px 10px 10px;
}

.scrollable:hover .scrollable-slider,
.scrollable.visible-slider .scrollable-slider {
	opacity: 1;
}

.text-right {
	text-align: right !important;
}

.text-xs {
	font-size: 1rem;
}

.text-sm {
	font-size: 1.2rem;
}

.text-md {
	font-size: 1.6rem;
}

.text-lg {
	font-size: 1.9rem;
}

.text-xl {
	font-size: 2.2rem;
}

.text-xlg {
	font-size: 2.4rem;
}

.text-muted {
	color: #94a3b8 !important;
}

html.dark .text-muted {
	color: #505461 !important;
}

.text-primary {
	color: #34d399 !important; /* Updated to Emerald 400 */
}

html.dark .text-primary {
	color: #34d399 !important; /* Updated to Emerald 400 */
}

.text-secondary {
	color: #E36159 !important;
}

.text-tertiary {
	color: #2BAAB1 !important;
}

.text-quaternary {
	color: #734BA9 !important;
}

.text-success {
	color: #47a447 !important;
}

.text-warning {
	color: #ed9c28 !important;
}

.text-danger {
	color: #10b981 !important; /* Changed to emerald green to match pie chart expense color */
}

.text-info {
	color: #5bc0de !important;
}

.text-dark {
	color: #171717 !important;
}

.text-primary-inverse {
	color: #FFF !important;
}

.text-secondary-inverse {
	color: #FFF !important;
}

.text-tertiary-inverse {
	color: #FFF !important;
}

.text-quaternary-inverse {
	color: #FFF !important;
}

.text-success-inverse {
	color: #FFF !important;
}

.text-warning-inverse {
	color: #FFF !important;
}

.text-danger-inverse {
	color: #FFF !important;
}

.text-info-inverse {
	color: #FFF !important;
}

.text-dark-inverse {
	color: #FFF !important;
}

.text-blue {
	color: #009efb !important;
}

/* weights */
.text-weight-light {
	font-weight: 300;
}

.text-weight-normal {
	font-weight: 400;
}

.text-weight-semibold {
	font-weight: 600;
}

.text-weight-bold {
	font-weight: 700;
}

.text-weight-extrabold {
	font-weight: 900;
}

.text-uppercase {
	text-transform: uppercase;
}

.text-lowercase {
	text-transform: lowercase;
}

.text-capitalize {
	text-transform: capitalize;
}

.rounded {
	border-radius: 500px;
}

.b-thin {
	border-width: 3px;
}

.b-normal {
	border-width: 5px;
}

.b-thick {
	border-width: 7px;
}

.b-none {
	border: none !important;
}

.list-style-none > li {
	list-style: none !important;
}

/* Spacements */
/* spacement top & bottom */
.m-none {
	margin: 0 !important;
}

.m-auto {
	margin: 0 auto !important;
}

.m-xs {
	margin: 5px !important;
}

.m-sm {
	margin: 10px !important;
}

.m-md {
	margin: 15px !important;
}

.m-lg {
	margin: 20px !important;
}

.m-xl {
	margin: 25px !important;
}

.m-xlg {
	margin: 30px !important;
}

/* spacement top	*/
.mt-none {
	margin-top: 0 !important;
}

.mt-xs {
	margin-top: 5px !important;
}

.mt-sm {
	margin-top: 10px !important;
}

.mt-md {
	margin-top: 15px !important;
}

.mt-lg {
	margin-top: 20px !important;
}

.mt-xlg {
	margin-top: 26px !important;
}

.mt-xl {
	margin-top: 25px !important;
}

.mt-xlg {
	margin-top: 30px !important;
}

.mt-xxlg {
	margin-top: 50px !important;
}
/* spacement bottom	*/
.mb-none {
	margin-bottom: 0 !important;
}

.mb-xs {
	margin-bottom: 5px !important;
}

.mb-sm {
	margin-bottom: 10px !important;
}

.mb-md {
	margin-bottom: 15px !important;
}

.mb-lg {
	margin-bottom: 20px !important;
}

.mb-xl {
	margin-bottom: 25px !important;
}

.mb-xlg {
	margin-bottom: 30px !important;
}

/* spacement left	*/
.ml-none {
	margin-left: 0 !important;
}

.ml-xs {
	margin-left: 5px !important;
}

.ml-sm {
	margin-left: 10px !important;
}

.ml-md {
	margin-left: 15px !important;
}

.ml-lg {
	margin-left: 20px !important;
}

.ml-xl {
	margin-left: 25px !important;
}

.ml-xlg {
	margin-left: 30px !important;
}

/* spacement right	*/
.mr-none {
	margin-right: 0 !important;
}

.mr-xs {
	margin-right: 5px !important;
}

.mr-sm {
	margin-right: 10px !important;
}

.mr-md {
	margin-right: 15px !important;
}

.mr-lg {
	margin-right: 20px !important;
}

.mr-xl {
	margin-right: 25px !important;
}

.mr-xlg {
	margin-right: 30px !important;
}

/* Spacement Padding */
.p-none {
	padding: 0 !important;
}

.p-xs {
	padding: 5px !important;
}

.p-sm {
	padding: 10px !important;
}

.p-md {
	padding: 15px !important;
}

.p-lg {
	padding: 20px !important;
}

.p-xl {
	padding: 25px !important;
}

.p-xlg {
	padding: 30px !important;
}

/* spacement top	*/
.pt-none {
	padding-top: 0 !important;
}

.pt-xs {
	padding-top: 5px !important;
}

.pt-sm {
	padding-top: 10px !important;
}

.pt-md {
	padding-top: 15px !important;
}

.pt-lg {
	padding-top: 20px !important;
}

.pt-xl {
	padding-top: 25px !important;
}

.pt-xlg {
	padding-top: 30px !important;
}

/* spacement bottom	*/
.pb-none {
	padding-bottom: 0 !important;
}

.pb-xs {
	padding-bottom: 5px !important;
}

.pb-sm {
	padding-bottom: 10px !important;
}

.pb-md {
	padding-bottom: 15px !important;
}

.pb-lg {
	padding-bottom: 20px !important;
}

.pb-xl {
	padding-bottom: 25px !important;
}

.pb-xlg {
	padding-bottom: 30px !important;
}

/* spacement left	*/
.pl-none {
	padding-left: 0 !important;
}



.pl-sm {
	padding-left: 10px !important;
}

.pl-md {
	padding-left: 15px !important;
}

.pl-lg {
	padding-left: 20px !important;
}

.pl-xl {
	padding-left: 25px !important;
}

.pl-xlg {
	padding-left: 30px !important;
}

/* spacement right	*/
.pr-none {
	padding-right: 0 !important;
}

@media (min-width: 992px) {
	.pr-xs {
		padding-right: 5px !important;
	}
	.pl-xs {
		padding-left: 5px !important;
	}
}

.pr-sm {
	padding-right: 10px !important;
}

.pr-md {
	padding-right: 15px !important;
}

.pr-lg {
	padding-right: 20px !important;
}

.pr-xl {
	padding-right: 25px !important;
}

.pr-xlg {
	padding-right: 30px !important;
}

.ib {
	display: inline-block;
	vertical-align: top;
}

.va-middle {
	vertical-align: middle;
}

.ws-nowrap {
	white-space: nowrap;
}

.ws-normal {
	white-space: normal;
}

.bg-none {
	background: none !important;
}

.bg-light {
	background-color: #FFF;
}

.bg-default {
	background: #ebebeb;
	color: #777;
}

.bg-primary {
	background: #34d399; /* Updated to Emerald 400 */
	color: #FFF;
}

.bg-secondary {
	background: #3b82f6;
	color: #FFF;
}

.bg-tertiary {
	background: #14b8a6;
	color: #FFF;
}

.bg-quaternary {
	background: #2563eb;
	color: #FFF;
}

.bg-success {
	background: #34d399;
	color: #FFF;
}

.bg-warning {
	background: #60a5fa;
	color: #FFF;
}

.bg-danger {
	background: #d2322d;
	color: #FFF;
}

.bg-info {
	background: #2dd4bf;
	color: #FFF;
}

.bg-dark {
	background: #0f172a;
	color: #FFF;
}

/* Container */
.container-xl {
	width: 100%;
	max-width: 1630px;
}

/* Form - iOS Override */
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
textarea {
	-webkit-appearance: none;
}

.form-control::-webkit-input-placeholder,
input[type="text"]::-webkit-input-placeholder,
input[type="password"]::-webkit-input-placeholder,
input[type="datetime"]::-webkit-input-placeholder,
input[type="datetime-local"]::-webkit-input-placeholder,
input[type="date"]::-webkit-input-placeholder,
input[type="month"]::-webkit-input-placeholder,
input[type="time"]::-webkit-input-placeholder,
input[type="week"]::-webkit-input-placeholder,
input[type="number"]::-webkit-input-placeholder,
input[type="email"]::-webkit-input-placeholder,
input[type="url"]::-webkit-input-placeholder,
input[type="search"]::-webkit-input-placeholder,
input[type="tel"]::-webkit-input-placeholder,
input[type="color"]::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
	color: #94a3b8;
}

.form-control::-moz-placeholder,
input[type="text"]::-moz-placeholder,
input[type="password"]::-moz-placeholder,
input[type="datetime"]::-moz-placeholder,
input[type="datetime-local"]::-moz-placeholder,
input[type="date"]::-moz-placeholder,
input[type="month"]::-moz-placeholder,
input[type="time"]::-moz-placeholder,
input[type="week"]::-moz-placeholder,
input[type="number"]::-moz-placeholder,
input[type="email"]::-moz-placeholder,
input[type="url"]::-moz-placeholder,
input[type="search"]::-moz-placeholder,
input[type="tel"]::-moz-placeholder,
input[type="color"]::-moz-placeholder,
textarea::-moz-placeholder {
	color: #94a3b8;
}

.form-control:-ms-input-placeholder,
input[type="text"]:-ms-input-placeholder,
input[type="password"]:-ms-input-placeholder,
input[type="datetime"]:-ms-input-placeholder,
input[type="datetime-local"]:-ms-input-placeholder,
input[type="date"]:-ms-input-placeholder,
input[type="month"]:-ms-input-placeholder,
input[type="time"]:-ms-input-placeholder,
input[type="week"]:-ms-input-placeholder,
input[type="number"]:-ms-input-placeholder,
input[type="email"]:-ms-input-placeholder,
input[type="url"]:-ms-input-placeholder,
input[type="search"]:-ms-input-placeholder,
input[type="tel"]:-ms-input-placeholder,
input[type="color"]:-ms-input-placeholder,
textarea:-ms-input-placeholder {
	color: #94a3b8;
}

.form-control:focus {
	border-color: #66afe9;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}

html.dark .form-control {
	background-color: #383838;
	border-color: #4c4e51;
	color: #e2e8f0;
}

html.dark .form-control[disabled],
html.dark .form-control[readonly],
html.dark fieldset[disabled] .form-control {
	background-color: #303030;
}

html.dark .input-group-addon {
	background-color: #2f2f2f !important;
	border-color: #4c4e51;
	color: #EEE;
}

/* Form - Bootstrap Override */
.btn-lg,
.btn-group-lg > .btn {
	line-height: 1.334;
}

select.input-sm, select.input-lg {
	line-height: 1;
}

.bootstrap-timepicker-widget input {
	border: 0;
}

.bootstrap-timepicker-widget table td input {
    width: 40px !important;
}

/* Form - Custom Fields */
.required {
	display: inline-block;
	color: #d2322d;
	font-size: 0.8em;
	font-weight: bold;
	position: relative;
	top: -0.2em;
}

label.error {
	color: #B94A48;
	margin-top: 2px;
}

/* Form - Group Override */
.form-group:after {
	clear: both;
	display: block;
	content: '';
}

.form-group:last-child, .form-group:last-of-type {
	margin-bottom: 0;
}

/* Form - Bordered */
.form-bordered .form-group {
	border-bottom: 1px solid #eff2f7;
	padding-bottom: 15px;
	margin-bottom: 15px;
}

.form-bordered .form-group:last-child, .form-bordered .form-group:last-of-type {
	border-bottom: none !important;
	padding-bottom: 0px !important;
	margin-bottom: 0px !important;
}

/* Dark - Form - Bordered */
html.dark .form-bordered .form-group {
	border-bottom: 1px solid #3c3e42;
	padding-bottom: 15px;
	margin-bottom: 15px;
}

/* Form - Vertical Group / Stacked */
.form-group-vertical {
	position: relative;
	white-space: nowrap;
}

.form-group-vertical .form-control {
	border-radius: 0;
	margin-top: -1px;
	z-index: 1;
}

.form-group-vertical .form-control:first-child, .form-group-vertical .form-control:first-of-type {
	border-radius: 4px 4px 0 0;
}

.form-group-vertical .form-control:last-child, .form-group-vertical .form-control:last-of-type {
	border-radius: 0 0 4px 4px;
}

.form-group-vertical .form-control:focus {
	position: relative;
	z-index: 2;
}

.form-group-vertical .input-group {
	margin-top: -1px;
}

.form-group-vertical .input-group .form-control {
	margin-top: 0;
}

.form-group-vertical .input-group:first-child .input-group-addon, .form-group-vertical .input-group:first-of-type .input-group-addon {
	border-radius: 4px 0 0 0;
}

.form-group-vertical .input-group:first-child .form-control, .form-group-vertical .input-group:first-of-type .form-control {
	border-radius: 0 4px 0 0;
}

.form-group-vertical .input-group:last-child .input-group-addon, .form-group-vertical .input-group:last-of-type .input-group-addon {
	border-radius: 0 0 0 4px;
}

.form-group-vertical .input-group:last-child .form-control, .form-group-vertical .input-group:last-of-type .form-control {
	border-radius: 0 0 4px 0;
}

.form-group-vertical .input-group.input-group-icon:first-child .input-group-addon, .form-group-vertical .input-group.input-group-icon:first-of-type .input-group-addon {
	border-radius: 4px 4px 0 0;
}

.form-group-vertical .input-group.input-group-icon:first-child .form-control, .form-group-vertical .input-group.input-group-icon:first-of-type .form-control {
	border-radius: 4px 4px 0 0;
}

.form-group-vertical .input-group.input-group-icon:last-child .input-group-addon, .form-group-vertical .input-group.input-group-icon:last-of-type .input-group-addon {
	border-radius: 0 0 4px 4px;
}

.form-group-vertical .input-group.input-group-icon:last-child .form-control, .form-group-vertical .input-group.input-group-icon:last-of-type .form-control {
	border-radius: 0 0 4px 4px;
}

/* Form - Input Override */
.input-lg {
	border-radius: 4px;
}

/* Form - Input Icon */
.input-group-icon,
.input-search {
	width: 100%;
	table-layout: fixed;
}

.input-group-icon input.form-control,
.input-search input.form-control {
	font-size: 1.2rem;
	padding-right: 36px;
}

.input-group-icon input.form-control:first-child, .input-group-icon input.form-control:last-child,
.input-search input.form-control:first-child,
.input-search input.form-control:last-child {
	border-radius: 4px;
}

.input-group-icon .input-group-btn,
.input-search .input-group-btn {
	border-radius: 500px;
	width: 0;
}

.input-group-icon .input-group-btn:first-child, .input-group-icon .input-group-btn:last-child,
.input-search .input-group-btn:first-child,
.input-search .input-group-btn:last-child {
	border-radius: 500px;
}

.input-group-icon .input-group-btn button,
.input-search .input-group-btn button {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	border: 0;
	z-index: 3;
	background: transparent;
}

.input-group-icon .input-group-btn button:active,
.input-search .input-group-btn button:active {
	-webkit-box-shadow: none;
	box-shadow: none;
}

.input-group-icon .input-group-btn:last-child button,
.input-search .input-group-btn:last-child button {
	left: auto;
	right: 0;
}

.input-group-icon .input-group-btn + input.form-control,
.input-search .input-group-btn + input.form-control {
	padding-right: 12px;
	padding-left: 36px;
}

.input-group-icon .input-group-addon,
.input-search .input-group-addon {
	position: relative;
	padding: 0;
	border: 0 none;
	width: 0;
}

.input-group-icon .input-group-addon span.icon,
.input-search .input-group-addon span.icon {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	border: 0;
	z-index: 3;
	width: auto;
	display: inline-block;
	vertical-align: middle;
	text-align: center;
	padding: 6px 12px;
	background: transparent;
	line-height: 1.42857143;
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	pointer-events: none;
}

.input-group-icon .input-group-addon span.icon.icon-lg,
.input-search .input-group-addon span.icon.icon-lg {
	padding: 10px 14px;
	font-size: 18px;
}

.input-group-icon .input-group-addon:last-child span.icon,
.input-search .input-group-addon:last-child span.icon {
	left: auto;
	right: 0;
}

.input-group-icon .input-group-addon + input.form-control,
.input-search .input-group-addon + input.form-control {
	padding-right: 12px;
	padding-left: 36px;
}

/* Form - Input Search */
.input-search {
	width: 100%;
}

.input-search input.form-control:focus {
	border-color: #ccc;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.input-search .input-group-btn {
	color: #ccc;
}

.input-search .input-group-btn .btn {
	padding-left: 15px;
}

.input-search .input-group-btn .btn-default {
	color: #ccc;
}

/* Dark */
.input-search {
	width: 100%;
}

.input-search input.form-control:focus {
	border-color: #1d2127;
}

.input-search .input-group-btn .btn {
	background: transparent !important;
}

/* Form - Round Input */
input.input-rounded {
	-webkit-border-radius: 500px;
	border-radius: 500px;
}

.input-group-rounded input.form-control,
.input-search input.form-control {
	-webkit-border-radius: 500px;
	border-radius: 500px;
}

.input-group-rounded input.form-control:first-child, .input-group-rounded input.form-control:last-child {
	border-radius: 500px;
}

.input-group-rounded .input-group-addon:first-child,
.input-search .input-group-addon:first-child {
	border-radius: 500px 0 0 500px;
}

.input-group-rounded .input-group-addon:last-child,
.input-search .input-group-addon:last-child {
	border-radius: 0 500px 500px 0;
}

/* Form - Custom Checkbox */
.checkbox-replace .i-checks {
	padding-left: 20px;
	cursor: pointer;
	margin-bottom: 0;
}

.checkbox-replace .i-checks input {
	position: absolute;
	margin-left: -20px;
	opacity: 0;
}

.checkbox-replace .i-checks input:checked + span .active {
	display: inherit;
}

.checkbox-replace .i-checks > span {
	margin-left: -20px;
}

.checkbox-replace .i-checks > span .active {
	display: none;
}

.checkbox-replace .i-checks > i {
	position: relative;
	display: inline-block;
	width: 20px;
	height: 20px;
	margin-top: -2px;
	margin-right: 4px;
	margin-left: -20px;
	line-height: 1;
	vertical-align: middle;
	background-color: transparent;
	border: 1px solid #cfdadd;
	-webkit-transition: all .2s;
	transition: all .2s;
}

html.dark .checkbox-replace .i-checks > i {
	border-color: #535353;
}

.checkbox-replace .i-checks > i:before {
	content: "";
	position: absolute;
	top: 4px;
	left: 4px;
	width: 10px;
	height: 10px;
	background-color: #34d399; /* Updated to Emerald 400 */
	-webkit-transform: scale(0);
	-moz-transform: scale(0);
	-ms-transform: scale(0);
	transform: scale(0);
	-webkit-transition: all .2s;
	transition: all .2s;
}

.checkbox-replace .i-checks input:checked + i {
	border-color: #34d399; /* Updated to Emerald 400 */
}

html.dark .checkbox-replace .i-checks input:checked + i{
	border-color: #34d399; /* Updated to Emerald 400 */
}

.checkbox-replace .i-checks input:checked + i:before {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
}

html.dark .checkbox-replace .i-checks input:checked + i:before {
	background-color: #34d399; /* Updated to Emerald 400 */
}

.checkbox-replace .i-checks input:disabled + i {
	cursor: not-allowed;
	border-color: #dee5e7;
}

.checkbox-replace .i-checks input:disabled + i:before{
	background-color: #dee5e7;
}

html.dark .checkbox-replace .i-checks input:disabled + i{
	border-color: #4c4c4d;
}

html.dark .checkbox-replace .i-checks input:disabled + i:before{
	background-color: #4c4c4d;
}

/* Form - Custom Radio */
.radio-custom {
	position: relative;
	padding: 0 0 0 25px;
	margin-bottom: 7px;
	margin-top: 0;
}

.radio-custom.radio-inline {
	display: inline-block;
	vertical-align: middle;
}

.form-group .radio-custom.radio-inline {
	margin-top: 7px;
	padding-top: 0;
}

.radio-custom:last-child, .radio-custom:last-of-type {
	margin-bottom: 5px;
}

.radio-custom input[type="radio"] {
	opacity: 0;
	position: absolute;
	top: 50%;
	left: 3px;
	margin: -6px 0 0 0;
	z-index: 2;
	cursor: pointer;
}

.radio-custom input[type="radio"]:checked + label:after {
	content: '';
	position: absolute;
	top: 50%;
	left: 4px;
	margin-top: -5px;
	display: inline-block;
	font-size: 11px;
	line-height: 1;
	width: 10px;
	height: 10px;
	background-color: #444;
	border-radius: 50px;
	-webkit-box-shadow: 0px 0px 1px #444;
	box-shadow: 0px 0px 1px #444;
}

.radio-custom input[type="radio"]:disabled {
	cursor: not-allowed;
}

.radio-custom input[type="radio"]:disabled:checked + label:after {
	color: #999;
}

.radio-custom input[type="radio"]:disabled + label {
	cursor: not-allowed;
}

.radio-custom input[type="radio"]:disabled + label:before {
	background-color: #eee;
}

.radio-custom label {
	cursor: pointer;
	margin-bottom: 0;
	text-align: left;
	line-height: 1.2;
}

.radio-custom label:before {
	content: '';
	position: absolute;
	top: 50%;
	left: 0;
	margin-top: -9px;
	width: 18px;
	height: 18px;
	display: inline-block;
	border-radius: 50px;
	border: 1px solid #bbb;
	background: #fff;
}

.radio-custom label + label.error {
	display: block;
}

html.dark .radio-custom label:before {
	background: #383838;
	border-color: #303030;
}

html.dark .radio-custom input[type="radio"]:checked + label:after {
	background-color: #fff;
}

html.dark .radio-custom input[type="radio"]:disabled + label:before {
	background: #242830;
	border-color: #242830;
}

html.dark .radio-primary input[type="radio"]:checked + label:after,
.radio-primary input[type="radio"]:checked + label:after {
	background: #CCC;
	-webkit-box-shadow: 0px 0px 1px #CCC;
	box-shadow: 0px 0px 1px #CCC;
}

html.dark .radio-success input[type="radio"]:checked + label:after,
.radio-success input[type="radio"]:checked + label:after {
	background: #34d399;
	-webkit-box-shadow: 0px 0px 1px #34d399;
	box-shadow: 0px 0px 1px #34d399;
}

html.dark .radio-warning input[type="radio"]:checked + label:after,
.radio-warning input[type="radio"]:checked + label:after {
	background: #60a5fa;
	-webkit-box-shadow: 0px 0px 1px #60a5fa;
	box-shadow: 0px 0px 1px #60a5fa;
}

html.dark .radio-danger input[type="radio"]:checked + label:after,
.radio-danger input[type="radio"]:checked + label:after {
	background: #d2322d;
	-webkit-box-shadow: 0px 0px 1px #d2322d;
	box-shadow: 0px 0px 1px #d2322d;
}

html.dark .radio-info input[type="radio"]:checked + label:after,
.radio-info input[type="radio"]:checked + label:after {
	background: #2dd4bf;
	-webkit-box-shadow: 0px 0px 1px #2dd4bf;
	box-shadow: 0px 0px 1px #2dd4bf;
}

html.dark .radio-dark input[type="radio"]:checked + label:after,
.radio-dark input[type="radio"]:checked + label:after {
	background: #0f172a;
	-webkit-box-shadow: 0px 0px 1px #0f172a;
	box-shadow: 0px 0px 1px #0f172a;
}

/* Form - Error Container */
div.validation-message ul {
	display: none;
	list-style: none;
	margin: -15px -15px 15px -15px;
	padding: 15px;
	border-bottom: 1px solid #FFCBCB;
	background: #FFEFEF;
}

div.validation-message ul label.error {
	display: block;
	padding-left: 22px;
	position: relative;
}

div.validation-message ul label.error:before {
	font-family: 'Font Awesome 5 Free';
	content: '\f00d';
	position: absolute;
	top: 0;
	left: 0;
	font-size: 16px;
	color: #D9534F;
	display: inline-block;
}

.select2-drop-mask {
	z-index: 10010;
}

.select2-drop {
	z-index: 10011;
}

.select2-search {
	z-index: 10012;
}

.select2-container--bootstrap.select2-container--open {
	z-index: 10013;
}

.select2-container--bootstrap .select2-selection--single .select2-selection__rendered {
	line-height: 1.5;
}

@media (max-width: 991px) {
	.select2-container--bootstrap {
		width: auto !important;
	}
}

.bootstrap-maxlength {
	z-index: 999999 !important;
}

html.dark .fileupload .uneditable-input {
	background-color: #383838;
	border-color: #383838;
}

html.dark .fileupload-new .input-append .btn-file {
	border-color: #303030;
}

/* Nano Scroller Plugin */
html.no-overflowscrolling .nano {
	height: 100%;
	position: relative;
	overflow: hidden;
	width: 100%;
}

html.no-overflowscrolling .nano > .nano-content {
	bottom: 0;
	left: 0;
	position: absolute;
	overflow: hidden;
	right: 0;
	top: 0;
}

html.no-overflowscrolling .nano > .nano-content:focus {
	outline: none;
}

html.no-overflowscrolling .nano > .nano-content::-webkit-scrollbar {
	display: none;
	visibility: hidden;
}

html.no-overflowscrolling .nano.has-scrollbar > .nano-content::-webkit-scrollbar {
	display: block;
	visibility: visible;
}

html.no-overflowscrolling .nano > .nano-pane {
	bottom: 0;
	position: absolute;
	opacity: .01;
	right: 0;
	top: 0;
	visibility: hidden\9;
	/* Target only IE7 and IE8 with this hack */
	width: 4px;
	-webkit-transition: .2s;
	-moz-transition: .2s;
	-o-transition: .2s;
	transition: .2s;
}

html.no-overflowscrolling .nano > .nano-pane > .nano-slider {
	background: #CCC;
	margin: 0;
	position: relative;
	visibility: hidden;
}

html.no-overflowscrolling .nano:hover > .nano-pane,
html.no-overflowscrolling .nano .nano-pane.active,
html.no-overflowscrolling .nano .nano-pane.flashed {
	opacity: 0.99;
}

html.no-overflowscrolling .nano:hover > .nano-pane > .nano-slider {
	visibility: visible;
}

html.no-overflowscrolling.custom-scroll .nano > .nano-content {
	overflow: scroll;
	overflow-x: hidden;
}

html.no-overflowscrolling .sidebar-left .nano {
	background: #313131;
	box-shadow: -5px 0 0 #3e3e3e inset;
}

html.no-overflowscrolling.sidebar-light:not(.dark) .sidebar-left .nano {
	background: #FFF;
	box-shadow: -5px 0 0 #F6F6F6 inset;
}

html.no-overflowscrolling.sidebar-light:not(.dark) .sidebar-right .nano {
	background: #F6F6F6;
	box-shadow: -5px 0 0 #F6F6F6 inset;
}

html.no-overflowscrolling.sidebar-light:not(.dark) .inner-menu .nano {
	background: #FFF;
	box-shadow: -5px 0 0 #e2e3e6 inset;
}

@media only screen and (max-width: 767px) {
	html.no-overflowscrolling .sidebar-left .nano > .nano-content,
	html.no-overflowscrolling .sidebar-right .nano > .nano-content,
	html.no-overflowscrolling .inner-menu .nano > .nano-content {
		overflow: scroll !important;
		overflow-x: hidden !important;
	}
}

@media only screen and (min-width: 768px) {
	html.overflowscrolling.fixed .sidebar-left .nano,
	html.overflowscrolling.fixed .sidebar-right .nano,
	html.overflowscrolling.fixed .inner-menu .nano {
		height: 100%;
		overflow: hidden;
		-webkit-overflow-scrolling: touch;
	}

	html.overflowscrolling.fixed .sidebar-left .nano > .nano-pane > .nano-slider,
	html.overflowscrolling.fixed .sidebar-right .nano > .nano-pane > .nano-slider,
	html.overflowscrolling.fixed .inner-menu .nano > .nano-pane > .nano-slider {
		visibility: visible;
	}

	html.overflowscrolling.fixed.custom-scroll .sidebar-left .nano > .nano-content,
	html.overflowscrolling.fixed.custom-scroll .sidebar-right .nano > .nano-content,
	html.overflowscrolling.fixed.custom-scroll .inner-menu .nano > .nano-content {
		overflow-y: scroll;
		overflow-x: hidden;
	}
}
/* Toolbar */
.inner-toolbar {
	background: #313131;
	margin: -40px -40px 30px;
	padding: 0;
	border: 1px solid transparent;
	border-left: 1px solid #121418;
}

.inner-toolbar > ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.inner-toolbar > ul > li {
	display: inline-block;
	padding: 15px;
	font-size: 13px;
	border-right: 1px solid #121418;
}

.inner-toolbar > ul > li > a {
	display: inline-block;
	padding: 0;
	color: #abb4be;
}

.inner-toolbar > ul > li > a:hover, .inner-toolbar > ul > li > a:focus {
	color: #fff;
	text-decoration: none;
}

.inner-toolbar > ul > li.right {
	float: right;
	padding-right: 10px;
}

.inner-toolbar > ul > li i.fa {
	font-size: 14px;
}

.inner-toolbar > ul > li > .btn {
	margin-top: -6px;
}

.inner-toolbar .nav-pills {
	margin-top: -8px;
}

.inner-toolbar .nav-pills > li > label {
	margin-bottom: 0;
	margin-right: 12px;
	margin-top: 8px;
}

.inner-toolbar .nav-pills a {
	color: #abb4be;
	padding-top: 8px;
	padding-bottom: 8px;
}

.inner-toolbar .nav-pills a:hover {
	background: #171717;
	color: #FFF;
}

.inner-toolbar .nav-pills .active a {
	color: #FFF;
}

html.sidebar-light:not(.dark) .inner-toolbar {
	background: #E2E3E6;
	border: 1px solid #D5D6D7;
}

html.sidebar-light:not(.dark) .inner-toolbar > ul > li {
	border-right: 1px solid #D5D6D7;
}

html.sidebar-light:not(.dark) .inner-toolbar > ul > li > a {
	color: #777;
}

html.sidebar-light:not(.dark) .inner-toolbar > ul > li > a:hover, html.sidebar-light:not(.dark) .inner-toolbar > ul > li > a:focus {
	color: #999;
}

html.sidebar-light:not(.dark) .inner-toolbar .nav-pills li:not(.active) a {
	color: #777;
}

html.sidebar-light:not(.dark) .inner-toolbar .nav-pills li:not(.active) a:hover {
	background: #E2E3E6;
	color: #999;
}

/* Toolbar - Responsive */
@media only screen and (max-width: 767px) {
	.inner-toolbar {
		clear: both;
		margin: -40px -15px 30px;
		padding: 0 15px;
	}

	.inner-toolbar ul > li {
		border-right: 0;
	}

	.inner-toolbar ul > li:first-child {
		padding-left: 0;
	}

	.inner-toolbar ul > li.right {
		padding-left: 0;
		padding-right: 0;
	}
}

@media only screen and (max-width: 480px) {
	.inner-toolbar .nav-pills a {
		padding-left: 10px;
		padding-right: 10px;
	}

	.inner-toolbar ul > li.right {
		clear: both;
		float: none;
		vertical-align: top;
	}
}
/* Toolbar + Layout Fixed */
@media only screen and (min-width: 768px) {
	html.fixed .inner-toolbar {
		left: 300px;
		right: 0;
		margin: 0;
		padding: 0;
		position: fixed;
		top: 114px;
		z-index: 1001;
	}
}
/* dark */
html.dark .inner-toolbar {
	border-left: none;
	border-bottom: 1px solid #242830;
}

html.dark .inner-toolbar > ul > li {
	border-color: #242830;
}

.call-to-action {
	padding: 25px;
	border-radius: 5px;
}

.call-to-action.call-to-action-primary {
	background-color: #CCC;
}

.call-to-action .call-to-action-content {
	text-align: left;
}

.call-to-action .call-to-action-content h2 {
	color: #FFF;
	font-weight: 100;
}

.call-to-action .call-to-action-content p {
	color: #FFF;
	font-size: 16px;
	font-weight: 100;
}

.call-to-action .call-to-action-btn {
	margin-top: 45px;
}

.call-to-action .call-to-action-btn a.btn:not(.btn-primary) {
	background-color: #3b82f6;
	border-color: #3b82f6 #3b82f6 #2563eb;
	color: #fff;
	border-radius: 6px;
	font-size: 16px;
	padding: 12px 33px;
	margin-right: 15px;
}

.call-to-action .call-to-action-btn a.btn:not(.btn-primary):hover {
	background-color: #2563eb;
	border-color: #2563eb #2563eb #1d4ed8;
}

.call-to-action .call-to-action-btn > span {
	position: relative;
	color: #FFF;
}

.call-to-action .call-to-action-btn > span .arrow {
	position: absolute;
	top: -55px;
	left: -70px;
}

@media (max-width: 1276px) {
	.call-to-action .call-to-action-btn > span {
		display: none;
	}
}

@media (max-width: 767px) {
	.call-to-action .call-to-action-btn {
		margin-top: 0;
		float: none !important;
	}
}

.call-to-action.call-to-action-grey {
	position: relative;
	background-color: #ecedf0;
}

.call-to-action.call-to-action-grey:before {
	content: '';
	display: block;
	position: absolute;
	top: 0;
	left: 50%;
	width: 100vw;
	height: 100%;
	background-color: #ecedf0;
	z-index: 0;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
}

.call-to-action.call-to-action-grey .call-to-action-content h2 {
	color: #171717;
}

.call-to-action.call-to-action-grey .call-to-action-content p {
	color: #777;
}

.call-to-action.call-to-action-top {
	margin: -40px;
	border-radius: 0;
	padding: 40px 25px;
}

@media (max-width: 767px) {
	.call-to-action.call-to-action-top {
		margin-top: -16px;
	}
}

/* Responsive */
html:not(.sidebar-left-collapsed) {
	/* Boxed Layout */;
}

@media (max-width: 1400px) {
	html:not(.sidebar-left-collapsed) .call-to-action .call-to-action-btn > span {
		display: none;
	}
}

@media (min-width: 768px) and (max-width: 1199px) {
	html:not(.sidebar-left-collapsed) .call-to-action .call-to-action-btn {
		margin-top: 0;
		float: none !important;
	}
}

@media (min-width: 768px) and (max-width: 991px) {
	html:not(.sidebar-left-collapsed) .call-to-action .col-sm-4, html:not(.sidebar-left-collapsed) .call-to-action .col-sm-8, html:not(.sidebar-left-collapsed) .call-to-action .col-sm-3, html:not(.sidebar-left-collapsed) .call-to-action .col-sm-9 {
		width: 100%;
	}
}

html:not(.sidebar-left-collapsed).boxed .call-to-action .call-to-action-btn {
	margin-top: 0;
	float: none !important;
}

html:not(.sidebar-left-collapsed).boxed .call-to-action .call-to-action-btn > span {
	display: none;
}

.loading-overlay-showing {
	overflow: hidden;
}

.loading-overlay-showing > .loading-overlay {
	opacity: 1;
	visibility: visible;
}

.loading-overlay {
	transition: visibility 0s ease-in-out 0.5s, opacity 0.5s ease-in-out;
	bottom: 0;
	left: 0;
	position: absolute;
	opacity: 0;
	right: 0;
	top: 0;
	visibility: hidden;
}

.loading-overlay.dark {
	background: #383838;
}

.loading-overlay.light {
	background: #FFF;
}

body > .loading-overlay {
	position: fixed;
	z-index: 999999;
}

/* ring Loading */
.ring-loader {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%,-50%);
	transform: translate(-50%,-50%);
	width: 150px;
	height: 150px;
	background: transparent;
	border: 3px solid #3c3c3c;
	border-radius: 50%;
	text-align: center;
	line-height: 150px;
	font-family: sans-serif;
	font-size: 20px;
	color: #34d399; /* Changed from #fff000 to Emerald 400 */
	letter-spacing:4px;
	text-transform: uppercase;
	text-shadow:0 0 10px #34d399; /* Changed from #fff000 to Emerald 400 */
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.39);
}

.ring-loader:before {
	content: '';
	position: absolute;
	top: -3px;
	left: -3px;
	width: 100%;
	height: 100%;
	border: 3px solid transparent;
	border-top: 3px solid #34d399; /* Changed from #fff000 to Emerald 400 */
	border-right: 3px solid #34d399; /* Changed from #fff000 to Emerald 400 */
	border-radius: 50%;
	-webkit-animation: animateCircle 2s linear infinite;
	animation: animateCircle 2s linear infinite;
}

.ring-loader span {
	display: block;
	position: absolute;
	top: calc(50% - 2px);
	left: 50%;
	width: 50%;
	height: 4px;
	background: transparent;
	transform-origin:left;
	-webkit-animation: animate 2s linear infinite;
	animation: animate 2s linear infinite;
}

.ring-loader span:before {
	content:'';
	position: absolute;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	background-color: #34d399; /* Changed from #fff000 to Emerald 400 */
	top: -6px;
	right: -8px;
	box-shadow: 0 0 20px #34d399; /* Changed from #fff000 to Emerald 400 */
}

@keyframes animateCircle
{
	0%
	{
		transform: rotate(0deg);
	}
	100%
	{
		transform: rotate(360deg);
	}
} 
@-webkit-keyframes animateCircle
{
	0%
	{
		-webkit-transform: rotate(0deg);
	}
	100%
	{
		-webkit-transform: rotate(360deg);
	}
}
@keyframes animate
{
	0%
	{
		transform: rotate(45deg);
	}
	100%
	{
		transform: rotate(405deg);
	}
}
@-webkit-keyframes animate
{
	0%
	{
		-webkit-transform: rotate(45deg);
	}
	100%
	{
		-webkit-transform: rotate(405deg);
	}
}

.img-rounded {
	border-radius: 6px;
}

.img-circle {
	border-radius: 50%;
}

.img-thumbnail {
	border-radius: 8px;
	position: relative;
}
.lang-img{
	max-height: 40px;
}

body {
	/* Button Basic */
	/* Border Buttons */
	/* Border Buttons - Sizes */
	/* 3D Buttons */
	/* Sizes */
	/* Buttons - Social */
	/* Buttons - States */;
}

body .btn:focus,
body .btn:active:focus,
body .btn.active:focus {
	outline: none;
}

body .btn {
	white-space: normal;
	transition: .3s;
}

body .btn-borders {
	border-width: 3px;
}

body .btn-borders.btn-primary {
	background: transparent;
	border-color: #34d399; /* Updated to Emerald 400 */
	color: #34d399; /* Updated to Emerald 400 */
	text-shadow: none;
}

body .btn-borders.btn-primary:hover, body .btn-borders.btn-primary:focus {
	background-color: #059669;
	border-color: #34d399 !important; /* Updated to Emerald 400 */
	color: #FFF;
}

body .btn-borders.btn-success {
	background: transparent;
	border-color: #34d399;
	color: #34d399;
	text-shadow: none;
}

body .btn-borders.btn-success:hover, body .btn-borders.btn-success:focus {
	background-color: #34d399; /* Updated to Emerald 400 */
	border-color: #34d399 !important;
	color: #FFF;
}

body .btn-borders.btn-warning {
	background: transparent;
	border-color: #ed9c28;
	color: #ed9c28;
	text-shadow: none;
}

body .btn-borders.btn-warning:hover, body .btn-borders.btn-warning:focus {
	background-color: #efa740;
	border-color: #ed9c28 !important;
	color: #FFF;
}

body .btn-borders.btn-danger {
	background: transparent;
	border-color: #d2322d;
	color: #d2322d;
	text-shadow: none;
}

body .btn-borders.btn-danger:hover, body .btn-borders.btn-danger:focus {
	background-color: #d74742;
	border-color: #d2322d !important;
	color: #FFF;
}

body .btn-borders.btn-info {
	background: transparent;
	border-color: #2dd4bf;
	color: #2dd4bf;
	text-shadow: none;
}

body .btn-borders.btn-info:hover, body .btn-borders.btn-info:focus {
	background-color: #14b8a6;
	border-color: #2dd4bf !important;
	color: #FFF;
}

body .btn-borders.btn-dark {
	background: transparent;
	border-color: #171717;
	color: #171717;
	text-shadow: none;
}

body .btn-borders.btn-dark:hover, body .btn-borders.btn-dark:focus {
	background-color: #242424;
	border-color: #171717 !important;
	color: #FFF;
}

body .btn-borders {
	padding: 4px 12px;
}

body .btn-borders.btn-lg, body .btn-group-lg > .btn-borders.btn {
	padding: 8px 16px;
}

body .btn-borders.btn-sm, body .btn-group-sm > .btn-borders.btn {
	border-width: 2px;
	padding: 4px 10px;
}

body .btn-borders.btn-xs, body .btn-group-xs > .btn-borders.btn {
	padding: 1px 5px;
	border-width: 1px;
}

body .btn-3d {
	border-bottom-width: 3px;
	padding: 5px 12px;
	border-radius: 6px;
}

body .btn-3d.btn-lg, body .btn-group-lg > .btn-3d.btn {
	padding: 9px 16px;
}

body .btn-3d.btn-sm, body .btn-group-sm > .btn-3d.btn {
	border-width: 2px;
	padding: 4px 10px;
}

body .btn-3d.btn-xs, body .btn-group-xs > .btn-3d.btn {
	padding: 1px 5px;
	border-width: 1px;
}

body .btn-xlg {
	border-radius: 6px;
	font-size: 16px;
	padding: 12px 33px;
}

body .btn-facebook, body .btn-facebook:active, body .btn-facebook:hover, body .btn-facebook:focus,
body .btn-twitter,
body .btn-twitter:active,
body .btn-twitter:hover,
body .btn-twitter:focus,
body .btn-gplus,
body .btn-gplus:active,
body .btn-gplus:hover,
body .btn-gplus:focus {
	color: #FFF;
	font-weight: 300;
	padding-left: 30px;
	padding-right: 30px;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.2);
}

body .btn-facebook, body .btn-facebook:focus {
	background: #3B5998;
	border: 1px solid #37538D;
}

body .btn-facebook:hover {
	background: #4162a7;
	border-color: #3d5c9c;
}

body .btn-facebook:active {
	background: #37538d;
	border-color: #334d82;
}

body .btn-twitter, body .btn-twitter:focus {
	background: #55ACEE;
	border: 1px solid #47A5ED;
}

body .btn-twitter:hover {
	background: #63b3ef;
	border-color: #55acee;
}

body .btn-twitter:active {
	background: #47a5ed;
	border-color: #399eec;
}

body .btn-gplus, body .btn-gplus:focus {
	background: #D95232;
	border: 1px solid #D44927;
}

body .btn-gplus:hover {
	background: #dc6143;
	border-color: #da5635;
}

body .btn-gplus:active {
	background: #d04727;
	border-color: #c34324;
}

body .btn-primary {
	border-color: #34d399; /* Updated to Emerald 400 */
	background-color: #34d399; /* Updated to Emerald 400 */
	border-color: #34d399 #34d399 #059669; /* Updated to Emerald 400 */
	color: #FFF;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

body .btn-primary:hover {
	border-color: #059669;
	background-color: #059669;
	color: #FFF;
}

body .btn-primary:active, body .btn-primary:focus {
	border-color: #047857;
	background-color: #047857;
	color: #FFF;
}

body .btn-primary.dropdown-toggle {
	border-left-color: #047857;
}

body .btn-primary[disabled] {
	border-color: white;
	background-color: white;
}

body .btn-success {
	border-color: #34d399;
	background-color: #34d399;
	border-color: #34d399 #34d399 #34d399; /* Updated to Emerald 400 */
	color: #FFF;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

body .btn-success:hover {
	border-color: #34d399; /* Updated to Emerald 400 */
	background-color: #34d399; /* Updated to Emerald 400 */
	color: #FFF;
}

body .btn-success:active, body .btn-success:focus {
	border-color: #059669;
	background-color: #059669;
	color: #FFF;
}

body .btn-success.dropdown-toggle {
	border-left-color: #059669;
}

body .btn-success[disabled] {
	border-color: #86cb86;
	background-color: #86cb86;
}

body .btn-warning {
	border-color: #ed9c28;
	background-color: #ed9c28;
	border-color: #ed9c28 #ed9c28 #d18211;
	color: #FFF;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

body .btn-warning:hover {
	border-color: #efa740;
	background-color: #efa740;
	color: #FFF;
}

body .btn-warning:active, body .btn-warning:focus {
	border-color: #e89113;
	background-color: #e89113;
	color: #FFF;
}

body .btn-warning.dropdown-toggle {
	border-left-color: #e89113;
}

body .btn-warning[disabled] {
	border-color: #f5c786;
	background-color: #f5c786;
}

body .btn-danger {
	border-color: #d2322d;
	background-color: #d2322d;
	border-color: #d2322d #d2322d #a82824;
	color: #FFF;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

body .btn-danger:hover {
	border-color: #d74742;
	background-color: #d74742;
	color: #FFF;
}

body .btn-danger:active, body .btn-danger:focus {
	border-color: #bd2d29;
	background-color: #bd2d29;
	color: #FFF;
}

body .btn-danger.dropdown-toggle {
	border-left-color: #bd2d29;
}

body .btn-danger[disabled] {
	border-color: #e48481;
	background-color: #e48481;
}

body .btn-info {
	border-color: #2dd4bf;
	background-color: #2dd4bf;
	border-color: #2dd4bf #2dd4bf #14b8a6;
	color: #FFF;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

body .btn-info:hover {
	border-color: #14b8a6;
	background-color: #14b8a6;
	color: #FFF;
}

body .btn-info:active, body .btn-info:focus {
	border-color: #0f766e;
	background-color: #0f766e;
	color: #FFF;
}

body .btn-info.dropdown-toggle {
	border-left-color: #0f766e;
}

body .btn-info[disabled] {
	border-color: #b0e1ef;
	background-color: #b0e1ef;
}

body .btn-dark {
	border-color: #171717;
	background-color: #171717;
	border-color: #171717 #171717 black;
	color: #FFF;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

body .btn-dark:hover {
	border-color: #242424;
	background-color: #242424;
	color: #FFF;
}

body .btn-dark:active, body .btn-dark:focus {
	border-color: #0a0a0a;
	background-color: #0a0a0a;
	color: #FFF;
}

body .btn-dark.dropdown-toggle {
	border-left-color: #0a0a0a;
}

body .btn-dark[disabled] {
	border-color: #4a4a4a;
	background-color: #4a4a4a;
}

html.dark .btn-default {
	background-color: #383838;
	border-color: #4c4e51;
	color: #EEE;
}

html.dark .btn-default:hover {
	background-color: #272727;
	border-color: #434548;
}

html.dark .btn-default:focus, html.dark .btn-default:active {
	background-color: #232323;
	border-color: #3f4245;
}


html.dark .btn-default:focus,
html.dark .btn-default:active,
html.dark .btn-default.active,
html.dark .open > .dropdown-toggle.btn-default {
	color: #EEE;
	background-color: #232323;
	border-color: #3f4245;
}

/* Notifications */
.header-menu {
	list-style: none;
	margin: 4px -10px 0 0;
	padding-left: 10px;
}

.header-menu > li {
	float: left;
	margin: -4px 17px 0 0;
	position: relative;
}

.header-menu > li .header-menu-icon {
	background: transparent;
	display: inline-block;
	height: 30px;
	width: 30px;
	position: relative;
	text-align: center;
	z-index: 1;
}

.header-menu .header-menu-icon:before {
	display: block;
	position: absolute;
	height: 30px;
	width: 30px;
	content: '';
	box-shadow: 2px 2px 2px 0 rgba(0, 0, 0, 0.07);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	background-color: #fff;
	border: 1px solid #ddd;
	z-index: -1;
	pointer-events: none;
	-webkit-transition: all .4s ease-in-out;
	transition: all .4s ease-in-out;
}

html.dark .header-menu .header-menu-icon:before {
	box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.18);
}

.header-menu .open .header-menu-icon:before {
	background-color: #ddd; 
}

.header-menu > li .header-menu-icon i {
	color: #34d399; /* Updated to Blue 500 for top navigation icons */
	line-height: 30px;
	vertical-align: middle;
	-webkit-transition: all 0.15s ease-in-out;
	-moz-transition: all 0.15s ease-in-out;
	transition: all 0.15s ease-in-out;
}

.header-left .header-menu > li .header-menu-icon {
	cursor: pointer;
}

html.dark .header-menu > li .header-menu-icon i {
	color: #3b82f6; /* Updated to Blue 500 for top navigation icons in dark mode */
}

html.dark .header-menu > li .header-menu-icon:hover i {
	color: #3b82f6; /* Updated to Blue 500 for top navigation icon hover states */
}

.header-menu > li .header-menu-icon .badge {
	background: #D2312D;
	color: #FFF;
	font-size: 10px;
	font-weight: normal;
	height: 16px;
	padding: 3px 5px 3px 5px;
	position: absolute;
	right: -8px;
	top: -3px;
	border-radius: 100%;
}

.header-menu > li > a {
	border: none;
	display: inline-block;
}

.header-menu .header-menubox {
	background: #fff;
	display: block;
	visibility: hidden;
	opacity: 0;
	border: 1px solid #D1D1D1;
	box-shadow: 1px 3px 6px rgba(0, 0, 0, 0.15);
	left: auto;
	margin: 30px 0 0 0;
	padding: 0;
	right: -5px;
	width: 245px;
	-webkit-transition: all .3s ease;
	transition: all .3s ease;
}

.header-left .header-menu .header-menubox {
	width: auto !important;
}

.header-right .header-menu .header-menubox {
	width: min-width: 180px;
}

.header-menubox .short-q{
	padding: 6px;
}

html.dark .header-menu .header-menubox {
	box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.2);
	border-color: #4c4e51;
	background-color: #383838;
}

.header-menu .open .header-menubox {
	visibility: visible;
	opacity: 1;
	margin: 14px 0 0 0;
}

.header-menu .header-menubox .notification-title {
	border-radius: 3px 3px 0 0;
	color: #6a6a6a;
	font-size: 1.3rem;
	line-height: 1.5rem;
	padding: 12px 6px 12px 12px;
	border-bottom: 1px solid #ddd;
}

.header-menu .header-menubox .notification-footer {
	color: #6a6a6a;
	font-size: 1.3rem;
	line-height: 1.5rem;
	padding: 12px 6px 12px 12px;
	border-top: 1px solid #ddd;
}

.header-menu .header-menubox .notification-title .label {
	font-size: 1rem;
	font-weight: 200;
	line-height: 14px;
	margin-left: 10px;
	margin-top: -2px;
	min-width: 35px;
}

.header-menu .header-menubox .notification-title .label-default {
	background: #006697;
	color: #FFF;
}

.header-menu .header-menubox .content {
	padding: 12px;
}

.header-menu .header-menubox hr {
	background: #E6E6E6;
	height: 1px;
	margin: 12px 0;
}

.header-menu .header-menubox .view-more {
	color: #ACACAC;
	font-size: 1.2rem;
	line-height: 1.2rem;
	padding-right: 12px;
}

/* notification menu hover */
.header-menu .hbox {
	padding-right: 3px;
}

.header-menu .hbox .visible-slider {
	height: 240px !important;
}

.header-menu .hbox ul li {
	margin-bottom: 5px;
}

.header-menu .hbox ul li img {
	max-height: 12px;
	max-width: 16px;
}

.header-menu .hbox ul li a {
	border-radius: 2px;
	color: #7d7d7d;
	display: block;
	font-size: 1.2rem;
	line-height: 1.5rem;
	padding: 5px 10px;
}

.header-menu .open .hbox ul li a:hover {
	background: #ffbd2e;
	color: #FFF;
}

html.dark .header-menu .open .hbox ul li a:hover {
	background: #ff685c;
}

/* notification menu - emails */
.header-menubox {
	color: #ACACAC;
}

.header-menubox ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

.header-menubox li {
	margin: 0 0 12px;
}

.header-menubox li:last-child {
	margin-bottom: 0;
}

.header-menubox a {
	display: block;
	text-decoration: none;
}

.header-menubox .image {
	float: left;
	margin: 0 10px 0 0;
}

.header-menubox .image i {
	border-radius: 35px;
	height: 35px;
	line-height: 35px;
	text-align: center;
	width: 35px;
}

.header-menubox .truncate {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.header-menubox .title {
	color: #000011;
	display: block;
	font-size: 1.3rem;
	line-height: 1.7rem;
	padding: 0;
}

.header-menubox .message {
	color: #ACACAC;
	display: block;
	font-size: 1.1rem;
	line-height: 1.5rem;
	padding: 0;
}

html.dark .header-menu > li .header-menu-icon:before,
html.header-dark .header-menu > li .header-menu-icon:before {
	background: #313131;
	border-color: #424447;
}

html.dark .header-menu .open .header-menu-icon:before,
html.header-dark .header-menu .open .header-menu-icon:before {
	background-color: #363c3e; 
}

html.dark .header-menu .header-menubox .notification-title,
html.dark .header-menu .header-menubox .notification-footer {
	border-color: #424447;
}


/* Notifications Mobile */
@media only screen and (max-width: 767px) {
	.header-menu {
		float: right;
		margin: 18px 8px 0 0;
	}

	.header-menu > li {
		position: static;
	}

	.header-menu > li.open .header-menu-icon:before {
		display: block;
	}

	.header-menu > li .header-menubox {
		left: 15px;
		right: 15px;
		top: auto;
		width: auto !important;
	}

	.header-menu .open .header-menubox {
		margin: 19px 0 0 0;
	}
}
/* Userbox */
.userbox {
	margin-right: 17px;
	position: relative;
}

.userbox > a {
	display: inline-block;
	text-decoration: none;
	position: relative;
}

.userbox a:hover {
	text-decoration: none;
}

.userbox .profile-info,
.userbox .profile-picture {
	display: inline-block;
	vertical-align: middle;
}

.userbox .profile-picture img {
	width: 35px;
	border-radius: 4px;
	color: transparent;
}

.userbox .profile-info {
	margin: 0 25px 0 10px;
}

.userbox .name,
.userbox .role {
	display: block;
}

.userbox .name {
	color: #000011;
	font-size: 1.3rem;
	line-height: 1.2em;
}

.userbox .role {
	color: #ACACAC;
	font-size: 1.1rem;
	line-height: 1.2em;
}

@media only screen and (max-width: 767px) {
	.userbox .name,
	.userbox .role {
		max-width: 68px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}

/* Userbox - Open */
.userbox.open > a {
	position: relative;
	z-index: 1;
}

.userbox .dropdown-menu {
	margin: 30px 0 0 0;
	display: block;
	width: 270px;
	border: 1px solid #D1D1D1;
	right: -5px;
	left: auto;
	visibility: hidden;
	opacity: 0;
	box-shadow: 1px 3px 6px rgba(0, 0, 0, 0.15);
	-webkit-transition: all .3s ease;
	transition: all .3s ease;
}

html.dark .userbox .dropdown-menu {
	border-color: #4c4e51;
}

.dw-user-box {

    padding: 10px 15px;
}

.userbox.open .dropdown-menu{
	visibility: visible;
	opacity: 1;
	margin: 12px 0 0 0;
}

.u-img {
    width: 80px;
    display: inline-block;
    vertical-align: top;
}

.u-img img {
    width: 100%;
    border-radius: 5px;
}
.u-text {
    display: inline-block;
    padding-left: 10px;
    max-width: 153px;
}

.u-text h4 {
    margin: 0px;
}

.u-text p {
    margin-bottom: 5px;
    font-size: 14px;
}

.userbox ul.dropdown-user li.divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: rgba(120, 130, 140, 0.13);
}

.userbox .dropdown-menu li {
	margin-bottom: 5px;
}

.userbox .dropdown-menu ul > li:not(.user-p-box) a {
	padding: 9px 15px;
	color: #6b6b6b;
	display: block;
	font-size: 1.2rem;
	line-height: 1.5rem;
	text-decoration: none;
}

html.dark .userbox .dropdown-menu ul > li:not(.user-p-box) a {
	color: #cbd5e1;
}

.userbox.open .dropdown-menu ul > li:not(.user-p-box) a:hover {
	background: #CCC;
	color: #FFF;
}

html.dark .userbox.open .dropdown-menu ul > li:not(.user-p-box) a:hover {
	background: #ff685c;
}

.userbox .dropdown-menu ul > li:not(.user-p-box) a i {
	font-size: 1.7rem;
	margin-right: 3px;
	vertical-align: text-bottom;
}

/* Userbox - Mobile */
@media only screen and (max-width: 767px) {
	.userbox {
		float: left;
		margin: 16px 0 0 12px;
		position: static;
	}

	.userbox > a:after {
		background: #E9E9E6;
		content: '';
		height: 63px;
		margin: 0;
		position: absolute;
		right: -21px;
		top: -18px;
		width: 1px;
	}

	.userbox .dropdown-menu {
		left: 15px;
		right: 15px;
		top: auto;
		width: auto !important;
	}
}
/* Header Dark - Userbox */
html.dark .userbox a:after,
html.header-dark .userbox a:after {
	background: #404142;
}

html.dark .userbox .name,
html.dark .userbox .custom-caret,
html.header-dark .userbox .name,
html.header-dark .userbox .custom-caret {
	color: #C3C3C3;
}

html.dark .userbox.open .dropdown-menu .name,
html.dark .userbox.open .dropdown-menu .custom-caret,
html.header-dark .userbox.open .dropdown-menu .name,
html.header-dark .userbox.open .dropdown-menu .custom-caret {
	color: #C3C3C3;
}

html.dark .userbox.open .dropdown-menu a,
html.header-dark .userbox.open .dropdown-menu a {
	color: #e2e8f0;
}

html.dark .userbox.open .dropdown-menu a:hover,
html.header-dark .userbox.open .dropdown-menu a:hover {
	color: #FFF;
}

html.dark .userbox .dropdown-menu,
html.header-dark .userbox .dropdown-menu {
	background: #383838;
}

html.dark .userbox .dropdown-menu .divider,
html.header-dark .userbox .dropdown-menu .divider {
	background: #4c4e51;
}

html.dark .userbox .dropdown-menu a,
html.header-dark .userbox .dropdown-menu a {
	color: #e2e8f0;
}

.nav-pills > .active a, .nav-pills > .active a:hover, .nav-pills > .active a:focus {
	background-color: #CCC;
}

.pagination > li a {
	color: #CCC;
}

.pagination > li a:hover, .pagination > li a:focus {
	color: #d9d9d9;
}

.pagination > li.active a, .pagination > li.active a:hover, .pagination > li.active a:focus,
.pagination > li.active span,
.pagination > li.active span:hover,
.pagination > li.active span:focus {
	background-color: #CCC;
	border-color: #CCC;
}

.pagination > li.active a {
	background-color: #CCC;
}

.progress-bar-primary {
	background-color: #CCC;
}

.progress-bar.progress-without-number[aria-valuenow="1"],
.progress-bar.progress-without-number[aria-valuenow="2"] {
	min-width: 0;
}

.progress-bar.progress-bar-primary[aria-valuenow="0"] {
	background: transparent;
}

/* Sidebar Light */
html.sidebar-light:not(.dark) .sidebar-widget .widget-header .btn-widget-act {
	border-color: #f2f2f2;
	background-color: #f2f2f2;
	border-color: #e6e6e6;
	color: #777;
	text-shadow: none;
}

html.sidebar-light:not(.dark) .sidebar-widget .widget-header .btn-widget-act:hover {
	border: 1px solid #d2d2d2 !important;
	background-color: #fafafa;
}

html.sidebar-light:not(.dark) .sidebar-widget .widget-header .btn-widget-act:active, html.sidebar-light:not(.dark) .sidebar-widget .widget-header .btn-widget-act:focus {
	border: 1px solid #d2d2d2 !important;
	background-color: #e6e6e6;
}

html.sidebar-light:not(.dark) .sidebar-widget .widget-header .btn-widget-act.dropdown-toggle {
	border-left-color: #e6e6e6;
}

html.sidebar-light:not(.dark) .sidebar-widget.widget-tasks ul li a {
	color: #777;
}

html.sidebar-light:not(.dark) .sidebar-widget.widget-tasks ul li a:hover {
	background: #fafafa;
}

html.sidebar-light:not(.dark) .sidebar-widget.widget-stats .stats-title {
	color: #777;
}

html.sidebar-light:not(.dark) .sidebar-widget.widget-stats .progress {
	background: #d8d8d8;
	box-shadow: 0 1px 0 #bfbfbf inset;
}

html.sidebar-light:not(.dark) .sidebar-widget.widget-calendar ul {
	border-top: 1px solid #DDD;
}

html.sidebar-light:not(.dark) .sidebar-widget.widget-calendar ul time {
	color: #777;
}

html.sidebar-light:not(.dark) .sidebar-widget.widget-calendar ul span {
	color: #777;
}

html.sidebar-light:not(.dark) .sidebar-widget.widget-friends ul li span.name {
	color: #777;
}

/* Scroll to Top */
html .scroll-to-top {
	-webkit-transition: all 0.3s;
	-moz-transition: all 0.3s;
	transition: all 0.3s;
	background: #282d36;
	border-radius: 7px 7px 0 0;
	bottom: 0px;
	color: #FFF;
	display: block;
	height: 9px;
	opacity: 0;
	padding: 5px 10px 25px;
	position: fixed;
	right: 10px;
	text-align: center;
	text-decoration: none;
	min-width: 39px;
	z-index: 1040;
}

html .scroll-to-top:hover {
	opacity: 1;
}

html .scroll-to-top.visible {
	opacity: 0.75;
}

html .scroll-to-top span {
	display: inline-block;
	padding: 0 5px;
}

html.ie11 .scroll-to-top {
	right: 25px;
}

/* Responsive */
@media (max-width: 991px) {
	html .scroll-to-top.hidden-mobile {
		display: none !important;
	}
}

.panel {
	background-color: #fdfdfd;
	-webkit-box-shadow: none;
	box-shadow: none;
	border-color: #D1D1D1;
	border-radius: 5px;
}

.panel-heading {
	background: #f6f6f6;
	border-radius: 5px 5px 0 0;
	position: relative;
    border-bottom: 2px solid #ffbd2e;
}

.panel-actions {
	right: 15px;
	position: absolute;
	top: 15px;
}

.panel-actions a,
.panel-actions .panel-action {
	background-color: transparent;
	border-radius: 2px;
	color: #B4B4B4;
	font-size: 14px;
	height: 24px;
	line-height: 24px;
	text-align: center;
	width: 24px;
}

.panel-actions a:hover,
.panel-actions .panel-action:hover {
	background-color: #eeeeee;
	color: #B4B4B4;
	text-decoration: none;
}

.panel-actions a, .panel-actions a:focus, .panel-actions a:hover, .panel-actions a:active, .panel-actions a:visited,
.panel-actions .panel-action,
.panel-actions .panel-action:focus,
.panel-actions .panel-action:hover,
.panel-actions .panel-action:active,
.panel-actions .panel-action:visited {
	outline: none !important;
	text-decoration: none !important;
}

.panel-title {
	color: #33353F;
	font-weight: 400;
	line-height: 20px;
	padding: 0;
	text-transform: none;
	font-size: 17px;
}

.panel-body {
	background: #fdfdfd;
	-webkit-box-shadow: none;
	border-radius: 5px;
}

.panel-body-nopadding {
	padding: 0;
}

.panel-heading + .panel-body {
	border-radius: 0 0 5px 5px;
}

.panel-footer {
	border-radius: 0 0 5px 5px;
	margin-top: -5px;
	border-top-color: #ddd;
}

.panel-footer-btn-group {
	display: table;
	width: 100%;
	padding: 0;
}

.panel-footer-btn-group a {
	background-color: #f5f5f5;
	display: table-cell;
	width: 1%;
	border-left: 1px solid #ddd;
	padding: 10px 15px;
	text-decoration: none;
}

.panel-footer-btn-group a:hover {
	background-color: #f0f0f0;
	box-shadow: 0 0 7px rgba(0, 0, 0, 0.1) inset;
}

.panel-footer-btn-group a:first-child {
	border-left: none;
}

.panel-highlight .panel-heading {
	background-color: #CCC;
	border-color: #CCC;
	color: #fff;
}

.panel-highlight .panel-title {
	color: #fff;
}

.panel-highlight .panel-subtitle {
	color: #fff;
	color: rgba(255, 255, 255, 0.7);
}

.panel-highlight .panel-actions a {
	background-color: rgba(0, 0, 0, 0.1);
	color: #fff;
}

.panel-highlight .panel-body {
	background-color: #CCC;
	color: #fff;
}

.panel-highlight-title .panel-heading {
	background-color: #2BAAB1;
}

.panel-highlight-title .panel-title {
	color: #fff;
}

.panel-highlight-title .panel-subtitle {
	color: #fff;
	color: rgba(255, 255, 255, 0.7);
}

.panel-highlight-title .panel-actions a {
	background-color: rgba(0, 0, 0, 0.1);
	color: #fff;
}

.panel-heading-icon {
	margin: 0 auto;
	font-size: 4.2rem;
	width: 90px;
	height: 90px;
	line-height: 90px;
	text-align: center;
	color: #fff;
	background-color: rgba(0, 0, 0, 0.1);
	-webkit-border-radius: 55px;
	border-radius: 55px;
}

.panel-heading-icon.bg-primary {
	background: #CCC;
	color: #FFF;
}

.panel-heading-icon.bg-secondary {
	background: #E36159;
	color: #FFF;
}

.panel-heading-icon.bg-tertiary {
	background: #2BAAB1;
	color: #FFF;
}

.panel-heading-icon.bg-quaternary {
	background: #734BA9;
	color: #FFF;
}

.panel-heading-icon.bg-success {
	background: #47a447;
	color: #FFF;
}

.panel-heading-icon.bg-warning {
	background: #ed9c28;
	color: #FFF;
}

.panel-heading-icon.bg-danger {
	background: #d2322d;
	color: #FFF;
}

.panel-heading-icon.bg-info {
	background: #5bc0de;
	color: #FFF;
}

.panel-heading-icon.bg-dark {
	background: #171717;
	color: #FFF;
}

.panel-heading-profile-picture img {
	display: block;
	margin: 0 auto;
	width: 100px;
	height: 100px;
	border: 4px solid #34d399; /* Updated to Emerald 400 border */
	-webkit-border-radius: 50px;
	border-radius: 50px;
}

.panel-icon {
	color: #fff;
	font-size: 42px;
	float: left;
}

.panel-icon ~ .panel-title, .panel-icon ~ .panel-subtitle {
	margin-left: 64px;
}

/* Dark - Panels */
html.dark .panel {
	background-color: #1e293b;
	border-color: #475569;
}

html.dark .panel-heading {
	background: #0f172a;
	border-bottom-color: #34d399; /* Updated to Emerald 400 */
}

html.dark .panel-actions a:hover {
	background-color: #363636;
}

html.dark .panel-body {
	background: #1e293b;
}

html.dark .panel-footer {
	background: #0f172a;
	border-top-color: #475569;
}

html .panel-primary .panel-heading {
	background: #CCC;
}

html .panel-primary .panel-subtitle {
	opacity: 0.8;
	color: #FFF;
}

html .panel-primary .panel-title {
	color: #FFF;
}

html .panel-primary .panel-actions a {
	background-color: transparent !important;
	color: #FFF;
}

html .panel-secondary .panel-heading {
	background: #E36159;
}

html .panel-secondary .panel-subtitle {
	opacity: 0.8;
	color: #FFF;
}

html .panel-secondary .panel-title {
	color: #FFF;
}

html .panel-secondary .panel-actions a {
	background-color: transparent !important;
	color: #FFF;
}

html .panel-tertiary .panel-heading {
	background: #2BAAB1;
}

html .panel-tertiary .panel-subtitle {
	opacity: 0.8;
	color: #FFF;
}

html .panel-tertiary .panel-title {
	color: #FFF;
}

html .panel-tertiary .panel-actions a {
	background-color: transparent !important;
	color: #FFF;
}

html .panel-quaternary .panel-heading {
	background: #734BA9;
}

html .panel-quaternary .panel-subtitle {
	opacity: 0.8;
	color: #FFF;
}

html .panel-quaternary .panel-title {
	color: #FFF;
}

html .panel-quaternary .panel-actions a {
	background-color: transparent !important;
	color: #FFF;
}

html .panel-success .panel-heading {
	background: #47a447;
}

html .panel-success .panel-subtitle {
	opacity: 0.8;
	color: #FFF;
}

html .panel-success .panel-title {
	color: #FFF;
}

html .panel-success .panel-actions a {
	background-color: transparent !important;
	color: #FFF;
}

html .panel-warning .panel-heading {
	background: #ed9c28;
}

html .panel-warning .panel-subtitle {
	opacity: 0.8;
	color: #FFF;
}

html .panel-warning .panel-title {
	color: #FFF;
}

html .panel-warning .panel-actions a {
	background-color: transparent !important;
	color: #FFF;
}

html .panel-danger .panel-heading {
	background: #d2322d;
}

html .panel-danger .panel-subtitle {
	opacity: 0.8;
	color: #FFF;
}

html .panel-danger .panel-title {
	color: #FFF;
}

html .panel-danger .panel-actions a {
	background-color: transparent !important;
	color: #FFF;
}

html .panel-info .panel-heading {
	background: #5bc0de;
}

html .panel-info .panel-subtitle {
	opacity: 0.8;
	color: #FFF;
}

html .panel-info .panel-title {
	color: #FFF;
}

html .panel-info .panel-actions a {
	background-color: transparent !important;
	color: #FFF;
}

html .panel-dark .panel-heading {
	background: #171717;
}

html .panel-dark .panel-subtitle {
	opacity: 0.8;
	color: #FFF;
}

html .panel-dark .panel-title {
	color: #FFF;
}

html .panel-dark .panel-actions a {
	background-color: transparent !important;
	color: #FFF;
}

html .panel-transparent > .panel-heading {
	background: none;
	border: 0;
	padding-left: 0;
	padding-right: 0;
}

html .panel-transparent > .panel-heading .panel-actions {
	right: 0;
}

html .panel-transparent > .panel-heading + .panel-body {
	border-radius: 5px;
}

html .panel-transparent > .panel-body {
	padding: 0;
	border-radius: 0;
	background: transparent;
	-webkit-box-shadow: none;
	box-shadow: none;
}

html .panel .panel-heading-transparent {
	background: none;
	border: 0;
	padding-left: 0;
	padding-right: 0;
}

html .panel .panel-heading-transparent .panel-actions {
	right: 0;
}

html .panel .panel-heading-transparent + .panel-body {
	border-radius: 5px;
}

.panel-horizontal {
	display: table;
	width: 100%;
}

.panel-horizontal .panel-heading,
.panel-horizontal .panel-body,
.panel-horizontal .panel-footer {
	display: table-cell;
	vertical-align: middle;
}

.panel-horizontal .panel-heading {
	border-radius: 5px 0 0 5px;
}

.panel-horizontal .panel-heading + .panel-body {
	border-radius: 0 5px 5px 0;
}

.panel-horizontal .panel-footer {
	border-radius: 0 5px 5px 0;
	margin-top: 0;
}

.panel-action-toggle,
.panel-action-dismiss {
	display: inline-block;
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
	font: normal normal normal 14px;
	font-size: inherit;
	text-rendering: auto;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.panel-action-toggle:before {
	content: "\f0d7";
}

.panel-collapsed .panel-action-toggle:before {
	content: "\f0d8";
}

.panel-action-dismiss:before {
	content: "\f00d";
}

.panel-collapsed .panel-body,
.panel-collapsed .panel-footer {
	display: none;
}

@media only screen and (max-width: 767px) {
	.panel-actions {
		float: none;
		margin-bottom: 15px;
		position: static;
		text-align: right;
	}

	.panel-actions a {
		vertical-align: top;
	}
}

/* tabs */
.tabs {
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
	border-radius: 4px;
	margin-bottom: 35px;
}

/* navigation */
.nav-tabs {
	margin: 0;
	font-size: 0;
}

.nav-tabs li {
	display: inline-block;
	float: none;
}

.nav-tabs li:last-child a {
	margin-right: 0;
}

.nav-tabs li a {
	border-radius: 5px 5px 0 0;
	font-size: 1.3rem;
	margin-right: 1px;
}

.nav-tabs li a .badge {
	border-radius: 100%;
}

.nav-tabs li a, .nav-tabs li a:hover {
	background: #F4F4F4;
	border-bottom: none;
	border-left: 1px solid #EEE;
	border-right: 1px solid #EEE;
	border-top: 3px solid #DDD;
	color: #555;
}

.nav-tabs li a:hover {
	border-bottom-color: transparent;
	border-top: 3px solid #555;
	box-shadow: none;
}

.nav-tabs li a:active, .nav-tabs li a:focus {
	border-bottom: 0;
}

.nav-tabs li.active a,
.nav-tabs li.active a:hover,
.nav-tabs li.active a:focus {
	background: #FFF;
	border-left-color: #EEE;
	border-right-color: #EEE;
	border-top: 3px solid #555;
	color: #555;
}

/* content */
.tab-content {
	border-radius: 0 0 4px 4px;
	box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.04);
	background-color: #FFF;
	border: 1px solid #EEE;
	border-top: 0;
	padding: 15px;
}

/* content - footer inside */
.tab-content .panel-footer {
	margin: -15px;
	margin-top: 15px;
	border: 0;
	border-top: 1px solid #D1D1D1;
}

html.dark .nav-tabs li.active a,
html.dark .nav-tabs li.active a:hover,
html.dark .nav-tabs li.active a:focus,
html.dark .nav-tabs li a {
	color: #34d399; /* Updated to Emerald 400 */
}

html.dark .tab-content {
	background: #323232;
	border-color: #1d2127;
}

/* bootstrap tab custom */
.tabs-custom .nav-tabs {
	margin: 15px;
	position: relative;
}

.tabs-custom .nav-tabs > li {
	margin-bottom: 0;
}

.tabs-custom .nav-tabs > li > a {
	border: 0;
	color: #666;
	padding: 15px;
}

.tabs-custom .nav-tabs > li.active > a,
.nav-tabs > li > a:hover {
	border: 0;
	background: transparent;
}

.tabs-custom .nav-tabs > li > a:after {
	content: '';
	background: #34d399; /* Updated to Emerald 400 */
	height: 2px;
	position: absolute;
	width: 100%;
	left: 0;
	bottom: -1px;
	-webkit-transition: all 250ms ease 0s;
	transition: all 250ms ease 0s;
	-webkit-transform: scale(0);
	transform: scale(0);
}

html.dark .tabs-custom .nav-tabs > li > a:after {
	background-color: #34d399; /* Updated to Emerald 400 */
}

.tabs-custom .nav-tabs > li.active > a:before {
	content: '';
	height: 4px;
	width: 8px;
	display: block;
	position: absolute;
	bottom: -5px;
	left: 50%;
	border-radius: 0 0 8px 8px;
	-webkit-transform: translateX(-50%);
	transform: translateX(-50%);
	background: #34d399; /* Updated to Emerald 400 */
}

html.dark .tabs-custom .nav-tabs > li.active > a:before {
	background-color: #34d399; /* Updated to Emerald 400 */
}

.tabs-custom .nav-tabs > li.active > a:after,
.tabs-custom .nav-tabs > li:hover > a:after {
	-webkit-transform: scale(1);
	transform: scale(1);
}

.tabs-custom .tab-content {
	background: transparent;
	border: 0;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.tabs-custom .nav-tabs li a,
.tabs-custom .nav-tabs li a:hover {
	background: transparent;
}

html.dark .tabs-custom .nav-tabs {
	border-bottom-color: #535151;
}

/* Alert new states */
.alert-default {
	background-color: #ebebeb;
	border-color: #e3e3e3;
	color: #6c6c6c;
}

.alert-default .alert-link {
	color: #4c4e51;
}

.alert-primary {
	background-color: #CCC;
	border-color: #c4c4c4;
	color: #FFF;
}

.alert-primary .alert-link {
	color: #999999;
}

.alert-dark {
	background-color: #313131;
	border-color: black;
	color: #cacaca;
}

.alert-dark .alert-link {
	color: #f0f0f0;
}

/* Progress bar overwrite style */
.progress-bar {
	background: #b5b5b5;
}

html.dark .progress-bar {
	background: #4a4a4a;
}

.progress .progress-bar {
	box-shadow: none;
	border-radius: 4px;
}

/* Progress bar default style */


.progress {
	background: #f6f7f8;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
}

html.dark .progress {
	background: #3c3c3e;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4) inset;
}

/* Progress bar light style */
.progress.light {
	background: #f6f7f8;
	background: -webkit-linear-gradient(#F6F7F8, #F6F7F8 10%, #f5f5f5 11%);
	background: linear-gradient(#F6F7F8, #F6F7F8 10%, #f5f5f5 11%);
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
}

/* Progress bar roundness generic */
.progress-squared, .progress-squared .progress-bar {
	border-radius: 0 !important;
}

/* Progress bar sizes */
.progress-xs {
	height: 7px;
}

.progress-xs, .progress-xs .progress-bar {
	border-radius: 7px;
}

.progress-xs .progress-bar {
	direction: ltr !important;
	text-indent: -9999px;
}

.progress-xs.progress-half-rounded, .progress-xs.progress-half-rounded .progress-bar {
	border-radius: 2px;
}

.progress-xs.progress-striped .progress-bar {
	background-size: 15px 15px;
}

.progress-sm {
	border-radius: 12px;
	height: 12px;
}

.progress-sm, .progress-sm .progress-bar {
	border-radius: 12px;
}

.progress-sm .progress-bar {
	font-size: 10px;
	line-height: 12px;
}

.progress-sm.progress-half-rounded, .progress-sm.progress-half-rounded .progress-bar {
	border-radius: 4px;
}

.progress-sm.progress-striped .progress-bar {
	background-size: 20px 20px;
}

.progress-md {
	border-radius: 14px;
	height: 14px;
}

.progress-md, .progress-md .progress-bar {
	border-radius: 14px;
}

.progress-md .progress-bar {
	font-size: 11px;
	line-height: 14px;
}

.progress-md.progress-half-rounded, .progress-md.progress-half-rounded .progress-bar {
	border-radius: 4px;
}

.progress-md.progress-striped .progress-bar {
	background-size: 25px 25px;
}

.progress-lg {
	border-radius: 16px;
	height: 16px;
}

.progress-lg, .progress-lg .progress-bar {
	border-radius: 16px;
}

.progress-lg .progress-bar {
	line-height: 16px;
}

.progress-lg.progress-half-rounded, .progress-lg.progress-half-rounded .progress-bar {
	border-radius: 5px;
}

.progress-lg.progress-striped .progress-bar {
	background-size: 30px 30px;
}

.progress-xl {
	border-radius: 18px;
	height: 18px;
}

.progress-xl, .progress-xl .progress-bar {
	border-radius: 20px;
}

.progress-xl .progress-bar {
	line-height: 20px;
}

.progress-xl.progress-half-rounded, .progress-xl.progress-half-rounded .progress-bar {
	border-radius: 6px;
}

.progress-xl.progress-striped .progress-bar {
	background-size: 35px 35px;
}

/* Progress bar states */
.progress .progress-bar-primary {
	background-color: #CCC;
}

.progress .progress-bar-success {
	background-color: #34d399;
}

.progress .progress-bar-warning {
	background-color: #60a5fa;
}

.progress .progress-bar-danger {
	background-color: #d2322d;
}

.progress .progress-bar-info {
	background-color: #2dd4bf;
}

.progress .progress-bar-dark {
	background-color: #0f172a;
}

.panel-group .panel-accordion {
	border-radius: 0;
}

.panel-group .panel-accordion .panel-heading {
	border: 0;
}

.panel-group .panel-accordion .panel .panel-heading {
	border-bottom: 2px solid #ffbd2e;
	margin-bottom: 0;
	padding: 9px;
}

html.dark .panel-group .panel-accordion .panel-heading {
	border-color: #ff685c;
}

.panel-group .panel-accordion .panel-heading a {
	color: #CCC;
	display: block;
	padding: 12px;
	font-size: 16px;
	border-radius: 5px;
}

.panel-group .panel-accordion .panel-heading a:hover, .panel-group .panel-accordion .panel-heading a:focus {
	text-decoration: none;
}

.panel-group .panel-accordion .panel-heading a .fa {
	margin-right: 4px;
}

.panel-group .panel-accordion.panel-accordion-first {
	border-radius: 0 0 5px 5px;
}

.panel-group .panel-accordion.panel-accordion-first .panel-heading {
	border-radius: 0 0 5px 5px;
}

.panel-group .panel-accordion-primary .panel-heading .panel-title a {
	background: #CCC;
	color: #FFF;
}

.panel-group .panel-accordion-success .panel-heading .panel-title a {
	background: #47a447;
	color: #FFF;
}

.panel-group .panel-accordion-warning .panel-heading .panel-title a {
	background: #ed9c28;
	color: #FFF;
}

.panel-group .panel-accordion-danger .panel-heading .panel-title a {
	background: #d2322d;
	color: #FFF;
}

.panel-group .panel-accordion-info .panel-heading .panel-title a {
	background: #5bc0de;
	color: #FFF;
}

.panel-group .panel-accordion-dark .panel-heading .panel-title a {
	background: #171717;
	color: #FFF;
}

html.dark .panel-group .panel-accordion .panel {
	background-color: #363636;
}

html.dark .panel-group .panel-accordion .panel-default {
	border-color: #363636;
}

.toggle {
	margin: 10px 0 0;
	position: relative;
	clear: both;
}

.toggle > input {
	cursor: pointer;
	filter: alpha(opacity=0);
	height: 45px;
	margin: 0;
	opacity: 0;
	position: absolute;
	width: 100%;
	z-index: 2;
}

.toggle > label {
	-webkit-transition: all 0.15s ease-out;
	-moz-transition: all 0.15s ease-out;
	transition: all 0.15s ease-out;
	background: #F4F4F4;
	border-left: 3px solid #CCC;
	border-radius: 5px;
	color: #CCC;
	display: block;
	font-size: 1.1em;
	min-height: 20px;
	padding: 12px 20px 12px 10px;
	position: relative;
	cursor: pointer;
	font-weight: 400;
}

.toggle > label:-moz-selection {
	background: none;
}

.toggle > label i.fa-minus {
	display: none;
}

.toggle > label i.fa-plus {
	display: inline;
}

.toggle > label:selection {
	background: none;
}

.toggle > label:before {
	border: 6px solid transparent;
	border-left-color: inherit;
	content: '';
	margin-top: -6px;
	position: absolute;
	right: 4px;
	top: 50%;
}

.toggle > label:hover {
	background: #f5f5f5;
}

.toggle > label + p {
	display: block;
	overflow: hidden;
	padding-left: 30px;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 25px;
}

.toggle > label i {
	font-size: 0.7em;
	margin-right: 8px;
	position: relative;
	top: -1px;
}

.toggle > .toggle-content {
	display: none;
}

.toggle > .toggle-content > p {
	margin-bottom: 0;
	padding: 10px 0;
}

.toggle.active i.fa-minus {
	display: inline;
	color: #FFF;
}

.toggle.active i.fa-plus {
	display: none;
}

.toggle.active > label {
	background: #CCC;
	border-color: #CCC;
	color: #FFF;
}

.toggle.active > label:before {
	border: 6px solid transparent;
	border-top-color: #FFF;
	margin-top: -3px;
	right: 10px;
}

.toggle.active > p {
	white-space: normal;
}

.toggle > p.preview-active {
	height: auto;
	white-space: normal;
}

/* dark */
html.dark .toggle > label {
	background: #363636;
}

.label-default {
	background: #ebebeb;
	color: #777;
}

.label-sm {
	font-size: 50%;
}

.label-primary {
	background: #CCC;
	color: #FFF;
}

html.dark .label-primary {
	background: #34d399; /* Updated to Emerald 400 */
	color: #FFF;
}

.label-success {
	background: #34d399;
	color: #FFF;
}

.label-warning {
	background: #60a5fa;
	color: #FFF;
}

.label-danger {
	background: #d2322d;
	color: #FFF;
}

.label-info {
	background: #2dd4bf;
	color: #FFF;
}

.label-dark {
	background: #0f172a;
	color: #FFF;
}

.mfp-bg {
	z-index: 10000;
}

.mfp-wrap {
	z-index: 10001;
}

.mfp-wrap .mfp-content {
	z-index: 10001;
}

.modal-block {
	background: transparent;
	padding: 0;
	text-align: left;
	max-width: 600px;
	margin: 40px auto;
	position: relative;
}

.modal-block.modal-block-xs {
	max-width: 200px;
}

.modal-block.modal-block-sm {
	max-width: 400px;
}

.modal-block.modal-block-md {
	max-width: 600px;
}

.modal-block.modal-block-lg {
	max-width: 900px;
}

.modal-block.modal-block-full {
	max-width: 98%;
}

.modal-block.modal-header-color .panel-heading h2 {
	color: #FFF;
}

.modal-block.modal-full-color {
	color: #FFF;
}

.modal-block.modal-full-color .panel-heading {
	border: 0;
}

.modal-block.modal-full-color .panel-heading h2 {
	color: #FFF;
}

.modal-block.modal-full-color .panel-footer {
	border: 0;
}

.modal-block.modal-full-color .panel-body {
	background-color: transparent;
}

.modal-block.modal-full-color .fa {
	color: #FFF !important;
}

/* Modal Wrapper */
.modal-wrapper {
	position: relative;
	padding: 25px 0;
}

/* Modal Icon */
.modal-icon {
	float: left;
	width: 20%;
	text-align: center;
}

.modal-icon .fa {
	font-size: 52px;
	position: relative;
	top: -10px;
	color: #CCC;
}

.modal-icon.center {
	float: none;
	width: auto;
	padding-top: 20px;
}

.modal-icon.center + .modal-text {
	float: none;
	width: auto;
}

.modal-icon + .modal-text {
	float: left;
	width: 80%;
}

/* Modal Text */
.modal-text {
	padding: 0 5px;
}

.modal-text h1, .modal-text h2, .modal-text h3, .modal-text h4, .modal-text h5, .modal-text h6 {
	padding: 0;
	margin: -7px 0 4px 0;
}

.modal-block-primary .fa {
	color: #CCC;
}

.modal-block-primary.modal-header-color .panel-heading {
	background-color: #CCC;
}

.modal-block-primary.modal-full-color .panel {
	background-color: #e0e0e0;
}

.modal-block-primary.modal-full-color .panel-heading {
	background-color: #CCC;
}

.modal-block-primary.modal-full-color .panel-footer {
	background-color: #e0e0e0;
}

.modal-block-success .fa {
	color: #47a447;
}

.modal-block-success.modal-header-color .panel-heading {
	background-color: #47a447;
}

.modal-block-success.modal-full-color .panel {
	background-color: #5cb85c;
}

.modal-block-success.modal-full-color .panel-heading {
	background-color: #47a447;
}

.modal-block-success.modal-full-color .panel-footer {
	background-color: #5cb85c;
}

.modal-block-warning .fa {
	color: #ed9c28;
}

.modal-block-warning.modal-header-color .panel-heading {
	background-color: #ed9c28;
}

.modal-block-warning.modal-full-color .panel {
	background-color: #f0ad4e;
}

.modal-block-warning.modal-full-color .panel-heading {
	background-color: #ed9c28;
}

.modal-block-warning.modal-full-color .panel-footer {
	background-color: #f0ad4e;
}

.modal-block-danger .fa {
	color: #d2322d;
}

.modal-block-danger.modal-header-color .panel-heading {
	background-color: #d2322d;
}

.modal-block-danger.modal-full-color .panel {
	background-color: #d9534f;
}

.modal-block-danger.modal-full-color .panel-heading {
	background-color: #d2322d;
}

.modal-block-danger.modal-full-color .panel-footer {
	background-color: #d9534f;
}

.modal-block-info .fa {
	color: #5bc0de;
}

.modal-block-info.modal-header-color .panel-heading {
	background-color: #5bc0de;
}

.modal-block-info.modal-full-color .panel {
	background-color: #7dcde5;
}

.modal-block-info.modal-full-color .panel-heading {
	background-color: #5bc0de;
}

.modal-block-info.modal-full-color .panel-footer {
	background-color: #7dcde5;
}

.modal-block-dark .fa {
	color: #171717;
}

.modal-block-dark.modal-header-color .panel-heading {
	background-color: #171717;
}

.modal-block-dark.modal-full-color .panel {
	background-color: #2b2b2b;
}

.modal-block-dark.modal-full-color .panel-heading {
	background-color: #171717;
}

.modal-block-dark.modal-full-color .panel-footer {
	background-color: #2b2b2b;
}

html.dark .modal-content {
	background-color: #1d2127;
}

html.dark .modal-header,
html.dark .modal-footer {
	border-color: #363636;
}

/* Close */
.mfp-close,
.mfp-close-btn-in .mfp-close {
	font-family: "Open Sans", Arial, sans-serif;
	font-weight: 600;
	font-size: 22px;
	color: #838383;
}

/* No Margins */
.mfp-no-margins img.mfp-img {
	padding: 0;
}

.mfp-no-margins .mfp-figure:after {
	top: 0;
	bottom: 0;
}

.mfp-no-margins .mfp-container {
	padding: 0;
}

/* Zoom */
.mfp-with-zoom .mfp-container, .mfp-with-zoom.mfp-bg {
	opacity: 0.001;
	-webkit-backface-visibility: hidden;
	-webkit-transition: all 0.3s ease-out;
	-moz-transition: all 0.3s ease-out;
	-o-transition: all 0.3s ease-out;
	transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
	opacity: 1;
}

.mfp-with-zoom.mfp-ready.mfp-bg {
	opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container, .mfp-with-zoom.mfp-removing.mfp-bg {
	opacity: 0;
}

/* Animnate */
.my-mfp-zoom-in .zoom-anim-dialog {
	opacity: 0;
	-webkit-transition: all 0.2s ease-in-out;
	-moz-transition: all 0.2s ease-in-out;
	-o-transition: all 0.2s ease-in-out;
	transition: all 0.2s ease-in-out;
	-webkit-transform: scale(0.8);
	-moz-transform: scale(0.8);
	-ms-transform: scale(0.8);
	-o-transform: scale(0.8);
	transform: scale(0.8);
}

.my-mfp-zoom-in.mfp-ready .zoom-anim-dialog {
	opacity: 1;
	-webkit-transform: scale(1);
	-moz-transform: scale(1);
	-ms-transform: scale(1);
	-o-transform: scale(1);
	transform: scale(1);
}

.my-mfp-zoom-in.mfp-removing .zoom-anim-dialog {
	-webkit-transform: scale(0.8);
	-moz-transform: scale(0.8);
	-ms-transform: scale(0.8);
	-o-transform: scale(0.8);
	transform: scale(0.8);
	opacity: 0;
}

.my-mfp-zoom-in.mfp-bg {
	opacity: 0.001;
	/* Chrome opacity transition bug */
	-webkit-transition: opacity 0.3s ease-out;
	-moz-transition: opacity 0.3s ease-out;
	-o-transition: opacity 0.3s ease-out;
	transition: opacity 0.3s ease-out;
}

.my-mfp-zoom-in.mfp-ready.mfp-bg {
	opacity: 0.8;
}

.my-mfp-zoom-in.mfp-removing.mfp-bg {
	opacity: 0;
}

.my-mfp-slide-bottom .zoom-anim-dialog {
	opacity: 0;
	-webkit-transition: all 0.2s ease-out;
	-moz-transition: all 0.2s ease-out;
	-o-transition: all 0.2s ease-out;
	transition: all 0.2s ease-out;
	-webkit-transform: translateY(-20px) perspective(600px) rotateX(10deg);
	-moz-transform: translateY(-20px) perspective(600px) rotateX(10deg);
	-ms-transform: translateY(-20px) perspective(600px) rotateX(10deg);
	-o-transform: translateY(-20px) perspective(600px) rotateX(10deg);
	transform: translateY(-20px) perspective(600px) rotateX(10deg);
}

.my-mfp-slide-bottom.mfp-ready .zoom-anim-dialog {
	opacity: 1;
	-webkit-transform: translateY(0) perspective(600px) rotateX(0);
	-moz-transform: translateY(0) perspective(600px) rotateX(0);
	-ms-transform: translateY(0) perspective(600px) rotateX(0);
	-o-transform: translateY(0) perspective(600px) rotateX(0);
	transform: translateY(0) perspective(600px) rotateX(0);
}

.my-mfp-slide-bottom.mfp-removing .zoom-anim-dialog {
	opacity: 0;
	-webkit-transform: translateY(-10px) perspective(600px) rotateX(10deg);
	-moz-transform: translateY(-10px) perspective(600px) rotateX(10deg);
	-ms-transform: translateY(-10px) perspective(600px) rotateX(10deg);
	-o-transform: translateY(-10px) perspective(600px) rotateX(10deg);
	transform: translateY(-10px) perspective(600px) rotateX(10deg);
}

.my-mfp-slide-bottom.mfp-bg {
	opacity: 0.01;
	-webkit-transition: opacity 0.3s ease-out;
	-moz-transition: opacity 0.3s ease-out;
	-o-transition: opacity 0.3s ease-out;
	transition: opacity 0.3s ease-out;
}

.my-mfp-slide-bottom.mfp-ready.mfp-bg {
	opacity: 0.8;
}

.my-mfp-slide-bottom.mfp-removing.mfp-bg {
	opacity: 0;
}

/* Dialog */
.dialog {
	background: white;
	padding: 20px 30px;
	text-align: left;
	margin: 40px auto;
	position: relative;
	max-width: 600px;
}

.dialog.dialog-xs {
	max-width: 200px;
}

.dialog.dialog-sm {
	max-width: 400px;
}

.dialog.dialog-md {
	max-width: 600px;
}

.dialog.dialog-lg {
	max-width: 900px;
}

/* White Popup Block */
.white-popup-block {
	background: #FFF;
	padding: 20px 30px;
	text-align: left;
	max-width: 600px;
	margin: 40px auto;
	position: relative;
}

.white-popup-block.white-popup-block-xs {
	max-width: 200px;
}

.white-popup-block.white-popup-block-sm {
	max-width: 400px;
}

.white-popup-block.white-popup-block-md {
	max-width: 600px;
}

.white-popup-block.white-popup-block-lg {
	max-width: 900px;
}

/* Dark */
html.dark .white-popup-block,
html.dark .dialog {
	background: #1d2127;
}

/*
Animate.css - http: //daneden.me/animate
Licensed under the MIT license

Copyright (c) 2013 Daniel Eden

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/
.appear-animation {
	opacity: 0;
}

.appear-animation-visible {
	opacity: 1;
}

.animated,
.appear-animation {
	-webkit-animation-fill-mode: both;
	-moz-animation-fill-mode: both;
	-ms-animation-fill-mode: both;
	-o-animation-fill-mode: both;
	animation-fill-mode: both;
	-webkit-animation-duration: 1s;
	-moz-animation-duration: 1s;
	-ms-animation-duration: 1s;
	-o-animation-duration: 1s;
	animation-duration: 1s;
}

@-moz-keyframes flash {
	0%, 50%, 100% {
		opacity: 1;
	}

	25%, 75% {
		opacity: 0;
	}
}

@-o-keyframes flash {
	0%, 50%, 100% {
		opacity: 1;
	}

	25%, 75% {
		opacity: 0;
	}
}

@keyframes flash {
	0%, 50%, 100% {
		opacity: 1;
	}

	25%, 75% {
		opacity: 0;
	}
}

.flash {
	-webkit-animation-name: flash;
	-moz-animation-name: flash;
	-o-animation-name: flash;
	animation-name: flash;
}

@-webkit-keyframes shake {
	0%, 100% {
		-webkit-transform: translateX(0);
		opacity: 1;
	}

	10%, 30%, 50%, 70%, 90% {
		-webkit-transform: translateX(-10px);
	}

	20%, 40%, 60%, 80% {
		-webkit-transform: translateX(10px);
	}
}

@-moz-keyframes shake {
	0%, 100% {
		-moz-transform: translateX(0);
		opacity: 1;
	}

	10%, 30%, 50%, 70%, 90% {
		-moz-transform: translateX(-10px);
	}

	20%, 40%, 60%, 80% {
		-moz-transform: translateX(10px);
	}
}

@-o-keyframes shake {
	0%, 100% {
		-o-transform: translateX(0);
		opacity: 1;
	}

	10%, 30%, 50%, 70%, 90% {
		-o-transform: translateX(-10px);
	}

	20%, 40%, 60%, 80% {
		-o-transform: translateX(10px);
	}
}

@keyframes shake {
	0%, 100% {
		transform: translateX(0);
		opacity: 1;
	}

	10%, 30%, 50%, 70%, 90% {
		transform: translateX(-10px);
	}

	20%, 40%, 60%, 80% {
		transform: translateX(10px);
	}
}

.shake {
	-webkit-animation-name: shake;
	-moz-animation-name: shake;
	-o-animation-name: shake;
	animation-name: shake;
}

@-webkit-keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		-webkit-transform: translateY(0);
		opacity: 1;
	}

	40% {
		-webkit-transform: translateY(-30px);
	}

	60% {
		-webkit-transform: translateY(-15px);
	}
}

@-moz-keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		-moz-transform: translateY(0);
		opacity: 1;
	}

	40% {
		-moz-transform: translateY(-30px);
	}

	60% {
		-moz-transform: translateY(-15px);
	}
}

@-o-keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		-o-transform: translateY(0);
		opacity: 1;
	}

	40% {
		-o-transform: translateY(-30px);
	}

	60% {
		-o-transform: translateY(-15px);
	}
}

@keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		transform: translateY(0);
		opacity: 1;
	}

	40% {
		transform: translateY(-30px);
	}

	60% {
		transform: translateY(-15px);
	}
}

.bounce {
	-webkit-animation-name: bounce;
	-moz-animation-name: bounce;
	-o-animation-name: bounce;
	animation-name: bounce;
}

@-webkit-keyframes tada {
	0% {
		-webkit-transform: scale(1);
	}

	10%, 20% {
		-webkit-transform: scale(0.9) rotate(-3deg);
	}

	30%, 50%, 70%, 90% {
		-webkit-transform: scale(1.1) rotate(3deg);
	}

	40%, 60%, 80% {
		-webkit-transform: scale(1.1) rotate(-3deg);
	}

	100% {
		-webkit-transform: scale(1) rotate(0);
		opacity: 1;
	}
}

@-moz-keyframes tada {
	0% {
		-moz-transform: scale(1);
	}

	10%, 20% {
		-moz-transform: scale(0.9) rotate(-3deg);
	}

	30%, 50%, 70%, 90% {
		-moz-transform: scale(1.1) rotate(3deg);
	}

	40%, 60%, 80% {
		-moz-transform: scale(1.1) rotate(-3deg);
	}

	100% {
		-moz-transform: scale(1) rotate(0);
		opacity: 1;
	}
}

@-o-keyframes tada {
	0% {
		-o-transform: scale(1);
	}

	10%, 20% {
		-o-transform: scale(0.9) rotate(-3deg);
	}

	30%, 50%, 70%, 90% {
		-o-transform: scale(1.1) rotate(3deg);
	}

	40%, 60%, 80% {
		-o-transform: scale(1.1) rotate(-3deg);
	}

	100% {
		-o-transform: scale(1) rotate(0);
		opacity: 1;
	}
}

@keyframes tada {
	0% {
		transform: scale(1);
	}

	10%, 20% {
		transform: scale(0.9) rotate(-3deg);
	}

	30%, 50%, 70%, 90% {
		transform: scale(1.1) rotate(3deg);
	}

	40%, 60%, 80% {
		transform: scale(1.1) rotate(-3deg);
	}

	100% {
		transform: scale(1) rotate(0);
		opacity: 1;
	}
}

.tada {
	-webkit-animation-name: tada;
	-moz-animation-name: tada;
	-o-animation-name: tada;
	animation-name: tada;
}

@-webkit-keyframes swing {
	20%, 40%, 60%, 80%, 100% {
		-webkit-transform-origin: top center;
	}

	20% {
		-webkit-transform: rotate(15deg);
	}

	40% {
		-webkit-transform: rotate(-10deg);
	}

	60% {
		-webkit-transform: rotate(5deg);
	}

	80% {
		-webkit-transform: rotate(-5deg);
	}

	100% {
		-webkit-transform: rotate(0deg);
		opacity: 1;
	}
}

@-moz-keyframes swing {
	20% {
		-moz-transform: rotate(15deg);
	}

	40% {
		-moz-transform: rotate(-10deg);
	}

	60% {
		-moz-transform: rotate(5deg);
	}

	80% {
		-moz-transform: rotate(-5deg);
	}

	100% {
		-moz-transform: rotate(0deg);
		opacity: 1;
	}
}

@-o-keyframes swing {
	20% {
		-o-transform: rotate(15deg);
	}

	40% {
		-o-transform: rotate(-10deg);
	}

	60% {
		-o-transform: rotate(5deg);
	}

	80% {
		-o-transform: rotate(-5deg);
	}

	100% {
		-o-transform: rotate(0deg);
		opacity: 1;
	}
}

@keyframes swing {
	20% {
		transform: rotate(15deg);
	}

	40% {
		transform: rotate(-10deg);
	}

	60% {
		transform: rotate(5deg);
	}

	80% {
		transform: rotate(-5deg);
	}

	100% {
		transform: rotate(0deg);
		opacity: 1;
	}
}

.swing {
	-webkit-transform-origin: top center;
	-moz-transform-origin: top center;
	-o-transform-origin: top center;
	transform-origin: top center;
	-webkit-animation-name: swing;
	-moz-animation-name: swing;
	-o-animation-name: swing;
	animation-name: swing;
}

/* originally authored by Nick Pettit - https: //github.com/nickpettit/glide */
@-webkit-keyframes wobble {
	0% {
		-webkit-transform: translateX(0%);
	}

	15% {
		-webkit-transform: translateX(-25%) rotate(-5deg);
	}

	30% {
		-webkit-transform: translateX(20%) rotate(3deg);
	}

	45% {
		-webkit-transform: translateX(-15%) rotate(-3deg);
	}

	60% {
		-webkit-transform: translateX(10%) rotate(2deg);
	}

	75% {
		-webkit-transform: translateX(-5%) rotate(-1deg);
	}

	100% {
		-webkit-transform: translateX(0%);
		opacity: 1;
	}
}

@-moz-keyframes wobble {
	0% {
		-moz-transform: translateX(0%);
	}

	15% {
		-moz-transform: translateX(-25%) rotate(-5deg);
	}

	30% {
		-moz-transform: translateX(20%) rotate(3deg);
	}

	45% {
		-moz-transform: translateX(-15%) rotate(-3deg);
	}

	60% {
		-moz-transform: translateX(10%) rotate(2deg);
	}

	75% {
		-moz-transform: translateX(-5%) rotate(-1deg);
	}

	100% {
		-moz-transform: translateX(0%);
		opacity: 1;
	}
}

@-o-keyframes wobble {
	0% {
		-o-transform: translateX(0%);
	}

	15% {
		-o-transform: translateX(-25%) rotate(-5deg);
	}

	30% {
		-o-transform: translateX(20%) rotate(3deg);
	}

	45% {
		-o-transform: translateX(-15%) rotate(-3deg);
	}

	60% {
		-o-transform: translateX(10%) rotate(2deg);
	}

	75% {
		-o-transform: translateX(-5%) rotate(-1deg);
	}

	100% {
		-o-transform: translateX(0%);
		opacity: 1;
	}
}

@keyframes wobble {
	0% {
		transform: translateX(0%);
	}

	15% {
		transform: translateX(-25%) rotate(-5deg);
	}

	30% {
		transform: translateX(20%) rotate(3deg);
	}

	45% {
		transform: translateX(-15%) rotate(-3deg);
	}

	60% {
		transform: translateX(10%) rotate(2deg);
	}

	75% {
		transform: translateX(-5%) rotate(-1deg);
	}

	100% {
		transform: translateX(0%);
		opacity: 1;
	}
}

.wobble {
	-webkit-animation-name: wobble;
	-moz-animation-name: wobble;
	-o-animation-name: wobble;
	animation-name: wobble;
}

@-webkit-keyframes wiggle {
	0% {
		-webkit-transform: skewX(9deg);
	}

	10% {
		-webkit-transform: skewX(-8deg);
	}

	20% {
		-webkit-transform: skewX(7deg);
	}

	30% {
		-webkit-transform: skewX(-6deg);
	}

	40% {
		-webkit-transform: skewX(5deg);
	}

	50% {
		-webkit-transform: skewX(-4deg);
	}

	60% {
		-webkit-transform: skewX(3deg);
	}

	70% {
		-webkit-transform: skewX(-2deg);
	}

	80% {
		-webkit-transform: skewX(1deg);
	}

	90% {
		-webkit-transform: skewX(0deg);
	}

	100% {
		-webkit-transform: skewX(0deg);
		opacity: 1;
	}
}

@-moz-keyframes wiggle {
	0% {
		-moz-transform: skewX(9deg);
	}

	10% {
		-moz-transform: skewX(-8deg);
	}

	20% {
		-moz-transform: skewX(7deg);
	}

	30% {
		-moz-transform: skewX(-6deg);
	}

	40% {
		-moz-transform: skewX(5deg);
	}

	50% {
		-moz-transform: skewX(-4deg);
	}

	60% {
		-moz-transform: skewX(3deg);
	}

	70% {
		-moz-transform: skewX(-2deg);
	}

	80% {
		-moz-transform: skewX(1deg);
	}

	90% {
		-moz-transform: skewX(0deg);
	}

	100% {
		-moz-transform: skewX(0deg);
		opacity: 1;
	}
}

@-o-keyframes wiggle {
	0% {
		-o-transform: skewX(9deg);
	}

	10% {
		-o-transform: skewX(-8deg);
	}

	20% {
		-o-transform: skewX(7deg);
	}

	30% {
		-o-transform: skewX(-6deg);
	}

	40% {
		-o-transform: skewX(5deg);
	}

	50% {
		-o-transform: skewX(-4deg);
	}

	60% {
		-o-transform: skewX(3deg);
	}

	70% {
		-o-transform: skewX(-2deg);
	}

	80% {
		-o-transform: skewX(1deg);
	}

	90% {
		-o-transform: skewX(0deg);
	}

	100% {
		-o-transform: skewX(0deg);
		opacity: 1;
	}
}

@keyframes wiggle {
	0% {
		transform: skewX(9deg);
	}

	10% {
		transform: skewX(-8deg);
	}

	20% {
		transform: skewX(7deg);
	}

	30% {
		transform: skewX(-6deg);
	}

	40% {
		transform: skewX(5deg);
	}

	50% {
		transform: skewX(-4deg);
	}

	60% {
		transform: skewX(3deg);
	}

	70% {
		transform: skewX(-2deg);
	}

	80% {
		transform: skewX(1deg);
	}

	90% {
		transform: skewX(0deg);
	}

	100% {
		transform: skewX(0deg);
		opacity: 1;
	}
}

.wiggle {
	-webkit-animation-name: wiggle;
	-moz-animation-name: wiggle;
	-o-animation-name: wiggle;
	animation-name: wiggle;
	-webkit-animation-timing-function: ease-in;
	-moz-animation-timing-function: ease-in;
	-o-animation-timing-function: ease-in;
	animation-timing-function: ease-in;
}

/* originally authored by Nick Pettit - https: //github.com/nickpettit/glide */
@-webkit-keyframes pulse {
	0% {
		-webkit-transform: scale(1);
	}

	50% {
		-webkit-transform: scale(1.1);
	}

	100% {
		-webkit-transform: scale(1);
		opacity: 1;
	}
}

@-moz-keyframes pulse {
	0% {
		-moz-transform: scale(1);
	}

	50% {
		-moz-transform: scale(1.1);
	}

	100% {
		-moz-transform: scale(1);
		opacity: 1;
	}
}

@-o-keyframes pulse {
	0% {
		-o-transform: scale(1);
	}

	50% {
		-o-transform: scale(1.1);
	}

	100% {
		-o-transform: scale(1);
		opacity: 1;
	}
}

@keyframes pulse {
	0% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.1);
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}

.pulse {
	-webkit-animation-name: pulse;
	-moz-animation-name: pulse;
	-o-animation-name: pulse;
	animation-name: pulse;
}

@-webkit-keyframes fadeIn {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@-moz-keyframes fadeIn {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@-o-keyframes fadeIn {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@keyframes fadeIn {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

.fadeIn {
	-webkit-animation-name: fadeIn;
	-moz-animation-name: fadeIn;
	-o-animation-name: fadeIn;
	animation-name: fadeIn;
}

@-webkit-keyframes fadeInUp {
	0% {
		opacity: 0;
		-webkit-transform: translateY(20px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateY(0);
	}
}

@-moz-keyframes fadeInUp {
	0% {
		opacity: 0;
		-moz-transform: translateY(20px);
	}

	100% {
		opacity: 1;
		-moz-transform: translateY(0);
	}
}

@-o-keyframes fadeInUp {
	0% {
		opacity: 0;
		-o-transform: translateY(20px);
	}

	100% {
		opacity: 1;
		-o-transform: translateY(0);
	}
}

@keyframes fadeInUp {
	0% {
		opacity: 0;
		transform: translateY(20px);
	}

	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.fadeInUp {
	-webkit-animation-name: fadeInUp;
	-moz-animation-name: fadeInUp;
	-o-animation-name: fadeInUp;
	animation-name: fadeInUp;
}

@-webkit-keyframes fadeInDown {
	0% {
		opacity: 0;
		-webkit-transform: translateY(-20px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateY(0);
	}
}

@-moz-keyframes fadeInDown {
	0% {
		opacity: 0;
		-moz-transform: translateY(-20px);
	}

	100% {
		opacity: 1;
		-moz-transform: translateY(0);
	}
}

@-o-keyframes fadeInDown {
	0% {
		opacity: 0;
		-o-transform: translateY(-20px);
	}

	100% {
		opacity: 1;
		-o-transform: translateY(0);
	}
}

@keyframes fadeInDown {
	0% {
		opacity: 0;
		transform: translateY(-20px);
	}

	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.fadeInDown {
	-webkit-animation-name: fadeInDown;
	-moz-animation-name: fadeInDown;
	-o-animation-name: fadeInDown;
	animation-name: fadeInDown;
}

@-webkit-keyframes fadeInLeft {
	0% {
		opacity: 0;
		-webkit-transform: translateX(-20px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
	}
}

@-moz-keyframes fadeInLeft {
	0% {
		opacity: 0;
		-moz-transform: translateX(-20px);
	}

	100% {
		opacity: 1;
		-moz-transform: translateX(0);
	}
}

@-o-keyframes fadeInLeft {
	0% {
		opacity: 0;
		-o-transform: translateX(-20px);
	}

	100% {
		opacity: 1;
		-o-transform: translateX(0);
	}
}

@keyframes fadeInLeft {
	0% {
		opacity: 0;
		transform: translateX(-20px);
	}

	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

.fadeInLeft {
	-webkit-animation-name: fadeInLeft;
	-moz-animation-name: fadeInLeft;
	-o-animation-name: fadeInLeft;
	animation-name: fadeInLeft;
}

@-webkit-keyframes fadeInRight {
	0% {
		opacity: 0;
		-webkit-transform: translateX(20px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
	}
}

@-moz-keyframes fadeInRight {
	0% {
		opacity: 0;
		-moz-transform: translateX(20px);
	}

	100% {
		opacity: 1;
		-moz-transform: translateX(0);
	}
}

@-o-keyframes fadeInRight {
	0% {
		opacity: 0;
		-o-transform: translateX(20px);
	}

	100% {
		opacity: 1;
		-o-transform: translateX(0);
	}
}

@keyframes fadeInRight {
	0% {
		opacity: 0;
		transform: translateX(20px);
	}

	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

.fadeInRight {
	-webkit-animation-name: fadeInRight;
	-moz-animation-name: fadeInRight;
	-o-animation-name: fadeInRight;
	animation-name: fadeInRight;
}

@-webkit-keyframes fadeInUpBig {
	0% {
		opacity: 0;
		-webkit-transform: translateY(2000px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateY(0);
	}
}

@-moz-keyframes fadeInUpBig {
	0% {
		opacity: 0;
		-moz-transform: translateY(2000px);
	}

	100% {
		opacity: 1;
		-moz-transform: translateY(0);
	}
}

@-o-keyframes fadeInUpBig {
	0% {
		opacity: 0;
		-o-transform: translateY(2000px);
	}

	100% {
		opacity: 1;
		-o-transform: translateY(0);
	}
}

@keyframes fadeInUpBig {
	0% {
		opacity: 0;
		transform: translateY(2000px);
	}

	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.fadeInUpBig {
	-webkit-animation-name: fadeInUpBig;
	-moz-animation-name: fadeInUpBig;
	-o-animation-name: fadeInUpBig;
	animation-name: fadeInUpBig;
}

@-webkit-keyframes fadeInDownBig {
	0% {
		opacity: 0;
		-webkit-transform: translateY(-2000px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateY(0);
	}
}

@-moz-keyframes fadeInDownBig {
	0% {
		opacity: 0;
		-moz-transform: translateY(-2000px);
	}

	100% {
		opacity: 1;
		-moz-transform: translateY(0);
	}
}

@-o-keyframes fadeInDownBig {
	0% {
		opacity: 0;
		-o-transform: translateY(-2000px);
	}

	100% {
		opacity: 1;
		-o-transform: translateY(0);
	}
}

@keyframes fadeInDownBig {
	0% {
		opacity: 0;
		transform: translateY(-2000px);
	}

	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.fadeInDownBig {
	-webkit-animation-name: fadeInDownBig;
	-moz-animation-name: fadeInDownBig;
	-o-animation-name: fadeInDownBig;
	animation-name: fadeInDownBig;
}

@-webkit-keyframes fadeInLeftBig {
	0% {
		opacity: 0;
		-webkit-transform: translateX(-2000px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
	}
}

@-moz-keyframes fadeInLeftBig {
	0% {
		opacity: 0;
		-moz-transform: translateX(-2000px);
	}

	100% {
		opacity: 1;
		-moz-transform: translateX(0);
	}
}

@-o-keyframes fadeInLeftBig {
	0% {
		opacity: 0;
		-o-transform: translateX(-2000px);
	}

	100% {
		opacity: 1;
		-o-transform: translateX(0);
	}
}

@keyframes fadeInLeftBig {
	0% {
		opacity: 0;
		transform: translateX(-2000px);
	}

	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

.fadeInLeftBig {
	-webkit-animation-name: fadeInLeftBig;
	-moz-animation-name: fadeInLeftBig;
	-o-animation-name: fadeInLeftBig;
	animation-name: fadeInLeftBig;
}

@-webkit-keyframes fadeInRightBig {
	0% {
		opacity: 0;
		-webkit-transform: translateX(2000px);
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
	}
}

@-moz-keyframes fadeInRightBig {
	0% {
		opacity: 0;
		-moz-transform: translateX(2000px);
	}

	100% {
		opacity: 1;
		-moz-transform: translateX(0);
	}
}

@-o-keyframes fadeInRightBig {
	0% {
		opacity: 0;
		-o-transform: translateX(2000px);
	}

	100% {
		opacity: 1;
		-o-transform: translateX(0);
	}
}

@keyframes fadeInRightBig {
	0% {
		opacity: 0;
		transform: translateX(2000px);
	}

	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

.fadeInRightBig {
	-webkit-animation-name: fadeInRightBig;
	-moz-animation-name: fadeInRightBig;
	-o-animation-name: fadeInRightBig;
	animation-name: fadeInRightBig;
}

@-webkit-keyframes bounceIn {
	0% {
		opacity: 0;
		-webkit-transform: scale(0.3);
	}

	50% {
		opacity: 1;
		-webkit-transform: scale(1.05);
	}

	70% {
		-webkit-transform: scale(0.9);
	}

	100% {
		-webkit-transform: scale(1);
		opacity: 1;
	}
}

@-moz-keyframes bounceIn {
	0% {
		opacity: 0;
		-moz-transform: scale(0.3);
	}

	50% {
		opacity: 1;
		-moz-transform: scale(1.05);
	}

	70% {
		-moz-transform: scale(0.9);
	}

	100% {
		-moz-transform: scale(1);
		opacity: 1;
	}
}

@-o-keyframes bounceIn {
	0% {
		opacity: 0;
		-o-transform: scale(0.3);
	}

	50% {
		opacity: 1;
		-o-transform: scale(1.05);
	}

	70% {
		-o-transform: scale(0.9);
	}

	100% {
		-o-transform: scale(1);
		opacity: 1;
	}
}

@keyframes bounceIn {
	0% {
		opacity: 0;
		transform: scale(0.3);
	}

	50% {
		opacity: 1;
		transform: scale(1.05);
	}

	70% {
		transform: scale(0.9);
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}

.bounceIn {
	-webkit-animation-name: bounceIn;
	-moz-animation-name: bounceIn;
	-o-animation-name: bounceIn;
	animation-name: bounceIn;
}

@-webkit-keyframes bounceInUp {
	0% {
		opacity: 0;
		-webkit-transform: translateY(2000px);
	}

	60% {
		opacity: 1;
		-webkit-transform: translateY(-30px);
	}

	80% {
		-webkit-transform: translateY(10px);
	}

	100% {
		-webkit-transform: translateY(0);
		opacity: 1;
	}
}

@-moz-keyframes bounceInUp {
	0% {
		opacity: 0;
		-moz-transform: translateY(2000px);
	}

	60% {
		opacity: 1;
		-moz-transform: translateY(-30px);
	}

	80% {
		-moz-transform: translateY(10px);
	}

	100% {
		-moz-transform: translateY(0);
		opacity: 1;
	}
}

@-o-keyframes bounceInUp {
	0% {
		opacity: 0;
		-o-transform: translateY(2000px);
	}

	60% {
		opacity: 1;
		-o-transform: translateY(-30px);
	}

	80% {
		-o-transform: translateY(10px);
	}

	100% {
		-o-transform: translateY(0);
		opacity: 1;
	}
}

@keyframes bounceInUp {
	0% {
		opacity: 0;
		transform: translateY(2000px);
	}

	60% {
		opacity: 1;
		transform: translateY(-30px);
	}

	80% {
		transform: translateY(10px);
	}

	100% {
		transform: translateY(0);
		opacity: 1;
	}
}

.bounceInUp {
	-webkit-animation-name: bounceInUp;
	-moz-animation-name: bounceInUp;
	-o-animation-name: bounceInUp;
	animation-name: bounceInUp;
}

@-webkit-keyframes bounceInDown {
	0% {
		opacity: 0;
		-webkit-transform: translateY(-2000px);
	}

	60% {
		opacity: 1;
		-webkit-transform: translateY(30px);
	}

	80% {
		-webkit-transform: translateY(-10px);
	}

	100% {
		-webkit-transform: translateY(0);
		opacity: 1;
	}
}

@-moz-keyframes bounceInDown {
	0% {
		opacity: 0;
		-moz-transform: translateY(-2000px);
	}

	60% {
		opacity: 1;
		-moz-transform: translateY(30px);
	}

	80% {
		-moz-transform: translateY(-10px);
	}

	100% {
		-moz-transform: translateY(0);
		opacity: 1;
	}
}

@-o-keyframes bounceInDown {
	0% {
		opacity: 0;
		-o-transform: translateY(-2000px);
	}

	60% {
		opacity: 1;
		-o-transform: translateY(30px);
	}

	80% {
		-o-transform: translateY(-10px);
	}

	100% {
		-o-transform: translateY(0);
		opacity: 1;
	}
}

@keyframes bounceInDown {
	0% {
		opacity: 0;
		transform: translateY(-2000px);
	}

	60% {
		opacity: 1;
		transform: translateY(30px);
	}

	80% {
		transform: translateY(-10px);
	}

	100% {
		transform: translateY(0);
		opacity: 1;
	}
}

.bounceInDown {
	-webkit-animation-name: bounceInDown;
	-moz-animation-name: bounceInDown;
	-o-animation-name: bounceInDown;
	animation-name: bounceInDown;
}

@-webkit-keyframes bounceInLeft {
	0% {
		opacity: 0;
		-webkit-transform: translateX(-2000px);
	}

	60% {
		opacity: 1;
		-webkit-transform: translateX(30px);
	}

	80% {
		-webkit-transform: translateX(-10px);
	}

	100% {
		-webkit-transform: translateX(0);
		opacity: 1;
	}
}

@-moz-keyframes bounceInLeft {
	0% {
		opacity: 0;
		-moz-transform: translateX(-2000px);
	}

	60% {
		opacity: 1;
		-moz-transform: translateX(30px);
	}

	80% {
		-moz-transform: translateX(-10px);
	}

	100% {
		-moz-transform: translateX(0);
		opacity: 1;
	}
}

@-o-keyframes bounceInLeft {
	0% {
		opacity: 0;
		-o-transform: translateX(-2000px);
	}

	60% {
		opacity: 1;
		-o-transform: translateX(30px);
	}

	80% {
		-o-transform: translateX(-10px);
	}

	100% {
		-o-transform: translateX(0);
		opacity: 1;
	}
}

@keyframes bounceInLeft {
	0% {
		opacity: 0;
		transform: translateX(-2000px);
	}

	60% {
		opacity: 1;
		transform: translateX(30px);
	}

	80% {
		transform: translateX(-10px);
	}

	100% {
		transform: translateX(0);
		opacity: 1;
	}
}

.bounceInLeft {
	-webkit-animation-name: bounceInLeft;
	-moz-animation-name: bounceInLeft;
	-o-animation-name: bounceInLeft;
	animation-name: bounceInLeft;
}

@-webkit-keyframes bounceInRight {
	0% {
		opacity: 0;
		-webkit-transform: translateX(2000px);
	}

	60% {
		opacity: 1;
		-webkit-transform: translateX(-30px);
	}

	80% {
		-webkit-transform: translateX(10px);
	}

	100% {
		-webkit-transform: translateX(0);
		opacity: 1;
	}
}

@-moz-keyframes bounceInRight {
	0% {
		opacity: 0;
		-moz-transform: translateX(2000px);
	}

	60% {
		opacity: 1;
		-moz-transform: translateX(-30px);
	}

	80% {
		-moz-transform: translateX(10px);
	}

	100% {
		-moz-transform: translateX(0);
		opacity: 1;
	}
}

@-o-keyframes bounceInRight {
	0% {
		opacity: 0;
		-o-transform: translateX(2000px);
	}

	60% {
		opacity: 1;
		-o-transform: translateX(-30px);
	}

	80% {
		-o-transform: translateX(10px);
	}

	100% {
		-o-transform: translateX(0);
		opacity: 1;
	}
}

@keyframes bounceInRight {
	0% {
		opacity: 0;
		transform: translateX(2000px);
	}

	60% {
		opacity: 1;
		transform: translateX(-30px);
	}

	80% {
		transform: translateX(10px);
	}

	100% {
		transform: translateX(0);
		opacity: 1;
	}
}

.bounceInRight {
	-webkit-animation-name: bounceInRight;
	-moz-animation-name: bounceInRight;
	-o-animation-name: bounceInRight;
	animation-name: bounceInRight;
}

@-webkit-keyframes rotateIn {
	0% {
		-webkit-transform-origin: center center;
		-webkit-transform: rotate(-200deg);
		opacity: 0;
	}

	100% {
		-webkit-transform-origin: center center;
		-webkit-transform: rotate(0);
		opacity: 1;
	}
}

@-moz-keyframes rotateIn {
	0% {
		-moz-transform-origin: center center;
		-moz-transform: rotate(-200deg);
		opacity: 0;
	}

	100% {
		-moz-transform-origin: center center;
		-moz-transform: rotate(0);
		opacity: 1;
	}
}

@-o-keyframes rotateIn {
	0% {
		-o-transform-origin: center center;
		-o-transform: rotate(-200deg);
		opacity: 0;
	}

	100% {
		-o-transform-origin: center center;
		-o-transform: rotate(0);
		opacity: 1;
	}
}

@keyframes rotateIn {
	0% {
		transform-origin: center center;
		transform: rotate(-200deg);
		opacity: 0;
	}

	100% {
		transform-origin: center center;
		transform: rotate(0);
		opacity: 1;
	}
}

.rotateIn {
	-webkit-animation-name: rotateIn;
	-moz-animation-name: rotateIn;
	-o-animation-name: rotateIn;
	animation-name: rotateIn;
}

@-webkit-keyframes rotateInUpLeft {
	0% {
		-webkit-transform-origin: left bottom;
		-webkit-transform: rotate(90deg);
		opacity: 0;
	}

	100% {
		-webkit-transform-origin: left bottom;
		-webkit-transform: rotate(0);
		opacity: 1;
	}
}

@-moz-keyframes rotateInUpLeft {
	0% {
		-moz-transform-origin: left bottom;
		-moz-transform: rotate(90deg);
		opacity: 0;
	}

	100% {
		-moz-transform-origin: left bottom;
		-moz-transform: rotate(0);
		opacity: 1;
	}
}

@-o-keyframes rotateInUpLeft {
	0% {
		-o-transform-origin: left bottom;
		-o-transform: rotate(90deg);
		opacity: 0;
	}

	100% {
		-o-transform-origin: left bottom;
		-o-transform: rotate(0);
		opacity: 1;
	}
}

@keyframes rotateInUpLeft {
	0% {
		transform-origin: left bottom;
		transform: rotate(90deg);
		opacity: 0;
	}

	100% {
		transform-origin: left bottom;
		transform: rotate(0);
		opacity: 1;
	}
}

.rotateInUpLeft {
	-webkit-animation-name: rotateInUpLeft;
	-moz-animation-name: rotateInUpLeft;
	-o-animation-name: rotateInUpLeft;
	animation-name: rotateInUpLeft;
}

@-webkit-keyframes rotateInDownLeft {
	0% {
		-webkit-transform-origin: left bottom;
		-webkit-transform: rotate(-90deg);
		opacity: 0;
	}

	100% {
		-webkit-transform-origin: left bottom;
		-webkit-transform: rotate(0);
		opacity: 1;
	}
}

@-moz-keyframes rotateInDownLeft {
	0% {
		-moz-transform-origin: left bottom;
		-moz-transform: rotate(-90deg);
		opacity: 0;
	}

	100% {
		-moz-transform-origin: left bottom;
		-moz-transform: rotate(0);
		opacity: 1;
	}
}

@-o-keyframes rotateInDownLeft {
	0% {
		-o-transform-origin: left bottom;
		-o-transform: rotate(-90deg);
		opacity: 0;
	}

	100% {
		-o-transform-origin: left bottom;
		-o-transform: rotate(0);
		opacity: 1;
	}
}

@keyframes rotateInDownLeft {
	0% {
		transform-origin: left bottom;
		transform: rotate(-90deg);
		opacity: 0;
	}

	100% {
		transform-origin: left bottom;
		transform: rotate(0);
		opacity: 1;
	}
}

.rotateInDownLeft {
	-webkit-animation-name: rotateInDownLeft;
	-moz-animation-name: rotateInDownLeft;
	-o-animation-name: rotateInDownLeft;
	animation-name: rotateInDownLeft;
}

@-webkit-keyframes rotateInUpRight {
	0% {
		-webkit-transform-origin: right bottom;
		-webkit-transform: rotate(-90deg);
		opacity: 0;
	}

	100% {
		-webkit-transform-origin: right bottom;
		-webkit-transform: rotate(0);
		opacity: 1;
	}
}

@-moz-keyframes rotateInUpRight {
	0% {
		-moz-transform-origin: right bottom;
		-moz-transform: rotate(-90deg);
		opacity: 0;
	}

	100% {
		-moz-transform-origin: right bottom;
		-moz-transform: rotate(0);
		opacity: 1;
	}
}

@-o-keyframes rotateInUpRight {
	0% {
		-o-transform-origin: right bottom;
		-o-transform: rotate(-90deg);
		opacity: 0;
	}

	100% {
		-o-transform-origin: right bottom;
		-o-transform: rotate(0);
		opacity: 1;
	}
}

@keyframes rotateInUpRight {
	0% {
		transform-origin: right bottom;
		transform: rotate(-90deg);
		opacity: 0;
	}

	100% {
		transform-origin: right bottom;
		transform: rotate(0);
		opacity: 1;
	}
}

.rotateInUpRight {
	-webkit-animation-name: rotateInUpRight;
	-moz-animation-name: rotateInUpRight;
	-o-animation-name: rotateInUpRight;
	animation-name: rotateInUpRight;
}

@-webkit-keyframes rotateInDownRight {
	0% {
		-webkit-transform-origin: right bottom;
		-webkit-transform: rotate(90deg);
		opacity: 0;
	}

	100% {
		-webkit-transform-origin: right bottom;
		-webkit-transform: rotate(0);
		opacity: 1;
	}
}

@-moz-keyframes rotateInDownRight {
	0% {
		-moz-transform-origin: right bottom;
		-moz-transform: rotate(90deg);
		opacity: 0;
	}

	100% {
		-moz-transform-origin: right bottom;
		-moz-transform: rotate(0);
		opacity: 1;
	}
}

@-o-keyframes rotateInDownRight {
	0% {
		-o-transform-origin: right bottom;
		-o-transform: rotate(90deg);
		opacity: 0;
	}

	100% {
		-o-transform-origin: right bottom;
		-o-transform: rotate(0);
		opacity: 1;
	}
}

@keyframes rotateInDownRight {
	0% {
		transform-origin: right bottom;
		transform: rotate(90deg);
		opacity: 0;
	}

	100% {
		transform-origin: right bottom;
		transform: rotate(0);
		opacity: 1;
	}
}

.rotateInDownRight {
	-webkit-animation-name: rotateInDownRight;
	-moz-animation-name: rotateInDownRight;
	-o-animation-name: rotateInDownRight;
	animation-name: rotateInDownRight;
}

/* Widget - Widget Toggle/Expand */
.widget-toggle-expand .widget-header {
	position: relative;
	margin: 0;
	padding: 5px 0;
}

.widget-toggle-expand .widget-header h6 {
	font-size: 1.3rem;
	margin: 0;
	padding: 0;
}

.widget-toggle-expand .widget-header .widget-toggle {
	font-size: 2.1rem;
	line-height: 2.1rem;
	position: absolute;
	right: 0;
	top: 0;
	cursor: pointer;
	text-align: center;
	color: #b4b4b4;
	-webkit-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-o-transform: rotate(45deg);
	transform: rotate(45deg);
	-webkit-transition-property: -webkit-transform;
	-moz-transition-property: -moz-transform;
	transition-property: transform;
	-webkit-transition-duration: 0.2s;
	-moz-transition-duration: 0.2s;
	transition-duration: 0.2s;
	-webkit-transition-timing-function: linear;
	-moz-transition-timing-function: linear;
	transition-timing-function: linear;
}

.widget-toggle-expand.widget-collapsed .widget-content-expanded {
	display: none;
}

.widget-toggle-expand.widget-collapsed .widget-header .widget-toggle {
	-webkit-transform: none;
	-moz-transform: none;
	-ms-transform: none;
	-o-transform: none;
	transform: none;
}

/* Widget - Social Icons */
.social-icons-list {
	display: block;
	margin: 0;
	padding: 0;
}

.social-icons-list a {
	background: #CCC;
	border-radius: 25px;
	display: inline-block;
	height: 30px;
	line-height: 30px;
	text-align: center;
	width: 30px;
}

.social-icons-list a:hover {
	text-decoration: none;
}

.social-icons-list a span {
	display: none;
}

.social-icons-list a i {
	font-size: 1.35rem;
	color: #FFF;
	font-weight: normal;
}

/* Buttons - States */
.nav-pills-primary > li a:hover,
.nav-pills-primary > li a:focus {
	color: #CCC;
	background-color: white;
}

.nav-pills-primary > li.active > a, .nav-pills-primary > li.active > a:hover, .nav-pills-primary > li.active > a:active, .nav-pills-primary > li.active > a:focus {
	background-color: #CCC;
}

.nav-pills-success > li a:hover,
.nav-pills-success > li a:focus {
	color: #47a447;
	background-color: #bfe2bc;
}

.nav-pills-success > li.active > a, .nav-pills-success > li.active > a:hover, .nav-pills-success > li.active > a:active, .nav-pills-success > li.active > a:focus {
	background-color: #34d399;
}

.nav-pills-warning > li a:hover,
.nav-pills-warning > li a:focus {
	color: #ed9c28;
	background-color: #fbe4cd;
}

.nav-pills-warning > li.active > a, .nav-pills-warning > li.active > a:hover, .nav-pills-warning > li.active > a:active, .nav-pills-warning > li.active > a:focus {
	background-color: #60a5fa;
}

.nav-pills-danger > li a:hover,
.nav-pills-danger > li a:focus {
	color: #d2322d;
	background-color: #f2c0c3;
}

.nav-pills-danger > li.active > a, .nav-pills-danger > li.active > a:hover, .nav-pills-danger > li.active > a:active, .nav-pills-danger > li.active > a:focus {
	background-color: #d2322d;
}

.nav-pills-info > li a:hover,
.nav-pills-info > li a:focus {
	color: #5bc0de;
	background-color: #f0fafc;
}

.nav-pills-info > li.active > a, .nav-pills-info > li.active > a:hover, .nav-pills-info > li.active > a:active, .nav-pills-info > li.active > a:focus {
	background-color: #2dd4bf;
}

.nav-pills-dark > li a:hover,
.nav-pills-dark > li a:focus {
	color: #171717;
	background-color: #707070;
}

.nav-pills-dark > li.active > a, .nav-pills-dark > li.active > a:hover, .nav-pills-dark > li.active > a:active, .nav-pills-dark > li.active > a:focus {
	background-color: #0f172a;
}

.portlet-handler {
	cursor: move;
}

.portlet-placeholder {
	margin-bottom: 15px;
	padding: 0;
	border: 1px dashed #dddddd;
	background: #fafafa;
	color: #444444;
}

/* Tables - Basic */
.table {
	width: 100%;
}

.table .table {
	background: transparent;
}

/* Bootstrap uses important, we need to force it here */
.table.mb-none {
	margin-bottom: 0 !important;
}

/* In case you dont want a border in some row */
.table .b-top-none td {
	border-top: none;
}

/* Tables - Actions */
.table .actions,
.table .actions-hover {
	vertical-align: middle;
}

.table .actions a,
.table .actions-hover a {
	display: inline-block;
	margin-right: 5px;
	color: #666;
}

.table .actions a:last-child,
.table .actions-hover a:last-child {
	margin-right: 0;
}

.table .actions a:hover,
.table .actions-hover a:hover {
	color: #333;
}

.table .actions-hover a {
	opacity: 0;
}

.table tr:hover .actions-hover a {
	opacity: 1;
}

.table .actions-fade a {
	-webkit-transition: all 0.2s linear;
	-moz-transition: all 0.2s linear;
	transition: all 0.2s linear;
}

.table td.min-w-xs {
	min-width: 100px;
}

.table td.min-w-c {
	min-width: 120px;
}

.table td.min-w-sm {
	min-width: 150px;
}

.table td.min-w-md {
	min-width: 180px;
}

.table td.min-w-lg {
	min-width: 200px;
}

.table td.min-w-xlg {
	min-width: 400px;
}

.table .top-b-none {
	border-top: 0 !important;
}

.table .action {
	white-space: nowrap;
}

/* Tables - No More Tables technique (991px is the bootstrap SM max-width) */
@media only screen and (max-width: 991px) {
	.table.table-no-more,
	.table.table-no-more thead,
	.table.table-no-more tbody,
	.table.table-no-more tr,
	.table.table-no-more th,
	.table.table-no-more td {
		display: block;
	}

	.table.table-no-more thead tr {
		left: -9999px;
		position: absolute;
		top: -9999px;
	}

	.table.table-no-more tr {
		border-bottom: 1px solid #DDD;
	}

	.table.table-no-more td {
		border: none;
		position: relative;
		padding-left: 50%;
		text-align: left;
		white-space: normal;
	}

	.table.table-no-more td:before {
		content: attr(data-title);
		font-weight: bold;
		left: 6px;
		padding-right: 10px;
		position: absolute;
		text-align: left;
		top: 8px;
		white-space: nowrap;
		width: 45%;
	}

	.table.table-no-more.table-bordered td {
		border-bottom: 1px solid #EFEFEF;
	}

	.table.table-no-more.table-condensed td:before {
		top: 5px;
	}
}
/* Dark - Tables */
html.dark .table > thead > tr > th,
html.dark .table > tbody > tr > th,
html.dark .table > tfoot > tr > th,
html.dark .table > thead > tr > td,
html.dark .table > tbody > tr > td,
html.dark .table > tfoot > tr > td,
html.dark .table-bordered {
	border-color: #424447;
}

html.dark .table-striped > tbody > tr:nth-child(2n+1) > td,
html.dark .table-striped > tbody > tr:nth-child(2n+1) > th {
	background-color: #363636;
}

html.dark .table-hover > tbody > tr:hover > td,
html.dark .table-hover > tbody > tr:hover > th {
	background-color: #383838;
}

html.dark .table .actions a,
html.dark .table .actions-hover a {
	color: #cbd5e1;
}

@media screen and (max-width: 991px) {
	html.dark .table-responsive {
		border-color: #424447;
	}
}

@media only screen and (max-width: 991px) {
	html.dark .table.table-no-more tr,
	html.dark .table.table-no-more.table-bordered td {
		border-bottom-color: #262b33;
	}
}
/* Tables - States */
.table > thead > tr > td.primary,
.table > tbody > tr > td.primary,
.table > tfoot > tr > td.primary,
.table > thead > tr > th.primary,
.table > tbody > tr > th.primary,
.table > tfoot > tr > th.primary,
.table > thead > tr.primary > td,
.table > tbody > tr.primary > td,
.table > tfoot > tr.primary > td,
.table > thead > tr.primary > th,
.table > tbody > tr.primary > th,
.table > tfoot > tr.primary > th {
	color: #FFF;
	background-color: #CCC !important;
}

.table > thead > tr > td.success,
.table > tbody > tr > td.success,
.table > tfoot > tr > td.success,
.table > thead > tr > th.success,
.table > tbody > tr > th.success,
.table > tfoot > tr > th.success,
.table > thead > tr.success > td,
.table > tbody > tr.success > td,
.table > tfoot > tr.success > td,
.table > thead > tr.success > th,
.table > tbody > tr.success > th,
.table > tfoot > tr.success > th {
	color: #FFF;
	background-color: #34d399 !important;
}

.table > thead > tr > td.warning,
.table > tbody > tr > td.warning,
.table > tfoot > tr > td.warning,
.table > thead > tr > th.warning,
.table > tbody > tr > th.warning,
.table > tfoot > tr > th.warning,
.table > thead > tr.warning > td,
.table > tbody > tr.warning > td,
.table > tfoot > tr.warning > td,
.table > thead > tr.warning > th,
.table > tbody > tr.warning > th,
.table > tfoot > tr.warning > th {
	color: #FFF;
	background-color: #60a5fa !important;
}

.table > thead > tr > td.danger,
.table > tbody > tr > td.danger,
.table > tfoot > tr > td.danger,
.table > thead > tr > th.danger,
.table > tbody > tr > th.danger,
.table > tfoot > tr > th.danger,
.table > thead > tr.danger > td,
.table > tbody > tr.danger > td,
.table > tfoot > tr.danger > td,
.table > thead > tr.danger > th,
.table > tbody > tr.danger > th,
.table > tfoot > tr.danger > th {
	color: #FFF;
	background-color: #d2322d !important;
}

.table > thead > tr > td.info,
.table > tbody > tr > td.info,
.table > tfoot > tr > td.info,
.table > thead > tr > th.info,
.table > tbody > tr > th.info,
.table > tfoot > tr > th.info,
.table > thead > tr.info > td,
.table > tbody > tr.info > td,
.table > tfoot > tr.info > td,
.table > thead > tr.info > th,
.table > tbody > tr.info > th,
.table > tfoot > tr.info > th {
	color: #FFF;
	background-color: #2dd4bf !important;
}

.table > thead > tr > td.dark,
.table > tbody > tr > td.dark,
.table > tfoot > tr > td.dark,
.table > thead > tr > th.dark,
.table > tbody > tr > th.dark,
.table > tfoot > tr > th.dark,
.table > thead > tr.dark > td,
.table > tbody > tr.dark > td,
.table > tfoot > tr.dark > td,
.table > thead > tr.dark > th,
.table > tbody > tr.dark > th,
.table > tfoot > tr.dark > th {
	color: #FFF;
	background-color: #0f172a !important;
}

.table > thead > tr > td.dark,
.table > tbody > tr > td.dark,
.table > tfoot > tr > td.dark,
.table > thead > tr > th.dark,
.table > tbody > tr > th.dark,
.table > tfoot > tr > th.dark,
.table > thead > tr.dark > td,
.table > tbody > tr.dark > td,
.table > tfoot > tr.dark > td,
.table > thead > tr.dark > th,
.table > tbody > tr.dark > th,
.table > tfoot > tr.dark > th {
	background-color: #4a4a4a;
	color: #FFF;
}

.table > thead > tr > th,
.table > thead > tr > td  {
	background-color: #eee;
}

html.dark .table > thead > tr > th,
html.dark .table > thead > tr > td  {
	background-color: #2d2d2d;
}

.table.tbr-middle tr > td {
	vertical-align: middle !important;
}

.table.borderless tr td,
.table.borderless tr th {
    border: none !important;
    padding: 5px !important;
}

.table tr th {
    font-weight: 600 !important;
}

.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
    border-bottom-width: 1px;
}


/* Data Tables */
.dataTables_wrapper {
	position: relative;
	padding: 0;
	margin: 0;
}

.dataTables_wrapper .DTTT_container {
	margin-bottom: 5px;
}

.dataTables_wrapper .DTTT_container .btn-group {
	display: inline-block !important;
}

.dataTables_wrapper .DTTT_container .btn {
	margin-left: 5px;
}

.dataTables_wrapper .datatables-header {
	margin-bottom: 15px;
}

.dataTables_wrapper .datatables-header label {
	font-weight: normal;
	margin: 0;
}

.table > thead > tr > th {
    vertical-align: top !important;
}

table.dataTable.table-condensed > thead > tr > th {
    padding-right: 8px !important;
}

table.dataTable {
	border-collapse: collapse !important;
	margin: 0 !important;
}

.dataTables_wrapper div.dataTables_info {
	font-size: 11.5px;
	padding-top: 3px !important;
	padding-bottom: 5px;
}

.dataTables_wrapper .pagination {
	margin-top: 13px !important;
}

.dataTables_wrapper .select2-container {
	display: inline-block;
	margin-right: 10px;
	width: 75px;
}

@media only screen and (max-width: 991px) {
	.dataTables_wrapper .dataTables_length {
		margin-bottom: 5px;
	}

	.dataTables_wrapper .dataTables_length label {
		float: none;
		width: 100%;
	}
}
@media screen and (max-width: 767px) {
  	.dataTables_wrapper .dataTables_length label {
    	text-align: center !important;
  	}
	.dataTables_wrapper div.dataTables_info {
		padding-top: 0 !important;
	}
}

/* Filter */
.dataTables_wrapper .dataTables_filter label {
	width: 50%;
}

.dataTables_wrapper .dataTables_filter input {
	width: 100% !important;
}

@media only screen and (max-width: 991px) {
	.dataTables_wrapper .dataTables_filter label {
		width: 100% !important;
	}
}
/* Empty Row */
.dataTables_wrapper .dataTables_empty {
	padding: 50px 0;
}

.dataTables_processing {
	background: #CCC;
	border-radius: 100px;
	-webkit-box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.3);
	        box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.3);
	color: #FFF;
	left: 50%;
	margin-left: -36px;
	padding: 5px 10px;
	position: absolute;
	top: 3px;
}

@media only screen and (max-width: 991px) {
	.dataTables_processing {
		left: auto;
		margin-left: 0;
		right: 0;
	}
}

.DTTT_Print,
.DTTT_Print .inner-wrapper,
.DTTT_Print .content-body,
.DTTT_Print .card {
	background: #FFF !important;
	margin: 0 !important;
	padding: 0 !important;
	top: 0 !important;
}

.DTTT_Print .dataTables_wrapper .DTTT.btn-group {
	display: none !important;
}

.DTTT_Print .DTTT_print_info {
	background: rgba(255, 255, 255, 0.9);
	display: block;
	left: 0;
	height: 100px;
	line-height: 100px;
	position: fixed;
	font-size: 14px;
	text-align: center;
	top: 0;
	width: 100%;
}

/* Dark Fixes */
html.dark div.DTTT_container .btn {
	color: #EEE !important;
}

.fileupload .uneditable-input .fas {
	position: absolute;
	top: 9px;
	left: 10px;
}

.fileupload .uneditable-input {
	position: relative;
}

.fileupload .uneditable-input .fileupload-preview {
	display: inline-block;
	float: left;
	overflow: hidden;
	padding: 0 0 0 17px;
	text-overflow: ellipsis;
	width: 100%;
}

.fileupload .btn {
	border-radius: 0;
}

@media only screen and (max-width: 479px) {
	.fileupload .uneditable-input {
		width: 170px;
	}
}
/* Datepicker - Input Group Addon */
.input-daterange .input-group-addon {
	min-width: 36px;
}

/* Datepicker - Base */
.datepicker {
	padding: 10px;
	margin: 0 auto;
	line-height: 1.1em;
}

.datepicker.datepicker-inline {
	line-height: 1.7em;
	width: 100%;
}

.datepicker table {
	width: 100%;
}

.datepicker table tr td {
	border-radius: 0;
}

.datepicker table thead tr th {
	cursor: pointer;
	font-size: 1.3rem;
	text-align: center;
	font-weight: normal;
}

.datepicker table thead tr th.prev {
	content: '\f0d9';
	font-family: Font Awesome 5 Free;
	font-weight: 900;
}

.datepicker table thead tr th.next {
	content: '\f0da';
	font-family: Font Awesome 5 Free;
	font-weight: 900;
}

.datepicker table td {
	text-align: center;
	font-size: 1.2rem;
}

.datepicker table td.day {
	-webkit-transition: background-color 0.1s ease-in 0.1s, color 0.1s ease-in 0.1s;
	-moz-transition: background-color 0.1s ease-in 0.1s, color 0.1s ease-in 0.1s;
	transition: background-color 0.1s ease-in 0.1s, color 0.1s ease-in 0.1s;
	cursor: pointer;
}

html.dark .datepicker table tr td span:hover,
html.dark .datepicker table tr td span.focused {
    background: #484848;
}

html.dark .datepicker .datepicker-switch:hover,
html.dark .datepicker .prev:hover,
html.dark .datepicker .next:hover,
html.dark .datepicker tfoot tr th:hover {
    background: #484848;
}

/* Widget - Summary */
.widget-summary {
	display: table;
	width: 100%;
}

.widget-summary:after {
	content: "";
	display: table;
	clear: both;
}

.widget-summary .widget-summary-col {
	display: table-cell;
	vertical-align: top;
	width: 100%;
}

.widget-summary .widget-summary-col.widget-summary-col-icon {
	width: 1%;
}

.widget-summary .summary-icon img {
	margin-right: 15px;
	width: 90px;
	height: 90px;
	border: 1px solid #ddd;
}

.widget-summary .summary {
	min-height: 65px;
	word-break: break-all;
}

.widget-summary .summary .title {
	margin: 0;
	font-size: 1.6rem;
	line-height: 2.2rem;
	color: #333;
	font-weight: 500;
}

.widget-summary .summary-footer {
	padding: 5px 0 0;
	border-top: 1px dotted #ddd;
	text-align: right;
}

html.dark .widget-summary .summary .title, html.dark .widget-summary .summary .amount {
    color: #EEE;
}

html.dark .widget-summary .summary-footer {
    border-color: #4C4C4C;
}

/* Datepicker - Skin Default */
.datepicker thead tr:first-child th:hover,
.datepicker tfoot tr th:hover,
.datepicker table tr td span:hover {
	background: #CCC;
	color: #FFF;
}

.datepicker table tbody tr td span.old,
.datepicker table tbody tr td span.new {
	color: #CCC;
}

.datepicker table tbody tr td span.old:hover,
.datepicker table tbody tr td span.new:hover {
	color: #FFF;
}

.datepicker table tbody tr td.day:hover {
	background: #CCC;
	color: #FFF;
}

.datepicker table tbody tr td.day.active {
	background: #b3b3b3;
	color: #FFF;
}

.datepicker table tbody tr td.day.new {
	color: #777;
}

.datepicker table tbody tr td.day.new:hover {
	color: #FFF;
}

/* Datepicker - Skin Dark */
html:not(.sidebar-light) .datepicker.datepicker-dark {
	background: transparent;
}

html:not(.sidebar-light) .datepicker.datepicker-dark table thead tr th.datepicker-switch {
	color: #FFF;
}

html:not(.sidebar-light) .datepicker.datepicker-dark table thead tr th.dow {
	color: #777;
}

html:not(.sidebar-light) .datepicker.datepicker-dark table tbody tr td span.old,
html:not(.sidebar-light) .datepicker.datepicker-dark table tbody tr td span.new {
	color: #444;
}

html:not(.sidebar-light) .datepicker.datepicker-dark table tbody tr td span.old:hover,
html:not(.sidebar-light) .datepicker.datepicker-dark table tbody tr td span.new:hover {
	color: #FFF;
}

html:not(.sidebar-light) .datepicker.datepicker-dark table tbody tr td.day {
	color: #FFF;
}

html:not(.sidebar-light) .datepicker.datepicker-dark table tbody tr td.day:hover {
	background: #CCC;
	color: #FFF;
}

html:not(.sidebar-light) .datepicker.datepicker-dark table tbody tr td.day.active {
	background: #b3b3b3;
	color: #FFF;
}

html:not(.sidebar-light) .datepicker.datepicker-dark table tbody tr td.day.new {
	color: #777;
}

html:not(.sidebar-light) .datepicker.datepicker-dark table tbody tr td.day.new:hover {
	color: #FFF;
}

/* Datepicker - Skin Primary */
.datepicker.datepicker-primary {
	min-width: 255px;
}

.datepicker.datepicker-primary.datepicker-inline {
	background: #fff;
	border: 1px solid #eee;
}

.datepicker.datepicker-primary table thead tr:first-child {
	background-color: #CCC;
	color: #FFF;
}

.datepicker.datepicker-primary table thead tr:first-child th:hover {
	background-color: #b3b3b3;
}

.datepicker.datepicker-primary table thead tr:first-child th:first-child {
	border-radius: 4px 0 0 0;
}

.datepicker.datepicker-primary table thead tr:first-child th:last-child {
	border-radius: 0 4px 0 0;
}

.datepicker.datepicker-primary table thead tr:last-child {
	background-color: #d9d9d9;
	color: #FFF;
}

.datepicker.datepicker-primary table thead tr:last-child th:hover {
	background-color: #CCC;
}

.datepicker.datepicker-primary table thead tr th {
	border-radius: 0;
}

html.dark .input-daterange .input-group-addon {
	text-shadow: none;
}

html.dark .datepicker-dropdown {
	color: #e2e8f0;
	background-color: #383838;
}

html.dark .datepicker-dropdown:after {
	border-bottom-color: #383838;
}

html.dark .datepicker-dropdown.datepicker-orient-bottom:before {
	border-top-color: rgba(0, 0, 0, 0.2);
}

html.dark .datepicker-dropdown.datepicker-orient-bottom:after {
	border-top-color: #383838;
}

html.dark .datepicker.datepicker-primary {
	border-color: #363636;
	background: #383838;
}

html.dark .select2-container--bootstrap .select2-selection,
html.dark .select2-container--bootstrap .select2-dropdown,
html.dark .select2-container--bootstrap .select2-choices .select2-search-field input,
html.dark .select2-container--bootstrap .select2-choice,
html.dark .select2-container--bootstrap .select2-choices {
	color: #EEE;
	background-color: #383838;
	border-color: #4c4e51;
}

html.dark .select2-container--bootstrap .select2-selection--single .select2-selection__rendered {
	color: #EEE;
}

html.dark .select2-container--bootstrap .select2-results__option[aria-selected="true"],
html.dark .select2-container--bootstrap .select2-search--dropdown .select2-search__field {
	color: #e2e8f0;
	background-color: #323232;
	border-color: #323232;
}

/* Summernote */
.note-editor {
	border-radius: 4px;
	-webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
	-moz-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
	transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.note-editor, .note-editor.note-frame {
	border: 1px solid #ddd;
}
.note-editor.active {
	border-color: #66afe9;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.note-editor .note-title {
	padding-top: 0 !important;
}
.note-editor .note-toolbar {
	background-color: #f5f5f5;
	border-bottom: 1px dashed #ddd;
	border-left: none;
	border-right: none;
	border-top: none;
	border-radius: 4px 4px 0 0;
	padding: 3px 10px 7px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	z-index: 8;
}
.note-editor .note-toolbar i {
	margin: 0;
}
.note-editor .note-toolbar i:before {
	margin: 0;
}
.note-editor .note-toolbar .btn-group {
	float: none !important;
}
.note-editor .note-toolbar .btn-group > .btn-group:last-child > .btn:first-child {
	border-bottom-left-radius: 3px;
	border-top-left-radius: 3px;
}
.note-editor .note-toolbar .btn-group > .btn-group:first-child > .btn:last-child,
.note-editor .note-toolbar .btn-group > .btn-group:first-child > .dropdown-toggle {
	border-bottom-right-radius: 3px;
	border-top-right-radius: 3px;
}
@media only screen and (max-width: 767px) {
	.note-editor .note-toolbar {
		text-align: center;
	}
}
.note-editor .note-editable {
	clear: both;
	background: #FFF;
	border: none;
	border-radius: 0 0 4px 4px;
	font-family: Arial, Helvetica, Sans-serif;
}
.note-editor .note-statusbar {
	background: #FFF;
	border-radius: 0 0 4px 4px;
}
.note-editor .note-statusbar .note-resizebar {
	border-color: #DDD;
	display: block;
}

html.dark .note-editor {
	border-color: #363636;
	color: #EEE;
}
html.dark .note-editor .note-toolbar,
html.dark .note-editor .note-statusbar {
	background: #363636;
	border-color: #1d2127;
}
html.dark .note-editor .note-editable {
	background: #363636;
	border-color: #1d2127;
}
html.dark .note-editor .note-statusbar .note-resizebar {
	border-color: #1d2127;
}
html.dark .note-editor .note-statusbar .note-resizebar .note-icon-bar {
	border-color: #444;
}
html.dark .note-editor .note-editing-area .note-editable {
	color: #e2e8f0;
}
html.dark .note-editor .caret {
	border-color: #FFF transparent transparent;
}

/* Bootstrap Markdown */
.md-editor {
	border-radius: 4px;
	-webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
	-moz-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
	transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.md-editor > .md-header {
	border-radius: 4px 4px 0 0;
	padding: 6px 4px 0;
}
.md-editor > .md-preview,
.md-editor > textarea {
	background: #FFF;
	border-bottom: none;
	border-radius: 0 0 4px 4px;
	padding: 10px;
	outline: none;
	width: 100% !important;
}
.md-editor .btn-group {
	margin-bottom: 6px;
}

html.dark .md-editor {
	border-color: #363636;
}
html.dark .md-editor > .md-header,
html.dark .md-editor .md-footer {
	background: #363636;
}
html.dark .md-editor > .md-preview,
html.dark .md-editor > textarea {
	background: #363636;
}
html.dark .md-editor > textarea {
	color: #EEE;
	border-color: #1d2127;
}

@media only screen and (max-width: 767px) {
	.bootstrap-maxlength.bottom-left {
		margin-left: 40px;
	}
}
.bootstrap-tagsinput {
	width: 100%;
}

.form-group-invisible .bootstrap-tagsinput {
	border: 0 none;
	box-shadow: none;
	background-color: transparent;
}

html.dark .bootstrap-tagsinput {
	background: #363636;
	border-color: #363636;
}

html.dark .bootstrap-timepicker-widget {
	background-color: #363636;
}
html.dark .bootstrap-timepicker-widget:before {
	border-bottom-color: #1d2127;
}
html.dark .bootstrap-timepicker-widget:after {
	border-bottom-color: #363636;
}
html.dark .bootstrap-timepicker-widget.timepicker-orient-bottom:before {
	border-top-color: #1d2127;
}
html.dark .bootstrap-timepicker-widget.timepicker-orient-bottom:after {
	border-top-color: #363636;
}
html.dark .bootstrap-timepicker-widget table td a {
	color: #FFF;
}
html.dark .bootstrap-timepicker-widget table td a:hover {
	border-color: #363636;
	background-color: #34d399; /* Updated to Emerald 400 */
}
html.dark .bootstrap-timepicker-widget table td input {
	background-color: #303030;
	border-color: #363636;
	color: #EEE;
}

html.dark .colorpicker {
	background-color: #363636;
}
html.dark .colorpicker:before {
	border-bottom-color: #1d2127;
}
html.dark .colorpicker:after {
	border-bottom-color: #363636;
}

html.dark .multiselect-container {
	background-color: #363636;
}
html.dark .multiselect-container > li > a {
	color: #EEE;
}
html.dark .multiselect-container > li > a:hover, html.dark .multiselect-container > li > a:focus {
	background-color: #1d2127;
	color: #FFF;
}

.spinner-buttons.btn-group-vertical .btn {
	height: 18px;
	margin: 0 0 0 -1px;
	padding-left: 6px;
	padding-right: 6px;
	text-align: center;
	width: 22px;
	line-height: 14px;
}
.spinner-buttons.btn-group-vertical .btn i {
	margin-top: -2px;
}
.spinner-buttons.btn-group-vertical .btn:first-child {
	border-radius: 0 4px 0 0 !important;
	-webkit-border-radius: 0 4px 0 0 !important;
}
.spinner-buttons.btn-group-vertical .btn:last-child {
	border-radius: 0 0 4px !important;
	-webkit-border-radius: 0 0 4px !important;
	margin-top: -1px;
	height: 17px;
}

.dropzone {
	background: rgba(0, 0, 0, 0.03) none repeat scroll 0 0;
	border: 1px solid rgba(0, 0, 0, 0.03);
	border-radius: 3px;
	min-height: 122px;
	padding: 23px;
}
.dropzone .dz-default span {
	-webkit-transition: color 0.1s ease-in;
	-moz-transition: color 0.1s ease-in;
	transition: color 0.1s ease-in;
	font-size: 20px;
	color: rgba(0, 0, 0, 0.2);
}
.dropzone:hover .dz-default span {
	color: rgba(0, 0, 0, 0.3);
}

html.dark .dropzone .dz-default span {
	color: rgba(255, 255, 255, 0.2);
}
html.dark .dropzone:hover .dz-default span {
	color: rgba(255, 255, 255, 0.3);
}

/* Form Group Invisible */
.form-group-invisible {
	position: relative;
}
.form-group-invisible.focus .control-label-invisible {
	color: #34d399; /* Updated to Emerald 400 */
}
.form-group-invisible .control-label-invisible {
	bottom: 0;
	display: block;
	float: none;
	left: 0;
	line-height: 64px;
	margin: 0;
	padding-left: 50px;
	position: absolute;
	right: 0;
	top: -15px;
	-webkit-transition: color ease-in-out 0.15s;
	-moz-transition: color ease-in-out 0.15s;
	transition: color ease-in-out 0.15s;
	width: auto;
}
.form-group-invisible .form-control-invisible, .form-group-invisible .form-control-invisible:focus, .form-group-invisible .form-control-invisible:active, .form-group-invisible .form-control-invisible + .bootstrap-tagsinput {
	background: transparent !important;
	border-color: transparent !important;
	box-shadow: none !important;
}
.form-group-invisible .form-control-invisible + .bootstrap-tagsinput {
	margin-bottom: 4px;
}

@media only screen and (max-width: 767px) {
	.form-group-invisible {
		padding-top: 30px;
	}
	.form-group-invisible .control-label-invisible {
		padding-left: 27px;
	}
}
html.dark .CodeMirror pre {
	background: transparent;
	border: none;
}

/* Turns Container With Sidebar Fluid when layout is Boxed */
html.boxed .container-with-sidebar {
	width: 100% !important;
}
html.boxed:not(.sidebar-left-collapsed) .container-with-sidebar [class*="col-"]:not(.isotope-item) {
	width: 100%;
}

/* Container With Sidebar - Sidebar Collapsed */
@media (min-width: 768px) and (max-width: 991px) {
	.container-with-sidebar {
		width: 100%;
	}
	.header .search {
		display: none;
	}
}
@media (min-width: 992px) {
	.container-with-sidebar {
		width: calc(970px - 144px);
	}
}
@media (min-width: 1200px) {
	.container-with-sidebar {
		width: calc(1170px - 144px);
	}
}
@media (min-width: 1600px) {
	.container-with-sidebar {
		width: calc(1570px - 144px) !important;
	}
}
/* Container With Sidebar */
@media (min-width: 768px) and (max-width: 1199px) {
	html:not(.sidebar-left-collapsed):not(.boxed) .container-with-sidebar {
		width: 100%;
	}
	html:not(.sidebar-left-collapsed):not(.boxed) .container-with-sidebar [class*="col-"]:not(.isotope-item) {
		width: 100%;
	}
}
@media (min-width: 992px) {
	html:not(.sidebar-left-collapsed):not(.boxed) .container-with-sidebar {
		width: calc(970px - 300px);
	}
}
@media (min-width: 1200px) {
	html:not(.sidebar-left-collapsed):not(.boxed) .container-with-sidebar {
		width: calc(1170px - 300px);
	}
}
@media (min-width: 1600px) {
	html:not(.sidebar-left-collapsed):not(.boxed) .container-with-sidebar {
		width: calc(1570px - 300px) !important;
	}
}

/* Section Padding */
.section-padding {
	padding: 90px 0 75px;
}

/* Section Full Width Background Light */
.section-full-width-bg-light {
	position: relative;
	background-color: #FFF;
}
.section-full-width-bg-light:before {
	content: '';
	display: block;
	position: absolute;
	top: 0;
	left: 50%;
	width: 100vw;
	height: 100%;
	background-color: #FFF;
	z-index: 0;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
}

/* Heading Margin Top */
@media (min-width: 1200px) {
	html:not(.sidebar-left-collapsed) .heading-margin-top {
		margin-top: 108px;
	}
}

@media (min-width: 992px) {
	.heading-margin-top {
		margin-top: 80px;
	}
}
/* Absolute Position Bottom Left */
.abs-bottom-left {
	position: absolute;
	bottom: -3px;
	left: 40px;
}

/* Overflow Hidden */
.overflow-hidden {
	overflow: hidden;
}

/* List */
.list.list-icons > li {
	line-height: 2.2;
}

/* Invoice */
.invoice {
	padding: 12px;
}

/* Invoice Address Tag */
.invoice address {
	color: #606060;
	line-height: 1.5em;
}

/* Invoice header */
.invoice header {
	border: 1px solid #DADADA;
	padding: 15px;
	box-shadow: 5px 5px 6px rgba(38, 38, 38, 0.1);
	border-radius: 6px;
}

.invoice header .h2,
.invoice header .h4 {
	letter-spacing: 0;
}

/* Invoice Billing Information */
.invoice .bill-data {
	padding: 15px 0;
}
.invoice .bill-data .value {
	display: inline-block;
	margin-left: 10px;
	min-width: 90px;
	padding: 5px;
}

/* Invoice table */
.invoice table.table {
	table-layout: fixed;
}
.invoice table.table > thead:first-child > tr > th {
	background-color: #F8F8F8;
	border-bottom: 2px solid #DADADA;
}
.invoice table.table > tbody tr > td {
	border-color: #DADADA;
}

/* Invoice table items */
.invoice .invoice-items > tbody tr:last-child > td {
	border-bottom: 1px solid #DADADA;
}
.invoice .invoice-items #cell-id {
	width: 10%;
}
.invoice .invoice-items #cell-item {
	width: 20%;
}
.invoice .invoice-items #cell-desc {
	width: 20%;
}
.invoice .invoice-items #cell-price {
	width: 10%;
}
.invoice .invoice-items #cell-qty {
	width: 10%;
}
.invoice .invoice-items #cell-total {
	width: 10%;
}

/* Invoice summary */
.invoice-summary .col-sm-4 {
	padding-left: 0;
}

/* Invoice Responsiveness */
@media only screen and (max-width: 991px) {
	.invoice .table-responsive > table.table {
		table-layout: auto;
	}

	.invoice-summary .col-sm-4 {
		padding-left: 15px;
	}
}
/* Invoice Print */
@media print {
	.invoice .table-responsive {
		border: none !important;
		overflow: visible !important;
		width: auto !important;
	}
	.invoice table.table.invoice-items {
		table-layout: auto;
	}
	.invoice header .col-sm-6:first-child,
	.invoice header .col-sm-6:last-child,
	.invoice .bill-info .col-md-6 {
		float: left !important;
	}
	.invoice header .col-sm-6:first-child {
		width: 25% !important;
	}
	.invoice header .col-sm-6:last-child {
		width: 75% !important;
	}
	.invoice .bill-info .col-md-6 {
		width: 50% !important;
	}
	.invoice .invoice-summary .col-sm-4 {
		float: right;
		padding: 0;
		width: 40%;
	}
	.invoice .ib img {
		max-width: 180px !important;
	}
}
/* dark */
html.dark .invoice header {
	border-color: #474747;
	box-shadow: 5px 5px 6px rgba(38, 38, 38, 0.51);
}
html.dark .invoice table.table > thead:first-child > tr > th {
	background-color: #2a2a2a;
	border-bottom-color: #424242;
}
html.dark .invoice table.table > tbody tr > td {
	border-color: #424242;
}

/* Error Pages - wrappers */
.body-error {
	margin: 0 auto;
	max-width: 900px;
	width: 100%;
}
.body-error.error-outside {
	display: table;
	height: 100vh;
}
.body-error.error-outside .center-error {
	display: table-cell;
	vertical-align: middle;
}
.body-error.error-inside {
	margin-top: 150px;
}

/* Error Pages - header */
.body-error .error-header {
	border-bottom: 1px solid #DADADA;
	margin-bottom: 50px;
	padding-bottom: 15px;
}
.body-error .error-header .form {
	margin-top: 12px;
}

/* Error Pages - typo */
.body-error .error-code {
	font-size: 14rem;
	line-height: 14rem;
	letter-spacing: -10px;
}
.body-error .error-explanation {
	font-size: 2rem;
	line-height: 3.6rem;
}

/* Error Pages - Responsive */
@media only screen and (max-width: 1150px) {
	.body-error.error-inside {
		margin-top: 50px;
		padding-bottom: 50px;
	}
}
@media only screen and (min-width: 768px) and (max-width: 1150px) {
	.body-error.error-inside .error-code {
		font-size: 10rem;
		line-height: 10rem;
		letter-spacing: -7px;
	}
	.body-error.error-inside .error-explanation {
		font-size: 1.8rem;
		line-height: 3.2rem;
	}
}
@media only screen and (max-width: 767px) {
	.body-error .error-code {
		font-size: 9rem;
		line-height: 9rem;
		letter-spacing: -7px;
	}
	.body-error .error-explanation {
		font-size: 1.6rem;
		line-height: 2.8rem;
	}

	.body-error.error-outside {
		height: auto;
		padding: 20px;
	}
}

.fc .fc-toolbar h2 {
	color: #171717;
	font-size: 22.4px;
	font-size: 1.8rem;
	font-weight: normal;
	margin-top: 4px;
}

.fc .fc-toolbar h2:before {
	color: #CCC;
	content: "\f073";
	display: inline-block;
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
	font-size: 28.8px;
	font-size: 1.8rem;
	font-style: normal;
	line-height: 1;
	margin-right: 10px;
	position: relative;
	top: 2px;
	-webkit-font-smoothing: antialiased;
}
.fc .fc-toolbar .fc-button {
	text-transform: capitalize;
	background: #FFF;
	box-shadow: none;
	text-shadow: none;
	font-size: 0.9em;
	padding: 2px 0.8em 3px;
	height: auto;
	border: 1px solid rgba(0, 0, 0, 0.15);
}
.fc .fc-toolbar .fc-button.fc-state-active {
	color: #FFF;
}
.fc .fc-day-grid-container {
	overflow: visible !important;
	height: auto !important;
}
.fc .fc-widget-header th {
	line-height: 35px;
}

/* Fullcalendar - Event States */
/* Buttons - States */
.fc-event.fc-event-default {
	background: #ebebeb;
	border-color: #ebebeb;
}
.fc-event.fc-event-default .fc-event-inner {
	color: #777;
}

a.fc-event.fc-event-default:hover {
	color: #777;
}

.fc-event.fc-event-primary {
	background: #CCC;
	border-color: #CCC;
}
.fc-event.fc-event-primary .fc-event-inner {
	color: #FFF;
}

a.fc-event.fc-event-primary:hover {
	color: #FFF;
}

.fc-event.fc-event-success {
	background: #34d399;
	border-color: #34d399;
}
.fc-event.fc-event-success .fc-event-inner {
	color: #FFF;
}

a.fc-event.fc-event-success:hover {
	color: #FFF;
}

.fc-event.fc-event-warning {
	background: #60a5fa;
	border-color: #60a5fa;
}
.fc-event.fc-event-warning .fc-event-inner {
	color: #FFF;
}

a.fc-event.fc-event-warning:hover {
	color: #FFF;
}

.fc-event.fc-event-danger {
	background: #d2322d;
	border-color: #d2322d;
}
.fc-event.fc-event-danger .fc-event-inner {
	color: #FFF;
}

a.fc-event.fc-event-danger:hover {
	color: #FFF;
}

.fc-event.fc-event-info {
	background: #2dd4bf;
	border-color: #2dd4bf;
}
.fc-event.fc-event-info .fc-event-inner {
	color: #FFF;
}

a.fc-event.fc-event-info:hover {
	color: #FFF;
}

.fc-event.fc-event-dark {
	background: #0f172a;
	border-color: #0f172a;
}
.fc-event.fc-event-dark .fc-event-inner {
	color: #FFF;
}

a.fc-event.fc-event-dark:hover {
	color: #FFF;
}

/* Fullcalendar - External Events */
.external-event {
	cursor: move;
	display: inline-block;
	font-size: 1.2rem;
	font-weight: normal;
	margin: 5px;
	padding: 10px;
	text-align: left;
}

/* dark */
html.dark .fc .fc-toolbar h2 {
	color: #FFF;
}
html.dark .fc .fc-toolbar .fc-button {
	background: #303030;
	color: #FFF;
}
html.dark .fc-unthemed th,
html.dark .fc-unthemed td,
html.dark .fc-unthemed thead,
html.dark .fc-unthemed tbody,
html.dark .fc-unthemed .fc-divider,
html.dark .fc-unthemed .fc-row,
html.dark .fc-unthemed .fc-popover {
	border-color: rgba(0, 0, 0, 0.3);
}
html.dark .fc-unthemed .fc-today {
	background: #303030;
}

.timeline .tm-body {
	position: relative;
	padding: 30px 0;
}
.timeline .tm-body:after {
	background: #505050;
	background: -moz-linear-gradient(top, rgba(80, 80, 80, 0) 0%, #505050 8%, #505050 92%, rgba(80, 80, 80, 0) 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #1e5799), color-stop(100%, #7db9e8));
	background: -webkit-linear-gradient(top, rgba(80, 80, 80, 0) 0%, #505050 8%, #505050 92%, rgba(80, 80, 80, 0) 100%);
	background: -o-linear-gradient(top, rgba(80, 80, 80, 0) 0%, #505050 8%, #505050 92%, rgba(80, 80, 80, 0) 100%);
	background: -ms-linear-gradient(top, rgba(80, 80, 80, 0) 0%, #505050 8%, #505050 92%, rgba(80, 80, 80, 0) 100%);
	background: linear, to bottom, rgba(80, 80, 80, 0) 0%, #505050 8%, #505050 92%, rgba(80, 80, 80, 0) 100%;
	content: '';
	display: block;
	height: 100%;
	left: 140px;
	margin-left: -2px;
	position: absolute;
	top: 0;
	width: 3px;
	z-index: 0;
	filter: alpha(opacity=35);
	opacity: 0.35;
}
.timeline .tm-title {
	position: relative;
	display: inline-block;
	text-align: center;
	min-width: 200px;
	background-color: #fff;
	padding: 4px 5px;
	margin: 0 40px;
	z-index: 1;
	-webkit-border-radius: 3px;
	border-radius: 3px;
}
.timeline .tm-items {
	list-style: none;
	padding: 0;
	margin: 0;
}
.timeline .tm-items > li {
	position: relative;
	margin: 30px 0;
	padding: 0 0 0 190px;
	min-height: 65px;
	z-index: 1;
}
.timeline .tm-items > li .tm-datetime {
	position: absolute;
	top: 50%;
	left: 0;
	width: 100px;
	height: 48px;
	margin-top: -24px;
	text-align: right;
	z-index: 3;
}
.timeline .tm-items > li .tm-datetime .tm-datetime-time {
	color: #CCC;
	font-size: 2.4rem;
	font-weight: 700;
	margin: 0;
	white-space: nowrap;
}
.timeline .tm-items > li .tm-icon {
	position: absolute;
	top: 50%;
	left: 140px;
	background-color: #ecedf0;
	border: 3px solid #CCC;
	color: #CCC;
	font-size: 28px;
	padding: 10px;
	width: 55px;
	height: 55px;
	text-align: center;
	line-height: 29px;
	margin-top: -28px;
	margin-left: -28px;
	z-index: 2;
	-webkit-border-radius: 28px;
	border-radius: 28px;
}
.timeline .tm-items > li .tm-box {
	position: relative;
	background: #fff;
	min-height: 65px;
	padding: 10px 20px;
	border: 1px solid #e9e9e9;
	-webkit-border-radius: 6px;
	border-radius: 6px;
}
.timeline .tm-items > li .tm-box:after {
	right: 100%;
	border: solid transparent;
	content: ' ';
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-right-color: #fff;
	border-width: 8px;
	top: 50%;
	margin-top: -8px;
	z-index: 2;
}
.timeline .tm-items > li .tm-box p:last-child {
	margin-bottom: 0;
}
.timeline .tm-items > li .tm-box .tm-meta {
	margin: 10px 0 0;
}
.timeline .tm-items > li .tm-box .tm-meta span {
	display: inline-block;
	padding-right: 8px;
}
.timeline .tm-items > li .tm-box .tm-meta span:last-child, .timeline .tm-items > li .tm-box .tm-meta span:last-of-type {
	padding-right: 0;
}
.timeline.timeline-simple .tm-body:after {
	left: 30px;
}
.timeline.timeline-simple .tm-body .tm-title {
	border: 1px solid #e9e9e9;
	margin: 0 10px;
}
.timeline.timeline-simple .tm-body .tm-items > li {
	padding: 0 0 0 55px;
}
.timeline.timeline-simple .tm-body .tm-items > li:before {
	display: block;
	position: absolute;
	content: ' ';
	background: none repeat scroll 0 0 #CCC;
	border-radius: 50%;
	box-shadow: 0 0 0 3px #FFF, 0 0 0 6px #CCC;
	height: 7px;
	left: 30px;
	top: 50%;
	width: 8px;
	margin-left: -4px;
	margin-top: -4px;
}
.timeline.timeline-simple .tm-body .tm-items > li .tm-box:before {
	left: -17px;
	border: solid transparent;
	content: ' ';
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-right-color: #e9e9e9;
	border-width: 8px;
	top: 50%;
	margin-top: -8px;
	z-index: 1;
}

@media only screen and (max-width: 991px) {
	.timeline .tm-body:after {
		left: 20px;
	}
	.timeline .tm-title {
		margin: 0;
	}
	.timeline .tm-items > li {
		padding-left: 50px;
	}
	.timeline .tm-items > li .tm-info {
		margin: 0 0 15px;
	}
	.timeline .tm-items > li .tm-info:after {
		content: "";
		display: table;
		clear: both;
	}
	.timeline .tm-items > li .tm-icon {
		border-width: 2px;
		float: left;
		font-size: 22px;
		height: 40px;
		line-height: 36px;
		margin: 0 15px 0 0;
		padding: 0;
		position: static;
		width: 40px;
	}
	.timeline .tm-items > li .tm-datetime {
		margin: 0;
		position: static;
		text-align: left;
	}
	.timeline .tm-items > li .tm-datetime .tm-datetime-date {
		font-size: 1.2rem;
		line-height: 1.3;
	}
	.timeline .tm-items > li .tm-datetime .tm-datetime-time {
		font-size: 1.8rem;
		line-height: 1.3;
	}
}
@media only screen and (max-width: 767px) {
	.timeline .tm-items > li .tm-box .tm-meta span {
		display: block;
	}
}
html.dark .timeline .tm-items > li .tm-box {
	background: #363636;
	border-color: #303030;
}
html.dark .timeline .tm-items > li .tm-box:after {
	border-right-color: #363636;
}
html.dark .timeline .tm-items > li .tm-box:before {
	border-right-color: #303030;
}
html.dark .timeline .tm-items > li .tm-icon {
	background-color: #1d2127;
}
html.dark .timeline .tm-title {
	background-color: #363636;
	border-color: #303030;
}
html.dark .timeline.timeline-simple .tm-body .tm-title {
	background-color: #363636;
	border-color: #303030;
}
html.dark .timeline.timeline-simple .tm-body .tm-items > li:before {
	box-shadow: 0 0 0 3px #323232, 0 0 0 6px #CCC;
}
html.dark .timeline.timeline-simple .tm-body .tm-items > li .tm-box:after {
	border-right-color: #363636;
}
html.dark .timeline.timeline-simple .tm-body .tm-items > li .tm-box:before {
	border-right-color: #303030;
}

/* dark */
html.dark .search-content .search-control-wrapper {
	background: #1d2127;
	border-bottom-color: #363636;
}
html.dark .search-content .tab-content {
	background: transparent;
}
html.dark .search-content .search-toolbar {
	background: #303030;
	border-bottom-color: #303030;
}
html.dark .search-content .search-toolbar .nav-pills a, html.dark .search-content .search-toolbar .nav-pills a:hover, html.dark .search-content .search-toolbar .nav-pills a:focus {
	border-bottom-color: #303030;
	border-top-color: #303030;
	color: #cbd5e1;
}
html.dark .search-content .search-toolbar .nav-pills a:hover, html.dark .search-content .search-toolbar .nav-pills a:focus {
	border-bottom-color: #303030;
	border-top-color: #303030;
	color: #e2e8f0;
}
html.dark .search-content .search-toolbar .nav-pills li.active a, html.dark .search-content .search-toolbar .nav-pills li.active a:hover, html.dark .search-content .search-toolbar .nav-pills li.active a:focus {
	color: #34d399; /* Updated to Emerald 400 */
	border-bottom-color: #34d399; /* Updated to Emerald 400 */
}
html.dark .search-content .search-results-list li {
	border-bottom-color: #363636;
}
html.dark .search-content .search-results-list a .description {
	color: #cbd5e1;
}
html.dark .search-content .search-results-list a:hover {
	background: #363636;
}

/* Dark - Background */
html.dark,
html.dark body {
	background-color: #0f172a;
}
html.dark.boxed .content-body {
	background-color: #1e293b;
}

html.dark body {
	color: #686868;
}
html.dark .hidden-on-dark {
	display: none !important;
}

/* Dark - Titles */
html.dark h1,
html.dark .h1,
html.dark h2,
html.dark .h2,
html.dark h3,
html.dark .h3,
html.dark h4,
html.dark .h4,
html.dark h5,
html.dark .h5,
html.dark h6,
html.dark .h6 {
	color: #FFF;
}

/* Dark - Alerts */
html.dark .alert h1,
html.dark .alert .h1,
html.dark .alert h2,
html.dark .alert .h2,
html.dark .alert h3,
html.dark .alert .h3,
html.dark .alert h4,
html.dark .alert .h4,
html.dark .alert h5,
html.dark .alert .h5,
html.dark .alert h6,
html.dark .alert .h6 {
	color: #111;
}

/* Dark - Helpers */
html.dark .text-dark {
	color: #FFF !important;
}

html.dark ul.nav-list.primary > li a {
	border-bottom-color: #363636;
}
html.dark ul.nav-list.primary > li a:hover {
	background-color: #363636;
}

html.dark .pagination > li > a,
html.dark .pagination > li > span {
	background-color: #363636;
	border-color: #363636;
}
html.dark .pagination > li.active > a,
html.dark .pagination > li.active > span {
	background-color: #CCC;
	border-color: #363636;
}

html.dark .dropdown-menu {
	background-color: #363636;
}
html.dark .dropdown-menu > li > a {
	color: #e2e8f0;
}
html.dark .dropdown-menu > li > a:hover, html.dark .dropdown-menu > li > a:focus {
	background-color: #1d2127;
	color: #FFF;
}

html.dark hr.dotted,
html.dark hr.solid {
	border-color: #4C4C4C;
}

html.dark .img-thumbnail,
html.dark .thumbnail {
	background-color: #303030;
	border-color: #363636;
}

/* whatsapp popup */
.whatsapp-popup {
    z-index: 999;
    position: fixed;
    bottom: 40px;
    right: 25px;
}

.whatsapp-popup .whatsapp-button {
    display: flex;
    align-items: center;
    width: 50px;
    height: 50px;
    color: #fff;
    background: #03cc0b;
    border-radius: 100%;
    cursor: pointer;
    transition: ease transform 300ms;
    overflow: hidden;
    box-shadow: 0 0 15px -5px rgba(0,0,0,.3);
}

.whatsapp-popup .whatsapp-button i {
    font-size: 30px;
}

.whatsapp-popup .popup-content {
    position: absolute;
    bottom: 100%;
    margin: 0;
    padding: 0;
    width: calc(100vw - 60px);
    max-width: 320px;
    margin: 0 0 15px 0 !important;
    margin-bottom: 6px;
    background: #ffffff;
    padding: 0;
    border-radius: 10px;
    box-shadow: 0 10px 20px rgb(0 0 0 / 10%);
    opacity: 0;
    visibility: hidden;
    transition: opacity ease-in-out 0.3s, visibility ease-in-out 0.3s, margin ease-in-out 0.3s;
    right: 0;
}

.whatsapp-popup .popup-content:after {
    content: "";
    position: absolute;
    display: block;
    top: 100%;
    border-width: 8px 8px 0 8px;
    border-style: solid;
    border-color: #ffffff transparent transparent transparent;
    right: 22px;
}

.whatsapp-popup.open .popup-content {
    opacity: 1;
    visibility: visible;
    margin-bottom: 25px !important;
    transition: opacity ease-in-out 0.3s, visibility ease-in-out 0.3s, margin ease-in-out 0.3s;
}

.whatsapp-popup .popup-content-header {
    padding: 15px;
    background: #03cc0b;
    border-radius: 10px 10px 0 0;
}

.whatsapp-popup .popup-content-header i {
    font-size: 40px;
    color: #fff;
    float: left;
}

.whatsapp-popup .popup-content-header h5 {
    font-size: 17px;
    font-weight: 700;
    color: #ffffff;
    letter-spacing: 0;
    text-transform: none;
    margin: 0 0 0 50px;
    padding: 0;
}

.whatsapp-popup .popup-content-header h5 span {
    display: block;
    font-size: 14px;
    font-weight: 400;
    line-height: 120%;
    margin: 5px 0 0 0;
}

.whatsapp-popup .whatsapp-content ul {
    list-style-type: none;
    padding: 14px;
    max-height: 327px;
    overflow-y: auto;
}

.whatsapp-popup .whatsapp-content ul li {
    line-height: 18px;
    background: #fafafc;
    padding: 15px 10px;
    position: relative;
    list-style-type: none;
    margin: 0;
    border-radius: 6px;
}

.whatsapp-popup .whatsapp-text {
    padding-left: 10px;
    font-weight: 600 !important;
    display: block;
    position: relative;
    margin-left: 0;
}

.whatsapp-popup .whatsapp-label {
    font-size: 12px;
    display: block;
    font-weight: 400;
    color: #bababa !important;
    overflow: hidden;
}

.whatsapp-popup li.online span.whatsapp-text span.whatsapp-label span.status {
    color: #03cc0b;
}

.whatsapp-popup .content-footer {
    background: #ddd;
    position: relative;
    z-index: 2;
    box-shadow: 0 -20px 20px rgb(82 99 158 / 3%);
    border-radius: 0 0 10px 10px;
    text-align: center;
    padding: 0;
}

.whatsapp-popup .content-footer p {
    font-size: 12px;
    line-height: 14px;
    padding: 15px;
    margin: 0;
}

.whatsapp-popup ul li:not(:first-child) {
    margin-top: 8px;
}

.whatsapp-popup a {
    text-decoration: none;
}

.whatsapp-popup .whatsapp-agent {
    display: flex;
    align-items: center;
    overflow: hidden;
    position: relative;
}

.whatsapp-popup .whatsapp-content .whatsapp-avatar {
    border-radius: 25px;
    border: solid 2px #03cc0b;
    max-width: 100%;
    max-height: 100%;
    position: absolute;
    left: 0;
}

.whatsapp-popup .whatsapp-img {
    float: left;
    width: 45px;
    height: 45px;
    position: relative;   
}

.whatsapp-popup .whatsapp-img:after {
    content: "";
    position: absolute;
    display: block;
    width: 12px;
    height: 12px;
    background: #03cc0b;
    top: 2px;
    margin-right: -44px;
    right: 100%;
    border-radius: 10px;
    border: solid 1px #ffffff;
}

.whatsapp-popup .offline .whatsapp-img:after {
    background: #bababa;
}

.whatsapp-popup .whatsapp-content .offline .whatsapp-avatar {
    border-color: #bababa;
}

.whatsapp-popup li.offline {
    pointer-events: none;
    filter: saturate(0);
    -ms-filter: saturate(0);
    -webkit-filter: saturate(0);
    opacity: 0.7;
}

.whatsapp-popup .i-open, .whatsapp-popup .i-close {
    flex: 0 0 auto;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: ease transform 300ms,ease opacity 300ms; 
}

.whatsapp-popup.open .i-open, .whatsapp-popup.open .i-close {
    transform: translate3d(-100%,0,0);
}

.whatsapp-popup.open .i-open {
    opacity:0;
}

.whatsapp-popup:not(.open) .i-close {
    opacity: 0;
}

html.dark .whatsapp-popup .popup-content {
    background: #383838;
}

html.dark .whatsapp-popup .whatsapp-content ul li {
    background: #323232;
}

html.dark .whatsapp-popup .content-footer {
    background: #262626;
}

html.dark .whatsapp-popup .whatsapp-label {
    color: #cbd5e1 !important;
}

html.dark .whatsapp-popup .popup-content:after {
    border-top-color: #5e5d5d;
}

/* online exam */
.step-pane {
	display: none;
}

.step-pane.active,
.bs-block {
	display: block;
}

.time_status h4 {
	margin: 4px 0;
	font-size: 1.6rem;
}

.ques-marks {
	background: #2f79a4;
	padding: 5px 15px;
	color: #fff;
	border-radius: 3px;
}

.on_answer_box > li a.que_btn {
	background-color: transparent;
	color: #000;
	border: 1px solid #ddd;
}

html.dark .on_answer_box > li a.que_btn {
	color: #fff;
	border-color: #4c4e51;
}

.on_answer_box > li a.que_btn.active {
	background-color:  #34d399; /* Updated to Emerald 400 */
}

html.dark .on_answer_box > li a.que_btn.active {
	background-color:  #34d399; /* Updated to Emerald 400 */
}

.on_answer_box > li > a {
	display: block;
	text-align: center;
	line-height: 1.3 !important;
	padding: 10px;
	margin: 5px;
	color: #ffffff;
	font-size: 18px;
	font-weight: 700;
	width: 45px;
	height: 45px;
	text-decoration: none;
	border-radius: 50px;
}

.on_answer_box {
	display: inline-block;
	padding-left: 0;
	margin: 20px 0;
	border-radius: 4px;
}

.on_answer_box > li {
	display: inline-block;
}

.questionmodal .modal-contentfull {
	height: 100%;
	margin: 0;
	border-radius: 0;
	overflow-y: auto;
	overflow-x: hidden;
}

.questionmodal .modal-dialogfullwidth {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
}

.questionmodal .modal {
	overflow-x: hidden;
	overflow-y: hidden;
	padding-right: 0 !important;
}

.questionmodal .modal-content {
	background-color: #fff;
}

ul.nav-main li a i:not(.nav-children a i) {
	border-radius: 4px;
	height: 30px;
	width: 30px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	vertical-align: middle;
	background-color: #fff;
	box-shadow: -3px 4px 20px rgba(0, 0, 0, 0.1);
}

html.dark ul.nav-main li a i:not(.nav-children a i) {
	background-color: #262626;
}

ul.nav-main li:not(.nav-children li) {
	border-bottom: 1px solid #e8e8e8;
}

html.dark ul.nav-main li:not(.nav-children li) {
	border-color: #4c4e51;
}

/* student disabled */
ul.stu-disabled li {
	border-bottom: 1px solid #ddd;
	padding: 10px 10px 10px 0;
	position: relative;
	padding-left: 0 !important;
	list-style: none !important;
}

html.dark ul.stu-disabled li {
	border-color: #4c4e51;
}

ul.stu-disabled li:last-child {
	border-bottom: 0 none;
}

.stu-disabled .main-r {
	display: inline-flex;
}

.stu-disabled .r-1 {
	padding-right: 10px;
	font-weight: 600;
}