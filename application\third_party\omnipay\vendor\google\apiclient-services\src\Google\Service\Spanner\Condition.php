<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Spanner_Condition extends Google_Collection
{
  protected $collection_key = 'values';
  public $iam;
  public $op;
  public $svc;
  public $sys;
  public $value;
  public $values;

  public function setIam($iam)
  {
    $this->iam = $iam;
  }
  public function getIam()
  {
    return $this->iam;
  }
  public function setOp($op)
  {
    $this->op = $op;
  }
  public function getOp()
  {
    return $this->op;
  }
  public function setSvc($svc)
  {
    $this->svc = $svc;
  }
  public function getSvc()
  {
    return $this->svc;
  }
  public function setSys($sys)
  {
    $this->sys = $sys;
  }
  public function getSys()
  {
    return $this->sys;
  }
  public function setValue($value)
  {
    $this->value = $value;
  }
  public function getValue()
  {
    return $this->value;
  }
  public function setValues($values)
  {
    $this->values = $values;
  }
  public function getValues()
  {
    return $this->values;
  }
}
