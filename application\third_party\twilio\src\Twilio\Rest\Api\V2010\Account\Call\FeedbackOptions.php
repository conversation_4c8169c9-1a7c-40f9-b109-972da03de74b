<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use Twilio\Options;
use Twilio\Values;

abstract class FeedbackOptions
{

    /**
     * @param int $qualityScore The call quality expressed as an integer from `1` to `5` where `1` represents very poor call quality and `5` represents a perfect call.
     * @param string $issue One or more issues experienced during the call. The issues can be: `imperfect-audio`, `dropped-call`, `incorrect-caller-id`, `post-dial-delay`, `digits-not-captured`, `audio-latency`, `unsolicited-call`, or `one-way-audio`.
     * @return UpdateFeedbackOptions Options builder
     */
    public static function update(
        
        int $qualityScore = Values::INT_NONE,
        array $issue = Values::ARRAY_NONE

    ): UpdateFeedbackOptions
    {
        return new UpdateFeedbackOptions(
            $qualityScore,
            $issue
        );
    }

}


class UpdateFeedbackOptions extends Options
    {
    /**
     * @param int $qualityScore The call quality expressed as an integer from `1` to `5` where `1` represents very poor call quality and `5` represents a perfect call.
     * @param string $issue One or more issues experienced during the call. The issues can be: `imperfect-audio`, `dropped-call`, `incorrect-caller-id`, `post-dial-delay`, `digits-not-captured`, `audio-latency`, `unsolicited-call`, or `one-way-audio`.
     */
    public function __construct(
        
        int $qualityScore = Values::INT_NONE,
        array $issue = Values::ARRAY_NONE

    ) {
        $this->options['qualityScore'] = $qualityScore;
        $this->options['issue'] = $issue;
    }

    /**
     * The call quality expressed as an integer from `1` to `5` where `1` represents very poor call quality and `5` represents a perfect call.
     *
     * @param int $qualityScore The call quality expressed as an integer from `1` to `5` where `1` represents very poor call quality and `5` represents a perfect call.
     * @return $this Fluent Builder
     */
    public function setQualityScore(int $qualityScore): self
    {
        $this->options['qualityScore'] = $qualityScore;
        return $this;
    }

    /**
     * One or more issues experienced during the call. The issues can be: `imperfect-audio`, `dropped-call`, `incorrect-caller-id`, `post-dial-delay`, `digits-not-captured`, `audio-latency`, `unsolicited-call`, or `one-way-audio`.
     *
     * @param string $issue One or more issues experienced during the call. The issues can be: `imperfect-audio`, `dropped-call`, `incorrect-caller-id`, `post-dial-delay`, `digits-not-captured`, `audio-latency`, `unsolicited-call`, or `one-way-audio`.
     * @return $this Fluent Builder
     */
    public function setIssue(array $issue): self
    {
        $this->options['issue'] = $issue;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.UpdateFeedbackOptions ' . $options . ']';
    }
}

