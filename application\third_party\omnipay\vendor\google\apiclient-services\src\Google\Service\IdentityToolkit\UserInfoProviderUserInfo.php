<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_IdentityToolkit_UserInfoProviderUserInfo extends Google_Model
{
  public $displayName;
  public $email;
  public $federatedId;
  public $photoUrl;
  public $providerId;
  public $rawId;
  public $screenName;

  public function setDisplayName($displayName)
  {
    $this->displayName = $displayName;
  }
  public function getDisplayName()
  {
    return $this->displayName;
  }
  public function setEmail($email)
  {
    $this->email = $email;
  }
  public function getEmail()
  {
    return $this->email;
  }
  public function setFederatedId($federatedId)
  {
    $this->federatedId = $federatedId;
  }
  public function getFederatedId()
  {
    return $this->federatedId;
  }
  public function setPhotoUrl($photoUrl)
  {
    $this->photoUrl = $photoUrl;
  }
  public function getPhotoUrl()
  {
    return $this->photoUrl;
  }
  public function setProviderId($providerId)
  {
    $this->providerId = $providerId;
  }
  public function getProviderId()
  {
    return $this->providerId;
  }
  public function setRawId($rawId)
  {
    $this->rawId = $rawId;
  }
  public function getRawId()
  {
    return $this->rawId;
  }
  public function setScreenName($screenName)
  {
    $this->screenName = $screenName;
  }
  public function getScreenName()
  {
    return $this->screenName;
  }
}
