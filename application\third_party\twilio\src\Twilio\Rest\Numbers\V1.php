<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Numbers;

use Twilio\Domain;
use Twilio\Exceptions\TwilioException;
use Twilio\InstanceContext;
use Twilio\Rest\Numbers\V1\BulkEligibilityList;
use Twilio\Rest\Numbers\V1\PortingBulkPortabilityList;
use Twilio\Rest\Numbers\V1\PortingPortabilityList;
use Twilio\Version;

/**
 * @property BulkEligibilityList $bulkEligibilities
 * @property PortingBulkPortabilityList $portingBulkPortabilities
 * @property PortingPortabilityList $portingPortabilities
 * @method \Twilio\Rest\Numbers\V1\BulkEligibilityContext bulkEligibilities(string $requestId)
 * @method \Twilio\Rest\Numbers\V1\PortingBulkPortabilityContext portingBulkPortabilities(string $sid)
 * @method \Twilio\Rest\Numbers\V1\PortingPortabilityContext portingPortabilities(string $phoneNumber)
 */
class V1 extends Version
{
    protected $_bulkEligibilities;
    protected $_portingBulkPortabilities;
    protected $_portingPortabilities;

    /**
     * Construct the V1 version of Numbers
     *
     * @param Domain $domain Domain that contains the version
     */
    public function __construct(Domain $domain)
    {
        parent::__construct($domain);
        $this->version = 'v1';
    }

    protected function getBulkEligibilities(): BulkEligibilityList
    {
        if (!$this->_bulkEligibilities) {
            $this->_bulkEligibilities = new BulkEligibilityList($this);
        }
        return $this->_bulkEligibilities;
    }

    protected function getPortingBulkPortabilities(): PortingBulkPortabilityList
    {
        if (!$this->_portingBulkPortabilities) {
            $this->_portingBulkPortabilities = new PortingBulkPortabilityList($this);
        }
        return $this->_portingBulkPortabilities;
    }

    protected function getPortingPortabilities(): PortingPortabilityList
    {
        if (!$this->_portingPortabilities) {
            $this->_portingPortabilities = new PortingPortabilityList($this);
        }
        return $this->_portingPortabilities;
    }

    /**
     * Magic getter to lazy load root resources
     *
     * @param string $name Resource to return
     * @return \Twilio\ListResource The requested resource
     * @throws TwilioException For unknown resource
     */
    public function __get(string $name)
    {
        $method = 'get' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return $this->$method();
        }

        throw new TwilioException('Unknown resource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Numbers.V1]';
    }
}
