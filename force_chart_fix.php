<?php
/**
 * Force Chart Fix - Alternative Implementation
 * This file provides an alternative approach to fix the pie charts
 * Upload this to your website root and include it in your dashboard
 */

// Force clear any output buffering that might interfere
if (ob_get_level()) {
    ob_end_clean();
}

// Set proper headers
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Chart Fix Test</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        .chart-container {
            width: 400px;
            height: 300px;
            margin: 20px;
            border: 1px solid #ccc;
            display: inline-block;
        }
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>🔧 Force Chart Fix Test</h1>
    <p>This page tests if ECharts can render on your server with the exact same logic as your dashboard.</p>
    
    <div id="status"></div>
    
    <div class="chart-container" id="test_income_chart"></div>
    <div class="chart-container" id="test_student_chart"></div>

    <script>
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.className = 'status ' + type;
            status.innerHTML = message;
        }

        // Test if ECharts is loaded
        if (typeof echarts === 'undefined') {
            showStatus('❌ ECharts library failed to load from CDN. Check your internet connection.', 'error');
        } else {
            showStatus('✅ ECharts library loaded successfully!', 'success');
            
            // Initialize charts with the exact same logic as your dashboard
            try {
                // Income vs Expense Chart
                const incomeChart = echarts.init(document.getElementById('test_income_chart'));
                
                // Simulate zero data scenario (most common issue)
                let incomeExpenseData = [
                    {name: 'expense', value: 0},
                    {name: 'income', value: 0}
                ];
                
                // Apply the same fix logic
                if (!incomeExpenseData || incomeExpenseData.length === 0 || 
                    (incomeExpenseData.length === 2 && incomeExpenseData[0].value === 0 && incomeExpenseData[1].value === 0)) {
                    incomeExpenseData = [
                        {name: 'Income', value: 0.01, itemStyle: {color: '#10b981'}},
                        {name: 'Expense', value: 0.01, itemStyle: {color: '#3b82f6'}}
                    ];
                } else {
                    if (incomeExpenseData.length >= 2) {
                        incomeExpenseData[0].itemStyle = {color: '#3b82f6'};
                        incomeExpenseData[1].itemStyle = {color: '#10b981'};
                    }
                }
                
                const incomeOption = {
                    title: {
                        text: 'Income vs Expense Test',
                        left: 'center',
                        textStyle: { color: '#333' }
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            if (params.value <= 0.01) {
                                return params.seriesName + '<br/>' + params.name + ' : $0.00 (0%)';
                            }
                            return params.seriesName + '<br/>' + params.name + ' : $' + params.value + ' (' + params.percent + '%)';
                        }
                    },
                    legend: { show: false },
                    color: ["#3b82f6", "#10b981"],
                    series: [{
                        name: 'Transaction',
                        type: 'pie',
                        radius: ['75%', '90%'],
                        center: ['50%', '50%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 4,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: { show: false },
                        emphasis: {
                            label: { show: false },
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        labelLine: { show: false },
                        data: incomeExpenseData,
                        animationType: 'scale',
                        animationEasing: 'elasticOut',
                        animationDelay: function (idx) {
                            return Math.random() * 200;
                        }
                    }]
                };
                
                incomeChart.setOption(incomeOption);
                
                // Student Quantity Chart
                const studentChart = echarts.init(document.getElementById('test_student_chart'));
                
                const modernColors = [
                    '#10b981', '#3b82f6', '#06b6d4', '#8b5cf6', '#f59e0b', 
                    '#ef4444', '#84cc16', '#f97316', '#ec4899', '#6366f1',
                    '#14b8a6', '#a855f7'
                ];
                
                // Simulate zero data scenario
                let strength_data = [
                    {value: 0, name: 'not_found_anything'}
                ];
                
                // Apply the same fix logic
                if (!strength_data || strength_data.length === 0 || 
                    (strength_data.length === 1 && strength_data[0].value === 0)) {
                    strength_data = [
                        {
                            name: 'No Data Available', 
                            value: 1, 
                            itemStyle: {
                                color: '#64748b',
                                opacity: 0.6
                            }
                        }
                    ];
                }
                
                // Apply colors
                for (let i = 0; i < strength_data.length; i++) {
                    if (!strength_data[i].itemStyle) {
                        strength_data[i].itemStyle = {};
                    }
                    strength_data[i].itemStyle.color = modernColors[i % modernColors.length];
                }
                
                const studentOption = {
                    title: {
                        text: 'Student Quantity Test',
                        left: 'center',
                        textStyle: { color: '#333' }
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            if (params.name === 'No Data Available' && params.value === 1) {
                                return params.seriesName + '<br/>' + params.name + ' : 0 (0%)';
                            }
                            return params.seriesName + '<br/>' + params.name + ' : ' + params.value + ' (' + params.percent + '%)';
                        }
                    },
                    legend: {
                        type: 'scroll',
                        orient: 'horizontal',
                        x: 'center',
                        y: 'bottom',
                        itemWidth: 14,
                        itemHeight: 14,
                        itemGap: 8,
                        textStyle: {
                            fontSize: 12,
                            color: '#475569'
                        },
                        inactiveColor: '#9ca3af'
                    },
                    series: [{
                        name: 'Strength',
                        type: 'pie',
                        radius: ['70%', '85%'],
                        center: ['50%', '46%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 4,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: { show: false },
                        emphasis: {
                            label: { show: false },
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        labelLine: { show: false },
                        data: strength_data,
                        animationType: 'scale',
                        animationEasing: 'elasticOut',
                        animationDelay: function (idx) {
                            return Math.random() * 200;
                        }
                    }]
                };
                
                studentChart.setOption(studentOption);
                
                showStatus('✅ Both charts initialized successfully! If you see the charts above, the fix works on your server.', 'success');
                
                // Handle resize
                window.addEventListener('resize', function() {
                    incomeChart.resize();
                    studentChart.resize();
                });
                
            } catch (error) {
                showStatus('❌ Error initializing charts: ' + error.message, 'error');
                console.error('Chart initialization error:', error);
            }
        }
    </script>

    <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h3>🔍 Troubleshooting Steps:</h3>
        <ol>
            <li><strong>If you see charts above:</strong> The fix works! Your dashboard files might not have uploaded correctly.</li>
            <li><strong>If you see empty containers:</strong> There's a server-side issue preventing ECharts from loading.</li>
            <li><strong>Check browser console (F12):</strong> Look for JavaScript errors that might prevent chart rendering.</li>
            <li><strong>Verify file uploads:</strong> Use the production_debug.php script to check if your files uploaded correctly.</li>
        </ol>
        
        <h3>📋 Next Actions:</h3>
        <ul>
            <li>Upload <code>production_debug.php</code> to your website root and access it</li>
            <li>Check the results and report back what you find</li>
            <li>If this test page shows charts but your dashboard doesn't, we'll need to force-refresh your dashboard files</li>
        </ul>
    </div>
</body>
</html>
