<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Dfareporting_VideoFormat extends Google_Model
{
  public $fileType;
  public $id;
  public $kind;
  protected $resolutionType = 'Google_Service_Dfareporting_Size';
  protected $resolutionDataType = '';
  public $targetBitRate;

  public function setFileType($fileType)
  {
    $this->fileType = $fileType;
  }
  public function getFileType()
  {
    return $this->fileType;
  }
  public function setId($id)
  {
    $this->id = $id;
  }
  public function getId()
  {
    return $this->id;
  }
  public function setKind($kind)
  {
    $this->kind = $kind;
  }
  public function getKind()
  {
    return $this->kind;
  }
  public function setResolution(Google_Service_Dfareporting_Size $resolution)
  {
    $this->resolution = $resolution;
  }
  public function getResolution()
  {
    return $this->resolution;
  }
  public function setTargetBitRate($targetBitRate)
  {
    $this->targetBitRate = $targetBitRate;
  }
  public function getTargetBitRate()
  {
    return $this->targetBitRate;
  }
}
