<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Preview\DeployedDevices\Fleet;

use Twilio\Options;
use Twilio\Values;

abstract class DeploymentOptions
{
    /**
     * @param string $friendlyName Provides a human readable descriptive text for this Deployment, up to 256 characters long.
     * @param string $syncServiceSid Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.
     * @return CreateDeploymentOptions Options builder
     */
    public static function create(
        
        string $friendlyName = Values::NONE,
        string $syncServiceSid = Values::NONE

    ): CreateDeploymentOptions
    {
        return new CreateDeploymentOptions(
            $friendlyName,
            $syncServiceSid
        );
    }




    /**
     * @param string $friendlyName Provides a human readable descriptive text for this Deployment, up to 64 characters long
     * @param string $syncServiceSid Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.
     * @return UpdateDeploymentOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $syncServiceSid = Values::NONE

    ): UpdateDeploymentOptions
    {
        return new UpdateDeploymentOptions(
            $friendlyName,
            $syncServiceSid
        );
    }

}

class CreateDeploymentOptions extends Options
    {
    /**
     * @param string $friendlyName Provides a human readable descriptive text for this Deployment, up to 256 characters long.
     * @param string $syncServiceSid Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $syncServiceSid = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['syncServiceSid'] = $syncServiceSid;
    }

    /**
     * Provides a human readable descriptive text for this Deployment, up to 256 characters long.
     *
     * @param string $friendlyName Provides a human readable descriptive text for this Deployment, up to 256 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.
     *
     * @param string $syncServiceSid Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.
     * @return $this Fluent Builder
     */
    public function setSyncServiceSid(string $syncServiceSid): self
    {
        $this->options['syncServiceSid'] = $syncServiceSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.DeployedDevices.CreateDeploymentOptions ' . $options . ']';
    }
}




class UpdateDeploymentOptions extends Options
    {
    /**
     * @param string $friendlyName Provides a human readable descriptive text for this Deployment, up to 64 characters long
     * @param string $syncServiceSid Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $syncServiceSid = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['syncServiceSid'] = $syncServiceSid;
    }

    /**
     * Provides a human readable descriptive text for this Deployment, up to 64 characters long
     *
     * @param string $friendlyName Provides a human readable descriptive text for this Deployment, up to 64 characters long
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.
     *
     * @param string $syncServiceSid Provides the unique string identifier of the Twilio Sync service instance that will be linked to and accessible by this Deployment.
     * @return $this Fluent Builder
     */
    public function setSyncServiceSid(string $syncServiceSid): self
    {
        $this->options['syncServiceSid'] = $syncServiceSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.DeployedDevices.UpdateDeploymentOptions ' . $options . ']';
    }
}

