<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

/**
 * The "entities" collection of methods.
 * Typical usage is:
 *  <code>
 *   $tagmanagerService = new Google_Service_TagManager(...);
 *   $entities = $tagmanagerService->entities;
 *  </code>
 */
class Google_Service_TagManager_Resource_AccountsContainersFoldersEntities extends Google_Service_Resource
{
  /**
   * List all entities in a GTM Folder.
   * (entities.listAccountsContainersFoldersEntities)
   *
   * @param string $accountId The GTM Account ID.
   * @param string $containerId The GTM Container ID.
   * @param string $folderId The GTM Folder ID.
   * @param array $optParams Optional parameters.
   * @return Google_Service_TagManager_FolderEntities
   */
  public function listAccountsContainersFoldersEntities($accountId, $containerId, $folderId, $optParams = array())
  {
    $params = array('accountId' => $accountId, 'containerId' => $containerId, 'folderId' => $folderId);
    $params = array_merge($params, $optParams);
    return $this->call('list', array($params), "Google_Service_TagManager_FolderEntities");
  }
}
