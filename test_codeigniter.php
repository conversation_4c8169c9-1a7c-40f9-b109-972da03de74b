<?php
/**
 * CodeIgniter Bootstrap Test for Hostinger
 * Upload this to: /domains/passdrc.com/public_html/school/
 * Access via: https://school.passdrc.com/test_codeigniter.php
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🚀 CodeIgniter Bootstrap Test</h1>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";

// Test 1: Check if we can define constants
echo "<h2>🔧 Constants Test</h2>";
try {
    $system_path = 'system';
    $application_folder = 'application';
    
    // Check if system path exists
    if (!is_dir($system_path)) {
        echo "<p>❌ <strong>System directory not found:</strong> $system_path</p>";
        exit;
    }
    
    // Resolve system path
    if (($_temp = realpath($system_path)) !== FALSE) {
        $system_path = $_temp . DIRECTORY_SEPARATOR;
    } else {
        $system_path = strtr(
            rtrim($system_path, '/\\'),
            '/\\',
            DIRECTORY_SEPARATOR . DIRECTORY_SEPARATOR
        ) . DIRECTORY_SEPARATOR;
    }
    
    // Check if application folder exists
    if (!is_dir($application_folder)) {
        echo "<p>❌ <strong>Application directory not found:</strong> $application_folder</p>";
        exit;
    }
    
    // Define constants
    define('BASEPATH', $system_path);
    define('FCPATH', dirname(__FILE__) . DIRECTORY_SEPARATOR);
    define('SYSDIR', basename(BASEPATH));
    define('APPPATH', $application_folder . DIRECTORY_SEPARATOR);
    
    echo "<p>✅ <strong>Constants defined successfully</strong></p>";
    echo "<p><strong>BASEPATH:</strong> " . BASEPATH . "</p>";
    echo "<p><strong>APPPATH:</strong> " . APPPATH . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Constants Error:</strong> " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Check CodeIgniter core file
echo "<h2>📁 CodeIgniter Core Check</h2>";
$codeigniter_file = BASEPATH . 'core/CodeIgniter.php';
if (file_exists($codeigniter_file)) {
    echo "<p>✅ <strong>CodeIgniter core file found</strong></p>";
    echo "<p><strong>Location:</strong> $codeigniter_file</p>";
} else {
    echo "<p>❌ <strong>CodeIgniter core file missing:</strong> $codeigniter_file</p>";
    exit;
}

// Test 3: Check application config files
echo "<h2>⚙️ Application Config Check</h2>";
$config_files = [
    'config.php',
    'database.php',
    'routes.php',
    'autoload.php'
];

foreach ($config_files as $config_file) {
    $file_path = APPPATH . 'config/' . $config_file;
    if (file_exists($file_path)) {
        echo "<p>✅ <strong>$config_file:</strong> Found</p>";
    } else {
        echo "<p>❌ <strong>$config_file:</strong> Missing</p>";
    }
}

// Test 4: Try to include config files
echo "<h2>📋 Config Loading Test</h2>";
try {
    // Test config.php
    $config_path = APPPATH . 'config/config.php';
    if (file_exists($config_path)) {
        include $config_path;
        if (isset($config['base_url'])) {
            echo "<p>✅ <strong>config.php loaded successfully</strong></p>";
            echo "<p><strong>Base URL:</strong> " . $config['base_url'] . "</p>";
        } else {
            echo "<p>⚠️ <strong>config.php loaded but base_url not set</strong></p>";
        }
    }
    
    // Test database.php
    $db_config_path = APPPATH . 'config/database.php';
    if (file_exists($db_config_path)) {
        include $db_config_path;
        if (isset($db['default'])) {
            echo "<p>✅ <strong>database.php loaded successfully</strong></p>";
            echo "<p><strong>DB Driver:</strong> " . $db['default']['dbdriver'] . "</p>";
        } else {
            echo "<p>⚠️ <strong>database.php loaded but config not found</strong></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Config Loading Error:</strong> " . $e->getMessage() . "</p>";
}

// Test 5: Check permissions
echo "<h2>🔐 Permissions Check</h2>";
$dirs_to_check = [
    APPPATH . 'cache',
    APPPATH . 'logs',
    'uploads'
];

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir);
        echo "<p><strong>$dir:</strong> " . ($writable ? "✅ Writable" : "❌ Not Writable") . "</p>";
    } else {
        echo "<p><strong>$dir:</strong> ❌ Directory not found</p>";
    }
}

echo "<hr>";
echo "<h2>🎯 Diagnosis Summary</h2>";
echo "<p>If all tests above pass, try accessing the main application:</p>";
echo "<p><a href='index.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none;'>Test Main Application</a></p>";
echo "<p><em>If the main application still shows 500 error, check Hostinger error logs.</em></p>";
?>
