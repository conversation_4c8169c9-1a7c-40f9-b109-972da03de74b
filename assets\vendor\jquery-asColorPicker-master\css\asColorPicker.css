.asColorPicker-wrap {
  position: relative;
  display: inline-block;
}
.asColorPicker_hideInput {
  display: none;
}
.asColorPicker_hideInput .asColorPicker-clear {
  display: none;
}
.asColorPicker-dropdown {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  display: none;
  position: absolute;
  z-index: 9999;
}
.asColorPicker-dropdown * {
  margin: 0;
  padding: 0;
}
.asColorPicker_open {
  display: block;
}
.asColorPicker-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9998;
}
.asColorPicker-trigger {
  display: inline-block;
  position: relative;
}
.asColorPicker-trigger {
  cursor: pointer;
  width: 18px;
  height: 20px;
  background-image: url('../images/transparent.png');
}
.asColorPicker-trigger span {
  width: 100%;
  height: 100%;
  display: inline-block;
}
.asColorPicker-input,
.asColorPicker-trigger {
  vertical-align: middle;
}
.asColorPicker-clear {
  display: none;
  position: absolute;
  top: 0;
  right: 26px;
  color: #777;
  text-decoration: none;
}
.asColorPicker-clear:after {
  content: 'x';
}
.asColorPicker-wrap:hover .asColorPicker-clear {
  display: inline-block;
}
.asColorPicker-preview {
  float: left;
  list-style: none;
}
.asColorPicker-preview li {
  background-image: url('../images/transparent.png');
  vertical-align: top;
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
.asColorPicker-preview li span {
  height: 100%;
  display: block;
}
.asColorPicker-preview-previous {
  cursor: pointer;
}
.asColorPicker-palettes ul {
  display: block;
}
.asColorPicker-palettes ul:before,
.asColorPicker-palettes ul:after {
  content: "";
  display: table;
}
.asColorPicker-palettes ul:after {
  clear: both;
}
.asColorPicker-palettes li {
  background-image: url('../images/transparent.png');
  display: block;
  float: left;
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer;
}
.asColorPicker-palettes li span {
  height: 100%;
  display: block;
}
.asColorPicker-saturation {
  clear: both;
  position: relative;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  width: 175px;
  height: 175px;
  background-image: url("../images/saturation.png");
}
.asColorPicker-saturation i {
  position: absolute;
}
.asColorPicker-hue,
.asColorPicker-alpha {
  cursor: pointer;
  position: relative;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  width: 20px;
  height: 175px;
}
.asColorPicker-hue i,
.asColorPicker-alpha i {
  position: absolute;
  cursor: row-resize;
}
.asColorPicker-hue {
  background-image: url('../images/hue.png');
}
.asColorPicker-alpha {
  background-image: url('../images/alpha.png');
}
.asColorPicker-buttons a,
.asColorPicker-gradient-control a {
  text-decoration: none;
  cursor: pointer;
}
.asColorPicker-gradient {
  display: none;
}
.asColorPicker-gradient_enable {
  display: block;
}
.asColorPicker-gradient-preview {
  float: left;
  height: 20px;
}
.asColorPicker-gradient-markers {
  position: relative;
  width: 100%;
}
.asColorPicker-gradient-marker {
  position: absolute;
  outline: none;
}
.asColorPicker-gradient-wheel {
  float: left;
  position: relative;
  border: 1px solid #bbbbbb;
  border-radius: 100%;
  width: 20px;
  height: 20px;
}
.asColorPicker-gradient-wheel i {
  width: 3px;
  height: 3px;
  position: absolute;
  border-radius: 100%;
}
.asColorPicker-gradient-angle {
  float: left;
}
.asColorPicker-dropdown {
  background: #fefefe;
  padding: 10px;
  border: 1px solid #bbbbbb;
  min-width: 205px;
  max-width: 235px;
}
[data-mode="palettes"] .asColorPicker-dropdown {
  min-width: auto;
  max-width: auto;
}
.asColorPicker-trigger {
  border: 1px solid #bbbbbb;
}
.asColorPicker-saturation {
  -webkit-box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
          box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
}
.asColorPicker-saturation i {
  width: 5px;
  height: 5px;
  margin-left: -2px;
  margin-top: -2px;
  border-radius: 100%;
  border: 2px
			solid #fff;
}
.asColorPicker-hue,
.asColorPicker-alpha {
  margin-left: 10px;
  -webkit-box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
          box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
}
.asColorPicker-hue i,
.asColorPicker-alpha i {
  width: 20px;
  height: 2px;
  margin-top: -2px;
  left: -2px;
  border: 2px solid #fff;
}
.asColorPicker-preview {
  position: relative;
  height: 33px;
  margin-bottom: 10px;
  margin-right: 10px;
}
.asColorPicker-preview:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
          box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
  content: '';
  pointer-events: none;
}
.asColorPicker-preview li {
  width: 48px;
  height: 33px;
}
.asColorPicker-hex {
  width: 100px;
  border-color: rgba(0, 0, 0, 0.05);
}
.asColorPicker-palettes li {
  width: 21px;
  height: 15px;
  margin-right: 6px;
  margin-bottom: 3px;
}
.asColorPicker-palettes li span {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.05);
}
.asColorPicker-palettes li:nth-child(5n) {
  margin-right: 0;
}
[data-mode="palettes"] .asColorPicker-palettes li:nth-child(5n) {
  margin-right: 6px;
}
.asColorPicker-buttons,
.asColorPicker-gradient-control {
  float: right;
}
.asColorPicker-buttons a,
.asColorPicker-gradient-control a {
  margin-left: 5px;
}
.asColorPicker-gradient {
  margin-top: 10px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}
.asColorPicker-gradient-preview {
  position: relative;
  width: 160px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}
.asColorPicker-gradient-preview:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../images/transparent.png');
  content: '';
  z-index: -1;
}
.asColorPicker-gradient-markers {
  top: -16px;
  width: 160px;
  height: 16px;
  display: block;
  list-style: none;
  margin: 0;
  padding: 0;
}
.asColorPicker-gradient-marker {
  width: 10px;
  height: 10px;
  margin-left: -6px;
  border: 1px solid #bbbbbb;
  background: #fff;
}
.asColorPicker-gradient-marker span {
  display: block;
  width: 100%;
  height: 100%;
}
.asColorPicker-gradient-marker i {
  position: absolute;
  left: 2px;
  bottom: -3px;
  width: 4px;
  height: 4px;
  border: 1px solid transparent;
  border-right-color: rgba(0, 0, 0, 0.05);
  border-bottom-color: rgba(0, 0, 0, 0.05);
  background: #fff;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
   -o-transform: rotate(45deg);
      transform: rotate(45deg);
}
.asColorPicker-gradient-marker_active {
  border: 2px solid #41a9e5;
  z-index: 1;
}
.asColorPicker-gradient-marker_active i {
  left: 1px;
  border: 2px solid transparent;
  border-right-color: #41a9e5;
  border-bottom-color: #41a9e5;
}
.asColorPicker-gradient-wheel {
  margin-left: 10px;
}
.asColorPicker-gradient-wheel i {
  background-color: #888888;
}
.asColorPicker-gradient-angle {
  margin-left: 10px;
  width: 24px;
}
