<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Dataflow_PubsubLocation extends Google_Model
{
  public $dropLateData;
  public $idLabel;
  public $subscription;
  public $timestampLabel;
  public $topic;
  public $trackingSubscription;
  public $withAttributes;

  public function setDropLateData($dropLateData)
  {
    $this->dropLateData = $dropLateData;
  }
  public function getDropLateData()
  {
    return $this->dropLateData;
  }
  public function setIdLabel($idLabel)
  {
    $this->idLabel = $idLabel;
  }
  public function getIdLabel()
  {
    return $this->idLabel;
  }
  public function setSubscription($subscription)
  {
    $this->subscription = $subscription;
  }
  public function getSubscription()
  {
    return $this->subscription;
  }
  public function setTimestampLabel($timestampLabel)
  {
    $this->timestampLabel = $timestampLabel;
  }
  public function getTimestampLabel()
  {
    return $this->timestampLabel;
  }
  public function setTopic($topic)
  {
    $this->topic = $topic;
  }
  public function getTopic()
  {
    return $this->topic;
  }
  public function setTrackingSubscription($trackingSubscription)
  {
    $this->trackingSubscription = $trackingSubscription;
  }
  public function getTrackingSubscription()
  {
    return $this->trackingSubscription;
  }
  public function setWithAttributes($withAttributes)
  {
    $this->withAttributes = $withAttributes;
  }
  public function getWithAttributes()
  {
    return $this->withAttributes;
  }
}
