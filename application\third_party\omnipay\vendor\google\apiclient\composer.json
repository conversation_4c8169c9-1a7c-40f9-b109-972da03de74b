{"name": "google/apiclient", "type": "library", "description": "Client library for Google APIs", "keywords": ["google"], "homepage": "http://developers.google.com/api-client-library/php", "license": "Apache-2.0", "require": {"php": ">=5.4", "google/auth": "^0.11", "google/apiclient-services": "^0.11", "firebase/php-jwt": "~2.0|~3.0|~4.0", "monolog/monolog": "^1.17", "phpseclib/phpseclib": "~0.3.10|~2.0", "guzzlehttp/guzzle": "~5.2|~6.0", "guzzlehttp/psr7": "^1.2"}, "require-dev": {"phpunit/phpunit": "~4", "squizlabs/php_codesniffer": "~2.3", "symfony/dom-crawler": "~2.1", "symfony/css-selector": "~2.1", "cache/filesystem-adapter": "^0.3.2"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google_Client::setCache)"}, "autoload": {"psr-0": {"Google_": "src/"}, "classmap": ["src/Google/Service/"]}, "extra": {"branch-alias": {"dev-master": "2.x-dev"}}}