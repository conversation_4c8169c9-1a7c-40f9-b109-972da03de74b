<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Serverless
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Serverless\V1\Service;

use Twilio\Options;
use Twilio\Values;

abstract class EnvironmentOptions
{
    /**
     * @param string $domainSuffix A URL-friendly name that represents the environment and forms part of the domain name. It can be a maximum of 16 characters.
     * @return CreateEnvironmentOptions Options builder
     */
    public static function create(
        
        string $domainSuffix = Values::NONE

    ): CreateEnvironmentOptions
    {
        return new CreateEnvironmentOptions(
            $domainSuffix
        );
    }




}

class CreateEnvironmentOptions extends Options
    {
    /**
     * @param string $domainSuffix A URL-friendly name that represents the environment and forms part of the domain name. It can be a maximum of 16 characters.
     */
    public function __construct(
        
        string $domainSuffix = Values::NONE

    ) {
        $this->options['domainSuffix'] = $domainSuffix;
    }

    /**
     * A URL-friendly name that represents the environment and forms part of the domain name. It can be a maximum of 16 characters.
     *
     * @param string $domainSuffix A URL-friendly name that represents the environment and forms part of the domain name. It can be a maximum of 16 characters.
     * @return $this Fluent Builder
     */
    public function setDomainSuffix(string $domainSuffix): self
    {
        $this->options['domainSuffix'] = $domainSuffix;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Serverless.V1.CreateEnvironmentOptions ' . $options . ']';
    }
}




