<?php
/**
 * Email Diagnostic Script for Password Recovery
 * This script helps diagnose email delivery issues in the school management system
 * 
 * Instructions:
 * 1. Place this file in the root directory of your school management system
 * 2. Access it via browser: http://yourdomain.com/email_diagnostic.php
 * 3. Review the diagnostic results to identify email configuration issues
 * 4. Remove this file after diagnosis is complete
 */

// Include CodeIgniter bootstrap
require_once('index.php');

// Get CodeIgniter instance
$CI =& get_instance();

// Load necessary models and libraries
$CI->load->model('authentication_model');
$CI->load->model('email_model');
$CI->load->library('mailer');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Email Diagnostic - Password Recovery</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .diagnostic-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .btn { padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Email Diagnostic - Password Recovery System</h1>
        <p><strong>Note:</strong> This diagnostic script helps identify email delivery issues. Remove it after diagnosis.</p>
        
        <?php
        // 1. Check Email Configuration
        echo '<div class="diagnostic-section">';
        echo '<h2>📧 1. Email Configuration Check</h2>';
        
        try {
            // Get branch ID (assuming default branch)
            $branch_id = 1; // You may need to adjust this
            $email_config = $CI->db->get_where('email_config', array('branch_id' => $branch_id))->row();
            
            if ($email_config) {
                echo '<div class="success"><strong>✓ Email Configuration Found</strong></div>';
                echo '<table>';
                echo '<tr><th>Setting</th><th>Value</th><th>Status</th></tr>';
                echo '<tr><td>Protocol</td><td>' . htmlspecialchars($email_config->protocol) . '</td><td>' . ($email_config->protocol ? '✓' : '❌') . '</td></tr>';
                echo '<tr><td>System Email</td><td>' . htmlspecialchars($email_config->email) . '</td><td>' . (filter_var($email_config->email, FILTER_VALIDATE_EMAIL) ? '✓' : '❌') . '</td></tr>';
                
                if ($email_config->protocol == 'smtp') {
                    echo '<tr><td>SMTP Host</td><td>' . htmlspecialchars($email_config->smtp_host) . '</td><td>' . ($email_config->smtp_host ? '✓' : '❌') . '</td></tr>';
                    echo '<tr><td>SMTP Port</td><td>' . htmlspecialchars($email_config->smtp_port) . '</td><td>' . ($email_config->smtp_port ? '✓' : '❌') . '</td></tr>';
                    echo '<tr><td>SMTP Username</td><td>' . htmlspecialchars($email_config->smtp_user) . '</td><td>' . ($email_config->smtp_user ? '✓' : '❌') . '</td></tr>';
                    echo '<tr><td>SMTP Password</td><td>' . (strlen($email_config->smtp_pass) > 0 ? str_repeat('*', strlen($email_config->smtp_pass)) : 'Not Set') . '</td><td>' . ($email_config->smtp_pass ? '✓' : '❌') . '</td></tr>';
                    echo '<tr><td>SMTP Encryption</td><td>' . htmlspecialchars($email_config->smtp_encryption) . '</td><td>' . ($email_config->smtp_encryption ? '✓' : '⚠️') . '</td></tr>';
                    echo '<tr><td>SMTP Auth</td><td>' . ($email_config->smtp_auth ? 'Enabled' : 'Disabled') . '</td><td>' . ($email_config->smtp_auth ? '✓' : '❌') . '</td></tr>';
                }
                echo '</table>';
            } else {
                echo '<div class="error"><strong>❌ No Email Configuration Found</strong><br>Email configuration is missing for branch ID: ' . $branch_id . '</div>';
            }
        } catch (Exception $e) {
            echo '<div class="error"><strong>❌ Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        echo '</div>';

        // 2. Check Email Template
        echo '<div class="diagnostic-section">';
        echo '<h2>📝 2. Email Template Check (Forgot Password)</h2>';
        
        try {
            $template = $CI->db->where(array('template_id' => 2, 'branch_id' => $branch_id))->get('email_templates_details')->row_array();
            
            if ($template) {
                echo '<div class="success"><strong>✓ Forgot Password Email Template Found</strong></div>';
                echo '<table>';
                echo '<tr><th>Property</th><th>Value</th><th>Status</th></tr>';
                echo '<tr><td>Template ID</td><td>' . htmlspecialchars($template['template_id']) . '</td><td>✓</td></tr>';
                echo '<tr><td>Notifications Enabled</td><td>' . ($template['notified'] ? 'Yes' : 'No') . '</td><td>' . ($template['notified'] ? '✓' : '❌') . '</td></tr>';
                echo '<tr><td>Subject</td><td>' . htmlspecialchars($template['subject']) . '</td><td>' . ($template['subject'] ? '✓' : '❌') . '</td></tr>';
                echo '<tr><td>Template Body Length</td><td>' . strlen($template['template_body']) . ' characters</td><td>' . (strlen($template['template_body']) > 0 ? '✓' : '❌') . '</td></tr>';
                echo '</table>';
                
                if ($template['template_body']) {
                    echo '<h4>Template Preview:</h4>';
                    echo '<div class="code">' . htmlspecialchars(substr($template['template_body'], 0, 500)) . (strlen($template['template_body']) > 500 ? '...' : '') . '</div>';
                }
            } else {
                echo '<div class="error"><strong>❌ Forgot Password Email Template Not Found</strong><br>Template ID 2 is missing for branch ID: ' . $branch_id . '</div>';
            }
        } catch (Exception $e) {
            echo '<div class="error"><strong>❌ Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        echo '</div>';

        // 3. Test Email Sending
        echo '<div class="diagnostic-section">';
        echo '<h2>📤 3. Email Sending Test</h2>';
        
        if (isset($_POST['test_email']) && !empty($_POST['test_email'])) {
            try {
                $test_email = $_POST['test_email'];
                
                // Test basic email sending
                $data = array(
                    'branch_id' => $branch_id,
                    'recipient' => $test_email,
                    'subject' => 'PASS-DRC Email Test - Password Recovery Diagnostic',
                    'message' => '<h2>Email Test Successful!</h2><p>This is a test email from your PASS-DRC school management system.</p><p>If you received this email, your email configuration is working correctly.</p><p>Test performed on: ' . date('Y-m-d H:i:s') . '</p>'
                );
                
                $result = $CI->mailer->send($data, true);
                
                if ($result === true) {
                    echo '<div class="success"><strong>✓ Test Email Sent Successfully!</strong><br>Check the inbox (and spam folder) of: ' . htmlspecialchars($test_email) . '</div>';
                } else {
                    echo '<div class="error"><strong>❌ Test Email Failed</strong><br>Error: ' . htmlspecialchars($result) . '</div>';
                }
            } catch (Exception $e) {
                echo '<div class="error"><strong>❌ Exception:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
        }
        
        echo '<form method="post" style="margin-top: 15px;">';
        echo '<label for="test_email">Test Email Address:</label><br>';
        echo '<input type="email" name="test_email" id="test_email" placeholder="<EMAIL>" required style="padding: 8px; width: 300px; margin: 5px 0;">';
        echo '<button type="submit" class="btn">Send Test Email</button>';
        echo '</form>';
        echo '</div>';

        // 4. Check PHPMailer
        echo '<div class="diagnostic-section">';
        echo '<h2>📦 4. PHPMailer Check</h2>';
        
        try {
            if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
                echo '<div class="success"><strong>✓ PHPMailer Library Available</strong></div>';
                
                // Check PHPMailer version if possible
                $reflection = new ReflectionClass('PHPMailer\PHPMailer\PHPMailer');
                $phpmailer_path = dirname($reflection->getFileName());
                echo '<p><strong>PHPMailer Path:</strong> ' . htmlspecialchars($phpmailer_path) . '</p>';
            } else {
                echo '<div class="error"><strong>❌ PHPMailer Library Not Found</strong><br>The PHPMailer library is required for email sending.</div>';
            }
        } catch (Exception $e) {
            echo '<div class="error"><strong>❌ Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        echo '</div>';

        // 5. Server Environment Check
        echo '<div class="diagnostic-section">';
        echo '<h2>🖥️ 5. Server Environment Check</h2>';
        
        echo '<table>';
        echo '<tr><th>Setting</th><th>Value</th><th>Status</th></tr>';
        echo '<tr><td>PHP Version</td><td>' . phpversion() . '</td><td>' . (version_compare(phpversion(), '7.0', '>=') ? '✓' : '❌') . '</td></tr>';
        echo '<tr><td>OpenSSL Extension</td><td>' . (extension_loaded('openssl') ? 'Loaded' : 'Not Loaded') . '</td><td>' . (extension_loaded('openssl') ? '✓' : '❌') . '</td></tr>';
        echo '<tr><td>cURL Extension</td><td>' . (extension_loaded('curl') ? 'Loaded' : 'Not Loaded') . '</td><td>' . (extension_loaded('curl') ? '✓' : '⚠️') . '</td></tr>';
        echo '<tr><td>Mail Function</td><td>' . (function_exists('mail') ? 'Available' : 'Not Available') . '</td><td>' . (function_exists('mail') ? '✓' : '❌') . '</td></tr>';
        echo '<tr><td>SMTP Support</td><td>' . (extension_loaded('openssl') && function_exists('fsockopen') ? 'Available' : 'Limited') . '</td><td>' . (extension_loaded('openssl') && function_exists('fsockopen') ? '✓' : '⚠️') . '</td></tr>';
        echo '</table>';
        echo '</div>';
        ?>

        <div class="diagnostic-section info">
            <h2>🔧 Recommended Actions</h2>
            <ol>
                <li><strong>If email configuration is missing:</strong> Go to School Settings → Email Configuration and set up SMTP settings</li>
                <li><strong>If email template is missing:</strong> Check the email_templates_details table in your database</li>
                <li><strong>For Hostinger SMTP settings:</strong>
                    <ul>
                        <li>SMTP Host: mail.yourdomain.com (or smtp.hostinger.com)</li>
                        <li>SMTP Port: 587 (recommended) or 465</li>
                        <li>SMTP Encryption: TLS (for port 587) or SSL (for port 465)</li>
                        <li>SMTP Auth: Enabled</li>
                        <li>Username: Your full email address</li>
                        <li>Password: Your email password</li>
                    </ul>
                </li>
                <li><strong>Test email delivery:</strong> Use the test form above to verify email sending works</li>
                <li><strong>Check spam folders:</strong> Password recovery emails might be filtered as spam</li>
            </ol>
        </div>

        <div class="diagnostic-section warning">
            <h2>⚠️ Security Note</h2>
            <p><strong>Important:</strong> This diagnostic script exposes sensitive information about your email configuration. 
            <strong>Delete this file immediately after diagnosis</strong> to prevent security risks.</p>
            <a href="<?php echo base_url('authentication/forgot'); ?>" class="btn">Go to Password Recovery Page</a>
        </div>
    </div>
</body>
</html>
