<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_CloudBuild_RepoSource extends Google_Model
{
  public $branchName;
  public $commitSha;
  public $projectId;
  public $repoName;
  public $tagName;

  public function setBranchName($branchName)
  {
    $this->branchName = $branchName;
  }
  public function getBranchName()
  {
    return $this->branchName;
  }
  public function setCommitSha($commitSha)
  {
    $this->commitSha = $commitSha;
  }
  public function getCommitSha()
  {
    return $this->commitSha;
  }
  public function setProjectId($projectId)
  {
    $this->projectId = $projectId;
  }
  public function getProjectId()
  {
    return $this->projectId;
  }
  public function setRepoName($repoName)
  {
    $this->repoName = $repoName;
  }
  public function getRepoName()
  {
    return $this->repoName;
  }
  public function setTagName($tagName)
  {
    $this->tagName = $tagName;
  }
  public function getTagName()
  {
    return $this->tagName;
  }
}
