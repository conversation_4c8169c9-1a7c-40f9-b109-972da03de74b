<?php
/**
 * Standalone Email Test - No Framework Dependencies
 * This script tests email delivery without using CodeIgniter
 * 
 * Instructions:
 * 1. Upload this file to your Hostinger root directory
 * 2. Access it via: https://passdrc.com/public_html/school/standalone_email_test.php
 * 3. Test different email methods
 * 4. Delete this file after testing
 */

$test_results = array();
$email_sent = false;

// Handle form submission
if ($_POST && isset($_POST['test_email'])) {
    $test_email = $_POST['test_email'];
    $test_method = $_POST['test_method'];
    
    switch ($test_method) {
        case 'php_mail':
            $test_results = test_php_mail($test_email);
            break;
        case 'smtp_basic':
            $test_results = test_smtp_basic($test_email);
            break;
        case 'smtp_hostinger':
            $test_results = test_smtp_hostinger($test_email);
            break;
    }
    
    $email_sent = true;
}

function test_php_mail($to_email) {
    $subject = "PASS-DRC Standalone Test - PHP Mail";
    $message = "
    <html>
    <head>
        <title>Email Test</title>
    </head>
    <body>
        <h2>PASS-DRC Email Test Successful!</h2>
        <p>This email was sent using PHP's built-in mail() function.</p>
        <p><strong>Test Details:</strong></p>
        <ul>
            <li>Method: PHP mail() function</li>
            <li>Server: " . $_SERVER['SERVER_NAME'] . "</li>
            <li>Time: " . date('Y-m-d H:i:s') . "</li>
            <li>From: <EMAIL></li>
        </ul>
        <p>If you received this email, PHP mail is working on your Hostinger server.</p>
        <hr>
        <p><small>This is an automated test email from your PASS-DRC school management system.</small></p>
    </body>
    </html>
    ";
    
    $headers = array(
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: PASS-DRC System <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'X-Mailer: PHP/' . phpversion(),
        'X-Priority: 3',
        'Return-Path: <EMAIL>'
    );
    
    $result = mail($to_email, $subject, $message, implode("\r\n", $headers));
    
    return array(
        'success' => $result,
        'method' => 'PHP mail() Function',
        'message' => $result ? 'Email sent successfully!' : 'PHP mail() function failed',
        'details' => array(
            'To: ' . $to_email,
            'From: <EMAIL>',
            'Subject: ' . $subject,
            'Headers: ' . implode(', ', $headers)
        )
    );
}

function test_smtp_basic($to_email) {
    // Basic SMTP test using fsockopen
    $smtp_host = 'mail.passdrc.com';
    $smtp_port = 587;
    
    $connection = @fsockopen($smtp_host, $smtp_port, $errno, $errstr, 30);
    
    if (!$connection) {
        return array(
            'success' => false,
            'method' => 'Basic SMTP Connection',
            'message' => "Connection failed: $errstr ($errno)",
            'details' => array(
                'Host: ' . $smtp_host,
                'Port: ' . $smtp_port,
                'Error: ' . $errstr
            )
        );
    }
    
    // Read server response
    $response = fgets($connection, 512);
    fclose($connection);
    
    return array(
        'success' => true,
        'method' => 'Basic SMTP Connection',
        'message' => 'SMTP server connection successful',
        'details' => array(
            'Host: ' . $smtp_host,
            'Port: ' . $smtp_port,
            'Response: ' . trim($response)
        )
    );
}

function test_smtp_hostinger($to_email) {
    // Test Hostinger's SMTP server
    $smtp_servers = array(
        'mail.passdrc.com:587',
        'mail.passdrc.com:465',
        'smtp.hostinger.com:587',
        'smtp.hostinger.com:465'
    );
    
    $results = array();
    $success_count = 0;
    
    foreach ($smtp_servers as $server) {
        list($host, $port) = explode(':', $server);
        $connection = @fsockopen($host, $port, $errno, $errstr, 10);
        
        if ($connection) {
            $response = fgets($connection, 512);
            $results[] = "✓ $server - Connected: " . trim($response);
            $success_count++;
            fclose($connection);
        } else {
            $results[] = "❌ $server - Failed: $errstr";
        }
    }
    
    return array(
        'success' => $success_count > 0,
        'method' => 'Hostinger SMTP Servers',
        'message' => "$success_count out of " . count($smtp_servers) . " servers accessible",
        'details' => $results
    );
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Standalone Email Test - PASS-DRC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Standalone Email Test</h1>
        <p><strong>Purpose:</strong> Test email delivery without CodeIgniter framework dependencies.</p>
        
        <!-- Test Results -->
        <?php if ($email_sent): ?>
            <div class="section <?php echo $test_results['success'] ? 'success' : 'error'; ?>">
                <h3><?php echo $test_results['success'] ? '✓' : '❌'; ?> <?php echo htmlspecialchars($test_results['method']); ?></h3>
                <p><strong>Result:</strong> <?php echo htmlspecialchars($test_results['message']); ?></p>
                
                <?php if (isset($test_results['details'])): ?>
                    <h4>Details:</h4>
                    <ul>
                        <?php foreach ($test_results['details'] as $detail): ?>
                            <li><?php echo htmlspecialchars($detail); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
                
                <?php if ($test_results['success']): ?>
                    <div class="info" style="margin-top: 15px;">
                        <strong>Next Step:</strong> Check your email inbox (and spam folder) for the test email.
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- Email Test Form -->
        <div class="section">
            <h2>🧪 Email Delivery Test</h2>
            <form method="post">
                <div class="form-group">
                    <label for="test_email">Your Email Address:</label>
                    <input type="email" name="test_email" id="test_email" 
                           placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="test_method">Test Method:</label>
                    <select name="test_method" id="test_method" required>
                        <option value="php_mail">PHP mail() Function (Recommended)</option>
                        <option value="smtp_basic">Basic SMTP Connection Test</option>
                        <option value="smtp_hostinger">Hostinger SMTP Servers Test</option>
                    </select>
                </div>
                
                <button type="submit" class="btn">Send Test Email</button>
            </form>
        </div>
        
        <!-- Server Information -->
        <div class="section info">
            <h2>🖥️ Server Information</h2>
            <ul>
                <li><strong>Server:</strong> <?php echo $_SERVER['SERVER_NAME']; ?></li>
                <li><strong>PHP Version:</strong> <?php echo phpversion(); ?></li>
                <li><strong>Mail Function:</strong> <?php echo function_exists('mail') ? 'Available' : 'Not Available'; ?></li>
                <li><strong>OpenSSL:</strong> <?php echo extension_loaded('openssl') ? 'Loaded' : 'Not Loaded'; ?></li>
                <li><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                <li><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></li>
            </ul>
        </div>
        
        <!-- Email Account Check -->
        <div class="section warning">
            <h2>⚠️ Critical: Email Account Requirements</h2>
            <p><strong>Most Important:</strong> The sender email address must exist in your Hostinger hosting!</p>
            
            <h3>Required Steps:</h3>
            <ol>
                <li><strong>Login to Hostinger Control Panel</strong></li>
                <li><strong>Go to Email Accounts</strong></li>
                <li><strong>Create:</strong> <code><EMAIL></code></li>
                <li><strong>Set a password</strong> and remember it</li>
                <li><strong>Test email sending</strong> using this form</li>
            </ol>
            
            <div class="error" style="margin: 15px 0; padding: 10px;">
                <strong>❌ Common Issue:</strong> If emails aren't being delivered, it's usually because the sender email account doesn't exist in Hostinger!
            </div>
        </div>
        
        <!-- Troubleshooting Guide -->
        <div class="section">
            <h2>🔧 Troubleshooting Guide</h2>
            
            <h3>If PHP mail() Test Fails:</h3>
            <ul>
                <li>Check if the sender email account exists in Hostinger</li>
                <li>Verify PHP mail function is enabled on server</li>
                <li>Try using a different sender email address</li>
                <li>Contact Hostinger support about mail restrictions</li>
            </ul>
            
            <h3>If SMTP Connection Test Fails:</h3>
            <ul>
                <li>Try different SMTP servers and ports</li>
                <li>Check if firewall is blocking SMTP ports</li>
                <li>Verify domain DNS settings are correct</li>
                <li>Use Hostinger's SMTP server: smtp.hostinger.com</li>
            </ul>
            
            <h3>If Email is Sent but Not Received:</h3>
            <ul>
                <li>Check spam/junk folders thoroughly</li>
                <li>Try different email providers (Gmail, Yahoo, Outlook)</li>
                <li>Wait 10-15 minutes for delivery</li>
                <li>Add SPF record to DNS: <code>v=spf1 include:_spf.hostinger.com ~all</code></li>
            </ul>
        </div>
        
        <!-- Next Steps -->
        <div class="section success">
            <h2>✅ Next Steps After Successful Test</h2>
            <ol>
                <li><strong>If test email works:</strong> Your server can send emails!</li>
                <li><strong>Update system configuration:</strong> Use PHP mail() method</li>
                <li><strong>Test password recovery:</strong> <a href="authentication/forgot" target="_blank">Password Recovery Page</a></li>
                <li><strong>Configure email templates:</strong> Ensure they're enabled in database</li>
                <li><strong>Delete this test file</strong> for security</li>
            </ol>
        </div>
        
        <div class="section error">
            <h2>🗑️ Security Notice</h2>
            <p><strong>Important:</strong> Delete this file after testing!</p>
            <p>This test file should not remain on your production server for security reasons.</p>
        </div>
    </div>
</body>
</html>
