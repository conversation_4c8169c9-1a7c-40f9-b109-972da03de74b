HTTP/1.1 200 OK
server: nginx
date: Wed, 07 Aug 2019 04:08:43 GMT
content-type: application/json
content-length: 4790
access-control-allow-credentials: true
access-control-allow-methods: GET, POST, HEAD, OPTIONS, DELETE
access-control-allow-origin: *
access-control-expose-headers: Request-Id, Stripe-Manage-Version, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required
access-control-max-age: 300
cache-control: no-cache, no-store
request-id: req_pDQDQOBBz7ipht
stripe-version: 2018-05-21
strict-transport-security: max-age=31556926; includeSubDomains; preload

{
  "id": "pi_1F4Oq22okp6n5dKoErGka6we",
  "object": "payment_intent",
  "allowed_source_types": [
    "card"
  ],
  "amount": 52510,
  "amount_capturable": 0,
  "amount_received": 0,
  "application": null,
  "application_fee_amount": null,
  "canceled_at": 1565150923,
  "cancellation_reason": null,
  "capture_method": "manual",
  "charges": {
    "object": "list",
    "data": [
      {
        "id": "ch_1F4OqH2okp6n5dKoWlN61H9w",
        "object": "charge",
        "amount": 52510,
        "amount_refunded": 52510,
        "application": null,
        "application_fee": null,
        "application_fee_amount": null,
        "balance_transaction": null,
        "billing_details": {
          "address": {
            "city": "New York",
            "country": "US",
            "line1": "325 Broadway",
            "line2": null,
            "postal_code": "10007",
            "state": "New York"
          },
          "email": null,
          "name": "Jack Beanstalk",
          "phone": null
        },
        "captured": false,
        "created": 1565083229,
        "currency": "usd",
        "customer": "cus_FZXvpeIdgVYdmq",
        "description": null,
        "destination": null,
        "dispute": null,
        "failure_code": null,
        "failure_message": null,
        "fraud_details": {
        },
        "invoice": null,
        "livemode": false,
        "metadata": {
        },
        "on_behalf_of": null,
        "order": null,
        "outcome": {
          "network_status": "approved_by_network",
          "reason": null,
          "risk_level": "normal",
          "risk_score": 1,
          "seller_message": "Payment complete.",
          "type": "authorized"
        },
        "paid": true,
        "payment_intent": "pi_1F4Oq22okp6n5dKoErGka6we",
        "payment_method": "pm_1F4Oq02okp6n5dKoKfHmMyJN",
        "payment_method_details": {
          "card": {
            "brand": "visa",
            "checks": {
              "address_line1_check": "pass",
              "address_postal_code_check": "pass",
              "cvc_check": "pass"
            },
            "country": null,
            "exp_month": 2,
            "exp_year": 2022,
            "fingerprint": "TfHYiz2pR4RQdLsw",
            "funding": "credit",
            "last4": "3220",
            "three_d_secure": {
              "authenticated": true,
              "succeeded": true,
              "version": "2.1.0"
            },
            "wallet": null
          },
          "type": "card"
        },
        "receipt_email": null,
        "receipt_number": null,
        "receipt_url": "redacted",
        "refunded": true,
        "refunds": {
          "object": "list",
          "data": [
            {
              "id": "re_1F4gS62okp6n5dKoWNSLVaLS",
              "object": "refund",
              "amount": 52510,
              "balance_transaction": null,
              "charge": "ch_1F4OqH2okp6n5dKoWlN61H9w",
              "created": 1565150922,
              "currency": "usd",
              "metadata": {
              },
              "reason": null,
              "receipt_number": null,
              "source_transfer_reversal": null,
              "status": "succeeded",
              "transfer_reversal": null
            }
          ],
          "has_more": false,
          "total_count": 1,
          "url": "/v1/charges/ch_1F4OqH2okp7n5dKoWlN61H9w/refunds"
        },
        "review": null,
        "shipping": null,
        "source": null,
        "source_transfer": null,
        "statement_descriptor": null,
        "statement_descriptor_suffix": null,
        "status": "succeeded",
        "transfer_data": null,
        "transfer_group": null
      }
    ],
    "has_more": false,
    "total_count": 1,
    "url": "/v1/charges?payment_intent=pi_1F4Oq22okp6n5dKoErGka6we"
  },
  "client_secret": "pi_1F4Oq22okp6n5dKoErGka68e_secret_rUBo6oC0Tvx1V12QBZMimJrCS",
  "confirmation_method": "manual",
  "created": 1565083214,
  "currency": "usd",
  "customer": "cus_FZXvpeIdgVYdmq",
  "description": null,
  "invoice": null,
  "last_payment_error": null,
  "livemode": false,
  "metadata": {
  },
  "next_action": null,
  "next_source_action": null,
  "on_behalf_of": null,
  "payment_method": "pm_1F4Oq02okp6n5dKoKfHmMyJN",
  "payment_method_options": {
    "card": {
      "request_three_d_secure": "automatic"
    }
  },
  "payment_method_types": [
    "card"
  ],
  "receipt_email": null,
  "review": null,
  "setup_future_usage": "on_session",
  "shipping": null,
  "source": null,
  "statement_descriptor": null,
  "statement_descriptor_suffix": null,
  "status": "canceled",
  "transfer_data": null,
  "transfer_group": null
}
