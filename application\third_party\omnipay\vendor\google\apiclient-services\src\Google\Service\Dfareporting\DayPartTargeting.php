<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Dfareporting_DayPartTargeting extends Google_Collection
{
  protected $collection_key = 'hoursOfDay';
  public $daysOfWeek;
  public $hoursOfDay;
  public $userLocalTime;

  public function setDaysOfWeek($daysOfWeek)
  {
    $this->daysOfWeek = $daysOfWeek;
  }
  public function getDaysOfWeek()
  {
    return $this->daysOfWeek;
  }
  public function setHoursOfDay($hoursOfDay)
  {
    $this->hoursOfDay = $hoursOfDay;
  }
  public function getHoursOfDay()
  {
    return $this->hoursOfDay;
  }
  public function setUserLocalTime($userLocalTime)
  {
    $this->userLocalTime = $userLocalTime;
  }
  public function getUserLocalTime()
  {
    return $this->userLocalTime;
  }
}
