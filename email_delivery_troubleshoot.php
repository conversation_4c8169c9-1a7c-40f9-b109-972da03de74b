<?php
/**
 * Advanced Email Delivery Troubleshooting Tool
 * This script performs deep diagnostics on email delivery issues
 * 
 * Instructions:
 * 1. Upload this file to your Hostinger root directory
 * 2. Access it via: https://passdrc.com/public_html/school/email_delivery_troubleshoot.php
 * 3. Run comprehensive email delivery tests
 * 4. Apply fixes based on diagnostic results
 * 5. Delete this file after troubleshooting
 */

// Direct database connection for Hostinger
$db_config = array(
    'hostname' => 'localhost',
    'username' => 'u467814674_schooladmin',
    'password' => 'n*qy@1=Tg',
    'database' => 'u467814674_schooldatabase'
);

$test_results = array();
$fix_applied = false;
$fix_message = '';

// Handle test email sending
if ($_POST && isset($_POST['test_email_address'])) {
    $test_email = $_POST['test_email_address'];
    
    // Test 1: Direct PHP mail() function
    $test_results['php_mail'] = test_php_mail($test_email);
    
    // Test 2: SMTP connection test
    $test_results['smtp_connection'] = test_smtp_connection();
    
    // Test 3: Database configuration test
    $test_results['db_config'] = test_database_config();
    
    // Test 4: Advanced SMTP test with authentication
    $test_results['smtp_auth'] = test_smtp_with_auth($test_email);
    
    // Test 5: Alternative email methods
    $test_results['alternative'] = test_alternative_methods($test_email);
}

// Handle fix application
if ($_POST && isset($_POST['apply_fix'])) {
    $fix_type = $_POST['apply_fix'];
    $fix_result = apply_email_fix($fix_type);
    $fix_applied = true;
    $fix_message = $fix_result;
}

function test_php_mail($email) {
    $subject = "PASS-DRC Test Email - PHP Mail Function";
    $message = "This is a test email sent using PHP's built-in mail() function.\n\nIf you receive this, PHP mail is working on your Hostinger server.\n\nTime: " . date('Y-m-d H:i:s');
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Reply-To: <EMAIL>\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    $result = mail($email, $subject, $message, $headers);
    
    return array(
        'success' => $result,
        'method' => 'PHP mail()',
        'message' => $result ? 'Email sent successfully using PHP mail()' : 'PHP mail() function failed'
    );
}

function test_smtp_connection() {
    $smtp_servers = array(
        'mail.passdrc.com' => 587,
        'smtp.hostinger.com' => 587,
        'mail.passdrc.com' => 465,
        'smtp.hostinger.com' => 465
    );
    
    $results = array();
    
    foreach ($smtp_servers as $host => $port) {
        $connection = @fsockopen($host, $port, $errno, $errstr, 10);
        if ($connection) {
            $results[] = "✓ $host:$port - Connection successful";
            fclose($connection);
        } else {
            $results[] = "❌ $host:$port - Connection failed: $errstr";
        }
    }
    
    return array(
        'success' => count($results) > 0,
        'method' => 'SMTP Connection Test',
        'details' => $results
    );
}

function test_database_config() {
    global $db_config;
    
    try {
        $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
        
        if ($mysqli->connect_error) {
            return array('success' => false, 'message' => 'Database connection failed');
        }
        
        $result = $mysqli->query("SELECT * FROM email_config WHERE branch_id = 1");
        if ($result && $result->num_rows > 0) {
            $config = $result->fetch_assoc();
            $mysqli->close();
            
            return array(
                'success' => true,
                'method' => 'Database Configuration',
                'config' => $config,
                'message' => 'Email configuration found in database'
            );
        } else {
            $mysqli->close();
            return array('success' => false, 'message' => 'No email configuration found in database');
        }
    } catch (Exception $e) {
        return array('success' => false, 'message' => 'Database error: ' . $e->getMessage());
    }
}

function test_smtp_with_auth($email) {
    // This would require PHPMailer, but we'll simulate the test
    return array(
        'success' => false,
        'method' => 'SMTP with Authentication',
        'message' => 'Advanced SMTP test requires PHPMailer library'
    );
}

function test_alternative_methods($email) {
    // Test using different approaches
    $results = array();
    
    // Test 1: Simple sendmail
    if (function_exists('mail')) {
        $simple_result = mail($email, 'PASS-DRC Alternative Test', 'Testing alternative email method', 'From: <EMAIL>');
        $results[] = 'Simple mail: ' . ($simple_result ? 'Success' : 'Failed');
    }
    
    // Test 2: Check mail logs (if accessible)
    $mail_log = '/var/log/mail.log';
    if (file_exists($mail_log) && is_readable($mail_log)) {
        $results[] = 'Mail log accessible: Yes';
    } else {
        $results[] = 'Mail log accessible: No (normal for shared hosting)';
    }
    
    return array(
        'success' => true,
        'method' => 'Alternative Methods',
        'details' => $results
    );
}

function apply_email_fix($fix_type) {
    global $db_config;
    
    try {
        $mysqli = new mysqli($db_config['hostname'], $db_config['username'], $db_config['password'], $db_config['database']);
        
        switch ($fix_type) {
            case 'switch_to_php_mail':
                $update = "UPDATE email_config SET protocol = 'mail' WHERE branch_id = 1";
                $mysqli->query($update);
                return "Switched to PHP mail() function instead of SMTP";
                
            case 'update_smtp_settings':
                $update = "UPDATE email_config SET 
                    smtp_host = 'smtp.hostinger.com',
                    smtp_port = 587,
                    smtp_encryption = 'tls'
                    WHERE branch_id = 1";
                $mysqli->query($update);
                return "Updated SMTP settings to use Hostinger's SMTP server";
                
            case 'fix_from_email':
                $update = "UPDATE email_config SET email = '<EMAIL>' WHERE branch_id = 1";
                $mysqli->query($update);
                return "Fixed sender email address";
                
            case 'enable_debug':
                // This would modify the Mailer.php to enable debug mode
                return "Debug mode would need to be enabled in Mailer.php manually";
                
            default:
                return "Unknown fix type";
        }
    } catch (Exception $e) {
        return "Fix failed: " . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Email Delivery Troubleshooting - PASS-DRC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .test-success { background: #d4edda; border: 1px solid #c3e6cb; }
        .test-failed { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Advanced Email Delivery Troubleshooting</h1>
        <p><strong>Purpose:</strong> Diagnose and fix email delivery issues on Hostinger hosting.</p>
        
        <?php if ($fix_applied): ?>
            <div class="section success">
                <strong>✓ Fix Applied:</strong> <?php echo htmlspecialchars($fix_message); ?>
                <br><small>Please test email delivery again after applying this fix.</small>
            </div>
        <?php endif; ?>
        
        <!-- Email Test Form -->
        <div class="section">
            <h2>📧 Comprehensive Email Delivery Test</h2>
            <form method="post">
                <div style="margin: 15px 0;">
                    <label for="test_email_address"><strong>Test Email Address:</strong></label><br>
                    <input type="email" name="test_email_address" id="test_email_address" 
                           placeholder="<EMAIL>" required 
                           style="width: 300px; padding: 8px; margin: 5px 0;">
                    <button type="submit" class="btn btn-success">Run Complete Email Test</button>
                </div>
                <p><small>This will test multiple email delivery methods and provide detailed diagnostics.</small></p>
            </form>
        </div>
        
        <!-- Test Results -->
        <?php if (!empty($test_results)): ?>
            <div class="section">
                <h2>🧪 Test Results</h2>
                
                <?php foreach ($test_results as $test_name => $result): ?>
                    <div class="test-result <?php echo $result['success'] ? 'test-success' : 'test-failed'; ?>">
                        <h4><?php echo $result['success'] ? '✓' : '❌'; ?> <?php echo htmlspecialchars($result['method']); ?></h4>
                        <p><?php echo htmlspecialchars($result['message']); ?></p>
                        
                        <?php if (isset($result['details'])): ?>
                            <ul>
                                <?php foreach ($result['details'] as $detail): ?>
                                    <li><?php echo htmlspecialchars($detail); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                        
                        <?php if (isset($result['config'])): ?>
                            <div class="code">
                                <strong>Current Configuration:</strong><br>
                                Email: <?php echo htmlspecialchars($result['config']['email']); ?><br>
                                Protocol: <?php echo htmlspecialchars($result['config']['protocol']); ?><br>
                                SMTP Host: <?php echo htmlspecialchars($result['config']['smtp_host']); ?><br>
                                SMTP Port: <?php echo htmlspecialchars($result['config']['smtp_port']); ?><br>
                                SMTP User: <?php echo htmlspecialchars($result['config']['smtp_user']); ?><br>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- Quick Fixes -->
        <div class="section">
            <h2>🔧 Quick Fixes for Common Issues</h2>
            <p>Based on common Hostinger email delivery problems, try these fixes:</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
                <form method="post">
                    <input type="hidden" name="apply_fix" value="switch_to_php_mail">
                    <button type="submit" class="btn btn-warning" style="width: 100%;">
                        Switch to PHP Mail<br>
                        <small>Use PHP mail() instead of SMTP</small>
                    </button>
                </form>
                
                <form method="post">
                    <input type="hidden" name="apply_fix" value="update_smtp_settings">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        Update SMTP Settings<br>
                        <small>Use Hostinger's SMTP server</small>
                    </button>
                </form>
                
                <form method="post">
                    <input type="hidden" name="apply_fix" value="fix_from_email">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        Fix Sender Email<br>
                        <small>Ensure proper from address</small>
                    </button>
                </form>
                
                <form method="post">
                    <input type="hidden" name="apply_fix" value="enable_debug">
                    <button type="submit" class="btn btn-info" style="width: 100%;">
                        Enable Debug Mode<br>
                        <small>Get detailed error messages</small>
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Hostinger Specific Issues -->
        <div class="section warning">
            <h2>⚠️ Common Hostinger Email Issues</h2>
            <h3>1. SMTP Restrictions</h3>
            <ul>
                <li><strong>Issue:</strong> Hostinger may block external SMTP servers</li>
                <li><strong>Solution:</strong> Use Hostinger's own SMTP server: <code>smtp.hostinger.com</code></li>
                <li><strong>Alternative:</strong> Switch to PHP mail() function</li>
            </ul>
            
            <h3>2. Email Authentication</h3>
            <ul>
                <li><strong>Issue:</strong> Missing SPF/DKIM records</li>
                <li><strong>Solution:</strong> Add SPF record: <code>v=spf1 include:_spf.hostinger.com ~all</code></li>
                <li><strong>Location:</strong> Hostinger DNS management</li>
            </ul>
            
            <h3>3. From Address Restrictions</h3>
            <ul>
                <li><strong>Issue:</strong> Must use email address from your domain</li>
                <li><strong>Solution:</strong> Create <code><EMAIL></code> email account</li>
                <li><strong>Alternative:</strong> Use existing email address</li>
            </ul>
            
            <h3>4. Rate Limiting</h3>
            <ul>
                <li><strong>Issue:</strong> Hostinger limits email sending rate</li>
                <li><strong>Solution:</strong> Add delays between emails</li>
                <li><strong>Limit:</strong> Usually 100-300 emails per hour</li>
            </ul>
        </div>
        
        <!-- Manual Configuration -->
        <div class="section info">
            <h2>🛠️ Manual Email Configuration for Hostinger</h2>
            <p>If automated fixes don't work, manually configure these settings:</p>
            
            <h3>Option 1: PHP Mail (Recommended for Hostinger)</h3>
            <div class="code">
Protocol: mail
System Email: <EMAIL> (must exist in your hosting)
            </div>
            
            <h3>Option 2: Hostinger SMTP</h3>
            <div class="code">
Protocol: smtp
SMTP Host: smtp.hostinger.com
SMTP Port: 587
SMTP Encryption: TLS
SMTP Username: <EMAIL>
SMTP Password: [your email password]
System Email: <EMAIL>
            </div>
            
            <h3>Option 3: Domain SMTP</h3>
            <div class="code">
Protocol: smtp
SMTP Host: mail.passdrc.com
SMTP Port: 587 or 465
SMTP Encryption: TLS or SSL
SMTP Username: <EMAIL>
SMTP Password: [your email password]
System Email: <EMAIL>
            </div>
        </div>
        
        <!-- Email Account Setup -->
        <div class="section error">
            <h2>📬 Critical: Email Account Setup</h2>
            <p><strong>Most Common Issue:</strong> The sender email address doesn't exist!</p>
            
            <h3>Steps to Fix:</h3>
            <ol>
                <li><strong>Login to Hostinger Control Panel</strong></li>
                <li><strong>Go to Email Accounts</strong></li>
                <li><strong>Create email account:</strong> <code><EMAIL></code></li>
                <li><strong>Set a strong password</strong></li>
                <li><strong>Use this email in your configuration</strong></li>
            </ol>
            
            <div class="warning" style="margin: 15px 0; padding: 10px;">
                <strong>⚠️ Important:</strong> You MUST create the email account in Hostinger before it can send emails!
            </div>
        </div>
        
        <!-- Testing Instructions -->
        <div class="section">
            <h2>🧪 Testing After Fixes</h2>
            <ol>
                <li><strong>Apply one of the fixes above</strong></li>
                <li><strong>Run the email test again</strong> using the form at the top</li>
                <li><strong>Check your email</strong> (including spam folder)</li>
                <li><strong>Test password recovery:</strong> <a href="authentication/forgot" target="_blank">Password Recovery Page</a></li>
                <li><strong>If still not working:</strong> Try the next fix option</li>
            </ol>
            
            <h3>Alternative Testing Methods:</h3>
            <ul>
                <li><strong>Use different email providers:</strong> Gmail, Yahoo, Outlook</li>
                <li><strong>Test from different devices:</strong> Phone, computer</li>
                <li><strong>Check email headers:</strong> Look for delivery errors</li>
                <li><strong>Contact Hostinger support:</strong> If all else fails</li>
            </ul>
        </div>
        
        <div class="section error">
            <h2>🗑️ Cleanup</h2>
            <p><strong>Security:</strong> Delete this file after troubleshooting!</p>
            <p>This file contains sensitive diagnostic information and should not remain on your server.</p>
        </div>
    </div>
</body>
</html>
