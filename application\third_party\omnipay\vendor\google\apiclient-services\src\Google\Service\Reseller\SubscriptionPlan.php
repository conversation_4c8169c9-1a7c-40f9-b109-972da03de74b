<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Reseller_SubscriptionPlan extends Google_Model
{
  protected $commitmentIntervalType = 'Google_Service_Reseller_SubscriptionPlanCommitmentInterval';
  protected $commitmentIntervalDataType = '';
  public $isCommitmentPlan;
  public $planName;

  public function setCommitmentInterval(Google_Service_Reseller_SubscriptionPlanCommitmentInterval $commitmentInterval)
  {
    $this->commitmentInterval = $commitmentInterval;
  }
  public function getCommitmentInterval()
  {
    return $this->commitmentInterval;
  }
  public function setIsCommitmentPlan($isCommitmentPlan)
  {
    $this->isCommitmentPlan = $isCommitmentPlan;
  }
  public function getIsCommitmentPlan()
  {
    return $this->isCommitmentPlan;
  }
  public function setPlanName($planName)
  {
    $this->planName = $planName;
  }
  public function getPlanName()
  {
    return $this->planName;
  }
}
