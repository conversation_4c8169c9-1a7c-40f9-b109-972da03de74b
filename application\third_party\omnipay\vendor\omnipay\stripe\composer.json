{"name": "omnipay/stripe", "type": "library", "description": "Stripe driver for the Omnipay payment processing library", "keywords": ["gateway", "merchant", "omnipay", "pay", "payment", "stripe"], "homepage": "https://github.com/thephpleague/omnipay-stripe", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Omnipay Contributors", "homepage": "https://github.com/thephpleague/omnipay-stripe/contributors"}], "autoload": {"psr-4": {"Omnipay\\Stripe\\": "src/"}}, "require": {"omnipay/common": "^3"}, "require-dev": {"omnipay/tests": "^3", "squizlabs/php_codesniffer": "^3", "phpro/grumphp": "^0.14"}, "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "prefer-stable": true}