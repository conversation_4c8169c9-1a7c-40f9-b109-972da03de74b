<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Conversations\V1\Service\Configuration;

use Twilio\Options;
use Twilio\Values;

abstract class NotificationOptions
{

    /**
     * @param bool $logEnabled Weather the notification logging is enabled.
     * @param bool $newMessageEnabled Whether to send a notification when a new message is added to a conversation. The default is `false`.
     * @param string $newMessageTemplate The template to use to create the notification text displayed when a new message is added to a conversation and `new_message.enabled` is `true`.
     * @param string $newMessageSound The name of the sound to play when a new message is added to a conversation and `new_message.enabled` is `true`.
     * @param bool $newMessageBadgeCountEnabled Whether the new message badge is enabled. The default is `false`.
     * @param bool $addedToConversationEnabled Whether to send a notification when a participant is added to a conversation. The default is `false`.
     * @param string $addedToConversationTemplate The template to use to create the notification text displayed when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
     * @param string $addedToConversationSound The name of the sound to play when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
     * @param bool $removedFromConversationEnabled Whether to send a notification to a user when they are removed from a conversation. The default is `false`.
     * @param string $removedFromConversationTemplate The template to use to create the notification text displayed to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
     * @param string $removedFromConversationSound The name of the sound to play to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
     * @param bool $newMessageWithMediaEnabled Whether to send a notification when a new message with media/file attachments is added to a conversation. The default is `false`.
     * @param string $newMessageWithMediaTemplate The template to use to create the notification text displayed when a new message with media/file attachments is added to a conversation and `new_message.attachments.enabled` is `true`.
     * @return UpdateNotificationOptions Options builder
     */
    public static function update(
        
        bool $logEnabled = Values::BOOL_NONE,
        bool $newMessageEnabled = Values::BOOL_NONE,
        string $newMessageTemplate = Values::NONE,
        string $newMessageSound = Values::NONE,
        bool $newMessageBadgeCountEnabled = Values::BOOL_NONE,
        bool $addedToConversationEnabled = Values::BOOL_NONE,
        string $addedToConversationTemplate = Values::NONE,
        string $addedToConversationSound = Values::NONE,
        bool $removedFromConversationEnabled = Values::BOOL_NONE,
        string $removedFromConversationTemplate = Values::NONE,
        string $removedFromConversationSound = Values::NONE,
        bool $newMessageWithMediaEnabled = Values::BOOL_NONE,
        string $newMessageWithMediaTemplate = Values::NONE

    ): UpdateNotificationOptions
    {
        return new UpdateNotificationOptions(
            $logEnabled,
            $newMessageEnabled,
            $newMessageTemplate,
            $newMessageSound,
            $newMessageBadgeCountEnabled,
            $addedToConversationEnabled,
            $addedToConversationTemplate,
            $addedToConversationSound,
            $removedFromConversationEnabled,
            $removedFromConversationTemplate,
            $removedFromConversationSound,
            $newMessageWithMediaEnabled,
            $newMessageWithMediaTemplate
        );
    }

}


class UpdateNotificationOptions extends Options
    {
    /**
     * @param bool $logEnabled Weather the notification logging is enabled.
     * @param bool $newMessageEnabled Whether to send a notification when a new message is added to a conversation. The default is `false`.
     * @param string $newMessageTemplate The template to use to create the notification text displayed when a new message is added to a conversation and `new_message.enabled` is `true`.
     * @param string $newMessageSound The name of the sound to play when a new message is added to a conversation and `new_message.enabled` is `true`.
     * @param bool $newMessageBadgeCountEnabled Whether the new message badge is enabled. The default is `false`.
     * @param bool $addedToConversationEnabled Whether to send a notification when a participant is added to a conversation. The default is `false`.
     * @param string $addedToConversationTemplate The template to use to create the notification text displayed when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
     * @param string $addedToConversationSound The name of the sound to play when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
     * @param bool $removedFromConversationEnabled Whether to send a notification to a user when they are removed from a conversation. The default is `false`.
     * @param string $removedFromConversationTemplate The template to use to create the notification text displayed to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
     * @param string $removedFromConversationSound The name of the sound to play to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
     * @param bool $newMessageWithMediaEnabled Whether to send a notification when a new message with media/file attachments is added to a conversation. The default is `false`.
     * @param string $newMessageWithMediaTemplate The template to use to create the notification text displayed when a new message with media/file attachments is added to a conversation and `new_message.attachments.enabled` is `true`.
     */
    public function __construct(
        
        bool $logEnabled = Values::BOOL_NONE,
        bool $newMessageEnabled = Values::BOOL_NONE,
        string $newMessageTemplate = Values::NONE,
        string $newMessageSound = Values::NONE,
        bool $newMessageBadgeCountEnabled = Values::BOOL_NONE,
        bool $addedToConversationEnabled = Values::BOOL_NONE,
        string $addedToConversationTemplate = Values::NONE,
        string $addedToConversationSound = Values::NONE,
        bool $removedFromConversationEnabled = Values::BOOL_NONE,
        string $removedFromConversationTemplate = Values::NONE,
        string $removedFromConversationSound = Values::NONE,
        bool $newMessageWithMediaEnabled = Values::BOOL_NONE,
        string $newMessageWithMediaTemplate = Values::NONE

    ) {
        $this->options['logEnabled'] = $logEnabled;
        $this->options['newMessageEnabled'] = $newMessageEnabled;
        $this->options['newMessageTemplate'] = $newMessageTemplate;
        $this->options['newMessageSound'] = $newMessageSound;
        $this->options['newMessageBadgeCountEnabled'] = $newMessageBadgeCountEnabled;
        $this->options['addedToConversationEnabled'] = $addedToConversationEnabled;
        $this->options['addedToConversationTemplate'] = $addedToConversationTemplate;
        $this->options['addedToConversationSound'] = $addedToConversationSound;
        $this->options['removedFromConversationEnabled'] = $removedFromConversationEnabled;
        $this->options['removedFromConversationTemplate'] = $removedFromConversationTemplate;
        $this->options['removedFromConversationSound'] = $removedFromConversationSound;
        $this->options['newMessageWithMediaEnabled'] = $newMessageWithMediaEnabled;
        $this->options['newMessageWithMediaTemplate'] = $newMessageWithMediaTemplate;
    }

    /**
     * Weather the notification logging is enabled.
     *
     * @param bool $logEnabled Weather the notification logging is enabled.
     * @return $this Fluent Builder
     */
    public function setLogEnabled(bool $logEnabled): self
    {
        $this->options['logEnabled'] = $logEnabled;
        return $this;
    }

    /**
     * Whether to send a notification when a new message is added to a conversation. The default is `false`.
     *
     * @param bool $newMessageEnabled Whether to send a notification when a new message is added to a conversation. The default is `false`.
     * @return $this Fluent Builder
     */
    public function setNewMessageEnabled(bool $newMessageEnabled): self
    {
        $this->options['newMessageEnabled'] = $newMessageEnabled;
        return $this;
    }

    /**
     * The template to use to create the notification text displayed when a new message is added to a conversation and `new_message.enabled` is `true`.
     *
     * @param string $newMessageTemplate The template to use to create the notification text displayed when a new message is added to a conversation and `new_message.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setNewMessageTemplate(string $newMessageTemplate): self
    {
        $this->options['newMessageTemplate'] = $newMessageTemplate;
        return $this;
    }

    /**
     * The name of the sound to play when a new message is added to a conversation and `new_message.enabled` is `true`.
     *
     * @param string $newMessageSound The name of the sound to play when a new message is added to a conversation and `new_message.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setNewMessageSound(string $newMessageSound): self
    {
        $this->options['newMessageSound'] = $newMessageSound;
        return $this;
    }

    /**
     * Whether the new message badge is enabled. The default is `false`.
     *
     * @param bool $newMessageBadgeCountEnabled Whether the new message badge is enabled. The default is `false`.
     * @return $this Fluent Builder
     */
    public function setNewMessageBadgeCountEnabled(bool $newMessageBadgeCountEnabled): self
    {
        $this->options['newMessageBadgeCountEnabled'] = $newMessageBadgeCountEnabled;
        return $this;
    }

    /**
     * Whether to send a notification when a participant is added to a conversation. The default is `false`.
     *
     * @param bool $addedToConversationEnabled Whether to send a notification when a participant is added to a conversation. The default is `false`.
     * @return $this Fluent Builder
     */
    public function setAddedToConversationEnabled(bool $addedToConversationEnabled): self
    {
        $this->options['addedToConversationEnabled'] = $addedToConversationEnabled;
        return $this;
    }

    /**
     * The template to use to create the notification text displayed when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
     *
     * @param string $addedToConversationTemplate The template to use to create the notification text displayed when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setAddedToConversationTemplate(string $addedToConversationTemplate): self
    {
        $this->options['addedToConversationTemplate'] = $addedToConversationTemplate;
        return $this;
    }

    /**
     * The name of the sound to play when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
     *
     * @param string $addedToConversationSound The name of the sound to play when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setAddedToConversationSound(string $addedToConversationSound): self
    {
        $this->options['addedToConversationSound'] = $addedToConversationSound;
        return $this;
    }

    /**
     * Whether to send a notification to a user when they are removed from a conversation. The default is `false`.
     *
     * @param bool $removedFromConversationEnabled Whether to send a notification to a user when they are removed from a conversation. The default is `false`.
     * @return $this Fluent Builder
     */
    public function setRemovedFromConversationEnabled(bool $removedFromConversationEnabled): self
    {
        $this->options['removedFromConversationEnabled'] = $removedFromConversationEnabled;
        return $this;
    }

    /**
     * The template to use to create the notification text displayed to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
     *
     * @param string $removedFromConversationTemplate The template to use to create the notification text displayed to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setRemovedFromConversationTemplate(string $removedFromConversationTemplate): self
    {
        $this->options['removedFromConversationTemplate'] = $removedFromConversationTemplate;
        return $this;
    }

    /**
     * The name of the sound to play to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
     *
     * @param string $removedFromConversationSound The name of the sound to play to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setRemovedFromConversationSound(string $removedFromConversationSound): self
    {
        $this->options['removedFromConversationSound'] = $removedFromConversationSound;
        return $this;
    }

    /**
     * Whether to send a notification when a new message with media/file attachments is added to a conversation. The default is `false`.
     *
     * @param bool $newMessageWithMediaEnabled Whether to send a notification when a new message with media/file attachments is added to a conversation. The default is `false`.
     * @return $this Fluent Builder
     */
    public function setNewMessageWithMediaEnabled(bool $newMessageWithMediaEnabled): self
    {
        $this->options['newMessageWithMediaEnabled'] = $newMessageWithMediaEnabled;
        return $this;
    }

    /**
     * The template to use to create the notification text displayed when a new message with media/file attachments is added to a conversation and `new_message.attachments.enabled` is `true`.
     *
     * @param string $newMessageWithMediaTemplate The template to use to create the notification text displayed when a new message with media/file attachments is added to a conversation and `new_message.attachments.enabled` is `true`.
     * @return $this Fluent Builder
     */
    public function setNewMessageWithMediaTemplate(string $newMessageWithMediaTemplate): self
    {
        $this->options['newMessageWithMediaTemplate'] = $newMessageWithMediaTemplate;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Conversations.V1.UpdateNotificationOptions ' . $options . ']';
    }
}

