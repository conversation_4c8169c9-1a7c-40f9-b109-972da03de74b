HTTP/1.1 201 Created
Server: Apache-Coyote/1.1
PROXY_SERVER_INFO: host=slcsbjava3.slc.paypal.com;threadId=3224
Paypal-Debug-Id: 2a1b9202663bc
SERVER_INFO: paymentsplatformserv:v1.payments.payment&CalThreadId=206&TopLevelTxnStartTime=14701b4cb42&Host=slcsbjm1.slc.paypal.com&pid=20119
CORRELATION-ID: 2a1b9202663bc
Content-Language: *
Date: Fri, 04 Jul 2014 14:09:00 GMT
Content-Type: application/json
Content-Length: 1435

{"id":"PAY-66G66446716792522KO3LK4Y","create_time":"2014-07-04T14:08:51Z","update_time":"2014-07-04T14:09:01Z","state":"approved","intent":"authorize","payer":{"payment_method":"credit_card","funding_instruments":[{"credit_card":{"type":"mastercard","number":"xxxxxxxxxxxx5559","expire_month":"12","expire_year":"2018","first_name":"Betsy","last_name":"Buyer"}}]},"transactions":[{"amount":{"total":"7.47","currency":"USD","details":{"subtotal":"7.47"}},"description":"This is the payment transaction description.","related_resources":[{"authorization":{"id":"58N7596879166930B","create_time":"2014-07-04T14:08:51Z","update_time":"2014-07-04T14:09:01Z","state":"authorized","amount":{"total":"7.47","currency":"USD"},"parent_payment":"PAY-66G66446716792522KO3LK4Y","valid_until":"2014-08-02T14:08:51Z","links":[{"href":"https://api.sandbox.paypal.com/v1/payments/authorization/58N7596879166930B","rel":"self","method":"GET"},{"href":"https://api.sandbox.paypal.com/v1/payments/authorization/58N7596879166930B/capture","rel":"capture","method":"POST"},{"href":"https://api.sandbox.paypal.com/v1/payments/authorization/58N7596879166930B/void","rel":"void","method":"POST"},{"href":"https://api.sandbox.paypal.com/v1/payments/payment/PAY-66G66446716792522KO3LK4Y","rel":"parent_payment","method":"GET"}]}}]}],"links":[{"href":"https://api.sandbox.paypal.com/v1/payments/payment/PAY-66G66446716792522KO3LK4Y","rel":"self","method":"GET"}]}