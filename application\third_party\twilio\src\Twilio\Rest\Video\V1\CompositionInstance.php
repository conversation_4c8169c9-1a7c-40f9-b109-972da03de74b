<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Video\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $accountSid
 * @property string $status
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateCompleted
 * @property \DateTime|null $dateDeleted
 * @property string|null $sid
 * @property string|null $roomSid
 * @property string[]|null $audioSources
 * @property string[]|null $audioSourcesExcluded
 * @property array|null $videoLayout
 * @property string|null $resolution
 * @property bool|null $trim
 * @property string $format
 * @property int|null $bitrate
 * @property int|null $size
 * @property int|null $duration
 * @property string|null $mediaExternalLocation
 * @property string|null $statusCallback
 * @property string|null $statusCallbackMethod
 * @property string|null $url
 * @property array|null $links
 */
class CompositionInstance extends InstanceResource
{
    /**
     * Initialize the CompositionInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $sid The SID of the Composition resource to delete.
     */
    public function __construct(Version $version, array $payload, string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'status' => Values::array_get($payload, 'status'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateCompleted' => Deserialize::dateTime(Values::array_get($payload, 'date_completed')),
            'dateDeleted' => Deserialize::dateTime(Values::array_get($payload, 'date_deleted')),
            'sid' => Values::array_get($payload, 'sid'),
            'roomSid' => Values::array_get($payload, 'room_sid'),
            'audioSources' => Values::array_get($payload, 'audio_sources'),
            'audioSourcesExcluded' => Values::array_get($payload, 'audio_sources_excluded'),
            'videoLayout' => Values::array_get($payload, 'video_layout'),
            'resolution' => Values::array_get($payload, 'resolution'),
            'trim' => Values::array_get($payload, 'trim'),
            'format' => Values::array_get($payload, 'format'),
            'bitrate' => Values::array_get($payload, 'bitrate'),
            'size' => Values::array_get($payload, 'size'),
            'duration' => Values::array_get($payload, 'duration'),
            'mediaExternalLocation' => Values::array_get($payload, 'media_external_location'),
            'statusCallback' => Values::array_get($payload, 'status_callback'),
            'statusCallbackMethod' => Values::array_get($payload, 'status_callback_method'),
            'url' => Values::array_get($payload, 'url'),
            'links' => Values::array_get($payload, 'links'),
        ];

        $this->solution = ['sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return CompositionContext Context for this CompositionInstance
     */
    protected function proxy(): CompositionContext
    {
        if (!$this->context) {
            $this->context = new CompositionContext(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the CompositionInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the CompositionInstance
     *
     * @return CompositionInstance Fetched CompositionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): CompositionInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Video.V1.CompositionInstance ' . \implode(' ', $context) . ']';
    }
}

