<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

/**
 * The "threatListUpdates" collection of methods.
 * Typical usage is:
 *  <code>
 *   $safebrowsingService = new Google_Service_Safebrowsing(...);
 *   $threatListUpdates = $safebrowsingService->threatListUpdates;
 *  </code>
 */
class Google_Service_Safebrowsing_Resource_ThreatListUpdates extends Google_Service_Resource
{
  /**
   * Fetches the most recent threat list updates. A client can request updates for
   * multiple lists at once. (threatListUpdates.fetch)
   *
   * @param Google_Service_Safebrowsing_FetchThreatListUpdatesRequest $postBody
   * @param array $optParams Optional parameters.
   * @return Google_Service_Safebrowsing_FetchThreatListUpdatesResponse
   */
  public function fetch(Google_Service_Safebrowsing_FetchThreatListUpdatesRequest $postBody, $optParams = array())
  {
    $params = array('postBody' => $postBody);
    $params = array_merge($params, $optParams);
    return $this->call('fetch', array($params), "Google_Service_Safebrowsing_FetchThreatListUpdatesResponse");
  }
}
