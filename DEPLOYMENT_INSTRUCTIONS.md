# 📦 INSTRUCTIONS DE DÉPLOIEMENT - SYSTÈME DE GESTION SCOLAIRE PASS-DRC

## 🎯 PACKAGE DE DÉPLOIEMENT PRÊT POUR HOSTINGER

**Fichier:** `school_management_system.zip` (74.72 MB)  
**Destination:** `https://school.passdrc.com/`  
**Date de création:** 2025-07-09

---

## 📋 CONTENU DU PACKAGE

Le fichier ZIP contient uniquement les fichiers nécessaires pour la production :

```
school_management_system.zip
├── application/          # Configuration et logique de l'application
├── assets/              # CSS, JS, images et ressources frontend
├── system/              # Noyau CodeIgniter
├── uploads/             # Dossiers pour les fichiers uploadés
├── index.php            # Point d'entrée principal
├── .htaccess           # Règles Apache avec optimisations
└── .user.ini           # Configuration PHP optimisée pour Hostinger
```

---

## 🚀 ÉTAPES DE DÉPLOIEMENT

### 1. **UPLOAD DU FICHIER ZIP**
1. Connectez-vous à votre panneau Hostinger
2. Allez dans **File Manager**
3. Naviguez vers `/domains/passdrc.com/public_html/school/`
4. Uploadez le fichier `school_management_system.zip`
5. Cliquez droit sur le fichier ZIP → **Extract**
6. Supprimez le fichier ZIP après extraction

### 2. **CONFIGURATION DE LA BASE DE DONNÉES**
Après l'upload, vous devez mettre à jour les informations de base de données :

**Fichier à modifier:** `application/config/database.php`

```php
// Remplacez ces valeurs par vos vraies informations Hostinger :
'hostname' => 'localhost',
'username' => 'u123456789_school_user',    // Votre vrai nom d'utilisateur
'password' => 'VotreMotDePasse',           // Votre vrai mot de passe
'database' => 'u123456789_school',         // Votre vraie base de données
```

### 3. **PERMISSIONS DES FICHIERS**
Définissez les permissions suivantes :
- **Dossiers:** 755
- **Fichiers:** 644
- **Dossier uploads/:** 777 (récursif)

### 4. **VÉRIFICATION SSL**
- Le système est configuré pour HTTPS automatique
- Vérifiez que le certificat SSL est actif sur `school.passdrc.com`

---

## ⚙️ CONFIGURATION POST-DÉPLOIEMENT

### **Variables à vérifier dans `application/config/config.php`:**
- `$config['environment'] = 'production';` ✅
- `$config['language'] = 'french';` ✅
- `$config['base_url']` est configuré dynamiquement ✅

### **Optimisations incluses:**
- Cache activé pour les performances
- Compression GZIP activée
- Headers de sécurité configurés
- Redirection HTTPS automatique

---

## 🔧 INFORMATIONS TECHNIQUES

### **Configuration PHP (.user.ini):**
```ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 50M
post_max_size = 50M
max_input_vars = 3000
```

### **Optimisations .htaccess:**
- Compression GZIP
- Cache des ressources statiques
- Redirection HTTPS forcée
- Protection des fichiers sensibles

---

## 🧪 TESTS À EFFECTUER

Après déploiement, testez :

1. **Accès principal:** `https://school.passdrc.com/`
2. **Connexion admin:** Vérifiez l'authentification
3. **Interface française:** Confirmez la langue par défaut
4. **Upload de fichiers:** Testez les fonctionnalités d'upload
5. **Base de données:** Vérifiez la connexion et les données

---

## 🆘 SUPPORT

**En cas de problème:**
- Email: <EMAIL>
- Téléphone: +243 995 618 678

**Fichiers de sauvegarde créés localement:**
- `application/config/config.php.backup.2025-07-09_10-32-56`
- `application/config/database.php.backup.2025-07-09_10-32-56`

---

## ✅ CHECKLIST DE DÉPLOIEMENT

- [ ] ZIP uploadé et extrait sur Hostinger
- [ ] Configuration base de données mise à jour
- [ ] Permissions des fichiers définies
- [ ] SSL vérifié et actif
- [ ] Tests de fonctionnalité effectués
- [ ] Interface française confirmée
- [ ] Branding PASS-DRC vérifié

---

**© 2025 PASS-DRC School Management - powered by PASS-DRC**
