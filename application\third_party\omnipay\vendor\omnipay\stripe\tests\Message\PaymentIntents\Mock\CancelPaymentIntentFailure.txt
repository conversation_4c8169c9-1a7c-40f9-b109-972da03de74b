HTTP/1.1 400 Bad Request
server: nginx
date: Wed, 07 Aug 2019 01:29:44 GMT
content-type: application/json
content-length: 2005
access-control-allow-credentials: true
access-control-allow-methods: GET, POST, HEAD, OPTIONS, DELETE
access-control-allow-origin: *
access-control-expose-headers: Request-Id, Stripe-Manage-Version, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required
access-control-max-age: 300
cache-control: no-cache, no-store
request-id: req_W9rdDlUahgd6iC
stripe-version: 2018-05-21
strict-transport-security: max-age=31556926; includeSubDomains; preload

{
  "error": {
    "code": "payment_intent_unexpected_state",
    "doc_url": "https://stripe.com/docs/error-codes/payment-intent-unexpected-state",
    "message": "You cannot cancel this PaymentIntent because it has a status of canceled. Only a PaymentIntent with one of the following statuses may be canceled: requires_payment_method, requires_capture, requires_confirmation, requires_action.",
    "payment_intent": {
      "id": "pi_1Evf5q2okp6n5dKoviPU2IkK",
      "object": "payment_intent",
      "allowed_source_types": [
        "card"
      ],
      "amount": 1099,
      "amount_capturable": 0,
      "amount_received": 0,
      "application": null,
      "application_fee_amount": null,
      "canceled_at": 1565141374,
      "cancellation_reason": null,
      "capture_method": "automatic",
      "charges": {
        "object": "list",
        "data": [

        ],
        "has_more": false,
        "total_count": 0,
        "url": "/v1/charges?payment_intent=pi_1Evf5q2okp6n5dKoviPU2IkK"
      },
      "client_secret": "pi_1Evf5q2okp6n5dKoviPU2IkK_secret_uXzcxJ2um6t790XAokzr2HsTN",
      "confirmation_method": "automatic",
      "created": 1563000746,
      "currency": "aud",
      "customer": null,
      "description": null,
      "invoice": null,
      "last_payment_error": null,
      "livemode": false,
      "metadata": {
      },
      "next_action": null,
      "next_source_action": null,
      "on_behalf_of": null,
      "payment_method": null,
      "payment_method_options": {
        "card": {
          "request_three_d_secure": "automatic"
        }
      },
      "payment_method_types": [
        "card"
      ],
      "receipt_email": null,
      "review": null,
      "setup_future_usage": "off_session",
      "shipping": null,
      "source": null,
      "statement_descriptor": null,
      "statement_descriptor_suffix": null,
      "status": "canceled",
      "transfer_data": null,
      "transfer_group": null
    },
    "type": "invalid_request_error"
  }
}
