<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Insights\V1;

use Twilio\Options;
use Twilio\Values;

abstract class CallSummariesOptions
{
    /**
     * @param string $from A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @param string $to A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @param string $fromCarrier An origination carrier.
     * @param string $toCarrier A destination carrier.
     * @param string $fromCountryCode A source country code based on phone number in From.
     * @param string $toCountryCode A destination country code. Based on phone number in To.
     * @param bool $branded A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls.
     * @param bool $verifiedCaller A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.
     * @param bool $hasTag A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
     * @param string $startTime A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
     * @param string $endTime An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
     * @param string $callType A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
     * @param string $callState A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
     * @param string $direction A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
     * @param string $processingState A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
     * @param string $sortBy A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
     * @param string $subaccount A unique SID identifier of a Subaccount.
     * @param bool $abnormalSession A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
     * @param string $answeredBy An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
     * @param string $connectivityIssue A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
     * @param string $qualityIssue A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
     * @param bool $spam A boolean flag indicating spam calls.
     * @param string $callScore A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
     * @return ReadCallSummariesOptions Options builder
     */
    public static function read(
        
        string $from = Values::NONE,
        string $to = Values::NONE,
        string $fromCarrier = Values::NONE,
        string $toCarrier = Values::NONE,
        string $fromCountryCode = Values::NONE,
        string $toCountryCode = Values::NONE,
        bool $branded = Values::BOOL_NONE,
        bool $verifiedCaller = Values::BOOL_NONE,
        bool $hasTag = Values::BOOL_NONE,
        string $startTime = Values::NONE,
        string $endTime = Values::NONE,
        string $callType = Values::NONE,
        string $callState = Values::NONE,
        string $direction = Values::NONE,
        string $processingState = Values::NONE,
        string $sortBy = Values::NONE,
        string $subaccount = Values::NONE,
        bool $abnormalSession = Values::BOOL_NONE,
        string $answeredBy = Values::NONE,
        string $connectivityIssue = Values::NONE,
        string $qualityIssue = Values::NONE,
        bool $spam = Values::BOOL_NONE,
        string $callScore = Values::NONE

    ): ReadCallSummariesOptions
    {
        return new ReadCallSummariesOptions(
            $from,
            $to,
            $fromCarrier,
            $toCarrier,
            $fromCountryCode,
            $toCountryCode,
            $branded,
            $verifiedCaller,
            $hasTag,
            $startTime,
            $endTime,
            $callType,
            $callState,
            $direction,
            $processingState,
            $sortBy,
            $subaccount,
            $abnormalSession,
            $answeredBy,
            $connectivityIssue,
            $qualityIssue,
            $spam,
            $callScore
        );
    }

}

class ReadCallSummariesOptions extends Options
    {
    /**
     * @param string $from A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @param string $to A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @param string $fromCarrier An origination carrier.
     * @param string $toCarrier A destination carrier.
     * @param string $fromCountryCode A source country code based on phone number in From.
     * @param string $toCountryCode A destination country code. Based on phone number in To.
     * @param bool $branded A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls.
     * @param bool $verifiedCaller A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.
     * @param bool $hasTag A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
     * @param string $startTime A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
     * @param string $endTime An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
     * @param string $callType A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
     * @param string $callState A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
     * @param string $direction A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
     * @param string $processingState A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
     * @param string $sortBy A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
     * @param string $subaccount A unique SID identifier of a Subaccount.
     * @param bool $abnormalSession A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
     * @param string $answeredBy An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
     * @param string $connectivityIssue A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
     * @param string $qualityIssue A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
     * @param bool $spam A boolean flag indicating spam calls.
     * @param string $callScore A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
     */
    public function __construct(
        
        string $from = Values::NONE,
        string $to = Values::NONE,
        string $fromCarrier = Values::NONE,
        string $toCarrier = Values::NONE,
        string $fromCountryCode = Values::NONE,
        string $toCountryCode = Values::NONE,
        bool $branded = Values::BOOL_NONE,
        bool $verifiedCaller = Values::BOOL_NONE,
        bool $hasTag = Values::BOOL_NONE,
        string $startTime = Values::NONE,
        string $endTime = Values::NONE,
        string $callType = Values::NONE,
        string $callState = Values::NONE,
        string $direction = Values::NONE,
        string $processingState = Values::NONE,
        string $sortBy = Values::NONE,
        string $subaccount = Values::NONE,
        bool $abnormalSession = Values::BOOL_NONE,
        string $answeredBy = Values::NONE,
        string $connectivityIssue = Values::NONE,
        string $qualityIssue = Values::NONE,
        bool $spam = Values::BOOL_NONE,
        string $callScore = Values::NONE

    ) {
        $this->options['from'] = $from;
        $this->options['to'] = $to;
        $this->options['fromCarrier'] = $fromCarrier;
        $this->options['toCarrier'] = $toCarrier;
        $this->options['fromCountryCode'] = $fromCountryCode;
        $this->options['toCountryCode'] = $toCountryCode;
        $this->options['branded'] = $branded;
        $this->options['verifiedCaller'] = $verifiedCaller;
        $this->options['hasTag'] = $hasTag;
        $this->options['startTime'] = $startTime;
        $this->options['endTime'] = $endTime;
        $this->options['callType'] = $callType;
        $this->options['callState'] = $callState;
        $this->options['direction'] = $direction;
        $this->options['processingState'] = $processingState;
        $this->options['sortBy'] = $sortBy;
        $this->options['subaccount'] = $subaccount;
        $this->options['abnormalSession'] = $abnormalSession;
        $this->options['answeredBy'] = $answeredBy;
        $this->options['connectivityIssue'] = $connectivityIssue;
        $this->options['qualityIssue'] = $qualityIssue;
        $this->options['spam'] = $spam;
        $this->options['callScore'] = $callScore;
    }

    /**
     * A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     *
     * @param string $from A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @return $this Fluent Builder
     */
    public function setFrom(string $from): self
    {
        $this->options['from'] = $from;
        return $this;
    }

    /**
     * A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     *
     * @param string $to A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @return $this Fluent Builder
     */
    public function setTo(string $to): self
    {
        $this->options['to'] = $to;
        return $this;
    }

    /**
     * An origination carrier.
     *
     * @param string $fromCarrier An origination carrier.
     * @return $this Fluent Builder
     */
    public function setFromCarrier(string $fromCarrier): self
    {
        $this->options['fromCarrier'] = $fromCarrier;
        return $this;
    }

    /**
     * A destination carrier.
     *
     * @param string $toCarrier A destination carrier.
     * @return $this Fluent Builder
     */
    public function setToCarrier(string $toCarrier): self
    {
        $this->options['toCarrier'] = $toCarrier;
        return $this;
    }

    /**
     * A source country code based on phone number in From.
     *
     * @param string $fromCountryCode A source country code based on phone number in From.
     * @return $this Fluent Builder
     */
    public function setFromCountryCode(string $fromCountryCode): self
    {
        $this->options['fromCountryCode'] = $fromCountryCode;
        return $this;
    }

    /**
     * A destination country code. Based on phone number in To.
     *
     * @param string $toCountryCode A destination country code. Based on phone number in To.
     * @return $this Fluent Builder
     */
    public function setToCountryCode(string $toCountryCode): self
    {
        $this->options['toCountryCode'] = $toCountryCode;
        return $this;
    }

    /**
     * A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls.
     *
     * @param bool $branded A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls.
     * @return $this Fluent Builder
     */
    public function setBranded(bool $branded): self
    {
        $this->options['branded'] = $branded;
        return $this;
    }

    /**
     * A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.
     *
     * @param bool $verifiedCaller A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.
     * @return $this Fluent Builder
     */
    public function setVerifiedCaller(bool $verifiedCaller): self
    {
        $this->options['verifiedCaller'] = $verifiedCaller;
        return $this;
    }

    /**
     * A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
     *
     * @param bool $hasTag A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
     * @return $this Fluent Builder
     */
    public function setHasTag(bool $hasTag): self
    {
        $this->options['hasTag'] = $hasTag;
        return $this;
    }

    /**
     * A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
     *
     * @param string $startTime A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
     * @return $this Fluent Builder
     */
    public function setStartTime(string $startTime): self
    {
        $this->options['startTime'] = $startTime;
        return $this;
    }

    /**
     * An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
     *
     * @param string $endTime An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
     * @return $this Fluent Builder
     */
    public function setEndTime(string $endTime): self
    {
        $this->options['endTime'] = $endTime;
        return $this;
    }

    /**
     * A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
     *
     * @param string $callType A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
     * @return $this Fluent Builder
     */
    public function setCallType(string $callType): self
    {
        $this->options['callType'] = $callType;
        return $this;
    }

    /**
     * A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
     *
     * @param string $callState A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
     * @return $this Fluent Builder
     */
    public function setCallState(string $callState): self
    {
        $this->options['callState'] = $callState;
        return $this;
    }

    /**
     * A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
     *
     * @param string $direction A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
     * @return $this Fluent Builder
     */
    public function setDirection(string $direction): self
    {
        $this->options['direction'] = $direction;
        return $this;
    }

    /**
     * A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
     *
     * @param string $processingState A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
     * @return $this Fluent Builder
     */
    public function setProcessingState(string $processingState): self
    {
        $this->options['processingState'] = $processingState;
        return $this;
    }

    /**
     * A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
     *
     * @param string $sortBy A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
     * @return $this Fluent Builder
     */
    public function setSortBy(string $sortBy): self
    {
        $this->options['sortBy'] = $sortBy;
        return $this;
    }

    /**
     * A unique SID identifier of a Subaccount.
     *
     * @param string $subaccount A unique SID identifier of a Subaccount.
     * @return $this Fluent Builder
     */
    public function setSubaccount(string $subaccount): self
    {
        $this->options['subaccount'] = $subaccount;
        return $this;
    }

    /**
     * A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
     *
     * @param bool $abnormalSession A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
     * @return $this Fluent Builder
     */
    public function setAbnormalSession(bool $abnormalSession): self
    {
        $this->options['abnormalSession'] = $abnormalSession;
        return $this;
    }

    /**
     * An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
     *
     * @param string $answeredBy An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
     * @return $this Fluent Builder
     */
    public function setAnsweredBy(string $answeredBy): self
    {
        $this->options['answeredBy'] = $answeredBy;
        return $this;
    }

    /**
     * A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
     *
     * @param string $connectivityIssue A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
     * @return $this Fluent Builder
     */
    public function setConnectivityIssue(string $connectivityIssue): self
    {
        $this->options['connectivityIssue'] = $connectivityIssue;
        return $this;
    }

    /**
     * A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
     *
     * @param string $qualityIssue A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
     * @return $this Fluent Builder
     */
    public function setQualityIssue(string $qualityIssue): self
    {
        $this->options['qualityIssue'] = $qualityIssue;
        return $this;
    }

    /**
     * A boolean flag indicating spam calls.
     *
     * @param bool $spam A boolean flag indicating spam calls.
     * @return $this Fluent Builder
     */
    public function setSpam(bool $spam): self
    {
        $this->options['spam'] = $spam;
        return $this;
    }

    /**
     * A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
     *
     * @param string $callScore A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
     * @return $this Fluent Builder
     */
    public function setCallScore(string $callScore): self
    {
        $this->options['callScore'] = $callScore;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Insights.V1.ReadCallSummariesOptions ' . $options . ']';
    }
}

