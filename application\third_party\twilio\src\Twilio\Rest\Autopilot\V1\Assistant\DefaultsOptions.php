<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Autopilot
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Autopilot\V1\Assistant;

use Twilio\Options;
use Twilio\Values;

abstract class DefaultsOptions
{

    /**
     * @param array $defaults A JSON string that describes the default task links for the `assistant_initiation`, `collect`, and `fallback` situations.
     * @return UpdateDefaultsOptions Options builder
     */
    public static function update(
        
        array $defaults = Values::ARRAY_NONE

    ): UpdateDefaultsOptions
    {
        return new UpdateDefaultsOptions(
            $defaults
        );
    }

}


class UpdateDefaultsOptions extends Options
    {
    /**
     * @param array $defaults A JSON string that describes the default task links for the `assistant_initiation`, `collect`, and `fallback` situations.
     */
    public function __construct(
        
        array $defaults = Values::ARRAY_NONE

    ) {
        $this->options['defaults'] = $defaults;
    }

    /**
     * A JSON string that describes the default task links for the `assistant_initiation`, `collect`, and `fallback` situations.
     *
     * @param array $defaults A JSON string that describes the default task links for the `assistant_initiation`, `collect`, and `fallback` situations.
     * @return $this Fluent Builder
     */
    public function setDefaults(array $defaults): self
    {
        $this->options['defaults'] = $defaults;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Autopilot.V1.UpdateDefaultsOptions ' . $options . ']';
    }
}

