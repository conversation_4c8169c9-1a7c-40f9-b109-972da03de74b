<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Numbers\V2;

use Twilio\Options;
use Twilio\Values;

abstract class HostedNumberOrderOptions
{
    /**
     * @param string $accountSid This defaults to the AccountSid of the authorization the user is using. This can be provided to specify a subaccount to add the HostedNumberOrder to.
     * @param string $friendlyName A 128 character string that is a human readable text that describes this resource.
     * @param string[] $ccEmails Optional. A list of emails that the LOA document for this HostedNumberOrder will be carbon copied to.
     * @param string $smsUrl The URL that Twilio should request when somebody sends an SMS to the phone number. This will be copied onto the IncomingPhoneNumber resource.
     * @param string $smsMethod The HTTP method that should be used to request the SmsUrl. Must be either `GET` or `POST`.  This will be copied onto the IncomingPhoneNumber resource.
     * @param string $smsFallbackUrl A URL that Twilio will request if an error occurs requesting or executing the TwiML defined by SmsUrl. This will be copied onto the IncomingPhoneNumber resource.
     * @param bool $smsCapability Used to specify that the SMS capability will be hosted on Twilio's platform.
     * @param string $smsFallbackMethod The HTTP method that should be used to request the SmsFallbackUrl. Must be either `GET` or `POST`. This will be copied onto the IncomingPhoneNumber resource.
     * @param string $statusCallbackUrl Optional. The Status Callback URL attached to the IncomingPhoneNumber resource.
     * @param string $statusCallbackMethod Optional. The Status Callback Method attached to the IncomingPhoneNumber resource.
     * @param string $smsApplicationSid Optional. The 34 character sid of the application Twilio should use to handle SMS messages sent to this number. If a `SmsApplicationSid` is present, Twilio will ignore all of the SMS urls above and use those set on the application.
     * @param string $contactTitle The title of the person authorized to sign the Authorization Document for this phone number.
     * @return CreateHostedNumberOrderOptions Options builder
     */
    public static function create(
        
        string $accountSid = Values::NONE,
        string $friendlyName = Values::NONE,
        array $ccEmails = Values::ARRAY_NONE,
        string $smsUrl = Values::NONE,
        string $smsMethod = Values::NONE,
        string $smsFallbackUrl = Values::NONE,
        bool $smsCapability = Values::BOOL_NONE,
        string $smsFallbackMethod = Values::NONE,
        string $statusCallbackUrl = Values::NONE,
        string $statusCallbackMethod = Values::NONE,
        string $smsApplicationSid = Values::NONE,
        string $contactTitle = Values::NONE

    ): CreateHostedNumberOrderOptions
    {
        return new CreateHostedNumberOrderOptions(
            $accountSid,
            $friendlyName,
            $ccEmails,
            $smsUrl,
            $smsMethod,
            $smsFallbackUrl,
            $smsCapability,
            $smsFallbackMethod,
            $statusCallbackUrl,
            $statusCallbackMethod,
            $smsApplicationSid,
            $contactTitle
        );
    }



    /**
     * @param string $status The Status of this HostedNumberOrder. One of `received`, `pending-verification`, `verified`, `pending-loa`, `carrier-processing`, `testing`, `completed`, `failed`, or `action-required`.
     * @param bool $smsCapability Whether the SMS capability will be hosted on our platform. Can be `true` of `false`.
     * @param string $phoneNumber An E164 formatted phone number hosted by this HostedNumberOrder.
     * @param string $incomingPhoneNumberSid A 34 character string that uniquely identifies the IncomingPhoneNumber resource created by this HostedNumberOrder.
     * @param string $friendlyName A human readable description of this resource, up to 128 characters.
     * @return ReadHostedNumberOrderOptions Options builder
     */
    public static function read(
        
        string $status = Values::NONE,
        bool $smsCapability = Values::BOOL_NONE,
        string $phoneNumber = Values::NONE,
        string $incomingPhoneNumberSid = Values::NONE,
        string $friendlyName = Values::NONE

    ): ReadHostedNumberOrderOptions
    {
        return new ReadHostedNumberOrderOptions(
            $status,
            $smsCapability,
            $phoneNumber,
            $incomingPhoneNumberSid,
            $friendlyName
        );
    }

}

class CreateHostedNumberOrderOptions extends Options
    {
    /**
     * @param string $accountSid This defaults to the AccountSid of the authorization the user is using. This can be provided to specify a subaccount to add the HostedNumberOrder to.
     * @param string $friendlyName A 128 character string that is a human readable text that describes this resource.
     * @param string[] $ccEmails Optional. A list of emails that the LOA document for this HostedNumberOrder will be carbon copied to.
     * @param string $smsUrl The URL that Twilio should request when somebody sends an SMS to the phone number. This will be copied onto the IncomingPhoneNumber resource.
     * @param string $smsMethod The HTTP method that should be used to request the SmsUrl. Must be either `GET` or `POST`.  This will be copied onto the IncomingPhoneNumber resource.
     * @param string $smsFallbackUrl A URL that Twilio will request if an error occurs requesting or executing the TwiML defined by SmsUrl. This will be copied onto the IncomingPhoneNumber resource.
     * @param bool $smsCapability Used to specify that the SMS capability will be hosted on Twilio's platform.
     * @param string $smsFallbackMethod The HTTP method that should be used to request the SmsFallbackUrl. Must be either `GET` or `POST`. This will be copied onto the IncomingPhoneNumber resource.
     * @param string $statusCallbackUrl Optional. The Status Callback URL attached to the IncomingPhoneNumber resource.
     * @param string $statusCallbackMethod Optional. The Status Callback Method attached to the IncomingPhoneNumber resource.
     * @param string $smsApplicationSid Optional. The 34 character sid of the application Twilio should use to handle SMS messages sent to this number. If a `SmsApplicationSid` is present, Twilio will ignore all of the SMS urls above and use those set on the application.
     * @param string $contactTitle The title of the person authorized to sign the Authorization Document for this phone number.
     */
    public function __construct(
        
        string $accountSid = Values::NONE,
        string $friendlyName = Values::NONE,
        array $ccEmails = Values::ARRAY_NONE,
        string $smsUrl = Values::NONE,
        string $smsMethod = Values::NONE,
        string $smsFallbackUrl = Values::NONE,
        bool $smsCapability = Values::BOOL_NONE,
        string $smsFallbackMethod = Values::NONE,
        string $statusCallbackUrl = Values::NONE,
        string $statusCallbackMethod = Values::NONE,
        string $smsApplicationSid = Values::NONE,
        string $contactTitle = Values::NONE

    ) {
        $this->options['accountSid'] = $accountSid;
        $this->options['friendlyName'] = $friendlyName;
        $this->options['ccEmails'] = $ccEmails;
        $this->options['smsUrl'] = $smsUrl;
        $this->options['smsMethod'] = $smsMethod;
        $this->options['smsFallbackUrl'] = $smsFallbackUrl;
        $this->options['smsCapability'] = $smsCapability;
        $this->options['smsFallbackMethod'] = $smsFallbackMethod;
        $this->options['statusCallbackUrl'] = $statusCallbackUrl;
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        $this->options['smsApplicationSid'] = $smsApplicationSid;
        $this->options['contactTitle'] = $contactTitle;
    }

    /**
     * This defaults to the AccountSid of the authorization the user is using. This can be provided to specify a subaccount to add the HostedNumberOrder to.
     *
     * @param string $accountSid This defaults to the AccountSid of the authorization the user is using. This can be provided to specify a subaccount to add the HostedNumberOrder to.
     * @return $this Fluent Builder
     */
    public function setAccountSid(string $accountSid): self
    {
        $this->options['accountSid'] = $accountSid;
        return $this;
    }

    /**
     * A 128 character string that is a human readable text that describes this resource.
     *
     * @param string $friendlyName A 128 character string that is a human readable text that describes this resource.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Optional. A list of emails that the LOA document for this HostedNumberOrder will be carbon copied to.
     *
     * @param string[] $ccEmails Optional. A list of emails that the LOA document for this HostedNumberOrder will be carbon copied to.
     * @return $this Fluent Builder
     */
    public function setCcEmails(array $ccEmails): self
    {
        $this->options['ccEmails'] = $ccEmails;
        return $this;
    }

    /**
     * The URL that Twilio should request when somebody sends an SMS to the phone number. This will be copied onto the IncomingPhoneNumber resource.
     *
     * @param string $smsUrl The URL that Twilio should request when somebody sends an SMS to the phone number. This will be copied onto the IncomingPhoneNumber resource.
     * @return $this Fluent Builder
     */
    public function setSmsUrl(string $smsUrl): self
    {
        $this->options['smsUrl'] = $smsUrl;
        return $this;
    }

    /**
     * The HTTP method that should be used to request the SmsUrl. Must be either `GET` or `POST`.  This will be copied onto the IncomingPhoneNumber resource.
     *
     * @param string $smsMethod The HTTP method that should be used to request the SmsUrl. Must be either `GET` or `POST`.  This will be copied onto the IncomingPhoneNumber resource.
     * @return $this Fluent Builder
     */
    public function setSmsMethod(string $smsMethod): self
    {
        $this->options['smsMethod'] = $smsMethod;
        return $this;
    }

    /**
     * A URL that Twilio will request if an error occurs requesting or executing the TwiML defined by SmsUrl. This will be copied onto the IncomingPhoneNumber resource.
     *
     * @param string $smsFallbackUrl A URL that Twilio will request if an error occurs requesting or executing the TwiML defined by SmsUrl. This will be copied onto the IncomingPhoneNumber resource.
     * @return $this Fluent Builder
     */
    public function setSmsFallbackUrl(string $smsFallbackUrl): self
    {
        $this->options['smsFallbackUrl'] = $smsFallbackUrl;
        return $this;
    }

    /**
     * Used to specify that the SMS capability will be hosted on Twilio's platform.
     *
     * @param bool $smsCapability Used to specify that the SMS capability will be hosted on Twilio's platform.
     * @return $this Fluent Builder
     */
    public function setSmsCapability(bool $smsCapability): self
    {
        $this->options['smsCapability'] = $smsCapability;
        return $this;
    }

    /**
     * The HTTP method that should be used to request the SmsFallbackUrl. Must be either `GET` or `POST`. This will be copied onto the IncomingPhoneNumber resource.
     *
     * @param string $smsFallbackMethod The HTTP method that should be used to request the SmsFallbackUrl. Must be either `GET` or `POST`. This will be copied onto the IncomingPhoneNumber resource.
     * @return $this Fluent Builder
     */
    public function setSmsFallbackMethod(string $smsFallbackMethod): self
    {
        $this->options['smsFallbackMethod'] = $smsFallbackMethod;
        return $this;
    }

    /**
     * Optional. The Status Callback URL attached to the IncomingPhoneNumber resource.
     *
     * @param string $statusCallbackUrl Optional. The Status Callback URL attached to the IncomingPhoneNumber resource.
     * @return $this Fluent Builder
     */
    public function setStatusCallbackUrl(string $statusCallbackUrl): self
    {
        $this->options['statusCallbackUrl'] = $statusCallbackUrl;
        return $this;
    }

    /**
     * Optional. The Status Callback Method attached to the IncomingPhoneNumber resource.
     *
     * @param string $statusCallbackMethod Optional. The Status Callback Method attached to the IncomingPhoneNumber resource.
     * @return $this Fluent Builder
     */
    public function setStatusCallbackMethod(string $statusCallbackMethod): self
    {
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        return $this;
    }

    /**
     * Optional. The 34 character sid of the application Twilio should use to handle SMS messages sent to this number. If a `SmsApplicationSid` is present, Twilio will ignore all of the SMS urls above and use those set on the application.
     *
     * @param string $smsApplicationSid Optional. The 34 character sid of the application Twilio should use to handle SMS messages sent to this number. If a `SmsApplicationSid` is present, Twilio will ignore all of the SMS urls above and use those set on the application.
     * @return $this Fluent Builder
     */
    public function setSmsApplicationSid(string $smsApplicationSid): self
    {
        $this->options['smsApplicationSid'] = $smsApplicationSid;
        return $this;
    }

    /**
     * The title of the person authorized to sign the Authorization Document for this phone number.
     *
     * @param string $contactTitle The title of the person authorized to sign the Authorization Document for this phone number.
     * @return $this Fluent Builder
     */
    public function setContactTitle(string $contactTitle): self
    {
        $this->options['contactTitle'] = $contactTitle;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Numbers.V2.CreateHostedNumberOrderOptions ' . $options . ']';
    }
}



class ReadHostedNumberOrderOptions extends Options
    {
    /**
     * @param string $status The Status of this HostedNumberOrder. One of `received`, `pending-verification`, `verified`, `pending-loa`, `carrier-processing`, `testing`, `completed`, `failed`, or `action-required`.
     * @param bool $smsCapability Whether the SMS capability will be hosted on our platform. Can be `true` of `false`.
     * @param string $phoneNumber An E164 formatted phone number hosted by this HostedNumberOrder.
     * @param string $incomingPhoneNumberSid A 34 character string that uniquely identifies the IncomingPhoneNumber resource created by this HostedNumberOrder.
     * @param string $friendlyName A human readable description of this resource, up to 128 characters.
     */
    public function __construct(
        
        string $status = Values::NONE,
        bool $smsCapability = Values::BOOL_NONE,
        string $phoneNumber = Values::NONE,
        string $incomingPhoneNumberSid = Values::NONE,
        string $friendlyName = Values::NONE

    ) {
        $this->options['status'] = $status;
        $this->options['smsCapability'] = $smsCapability;
        $this->options['phoneNumber'] = $phoneNumber;
        $this->options['incomingPhoneNumberSid'] = $incomingPhoneNumberSid;
        $this->options['friendlyName'] = $friendlyName;
    }

    /**
     * The Status of this HostedNumberOrder. One of `received`, `pending-verification`, `verified`, `pending-loa`, `carrier-processing`, `testing`, `completed`, `failed`, or `action-required`.
     *
     * @param string $status The Status of this HostedNumberOrder. One of `received`, `pending-verification`, `verified`, `pending-loa`, `carrier-processing`, `testing`, `completed`, `failed`, or `action-required`.
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * Whether the SMS capability will be hosted on our platform. Can be `true` of `false`.
     *
     * @param bool $smsCapability Whether the SMS capability will be hosted on our platform. Can be `true` of `false`.
     * @return $this Fluent Builder
     */
    public function setSmsCapability(bool $smsCapability): self
    {
        $this->options['smsCapability'] = $smsCapability;
        return $this;
    }

    /**
     * An E164 formatted phone number hosted by this HostedNumberOrder.
     *
     * @param string $phoneNumber An E164 formatted phone number hosted by this HostedNumberOrder.
     * @return $this Fluent Builder
     */
    public function setPhoneNumber(string $phoneNumber): self
    {
        $this->options['phoneNumber'] = $phoneNumber;
        return $this;
    }

    /**
     * A 34 character string that uniquely identifies the IncomingPhoneNumber resource created by this HostedNumberOrder.
     *
     * @param string $incomingPhoneNumberSid A 34 character string that uniquely identifies the IncomingPhoneNumber resource created by this HostedNumberOrder.
     * @return $this Fluent Builder
     */
    public function setIncomingPhoneNumberSid(string $incomingPhoneNumberSid): self
    {
        $this->options['incomingPhoneNumberSid'] = $incomingPhoneNumberSid;
        return $this;
    }

    /**
     * A human readable description of this resource, up to 128 characters.
     *
     * @param string $friendlyName A human readable description of this resource, up to 128 characters.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Numbers.V2.ReadHostedNumberOrderOptions ' . $options . ']';
    }
}

