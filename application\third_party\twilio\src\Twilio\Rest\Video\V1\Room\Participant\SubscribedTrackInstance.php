<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Video\V1\Room\Participant;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $sid
 * @property string|null $participantSid
 * @property string|null $publisherSid
 * @property string|null $roomSid
 * @property string|null $name
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property bool|null $enabled
 * @property string $kind
 * @property string|null $url
 */
class SubscribedTrackInstance extends InstanceResource
{
    /**
     * Initialize the SubscribedTrackInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $roomSid The SID of the Room where the Track resource to fetch is subscribed.
     * @param string $participantSid The SID of the participant that subscribes to the Track resource to fetch.
     * @param string $sid The SID of the RoomParticipantSubscribedTrack resource to fetch.
     */
    public function __construct(Version $version, array $payload, string $roomSid, string $participantSid, string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'participantSid' => Values::array_get($payload, 'participant_sid'),
            'publisherSid' => Values::array_get($payload, 'publisher_sid'),
            'roomSid' => Values::array_get($payload, 'room_sid'),
            'name' => Values::array_get($payload, 'name'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'enabled' => Values::array_get($payload, 'enabled'),
            'kind' => Values::array_get($payload, 'kind'),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['roomSid' => $roomSid, 'participantSid' => $participantSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return SubscribedTrackContext Context for this SubscribedTrackInstance
     */
    protected function proxy(): SubscribedTrackContext
    {
        if (!$this->context) {
            $this->context = new SubscribedTrackContext(
                $this->version,
                $this->solution['roomSid'],
                $this->solution['participantSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the SubscribedTrackInstance
     *
     * @return SubscribedTrackInstance Fetched SubscribedTrackInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): SubscribedTrackInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Video.V1.SubscribedTrackInstance ' . \implode(' ', $context) . ']';
    }
}

