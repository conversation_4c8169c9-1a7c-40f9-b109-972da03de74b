<?php return array (
  'AFN' => 
  array (
    'alphabeticCode' => 'AFN',
    'currency' => 'Afghani',
    'minorUnit' => 2,
    'numericCode' => 971,
  ),
  'EUR' => 
  array (
    'alphabeticCode' => 'EUR',
    'currency' => 'Euro',
    'minorUnit' => 2,
    'numericCode' => 978,
  ),
  'ALL' => 
  array (
    'alphabeticCode' => 'ALL',
    'currency' => 'Lek',
    'minorUnit' => 2,
    'numericCode' => 8,
  ),
  'DZD' => 
  array (
    'alphabeticCode' => 'DZD',
    'currency' => 'Algerian Dinar',
    'minorUnit' => 2,
    'numericCode' => 12,
  ),
  'USD' => 
  array (
    'alphabeticCode' => 'USD',
    'currency' => 'US Dollar',
    'minorUnit' => 2,
    'numericCode' => 840,
  ),
  'AOA' => 
  array (
    'alphabeticCode' => 'AOA',
    'currency' => 'Kwanza',
    'minorUnit' => 2,
    'numericCode' => 973,
  ),
  'XCD' => 
  array (
    'alphabeticCode' => 'XCD',
    'currency' => 'East Caribbean Dollar',
    'minorUnit' => 2,
    'numericCode' => 951,
  ),
  'ARS' => 
  array (
    'alphabeticCode' => 'ARS',
    'currency' => 'Argentine Peso',
    'minorUnit' => 2,
    'numericCode' => 32,
  ),
  'AMD' => 
  array (
    'alphabeticCode' => 'AMD',
    'currency' => 'Armenian Dram',
    'minorUnit' => 2,
    'numericCode' => 51,
  ),
  'AWG' => 
  array (
    'alphabeticCode' => 'AWG',
    'currency' => 'Aruban Florin',
    'minorUnit' => 2,
    'numericCode' => 533,
  ),
  'AUD' => 
  array (
    'alphabeticCode' => 'AUD',
    'currency' => 'Australian Dollar',
    'minorUnit' => 2,
    'numericCode' => 36,
  ),
  'AZN' => 
  array (
    'alphabeticCode' => 'AZN',
    'currency' => 'Azerbaijan Manat',
    'minorUnit' => 2,
    'numericCode' => 944,
  ),
  'BSD' => 
  array (
    'alphabeticCode' => 'BSD',
    'currency' => 'Bahamian Dollar',
    'minorUnit' => 2,
    'numericCode' => 44,
  ),
  'BHD' => 
  array (
    'alphabeticCode' => 'BHD',
    'currency' => 'Bahraini Dinar',
    'minorUnit' => 3,
    'numericCode' => 48,
  ),
  'BDT' => 
  array (
    'alphabeticCode' => 'BDT',
    'currency' => 'Taka',
    'minorUnit' => 2,
    'numericCode' => 50,
  ),
  'BBD' => 
  array (
    'alphabeticCode' => 'BBD',
    'currency' => 'Barbados Dollar',
    'minorUnit' => 2,
    'numericCode' => 52,
  ),
  'BYN' => 
  array (
    'alphabeticCode' => 'BYN',
    'currency' => 'Belarusian Ruble',
    'minorUnit' => 2,
    'numericCode' => 933,
  ),
  'BZD' => 
  array (
    'alphabeticCode' => 'BZD',
    'currency' => 'Belize Dollar',
    'minorUnit' => 2,
    'numericCode' => 84,
  ),
  'XOF' => 
  array (
    'alphabeticCode' => 'XOF',
    'currency' => 'CFA Franc BCEAO',
    'minorUnit' => 0,
    'numericCode' => 952,
  ),
  'BMD' => 
  array (
    'alphabeticCode' => 'BMD',
    'currency' => 'Bermudian Dollar',
    'minorUnit' => 2,
    'numericCode' => 60,
  ),
  'INR' => 
  array (
    'alphabeticCode' => 'INR',
    'currency' => 'Indian Rupee',
    'minorUnit' => 2,
    'numericCode' => 356,
  ),
  'BTN' => 
  array (
    'alphabeticCode' => 'BTN',
    'currency' => 'Ngultrum',
    'minorUnit' => 2,
    'numericCode' => 64,
  ),
  'BOB' => 
  array (
    'alphabeticCode' => 'BOB',
    'currency' => 'Boliviano',
    'minorUnit' => 2,
    'numericCode' => 68,
  ),
  'BOV' => 
  array (
    'alphabeticCode' => 'BOV',
    'currency' => 'Mvdol',
    'minorUnit' => 2,
    'numericCode' => 984,
  ),
  'BAM' => 
  array (
    'alphabeticCode' => 'BAM',
    'currency' => 'Convertible Mark',
    'minorUnit' => 2,
    'numericCode' => 977,
  ),
  'BWP' => 
  array (
    'alphabeticCode' => 'BWP',
    'currency' => 'Pula',
    'minorUnit' => 2,
    'numericCode' => 72,
  ),
  'NOK' => 
  array (
    'alphabeticCode' => 'NOK',
    'currency' => 'Norwegian Krone',
    'minorUnit' => 2,
    'numericCode' => 578,
  ),
  'BRL' => 
  array (
    'alphabeticCode' => 'BRL',
    'currency' => 'Brazilian Real',
    'minorUnit' => 2,
    'numericCode' => 986,
  ),
  'BND' => 
  array (
    'alphabeticCode' => 'BND',
    'currency' => 'Brunei Dollar',
    'minorUnit' => 2,
    'numericCode' => 96,
  ),
  'BGN' => 
  array (
    'alphabeticCode' => 'BGN',
    'currency' => 'Bulgarian Lev',
    'minorUnit' => 2,
    'numericCode' => 975,
  ),
  'BIF' => 
  array (
    'alphabeticCode' => 'BIF',
    'currency' => 'Burundi Franc',
    'minorUnit' => 0,
    'numericCode' => 108,
  ),
  'CVE' => 
  array (
    'alphabeticCode' => 'CVE',
    'currency' => 'Cabo Verde Escudo',
    'minorUnit' => 2,
    'numericCode' => 132,
  ),
  'KHR' => 
  array (
    'alphabeticCode' => 'KHR',
    'currency' => 'Riel',
    'minorUnit' => 2,
    'numericCode' => 116,
  ),
  'XAF' => 
  array (
    'alphabeticCode' => 'XAF',
    'currency' => 'CFA Franc BEAC',
    'minorUnit' => 0,
    'numericCode' => 950,
  ),
  'CAD' => 
  array (
    'alphabeticCode' => 'CAD',
    'currency' => 'Canadian Dollar',
    'minorUnit' => 2,
    'numericCode' => 124,
  ),
  'KYD' => 
  array (
    'alphabeticCode' => 'KYD',
    'currency' => 'Cayman Islands Dollar',
    'minorUnit' => 2,
    'numericCode' => 136,
  ),
  'CLP' => 
  array (
    'alphabeticCode' => 'CLP',
    'currency' => 'Chilean Peso',
    'minorUnit' => 0,
    'numericCode' => 152,
  ),
  'CLF' => 
  array (
    'alphabeticCode' => 'CLF',
    'currency' => 'Unidad de Fomento',
    'minorUnit' => 4,
    'numericCode' => 990,
  ),
  'CNY' => 
  array (
    'alphabeticCode' => 'CNY',
    'currency' => 'Yuan Renminbi',
    'minorUnit' => 2,
    'numericCode' => 156,
  ),
  'COP' => 
  array (
    'alphabeticCode' => 'COP',
    'currency' => 'Colombian Peso',
    'minorUnit' => 2,
    'numericCode' => 170,
  ),
  'COU' => 
  array (
    'alphabeticCode' => 'COU',
    'currency' => 'Unidad de Valor Real',
    'minorUnit' => 2,
    'numericCode' => 970,
  ),
  'KMF' => 
  array (
    'alphabeticCode' => 'KMF',
    'currency' => 'Comorian Franc ',
    'minorUnit' => 0,
    'numericCode' => 174,
  ),
  'CDF' => 
  array (
    'alphabeticCode' => 'CDF',
    'currency' => 'Congolese Franc',
    'minorUnit' => 2,
    'numericCode' => 976,
  ),
  'NZD' => 
  array (
    'alphabeticCode' => 'NZD',
    'currency' => 'New Zealand Dollar',
    'minorUnit' => 2,
    'numericCode' => 554,
  ),
  'CRC' => 
  array (
    'alphabeticCode' => 'CRC',
    'currency' => 'Costa Rican Colon',
    'minorUnit' => 2,
    'numericCode' => 188,
  ),
  'HRK' => 
  array (
    'alphabeticCode' => 'HRK',
    'currency' => 'Kuna',
    'minorUnit' => 2,
    'numericCode' => 191,
  ),
  'CUP' => 
  array (
    'alphabeticCode' => 'CUP',
    'currency' => 'Cuban Peso',
    'minorUnit' => 2,
    'numericCode' => 192,
  ),
  'CUC' => 
  array (
    'alphabeticCode' => 'CUC',
    'currency' => 'Peso Convertible',
    'minorUnit' => 2,
    'numericCode' => 931,
  ),
  'ANG' => 
  array (
    'alphabeticCode' => 'ANG',
    'currency' => 'Netherlands Antillean Guilder',
    'minorUnit' => 2,
    'numericCode' => 532,
  ),
  'CZK' => 
  array (
    'alphabeticCode' => 'CZK',
    'currency' => 'Czech Koruna',
    'minorUnit' => 2,
    'numericCode' => 203,
  ),
  'DKK' => 
  array (
    'alphabeticCode' => 'DKK',
    'currency' => 'Danish Krone',
    'minorUnit' => 2,
    'numericCode' => 208,
  ),
  'DJF' => 
  array (
    'alphabeticCode' => 'DJF',
    'currency' => 'Djibouti Franc',
    'minorUnit' => 0,
    'numericCode' => 262,
  ),
  'DOP' => 
  array (
    'alphabeticCode' => 'DOP',
    'currency' => 'Dominican Peso',
    'minorUnit' => 2,
    'numericCode' => 214,
  ),
  'EGP' => 
  array (
    'alphabeticCode' => 'EGP',
    'currency' => 'Egyptian Pound',
    'minorUnit' => 2,
    'numericCode' => 818,
  ),
  'SVC' => 
  array (
    'alphabeticCode' => 'SVC',
    'currency' => 'El Salvador Colon',
    'minorUnit' => 2,
    'numericCode' => 222,
  ),
  'ERN' => 
  array (
    'alphabeticCode' => 'ERN',
    'currency' => 'Nakfa',
    'minorUnit' => 2,
    'numericCode' => 232,
  ),
  'ETB' => 
  array (
    'alphabeticCode' => 'ETB',
    'currency' => 'Ethiopian Birr',
    'minorUnit' => 2,
    'numericCode' => 230,
  ),
  'FKP' => 
  array (
    'alphabeticCode' => 'FKP',
    'currency' => 'Falkland Islands Pound',
    'minorUnit' => 2,
    'numericCode' => 238,
  ),
  'FJD' => 
  array (
    'alphabeticCode' => 'FJD',
    'currency' => 'Fiji Dollar',
    'minorUnit' => 2,
    'numericCode' => 242,
  ),
  'XPF' => 
  array (
    'alphabeticCode' => 'XPF',
    'currency' => 'CFP Franc',
    'minorUnit' => 0,
    'numericCode' => 953,
  ),
  'GMD' => 
  array (
    'alphabeticCode' => 'GMD',
    'currency' => 'Dalasi',
    'minorUnit' => 2,
    'numericCode' => 270,
  ),
  'GEL' => 
  array (
    'alphabeticCode' => 'GEL',
    'currency' => 'Lari',
    'minorUnit' => 2,
    'numericCode' => 981,
  ),
  'GHS' => 
  array (
    'alphabeticCode' => 'GHS',
    'currency' => 'Ghana Cedi',
    'minorUnit' => 2,
    'numericCode' => 936,
  ),
  'GIP' => 
  array (
    'alphabeticCode' => 'GIP',
    'currency' => 'Gibraltar Pound',
    'minorUnit' => 2,
    'numericCode' => 292,
  ),
  'GTQ' => 
  array (
    'alphabeticCode' => 'GTQ',
    'currency' => 'Quetzal',
    'minorUnit' => 2,
    'numericCode' => 320,
  ),
  'GBP' => 
  array (
    'alphabeticCode' => 'GBP',
    'currency' => 'Pound Sterling',
    'minorUnit' => 2,
    'numericCode' => 826,
  ),
  'GNF' => 
  array (
    'alphabeticCode' => 'GNF',
    'currency' => 'Guinean Franc',
    'minorUnit' => 0,
    'numericCode' => 324,
  ),
  'GYD' => 
  array (
    'alphabeticCode' => 'GYD',
    'currency' => 'Guyana Dollar',
    'minorUnit' => 2,
    'numericCode' => 328,
  ),
  'HTG' => 
  array (
    'alphabeticCode' => 'HTG',
    'currency' => 'Gourde',
    'minorUnit' => 2,
    'numericCode' => 332,
  ),
  'HNL' => 
  array (
    'alphabeticCode' => 'HNL',
    'currency' => 'Lempira',
    'minorUnit' => 2,
    'numericCode' => 340,
  ),
  'HKD' => 
  array (
    'alphabeticCode' => 'HKD',
    'currency' => 'Hong Kong Dollar',
    'minorUnit' => 2,
    'numericCode' => 344,
  ),
  'HUF' => 
  array (
    'alphabeticCode' => 'HUF',
    'currency' => 'Forint',
    'minorUnit' => 2,
    'numericCode' => 348,
  ),
  'ISK' => 
  array (
    'alphabeticCode' => 'ISK',
    'currency' => 'Iceland Krona',
    'minorUnit' => 0,
    'numericCode' => 352,
  ),
  'IDR' => 
  array (
    'alphabeticCode' => 'IDR',
    'currency' => 'Rupiah',
    'minorUnit' => 2,
    'numericCode' => 360,
  ),
  'XDR' => 
  array (
    'alphabeticCode' => 'XDR',
    'currency' => 'SDR (Special Drawing Right)',
    'minorUnit' => 0,
    'numericCode' => 960,
  ),
  'IRR' => 
  array (
    'alphabeticCode' => 'IRR',
    'currency' => 'Iranian Rial',
    'minorUnit' => 2,
    'numericCode' => 364,
  ),
  'IQD' => 
  array (
    'alphabeticCode' => 'IQD',
    'currency' => 'Iraqi Dinar',
    'minorUnit' => 3,
    'numericCode' => 368,
  ),
  'ILS' => 
  array (
    'alphabeticCode' => 'ILS',
    'currency' => 'New Israeli Sheqel',
    'minorUnit' => 2,
    'numericCode' => 376,
  ),
  'JMD' => 
  array (
    'alphabeticCode' => 'JMD',
    'currency' => 'Jamaican Dollar',
    'minorUnit' => 2,
    'numericCode' => 388,
  ),
  'JPY' => 
  array (
    'alphabeticCode' => 'JPY',
    'currency' => 'Yen',
    'minorUnit' => 0,
    'numericCode' => 392,
  ),
  'JOD' => 
  array (
    'alphabeticCode' => 'JOD',
    'currency' => 'Jordanian Dinar',
    'minorUnit' => 3,
    'numericCode' => 400,
  ),
  'KZT' => 
  array (
    'alphabeticCode' => 'KZT',
    'currency' => 'Tenge',
    'minorUnit' => 2,
    'numericCode' => 398,
  ),
  'KES' => 
  array (
    'alphabeticCode' => 'KES',
    'currency' => 'Kenyan Shilling',
    'minorUnit' => 2,
    'numericCode' => 404,
  ),
  'KPW' => 
  array (
    'alphabeticCode' => 'KPW',
    'currency' => 'North Korean Won',
    'minorUnit' => 2,
    'numericCode' => 408,
  ),
  'KRW' => 
  array (
    'alphabeticCode' => 'KRW',
    'currency' => 'Won',
    'minorUnit' => 0,
    'numericCode' => 410,
  ),
  'KWD' => 
  array (
    'alphabeticCode' => 'KWD',
    'currency' => 'Kuwaiti Dinar',
    'minorUnit' => 3,
    'numericCode' => 414,
  ),
  'KGS' => 
  array (
    'alphabeticCode' => 'KGS',
    'currency' => 'Som',
    'minorUnit' => 2,
    'numericCode' => 417,
  ),
  'LAK' => 
  array (
    'alphabeticCode' => 'LAK',
    'currency' => 'Lao Kip',
    'minorUnit' => 2,
    'numericCode' => 418,
  ),
  'LBP' => 
  array (
    'alphabeticCode' => 'LBP',
    'currency' => 'Lebanese Pound',
    'minorUnit' => 2,
    'numericCode' => 422,
  ),
  'LSL' => 
  array (
    'alphabeticCode' => 'LSL',
    'currency' => 'Loti',
    'minorUnit' => 2,
    'numericCode' => 426,
  ),
  'ZAR' => 
  array (
    'alphabeticCode' => 'ZAR',
    'currency' => 'Rand',
    'minorUnit' => 2,
    'numericCode' => 710,
  ),
  'LRD' => 
  array (
    'alphabeticCode' => 'LRD',
    'currency' => 'Liberian Dollar',
    'minorUnit' => 2,
    'numericCode' => 430,
  ),
  'LYD' => 
  array (
    'alphabeticCode' => 'LYD',
    'currency' => 'Libyan Dinar',
    'minorUnit' => 3,
    'numericCode' => 434,
  ),
  'CHF' => 
  array (
    'alphabeticCode' => 'CHF',
    'currency' => 'Swiss Franc',
    'minorUnit' => 2,
    'numericCode' => 756,
  ),
  'MOP' => 
  array (
    'alphabeticCode' => 'MOP',
    'currency' => 'Pataca',
    'minorUnit' => 2,
    'numericCode' => 446,
  ),
  'MKD' => 
  array (
    'alphabeticCode' => 'MKD',
    'currency' => 'Denar',
    'minorUnit' => 2,
    'numericCode' => 807,
  ),
  'MGA' => 
  array (
    'alphabeticCode' => 'MGA',
    'currency' => 'Malagasy Ariary',
    'minorUnit' => 2,
    'numericCode' => 969,
  ),
  'MWK' => 
  array (
    'alphabeticCode' => 'MWK',
    'currency' => 'Malawi Kwacha',
    'minorUnit' => 2,
    'numericCode' => 454,
  ),
  'MYR' => 
  array (
    'alphabeticCode' => 'MYR',
    'currency' => 'Malaysian Ringgit',
    'minorUnit' => 2,
    'numericCode' => 458,
  ),
  'MVR' => 
  array (
    'alphabeticCode' => 'MVR',
    'currency' => 'Rufiyaa',
    'minorUnit' => 2,
    'numericCode' => 462,
  ),
  'MRU' => 
  array (
    'alphabeticCode' => 'MRU',
    'currency' => 'Ouguiya',
    'minorUnit' => 2,
    'numericCode' => 929,
  ),
  'MUR' => 
  array (
    'alphabeticCode' => 'MUR',
    'currency' => 'Mauritius Rupee',
    'minorUnit' => 2,
    'numericCode' => 480,
  ),
  'XUA' => 
  array (
    'alphabeticCode' => 'XUA',
    'currency' => 'ADB Unit of Account',
    'minorUnit' => 0,
    'numericCode' => 965,
  ),
  'MXN' => 
  array (
    'alphabeticCode' => 'MXN',
    'currency' => 'Mexican Peso',
    'minorUnit' => 2,
    'numericCode' => 484,
  ),
  'MXV' => 
  array (
    'alphabeticCode' => 'MXV',
    'currency' => 'Mexican Unidad de Inversion (UDI)',
    'minorUnit' => 2,
    'numericCode' => 979,
  ),
  'MDL' => 
  array (
    'alphabeticCode' => 'MDL',
    'currency' => 'Moldovan Leu',
    'minorUnit' => 2,
    'numericCode' => 498,
  ),
  'MNT' => 
  array (
    'alphabeticCode' => 'MNT',
    'currency' => 'Tugrik',
    'minorUnit' => 2,
    'numericCode' => 496,
  ),
  'MAD' => 
  array (
    'alphabeticCode' => 'MAD',
    'currency' => 'Moroccan Dirham',
    'minorUnit' => 2,
    'numericCode' => 504,
  ),
  'MZN' => 
  array (
    'alphabeticCode' => 'MZN',
    'currency' => 'Mozambique Metical',
    'minorUnit' => 2,
    'numericCode' => 943,
  ),
  'MMK' => 
  array (
    'alphabeticCode' => 'MMK',
    'currency' => 'Kyat',
    'minorUnit' => 2,
    'numericCode' => 104,
  ),
  'NAD' => 
  array (
    'alphabeticCode' => 'NAD',
    'currency' => 'Namibia Dollar',
    'minorUnit' => 2,
    'numericCode' => 516,
  ),
  'NPR' => 
  array (
    'alphabeticCode' => 'NPR',
    'currency' => 'Nepalese Rupee',
    'minorUnit' => 2,
    'numericCode' => 524,
  ),
  'NIO' => 
  array (
    'alphabeticCode' => 'NIO',
    'currency' => 'Cordoba Oro',
    'minorUnit' => 2,
    'numericCode' => 558,
  ),
  'NGN' => 
  array (
    'alphabeticCode' => 'NGN',
    'currency' => 'Naira',
    'minorUnit' => 2,
    'numericCode' => 566,
  ),
  'OMR' => 
  array (
    'alphabeticCode' => 'OMR',
    'currency' => 'Rial Omani',
    'minorUnit' => 3,
    'numericCode' => 512,
  ),
  'PKR' => 
  array (
    'alphabeticCode' => 'PKR',
    'currency' => 'Pakistan Rupee',
    'minorUnit' => 2,
    'numericCode' => 586,
  ),
  'PAB' => 
  array (
    'alphabeticCode' => 'PAB',
    'currency' => 'Balboa',
    'minorUnit' => 2,
    'numericCode' => 590,
  ),
  'PGK' => 
  array (
    'alphabeticCode' => 'PGK',
    'currency' => 'Kina',
    'minorUnit' => 2,
    'numericCode' => 598,
  ),
  'PYG' => 
  array (
    'alphabeticCode' => 'PYG',
    'currency' => 'Guarani',
    'minorUnit' => 0,
    'numericCode' => 600,
  ),
  'PEN' => 
  array (
    'alphabeticCode' => 'PEN',
    'currency' => 'Sol',
    'minorUnit' => 2,
    'numericCode' => 604,
  ),
  'PHP' => 
  array (
    'alphabeticCode' => 'PHP',
    'currency' => 'Philippine Peso',
    'minorUnit' => 2,
    'numericCode' => 608,
  ),
  'PLN' => 
  array (
    'alphabeticCode' => 'PLN',
    'currency' => 'Zloty',
    'minorUnit' => 2,
    'numericCode' => 985,
  ),
  'QAR' => 
  array (
    'alphabeticCode' => 'QAR',
    'currency' => 'Qatari Rial',
    'minorUnit' => 2,
    'numericCode' => 634,
  ),
  'RON' => 
  array (
    'alphabeticCode' => 'RON',
    'currency' => 'Romanian Leu',
    'minorUnit' => 2,
    'numericCode' => 946,
  ),
  'RUB' => 
  array (
    'alphabeticCode' => 'RUB',
    'currency' => 'Russian Ruble',
    'minorUnit' => 2,
    'numericCode' => 643,
  ),
  'RWF' => 
  array (
    'alphabeticCode' => 'RWF',
    'currency' => 'Rwanda Franc',
    'minorUnit' => 0,
    'numericCode' => 646,
  ),
  'SHP' => 
  array (
    'alphabeticCode' => 'SHP',
    'currency' => 'Saint Helena Pound',
    'minorUnit' => 2,
    'numericCode' => 654,
  ),
  'WST' => 
  array (
    'alphabeticCode' => 'WST',
    'currency' => 'Tala',
    'minorUnit' => 2,
    'numericCode' => 882,
  ),
  'STN' => 
  array (
    'alphabeticCode' => 'STN',
    'currency' => 'Dobra',
    'minorUnit' => 2,
    'numericCode' => 930,
  ),
  'SAR' => 
  array (
    'alphabeticCode' => 'SAR',
    'currency' => 'Saudi Riyal',
    'minorUnit' => 2,
    'numericCode' => 682,
  ),
  'RSD' => 
  array (
    'alphabeticCode' => 'RSD',
    'currency' => 'Serbian Dinar',
    'minorUnit' => 2,
    'numericCode' => 941,
  ),
  'SCR' => 
  array (
    'alphabeticCode' => 'SCR',
    'currency' => 'Seychelles Rupee',
    'minorUnit' => 2,
    'numericCode' => 690,
  ),
  'SLL' => 
  array (
    'alphabeticCode' => 'SLL',
    'currency' => 'Leone',
    'minorUnit' => 2,
    'numericCode' => 694,
  ),
  'SGD' => 
  array (
    'alphabeticCode' => 'SGD',
    'currency' => 'Singapore Dollar',
    'minorUnit' => 2,
    'numericCode' => 702,
  ),
  'XSU' => 
  array (
    'alphabeticCode' => 'XSU',
    'currency' => 'Sucre',
    'minorUnit' => 0,
    'numericCode' => 994,
  ),
  'SBD' => 
  array (
    'alphabeticCode' => 'SBD',
    'currency' => 'Solomon Islands Dollar',
    'minorUnit' => 2,
    'numericCode' => 90,
  ),
  'SOS' => 
  array (
    'alphabeticCode' => 'SOS',
    'currency' => 'Somali Shilling',
    'minorUnit' => 2,
    'numericCode' => 706,
  ),
  'SSP' => 
  array (
    'alphabeticCode' => 'SSP',
    'currency' => 'South Sudanese Pound',
    'minorUnit' => 2,
    'numericCode' => 728,
  ),
  'LKR' => 
  array (
    'alphabeticCode' => 'LKR',
    'currency' => 'Sri Lanka Rupee',
    'minorUnit' => 2,
    'numericCode' => 144,
  ),
  'SDG' => 
  array (
    'alphabeticCode' => 'SDG',
    'currency' => 'Sudanese Pound',
    'minorUnit' => 2,
    'numericCode' => 938,
  ),
  'SRD' => 
  array (
    'alphabeticCode' => 'SRD',
    'currency' => 'Surinam Dollar',
    'minorUnit' => 2,
    'numericCode' => 968,
  ),
  'SZL' => 
  array (
    'alphabeticCode' => 'SZL',
    'currency' => 'Lilangeni',
    'minorUnit' => 2,
    'numericCode' => 748,
  ),
  'SEK' => 
  array (
    'alphabeticCode' => 'SEK',
    'currency' => 'Swedish Krona',
    'minorUnit' => 2,
    'numericCode' => 752,
  ),
  'CHE' => 
  array (
    'alphabeticCode' => 'CHE',
    'currency' => 'WIR Euro',
    'minorUnit' => 2,
    'numericCode' => 947,
  ),
  'CHW' => 
  array (
    'alphabeticCode' => 'CHW',
    'currency' => 'WIR Franc',
    'minorUnit' => 2,
    'numericCode' => 948,
  ),
  'SYP' => 
  array (
    'alphabeticCode' => 'SYP',
    'currency' => 'Syrian Pound',
    'minorUnit' => 2,
    'numericCode' => 760,
  ),
  'TWD' => 
  array (
    'alphabeticCode' => 'TWD',
    'currency' => 'New Taiwan Dollar',
    'minorUnit' => 2,
    'numericCode' => 901,
  ),
  'TJS' => 
  array (
    'alphabeticCode' => 'TJS',
    'currency' => 'Somoni',
    'minorUnit' => 2,
    'numericCode' => 972,
  ),
  'TZS' => 
  array (
    'alphabeticCode' => 'TZS',
    'currency' => 'Tanzanian Shilling',
    'minorUnit' => 2,
    'numericCode' => 834,
  ),
  'THB' => 
  array (
    'alphabeticCode' => 'THB',
    'currency' => 'Baht',
    'minorUnit' => 2,
    'numericCode' => 764,
  ),
  'TOP' => 
  array (
    'alphabeticCode' => 'TOP',
    'currency' => 'Pa’anga',
    'minorUnit' => 2,
    'numericCode' => 776,
  ),
  'TTD' => 
  array (
    'alphabeticCode' => 'TTD',
    'currency' => 'Trinidad and Tobago Dollar',
    'minorUnit' => 2,
    'numericCode' => 780,
  ),
  'TND' => 
  array (
    'alphabeticCode' => 'TND',
    'currency' => 'Tunisian Dinar',
    'minorUnit' => 3,
    'numericCode' => 788,
  ),
  'TRY' => 
  array (
    'alphabeticCode' => 'TRY',
    'currency' => 'Turkish Lira',
    'minorUnit' => 2,
    'numericCode' => 949,
  ),
  'TMT' => 
  array (
    'alphabeticCode' => 'TMT',
    'currency' => 'Turkmenistan New Manat',
    'minorUnit' => 2,
    'numericCode' => 934,
  ),
  'UGX' => 
  array (
    'alphabeticCode' => 'UGX',
    'currency' => 'Uganda Shilling',
    'minorUnit' => 0,
    'numericCode' => 800,
  ),
  'UAH' => 
  array (
    'alphabeticCode' => 'UAH',
    'currency' => 'Hryvnia',
    'minorUnit' => 2,
    'numericCode' => 980,
  ),
  'AED' => 
  array (
    'alphabeticCode' => 'AED',
    'currency' => 'UAE Dirham',
    'minorUnit' => 2,
    'numericCode' => 784,
  ),
  'USN' => 
  array (
    'alphabeticCode' => 'USN',
    'currency' => 'US Dollar (Next day)',
    'minorUnit' => 2,
    'numericCode' => 997,
  ),
  'UYU' => 
  array (
    'alphabeticCode' => 'UYU',
    'currency' => 'Peso Uruguayo',
    'minorUnit' => 2,
    'numericCode' => 858,
  ),
  'UYI' => 
  array (
    'alphabeticCode' => 'UYI',
    'currency' => 'Uruguay Peso en Unidades Indexadas (UI)',
    'minorUnit' => 0,
    'numericCode' => 940,
  ),
  'UYW' => 
  array (
    'alphabeticCode' => 'UYW',
    'currency' => 'Unidad Previsional',
    'minorUnit' => 4,
    'numericCode' => 927,
  ),
  'UZS' => 
  array (
    'alphabeticCode' => 'UZS',
    'currency' => 'Uzbekistan Sum',
    'minorUnit' => 2,
    'numericCode' => 860,
  ),
  'VUV' => 
  array (
    'alphabeticCode' => 'VUV',
    'currency' => 'Vatu',
    'minorUnit' => 0,
    'numericCode' => 548,
  ),
  'VES' => 
  array (
    'alphabeticCode' => 'VES',
    'currency' => 'Bolívar Soberano',
    'minorUnit' => 2,
    'numericCode' => 928,
  ),
  'VND' => 
  array (
    'alphabeticCode' => 'VND',
    'currency' => 'Dong',
    'minorUnit' => 0,
    'numericCode' => 704,
  ),
  'YER' => 
  array (
    'alphabeticCode' => 'YER',
    'currency' => 'Yemeni Rial',
    'minorUnit' => 2,
    'numericCode' => 886,
  ),
  'ZMW' => 
  array (
    'alphabeticCode' => 'ZMW',
    'currency' => 'Zambian Kwacha',
    'minorUnit' => 2,
    'numericCode' => 967,
  ),
  'ZWL' => 
  array (
    'alphabeticCode' => 'ZWL',
    'currency' => 'Zimbabwe Dollar',
    'minorUnit' => 2,
    'numericCode' => 932,
  ),
  'XBA' => 
  array (
    'alphabeticCode' => 'XBA',
    'currency' => 'Bond Markets Unit European Composite Unit (EURCO)',
    'minorUnit' => 0,
    'numericCode' => 955,
  ),
  'XBB' => 
  array (
    'alphabeticCode' => 'XBB',
    'currency' => 'Bond Markets Unit European Monetary Unit (E.M.U.-6)',
    'minorUnit' => 0,
    'numericCode' => 956,
  ),
  'XBC' => 
  array (
    'alphabeticCode' => 'XBC',
    'currency' => 'Bond Markets Unit European Unit of Account 9 (E.U.A.-9)',
    'minorUnit' => 0,
    'numericCode' => 957,
  ),
  'XBD' => 
  array (
    'alphabeticCode' => 'XBD',
    'currency' => 'Bond Markets Unit European Unit of Account 17 (E.U.A.-17)',
    'minorUnit' => 0,
    'numericCode' => 958,
  ),
  'XTS' => 
  array (
    'alphabeticCode' => 'XTS',
    'currency' => 'Codes specifically reserved for testing purposes',
    'minorUnit' => 0,
    'numericCode' => 963,
  ),
  'XXX' => 
  array (
    'alphabeticCode' => 'XXX',
    'currency' => 'The codes assigned for transactions where no currency is involved',
    'minorUnit' => 0,
    'numericCode' => 999,
  ),
  'XAU' => 
  array (
    'alphabeticCode' => 'XAU',
    'currency' => 'Gold',
    'minorUnit' => 0,
    'numericCode' => 959,
  ),
  'XPD' => 
  array (
    'alphabeticCode' => 'XPD',
    'currency' => 'Palladium',
    'minorUnit' => 0,
    'numericCode' => 964,
  ),
  'XPT' => 
  array (
    'alphabeticCode' => 'XPT',
    'currency' => 'Platinum',
    'minorUnit' => 0,
    'numericCode' => 962,
  ),
  'XAG' => 
  array (
    'alphabeticCode' => 'XAG',
    'currency' => 'Silver',
    'minorUnit' => 0,
    'numericCode' => 961,
  ),
);