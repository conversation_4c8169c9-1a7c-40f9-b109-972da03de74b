{"disallowEmptyBlocks": true, "disallowKeywords": ["with"], "disallowMixedSpacesAndTabs": true, "disallowMultipleLineStrings": true, "disallowMultipleVarDecl": true, "disallowSpaceAfterPrefixUnaryOperators": ["!", "+", "++", "-", "--", "~"], "disallowSpaceBeforeBinaryOperators": [","], "disallowSpaceBeforePostfixUnaryOperators": true, "disallowSpacesInNamedFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInsideArrayBrackets": true, "disallowSpacesInsideParentheses": true, "disallowTrailingComma": true, "disallowTrailingWhitespace": true, "requireCamelCaseOrUpperCaseIdentifiers": true, "requireCapitalizedConstructors": true, "requireCommaBeforeLineBreak": true, "requireCurlyBraces": true, "requireDotNotation": true, "requireLineFeedAtFileEnd": true, "requireParenthesesAroundIIFE": true, "requireSpaceAfterBinaryOperators": true, "requireSpaceAfterKeywords": ["catch", "do", "else", "for", "if", "return", "switch", "try", "while"], "requireSpaceAfterLineComment": true, "requireSpaceBeforeBinaryOperators": true, "requireSpaceBeforeBlockStatements": true, "requireSpacesInAnonymousFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpacesInConditionalExpression": true, "requireSpacesInFunctionDeclaration": {"beforeOpeningCurlyBrace": true}, "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpacesInNamedFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpacesInsideObjectBrackets": "allButNested", "validateIndentation": 4, "validateLineBreaks": "LF", "validateParameterSeparator": ", ", "validateQuoteMarks": "'"}