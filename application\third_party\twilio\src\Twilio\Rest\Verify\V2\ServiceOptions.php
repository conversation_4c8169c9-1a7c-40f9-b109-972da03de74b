<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Verify\V2;

use Twilio\Options;
use Twilio\Values;

abstract class ServiceOptions
{
    /**
     * @param int $codeLength The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
     * @param bool $lookupEnabled Whether to perform a lookup with each verification started and return info about the phone number.
     * @param bool $skipSmsToLandlines Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
     * @param bool $dtmfInputRequired Whether to ask the user to press a number before delivering the verify code in a phone call.
     * @param string $ttsName The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
     * @param bool $psd2Enabled Whether to pass PSD2 transaction parameters when starting a verification.
     * @param bool $doNotShareWarningEnabled Whether to add a security warning at the end of an SMS verification body. Disabled by default and applies only to SMS. Example SMS body: `Your AppName verification code is: 1234. Don’t share this code with anyone; our employees will never ask for the code`
     * @param bool $customCodeEnabled Whether to allow sending verifications with a custom code instead of a randomly generated one. Not available for all customers.
     * @param bool $pushIncludeDate Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter. This timestamp value is the same one as the one found in `date_created`, please use that one instead.
     * @param string $pushApnCredentialSid Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @param string $pushFcmCredentialSid Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @param string $totpIssuer Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI. Defaults to the service friendly name if not provided.
     * @param int $totpTimeStep Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
     * @param int $totpCodeLength Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
     * @param int $totpSkew Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
     * @param string $defaultTemplateSid The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
     * @return CreateServiceOptions Options builder
     */
    public static function create(
        
        int $codeLength = Values::INT_NONE,
        bool $lookupEnabled = Values::BOOL_NONE,
        bool $skipSmsToLandlines = Values::BOOL_NONE,
        bool $dtmfInputRequired = Values::BOOL_NONE,
        string $ttsName = Values::NONE,
        bool $psd2Enabled = Values::BOOL_NONE,
        bool $doNotShareWarningEnabled = Values::BOOL_NONE,
        bool $customCodeEnabled = Values::BOOL_NONE,
        bool $pushIncludeDate = Values::BOOL_NONE,
        string $pushApnCredentialSid = Values::NONE,
        string $pushFcmCredentialSid = Values::NONE,
        string $totpIssuer = Values::NONE,
        int $totpTimeStep = Values::INT_NONE,
        int $totpCodeLength = Values::INT_NONE,
        int $totpSkew = Values::INT_NONE,
        string $defaultTemplateSid = Values::NONE

    ): CreateServiceOptions
    {
        return new CreateServiceOptions(
            $codeLength,
            $lookupEnabled,
            $skipSmsToLandlines,
            $dtmfInputRequired,
            $ttsName,
            $psd2Enabled,
            $doNotShareWarningEnabled,
            $customCodeEnabled,
            $pushIncludeDate,
            $pushApnCredentialSid,
            $pushFcmCredentialSid,
            $totpIssuer,
            $totpTimeStep,
            $totpCodeLength,
            $totpSkew,
            $defaultTemplateSid
        );
    }




    /**
     * @param string $friendlyName A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
     * @param int $codeLength The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
     * @param bool $lookupEnabled Whether to perform a lookup with each verification started and return info about the phone number.
     * @param bool $skipSmsToLandlines Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
     * @param bool $dtmfInputRequired Whether to ask the user to press a number before delivering the verify code in a phone call.
     * @param string $ttsName The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
     * @param bool $psd2Enabled Whether to pass PSD2 transaction parameters when starting a verification.
     * @param bool $doNotShareWarningEnabled Whether to add a privacy warning at the end of an SMS. **Disabled by default and applies only for SMS.**
     * @param bool $customCodeEnabled Whether to allow sending verifications with a custom code instead of a randomly generated one. Not available for all customers.
     * @param bool $pushIncludeDate Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter.
     * @param string $pushApnCredentialSid Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @param string $pushFcmCredentialSid Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @param string $totpIssuer Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI.
     * @param int $totpTimeStep Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
     * @param int $totpCodeLength Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
     * @param int $totpSkew Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
     * @param string $defaultTemplateSid The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
     * @return UpdateServiceOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        int $codeLength = Values::INT_NONE,
        bool $lookupEnabled = Values::BOOL_NONE,
        bool $skipSmsToLandlines = Values::BOOL_NONE,
        bool $dtmfInputRequired = Values::BOOL_NONE,
        string $ttsName = Values::NONE,
        bool $psd2Enabled = Values::BOOL_NONE,
        bool $doNotShareWarningEnabled = Values::BOOL_NONE,
        bool $customCodeEnabled = Values::BOOL_NONE,
        bool $pushIncludeDate = Values::BOOL_NONE,
        string $pushApnCredentialSid = Values::NONE,
        string $pushFcmCredentialSid = Values::NONE,
        string $totpIssuer = Values::NONE,
        int $totpTimeStep = Values::INT_NONE,
        int $totpCodeLength = Values::INT_NONE,
        int $totpSkew = Values::INT_NONE,
        string $defaultTemplateSid = Values::NONE

    ): UpdateServiceOptions
    {
        return new UpdateServiceOptions(
            $friendlyName,
            $codeLength,
            $lookupEnabled,
            $skipSmsToLandlines,
            $dtmfInputRequired,
            $ttsName,
            $psd2Enabled,
            $doNotShareWarningEnabled,
            $customCodeEnabled,
            $pushIncludeDate,
            $pushApnCredentialSid,
            $pushFcmCredentialSid,
            $totpIssuer,
            $totpTimeStep,
            $totpCodeLength,
            $totpSkew,
            $defaultTemplateSid
        );
    }

}

class CreateServiceOptions extends Options
    {
    /**
     * @param int $codeLength The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
     * @param bool $lookupEnabled Whether to perform a lookup with each verification started and return info about the phone number.
     * @param bool $skipSmsToLandlines Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
     * @param bool $dtmfInputRequired Whether to ask the user to press a number before delivering the verify code in a phone call.
     * @param string $ttsName The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
     * @param bool $psd2Enabled Whether to pass PSD2 transaction parameters when starting a verification.
     * @param bool $doNotShareWarningEnabled Whether to add a security warning at the end of an SMS verification body. Disabled by default and applies only to SMS. Example SMS body: `Your AppName verification code is: 1234. Don’t share this code with anyone; our employees will never ask for the code`
     * @param bool $customCodeEnabled Whether to allow sending verifications with a custom code instead of a randomly generated one. Not available for all customers.
     * @param bool $pushIncludeDate Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter. This timestamp value is the same one as the one found in `date_created`, please use that one instead.
     * @param string $pushApnCredentialSid Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @param string $pushFcmCredentialSid Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @param string $totpIssuer Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI. Defaults to the service friendly name if not provided.
     * @param int $totpTimeStep Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
     * @param int $totpCodeLength Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
     * @param int $totpSkew Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
     * @param string $defaultTemplateSid The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
     */
    public function __construct(
        
        int $codeLength = Values::INT_NONE,
        bool $lookupEnabled = Values::BOOL_NONE,
        bool $skipSmsToLandlines = Values::BOOL_NONE,
        bool $dtmfInputRequired = Values::BOOL_NONE,
        string $ttsName = Values::NONE,
        bool $psd2Enabled = Values::BOOL_NONE,
        bool $doNotShareWarningEnabled = Values::BOOL_NONE,
        bool $customCodeEnabled = Values::BOOL_NONE,
        bool $pushIncludeDate = Values::BOOL_NONE,
        string $pushApnCredentialSid = Values::NONE,
        string $pushFcmCredentialSid = Values::NONE,
        string $totpIssuer = Values::NONE,
        int $totpTimeStep = Values::INT_NONE,
        int $totpCodeLength = Values::INT_NONE,
        int $totpSkew = Values::INT_NONE,
        string $defaultTemplateSid = Values::NONE

    ) {
        $this->options['codeLength'] = $codeLength;
        $this->options['lookupEnabled'] = $lookupEnabled;
        $this->options['skipSmsToLandlines'] = $skipSmsToLandlines;
        $this->options['dtmfInputRequired'] = $dtmfInputRequired;
        $this->options['ttsName'] = $ttsName;
        $this->options['psd2Enabled'] = $psd2Enabled;
        $this->options['doNotShareWarningEnabled'] = $doNotShareWarningEnabled;
        $this->options['customCodeEnabled'] = $customCodeEnabled;
        $this->options['pushIncludeDate'] = $pushIncludeDate;
        $this->options['pushApnCredentialSid'] = $pushApnCredentialSid;
        $this->options['pushFcmCredentialSid'] = $pushFcmCredentialSid;
        $this->options['totpIssuer'] = $totpIssuer;
        $this->options['totpTimeStep'] = $totpTimeStep;
        $this->options['totpCodeLength'] = $totpCodeLength;
        $this->options['totpSkew'] = $totpSkew;
        $this->options['defaultTemplateSid'] = $defaultTemplateSid;
    }

    /**
     * The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
     *
     * @param int $codeLength The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
     * @return $this Fluent Builder
     */
    public function setCodeLength(int $codeLength): self
    {
        $this->options['codeLength'] = $codeLength;
        return $this;
    }

    /**
     * Whether to perform a lookup with each verification started and return info about the phone number.
     *
     * @param bool $lookupEnabled Whether to perform a lookup with each verification started and return info about the phone number.
     * @return $this Fluent Builder
     */
    public function setLookupEnabled(bool $lookupEnabled): self
    {
        $this->options['lookupEnabled'] = $lookupEnabled;
        return $this;
    }

    /**
     * Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
     *
     * @param bool $skipSmsToLandlines Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
     * @return $this Fluent Builder
     */
    public function setSkipSmsToLandlines(bool $skipSmsToLandlines): self
    {
        $this->options['skipSmsToLandlines'] = $skipSmsToLandlines;
        return $this;
    }

    /**
     * Whether to ask the user to press a number before delivering the verify code in a phone call.
     *
     * @param bool $dtmfInputRequired Whether to ask the user to press a number before delivering the verify code in a phone call.
     * @return $this Fluent Builder
     */
    public function setDtmfInputRequired(bool $dtmfInputRequired): self
    {
        $this->options['dtmfInputRequired'] = $dtmfInputRequired;
        return $this;
    }

    /**
     * The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
     *
     * @param string $ttsName The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
     * @return $this Fluent Builder
     */
    public function setTtsName(string $ttsName): self
    {
        $this->options['ttsName'] = $ttsName;
        return $this;
    }

    /**
     * Whether to pass PSD2 transaction parameters when starting a verification.
     *
     * @param bool $psd2Enabled Whether to pass PSD2 transaction parameters when starting a verification.
     * @return $this Fluent Builder
     */
    public function setPsd2Enabled(bool $psd2Enabled): self
    {
        $this->options['psd2Enabled'] = $psd2Enabled;
        return $this;
    }

    /**
     * Whether to add a security warning at the end of an SMS verification body. Disabled by default and applies only to SMS. Example SMS body: `Your AppName verification code is: 1234. Don’t share this code with anyone; our employees will never ask for the code`
     *
     * @param bool $doNotShareWarningEnabled Whether to add a security warning at the end of an SMS verification body. Disabled by default and applies only to SMS. Example SMS body: `Your AppName verification code is: 1234. Don’t share this code with anyone; our employees will never ask for the code`
     * @return $this Fluent Builder
     */
    public function setDoNotShareWarningEnabled(bool $doNotShareWarningEnabled): self
    {
        $this->options['doNotShareWarningEnabled'] = $doNotShareWarningEnabled;
        return $this;
    }

    /**
     * Whether to allow sending verifications with a custom code instead of a randomly generated one. Not available for all customers.
     *
     * @param bool $customCodeEnabled Whether to allow sending verifications with a custom code instead of a randomly generated one. Not available for all customers.
     * @return $this Fluent Builder
     */
    public function setCustomCodeEnabled(bool $customCodeEnabled): self
    {
        $this->options['customCodeEnabled'] = $customCodeEnabled;
        return $this;
    }

    /**
     * Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter. This timestamp value is the same one as the one found in `date_created`, please use that one instead.
     *
     * @param bool $pushIncludeDate Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter. This timestamp value is the same one as the one found in `date_created`, please use that one instead.
     * @return $this Fluent Builder
     */
    public function setPushIncludeDate(bool $pushIncludeDate): self
    {
        $this->options['pushIncludeDate'] = $pushIncludeDate;
        return $this;
    }

    /**
     * Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     *
     * @param string $pushApnCredentialSid Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @return $this Fluent Builder
     */
    public function setPushApnCredentialSid(string $pushApnCredentialSid): self
    {
        $this->options['pushApnCredentialSid'] = $pushApnCredentialSid;
        return $this;
    }

    /**
     * Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     *
     * @param string $pushFcmCredentialSid Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @return $this Fluent Builder
     */
    public function setPushFcmCredentialSid(string $pushFcmCredentialSid): self
    {
        $this->options['pushFcmCredentialSid'] = $pushFcmCredentialSid;
        return $this;
    }

    /**
     * Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI. Defaults to the service friendly name if not provided.
     *
     * @param string $totpIssuer Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI. Defaults to the service friendly name if not provided.
     * @return $this Fluent Builder
     */
    public function setTotpIssuer(string $totpIssuer): self
    {
        $this->options['totpIssuer'] = $totpIssuer;
        return $this;
    }

    /**
     * Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
     *
     * @param int $totpTimeStep Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
     * @return $this Fluent Builder
     */
    public function setTotpTimeStep(int $totpTimeStep): self
    {
        $this->options['totpTimeStep'] = $totpTimeStep;
        return $this;
    }

    /**
     * Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
     *
     * @param int $totpCodeLength Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
     * @return $this Fluent Builder
     */
    public function setTotpCodeLength(int $totpCodeLength): self
    {
        $this->options['totpCodeLength'] = $totpCodeLength;
        return $this;
    }

    /**
     * Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
     *
     * @param int $totpSkew Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
     * @return $this Fluent Builder
     */
    public function setTotpSkew(int $totpSkew): self
    {
        $this->options['totpSkew'] = $totpSkew;
        return $this;
    }

    /**
     * The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
     *
     * @param string $defaultTemplateSid The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
     * @return $this Fluent Builder
     */
    public function setDefaultTemplateSid(string $defaultTemplateSid): self
    {
        $this->options['defaultTemplateSid'] = $defaultTemplateSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Verify.V2.CreateServiceOptions ' . $options . ']';
    }
}




class UpdateServiceOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
     * @param int $codeLength The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
     * @param bool $lookupEnabled Whether to perform a lookup with each verification started and return info about the phone number.
     * @param bool $skipSmsToLandlines Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
     * @param bool $dtmfInputRequired Whether to ask the user to press a number before delivering the verify code in a phone call.
     * @param string $ttsName The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
     * @param bool $psd2Enabled Whether to pass PSD2 transaction parameters when starting a verification.
     * @param bool $doNotShareWarningEnabled Whether to add a privacy warning at the end of an SMS. **Disabled by default and applies only for SMS.**
     * @param bool $customCodeEnabled Whether to allow sending verifications with a custom code instead of a randomly generated one. Not available for all customers.
     * @param bool $pushIncludeDate Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter.
     * @param string $pushApnCredentialSid Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @param string $pushFcmCredentialSid Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @param string $totpIssuer Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI.
     * @param int $totpTimeStep Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
     * @param int $totpCodeLength Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
     * @param int $totpSkew Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
     * @param string $defaultTemplateSid The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        int $codeLength = Values::INT_NONE,
        bool $lookupEnabled = Values::BOOL_NONE,
        bool $skipSmsToLandlines = Values::BOOL_NONE,
        bool $dtmfInputRequired = Values::BOOL_NONE,
        string $ttsName = Values::NONE,
        bool $psd2Enabled = Values::BOOL_NONE,
        bool $doNotShareWarningEnabled = Values::BOOL_NONE,
        bool $customCodeEnabled = Values::BOOL_NONE,
        bool $pushIncludeDate = Values::BOOL_NONE,
        string $pushApnCredentialSid = Values::NONE,
        string $pushFcmCredentialSid = Values::NONE,
        string $totpIssuer = Values::NONE,
        int $totpTimeStep = Values::INT_NONE,
        int $totpCodeLength = Values::INT_NONE,
        int $totpSkew = Values::INT_NONE,
        string $defaultTemplateSid = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['codeLength'] = $codeLength;
        $this->options['lookupEnabled'] = $lookupEnabled;
        $this->options['skipSmsToLandlines'] = $skipSmsToLandlines;
        $this->options['dtmfInputRequired'] = $dtmfInputRequired;
        $this->options['ttsName'] = $ttsName;
        $this->options['psd2Enabled'] = $psd2Enabled;
        $this->options['doNotShareWarningEnabled'] = $doNotShareWarningEnabled;
        $this->options['customCodeEnabled'] = $customCodeEnabled;
        $this->options['pushIncludeDate'] = $pushIncludeDate;
        $this->options['pushApnCredentialSid'] = $pushApnCredentialSid;
        $this->options['pushFcmCredentialSid'] = $pushFcmCredentialSid;
        $this->options['totpIssuer'] = $totpIssuer;
        $this->options['totpTimeStep'] = $totpTimeStep;
        $this->options['totpCodeLength'] = $totpCodeLength;
        $this->options['totpSkew'] = $totpSkew;
        $this->options['defaultTemplateSid'] = $defaultTemplateSid;
    }

    /**
     * A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
     *
     * @param string $friendlyName A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
     *
     * @param int $codeLength The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
     * @return $this Fluent Builder
     */
    public function setCodeLength(int $codeLength): self
    {
        $this->options['codeLength'] = $codeLength;
        return $this;
    }

    /**
     * Whether to perform a lookup with each verification started and return info about the phone number.
     *
     * @param bool $lookupEnabled Whether to perform a lookup with each verification started and return info about the phone number.
     * @return $this Fluent Builder
     */
    public function setLookupEnabled(bool $lookupEnabled): self
    {
        $this->options['lookupEnabled'] = $lookupEnabled;
        return $this;
    }

    /**
     * Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
     *
     * @param bool $skipSmsToLandlines Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
     * @return $this Fluent Builder
     */
    public function setSkipSmsToLandlines(bool $skipSmsToLandlines): self
    {
        $this->options['skipSmsToLandlines'] = $skipSmsToLandlines;
        return $this;
    }

    /**
     * Whether to ask the user to press a number before delivering the verify code in a phone call.
     *
     * @param bool $dtmfInputRequired Whether to ask the user to press a number before delivering the verify code in a phone call.
     * @return $this Fluent Builder
     */
    public function setDtmfInputRequired(bool $dtmfInputRequired): self
    {
        $this->options['dtmfInputRequired'] = $dtmfInputRequired;
        return $this;
    }

    /**
     * The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
     *
     * @param string $ttsName The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
     * @return $this Fluent Builder
     */
    public function setTtsName(string $ttsName): self
    {
        $this->options['ttsName'] = $ttsName;
        return $this;
    }

    /**
     * Whether to pass PSD2 transaction parameters when starting a verification.
     *
     * @param bool $psd2Enabled Whether to pass PSD2 transaction parameters when starting a verification.
     * @return $this Fluent Builder
     */
    public function setPsd2Enabled(bool $psd2Enabled): self
    {
        $this->options['psd2Enabled'] = $psd2Enabled;
        return $this;
    }

    /**
     * Whether to add a privacy warning at the end of an SMS. **Disabled by default and applies only for SMS.**
     *
     * @param bool $doNotShareWarningEnabled Whether to add a privacy warning at the end of an SMS. **Disabled by default and applies only for SMS.**
     * @return $this Fluent Builder
     */
    public function setDoNotShareWarningEnabled(bool $doNotShareWarningEnabled): self
    {
        $this->options['doNotShareWarningEnabled'] = $doNotShareWarningEnabled;
        return $this;
    }

    /**
     * Whether to allow sending verifications with a custom code instead of a randomly generated one. Not available for all customers.
     *
     * @param bool $customCodeEnabled Whether to allow sending verifications with a custom code instead of a randomly generated one. Not available for all customers.
     * @return $this Fluent Builder
     */
    public function setCustomCodeEnabled(bool $customCodeEnabled): self
    {
        $this->options['customCodeEnabled'] = $customCodeEnabled;
        return $this;
    }

    /**
     * Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter.
     *
     * @param bool $pushIncludeDate Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter.
     * @return $this Fluent Builder
     */
    public function setPushIncludeDate(bool $pushIncludeDate): self
    {
        $this->options['pushIncludeDate'] = $pushIncludeDate;
        return $this;
    }

    /**
     * Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     *
     * @param string $pushApnCredentialSid Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @return $this Fluent Builder
     */
    public function setPushApnCredentialSid(string $pushApnCredentialSid): self
    {
        $this->options['pushApnCredentialSid'] = $pushApnCredentialSid;
        return $this;
    }

    /**
     * Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     *
     * @param string $pushFcmCredentialSid Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
     * @return $this Fluent Builder
     */
    public function setPushFcmCredentialSid(string $pushFcmCredentialSid): self
    {
        $this->options['pushFcmCredentialSid'] = $pushFcmCredentialSid;
        return $this;
    }

    /**
     * Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI.
     *
     * @param string $totpIssuer Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI.
     * @return $this Fluent Builder
     */
    public function setTotpIssuer(string $totpIssuer): self
    {
        $this->options['totpIssuer'] = $totpIssuer;
        return $this;
    }

    /**
     * Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
     *
     * @param int $totpTimeStep Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
     * @return $this Fluent Builder
     */
    public function setTotpTimeStep(int $totpTimeStep): self
    {
        $this->options['totpTimeStep'] = $totpTimeStep;
        return $this;
    }

    /**
     * Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
     *
     * @param int $totpCodeLength Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
     * @return $this Fluent Builder
     */
    public function setTotpCodeLength(int $totpCodeLength): self
    {
        $this->options['totpCodeLength'] = $totpCodeLength;
        return $this;
    }

    /**
     * Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
     *
     * @param int $totpSkew Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
     * @return $this Fluent Builder
     */
    public function setTotpSkew(int $totpSkew): self
    {
        $this->options['totpSkew'] = $totpSkew;
        return $this;
    }

    /**
     * The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
     *
     * @param string $defaultTemplateSid The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
     * @return $this Fluent Builder
     */
    public function setDefaultTemplateSid(string $defaultTemplateSid): self
    {
        $this->options['defaultTemplateSid'] = $defaultTemplateSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Verify.V2.UpdateServiceOptions ' . $options . ']';
    }
}

