language: php

branches:
  only: [master]

sudo: false

php:
  - 5.4
  - 5.5
  - 5.6
  - 7.0
  - hhvm

env:
  - COMPOSER_CMD="composer install"
  - COMPOSER_CMD="composer update --prefer-lowest"
matrix:
  include:
    - php: "7.0"
      env: RUN_CS_FIXER=true COMPOSER_CMD="composer install"

before_script:
  - $(echo $COMPOSER_CMD)

script:
  - if [ "${RUN_CS_FIXER}" = "true" ]; then
      vendor/bin/php-cs-fixer fix --dry-run --diff --config-file=.php_cs .;
    else
      vendor/bin/phpunit;
    fi
