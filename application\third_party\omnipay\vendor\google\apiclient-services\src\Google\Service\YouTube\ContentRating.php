<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_YouTube_ContentRating extends Google_Collection
{
  protected $collection_key = 'fpbRatingReasons';
  public $acbRating;
  public $agcomRating;
  public $anatelRating;
  public $bbfcRating;
  public $bfvcRating;
  public $bmukkRating;
  public $catvRating;
  public $catvfrRating;
  public $cbfcRating;
  public $cccRating;
  public $cceRating;
  public $chfilmRating;
  public $chvrsRating;
  public $cicfRating;
  public $cnaRating;
  public $cncRating;
  public $csaRating;
  public $cscfRating;
  public $czfilmRating;
  public $djctqRating;
  public $djctqRatingReasons;
  public $ecbmctRating;
  public $eefilmRating;
  public $egfilmRating;
  public $eirinRating;
  public $fcbmRating;
  public $fcoRating;
  public $fmocRating;
  public $fpbRating;
  public $fpbRatingReasons;
  public $fskRating;
  public $grfilmRating;
  public $icaaRating;
  public $ifcoRating;
  public $ilfilmRating;
  public $incaaRating;
  public $kfcbRating;
  public $kijkwijzerRating;
  public $kmrbRating;
  public $lsfRating;
  public $mccaaRating;
  public $mccypRating;
  public $mcstRating;
  public $mdaRating;
  public $medietilsynetRating;
  public $mekuRating;
  public $mibacRating;
  public $mocRating;
  public $moctwRating;
  public $mpaaRating;
  public $mtrcbRating;
  public $nbcRating;
  public $nbcplRating;
  public $nfrcRating;
  public $nfvcbRating;
  public $nkclvRating;
  public $oflcRating;
  public $pefilmRating;
  public $rcnofRating;
  public $resorteviolenciaRating;
  public $rtcRating;
  public $rteRating;
  public $russiaRating;
  public $skfilmRating;
  public $smaisRating;
  public $smsaRating;
  public $tvpgRating;
  public $ytRating;

  public function setAcbRating($acbRating)
  {
    $this->acbRating = $acbRating;
  }
  public function getAcbRating()
  {
    return $this->acbRating;
  }
  public function setAgcomRating($agcomRating)
  {
    $this->agcomRating = $agcomRating;
  }
  public function getAgcomRating()
  {
    return $this->agcomRating;
  }
  public function setAnatelRating($anatelRating)
  {
    $this->anatelRating = $anatelRating;
  }
  public function getAnatelRating()
  {
    return $this->anatelRating;
  }
  public function setBbfcRating($bbfcRating)
  {
    $this->bbfcRating = $bbfcRating;
  }
  public function getBbfcRating()
  {
    return $this->bbfcRating;
  }
  public function setBfvcRating($bfvcRating)
  {
    $this->bfvcRating = $bfvcRating;
  }
  public function getBfvcRating()
  {
    return $this->bfvcRating;
  }
  public function setBmukkRating($bmukkRating)
  {
    $this->bmukkRating = $bmukkRating;
  }
  public function getBmukkRating()
  {
    return $this->bmukkRating;
  }
  public function setCatvRating($catvRating)
  {
    $this->catvRating = $catvRating;
  }
  public function getCatvRating()
  {
    return $this->catvRating;
  }
  public function setCatvfrRating($catvfrRating)
  {
    $this->catvfrRating = $catvfrRating;
  }
  public function getCatvfrRating()
  {
    return $this->catvfrRating;
  }
  public function setCbfcRating($cbfcRating)
  {
    $this->cbfcRating = $cbfcRating;
  }
  public function getCbfcRating()
  {
    return $this->cbfcRating;
  }
  public function setCccRating($cccRating)
  {
    $this->cccRating = $cccRating;
  }
  public function getCccRating()
  {
    return $this->cccRating;
  }
  public function setCceRating($cceRating)
  {
    $this->cceRating = $cceRating;
  }
  public function getCceRating()
  {
    return $this->cceRating;
  }
  public function setChfilmRating($chfilmRating)
  {
    $this->chfilmRating = $chfilmRating;
  }
  public function getChfilmRating()
  {
    return $this->chfilmRating;
  }
  public function setChvrsRating($chvrsRating)
  {
    $this->chvrsRating = $chvrsRating;
  }
  public function getChvrsRating()
  {
    return $this->chvrsRating;
  }
  public function setCicfRating($cicfRating)
  {
    $this->cicfRating = $cicfRating;
  }
  public function getCicfRating()
  {
    return $this->cicfRating;
  }
  public function setCnaRating($cnaRating)
  {
    $this->cnaRating = $cnaRating;
  }
  public function getCnaRating()
  {
    return $this->cnaRating;
  }
  public function setCncRating($cncRating)
  {
    $this->cncRating = $cncRating;
  }
  public function getCncRating()
  {
    return $this->cncRating;
  }
  public function setCsaRating($csaRating)
  {
    $this->csaRating = $csaRating;
  }
  public function getCsaRating()
  {
    return $this->csaRating;
  }
  public function setCscfRating($cscfRating)
  {
    $this->cscfRating = $cscfRating;
  }
  public function getCscfRating()
  {
    return $this->cscfRating;
  }
  public function setCzfilmRating($czfilmRating)
  {
    $this->czfilmRating = $czfilmRating;
  }
  public function getCzfilmRating()
  {
    return $this->czfilmRating;
  }
  public function setDjctqRating($djctqRating)
  {
    $this->djctqRating = $djctqRating;
  }
  public function getDjctqRating()
  {
    return $this->djctqRating;
  }
  public function setDjctqRatingReasons($djctqRatingReasons)
  {
    $this->djctqRatingReasons = $djctqRatingReasons;
  }
  public function getDjctqRatingReasons()
  {
    return $this->djctqRatingReasons;
  }
  public function setEcbmctRating($ecbmctRating)
  {
    $this->ecbmctRating = $ecbmctRating;
  }
  public function getEcbmctRating()
  {
    return $this->ecbmctRating;
  }
  public function setEefilmRating($eefilmRating)
  {
    $this->eefilmRating = $eefilmRating;
  }
  public function getEefilmRating()
  {
    return $this->eefilmRating;
  }
  public function setEgfilmRating($egfilmRating)
  {
    $this->egfilmRating = $egfilmRating;
  }
  public function getEgfilmRating()
  {
    return $this->egfilmRating;
  }
  public function setEirinRating($eirinRating)
  {
    $this->eirinRating = $eirinRating;
  }
  public function getEirinRating()
  {
    return $this->eirinRating;
  }
  public function setFcbmRating($fcbmRating)
  {
    $this->fcbmRating = $fcbmRating;
  }
  public function getFcbmRating()
  {
    return $this->fcbmRating;
  }
  public function setFcoRating($fcoRating)
  {
    $this->fcoRating = $fcoRating;
  }
  public function getFcoRating()
  {
    return $this->fcoRating;
  }
  public function setFmocRating($fmocRating)
  {
    $this->fmocRating = $fmocRating;
  }
  public function getFmocRating()
  {
    return $this->fmocRating;
  }
  public function setFpbRating($fpbRating)
  {
    $this->fpbRating = $fpbRating;
  }
  public function getFpbRating()
  {
    return $this->fpbRating;
  }
  public function setFpbRatingReasons($fpbRatingReasons)
  {
    $this->fpbRatingReasons = $fpbRatingReasons;
  }
  public function getFpbRatingReasons()
  {
    return $this->fpbRatingReasons;
  }
  public function setFskRating($fskRating)
  {
    $this->fskRating = $fskRating;
  }
  public function getFskRating()
  {
    return $this->fskRating;
  }
  public function setGrfilmRating($grfilmRating)
  {
    $this->grfilmRating = $grfilmRating;
  }
  public function getGrfilmRating()
  {
    return $this->grfilmRating;
  }
  public function setIcaaRating($icaaRating)
  {
    $this->icaaRating = $icaaRating;
  }
  public function getIcaaRating()
  {
    return $this->icaaRating;
  }
  public function setIfcoRating($ifcoRating)
  {
    $this->ifcoRating = $ifcoRating;
  }
  public function getIfcoRating()
  {
    return $this->ifcoRating;
  }
  public function setIlfilmRating($ilfilmRating)
  {
    $this->ilfilmRating = $ilfilmRating;
  }
  public function getIlfilmRating()
  {
    return $this->ilfilmRating;
  }
  public function setIncaaRating($incaaRating)
  {
    $this->incaaRating = $incaaRating;
  }
  public function getIncaaRating()
  {
    return $this->incaaRating;
  }
  public function setKfcbRating($kfcbRating)
  {
    $this->kfcbRating = $kfcbRating;
  }
  public function getKfcbRating()
  {
    return $this->kfcbRating;
  }
  public function setKijkwijzerRating($kijkwijzerRating)
  {
    $this->kijkwijzerRating = $kijkwijzerRating;
  }
  public function getKijkwijzerRating()
  {
    return $this->kijkwijzerRating;
  }
  public function setKmrbRating($kmrbRating)
  {
    $this->kmrbRating = $kmrbRating;
  }
  public function getKmrbRating()
  {
    return $this->kmrbRating;
  }
  public function setLsfRating($lsfRating)
  {
    $this->lsfRating = $lsfRating;
  }
  public function getLsfRating()
  {
    return $this->lsfRating;
  }
  public function setMccaaRating($mccaaRating)
  {
    $this->mccaaRating = $mccaaRating;
  }
  public function getMccaaRating()
  {
    return $this->mccaaRating;
  }
  public function setMccypRating($mccypRating)
  {
    $this->mccypRating = $mccypRating;
  }
  public function getMccypRating()
  {
    return $this->mccypRating;
  }
  public function setMcstRating($mcstRating)
  {
    $this->mcstRating = $mcstRating;
  }
  public function getMcstRating()
  {
    return $this->mcstRating;
  }
  public function setMdaRating($mdaRating)
  {
    $this->mdaRating = $mdaRating;
  }
  public function getMdaRating()
  {
    return $this->mdaRating;
  }
  public function setMedietilsynetRating($medietilsynetRating)
  {
    $this->medietilsynetRating = $medietilsynetRating;
  }
  public function getMedietilsynetRating()
  {
    return $this->medietilsynetRating;
  }
  public function setMekuRating($mekuRating)
  {
    $this->mekuRating = $mekuRating;
  }
  public function getMekuRating()
  {
    return $this->mekuRating;
  }
  public function setMibacRating($mibacRating)
  {
    $this->mibacRating = $mibacRating;
  }
  public function getMibacRating()
  {
    return $this->mibacRating;
  }
  public function setMocRating($mocRating)
  {
    $this->mocRating = $mocRating;
  }
  public function getMocRating()
  {
    return $this->mocRating;
  }
  public function setMoctwRating($moctwRating)
  {
    $this->moctwRating = $moctwRating;
  }
  public function getMoctwRating()
  {
    return $this->moctwRating;
  }
  public function setMpaaRating($mpaaRating)
  {
    $this->mpaaRating = $mpaaRating;
  }
  public function getMpaaRating()
  {
    return $this->mpaaRating;
  }
  public function setMtrcbRating($mtrcbRating)
  {
    $this->mtrcbRating = $mtrcbRating;
  }
  public function getMtrcbRating()
  {
    return $this->mtrcbRating;
  }
  public function setNbcRating($nbcRating)
  {
    $this->nbcRating = $nbcRating;
  }
  public function getNbcRating()
  {
    return $this->nbcRating;
  }
  public function setNbcplRating($nbcplRating)
  {
    $this->nbcplRating = $nbcplRating;
  }
  public function getNbcplRating()
  {
    return $this->nbcplRating;
  }
  public function setNfrcRating($nfrcRating)
  {
    $this->nfrcRating = $nfrcRating;
  }
  public function getNfrcRating()
  {
    return $this->nfrcRating;
  }
  public function setNfvcbRating($nfvcbRating)
  {
    $this->nfvcbRating = $nfvcbRating;
  }
  public function getNfvcbRating()
  {
    return $this->nfvcbRating;
  }
  public function setNkclvRating($nkclvRating)
  {
    $this->nkclvRating = $nkclvRating;
  }
  public function getNkclvRating()
  {
    return $this->nkclvRating;
  }
  public function setOflcRating($oflcRating)
  {
    $this->oflcRating = $oflcRating;
  }
  public function getOflcRating()
  {
    return $this->oflcRating;
  }
  public function setPefilmRating($pefilmRating)
  {
    $this->pefilmRating = $pefilmRating;
  }
  public function getPefilmRating()
  {
    return $this->pefilmRating;
  }
  public function setRcnofRating($rcnofRating)
  {
    $this->rcnofRating = $rcnofRating;
  }
  public function getRcnofRating()
  {
    return $this->rcnofRating;
  }
  public function setResorteviolenciaRating($resorteviolenciaRating)
  {
    $this->resorteviolenciaRating = $resorteviolenciaRating;
  }
  public function getResorteviolenciaRating()
  {
    return $this->resorteviolenciaRating;
  }
  public function setRtcRating($rtcRating)
  {
    $this->rtcRating = $rtcRating;
  }
  public function getRtcRating()
  {
    return $this->rtcRating;
  }
  public function setRteRating($rteRating)
  {
    $this->rteRating = $rteRating;
  }
  public function getRteRating()
  {
    return $this->rteRating;
  }
  public function setRussiaRating($russiaRating)
  {
    $this->russiaRating = $russiaRating;
  }
  public function getRussiaRating()
  {
    return $this->russiaRating;
  }
  public function setSkfilmRating($skfilmRating)
  {
    $this->skfilmRating = $skfilmRating;
  }
  public function getSkfilmRating()
  {
    return $this->skfilmRating;
  }
  public function setSmaisRating($smaisRating)
  {
    $this->smaisRating = $smaisRating;
  }
  public function getSmaisRating()
  {
    return $this->smaisRating;
  }
  public function setSmsaRating($smsaRating)
  {
    $this->smsaRating = $smsaRating;
  }
  public function getSmsaRating()
  {
    return $this->smsaRating;
  }
  public function setTvpgRating($tvpgRating)
  {
    $this->tvpgRating = $tvpgRating;
  }
  public function getTvpgRating()
  {
    return $this->tvpgRating;
  }
  public function setYtRating($ytRating)
  {
    $this->ytRating = $ytRating;
  }
  public function getYtRating()
  {
    return $this->ytRating;
  }
}
