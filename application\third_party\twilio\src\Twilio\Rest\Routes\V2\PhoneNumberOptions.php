<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Routes
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Routes\V2;

use Twilio\Options;
use Twilio\Values;

abstract class PhoneNumberOptions
{

    /**
     * @param string $voiceRegion The Inbound Processing Region used for this phone number for voice
     * @param string $friendlyName A human readable description of this resource, up to 64 characters.
     * @return UpdatePhoneNumberOptions Options builder
     */
    public static function update(
        
        string $voiceRegion = Values::NONE,
        string $friendlyName = Values::NONE

    ): UpdatePhoneNumberOptions
    {
        return new UpdatePhoneNumberOptions(
            $voiceRegion,
            $friendlyName
        );
    }

}


class UpdatePhoneNumberOptions extends Options
    {
    /**
     * @param string $voiceRegion The Inbound Processing Region used for this phone number for voice
     * @param string $friendlyName A human readable description of this resource, up to 64 characters.
     */
    public function __construct(
        
        string $voiceRegion = Values::NONE,
        string $friendlyName = Values::NONE

    ) {
        $this->options['voiceRegion'] = $voiceRegion;
        $this->options['friendlyName'] = $friendlyName;
    }

    /**
     * The Inbound Processing Region used for this phone number for voice
     *
     * @param string $voiceRegion The Inbound Processing Region used for this phone number for voice
     * @return $this Fluent Builder
     */
    public function setVoiceRegion(string $voiceRegion): self
    {
        $this->options['voiceRegion'] = $voiceRegion;
        return $this;
    }

    /**
     * A human readable description of this resource, up to 64 characters.
     *
     * @param string $friendlyName A human readable description of this resource, up to 64 characters.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Routes.V2.UpdatePhoneNumberOptions ' . $options . ']';
    }
}

