<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\FlexApi\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;


class InsightsSettingsAnswerSetsList extends ListResource
    {
    /**
     * Construct the InsightsSettingsAnswerSetsList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/Insights/QualityManagement/Settings/AnswerSets';
    }

    /**
     * Fetch the InsightsSettingsAnswerSetsInstance
     *
     * @param array|Options $options Optional Arguments
     * @return InsightsSettingsAnswerSetsInstance Fetched InsightsSettingsAnswerSetsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(array $options = []): InsightsSettingsAnswerSetsInstance
    {

        $options = new Values($options);

        $headers = Values::of(['Authorization' => $options['authorization']]);

        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new InsightsSettingsAnswerSetsInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.FlexApi.V1.InsightsSettingsAnswerSetsList]';
    }
}
