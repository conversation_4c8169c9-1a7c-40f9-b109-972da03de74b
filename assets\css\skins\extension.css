/* Checkboxes */
html.dark .checkbox-primary label:before,
.checkbox-primary label:before {
	background: #0088cc;
	border-color: #0077b3;
}

html.dark .checkbox-text-primary input[type="checkbox"]:checked + label:after,
.checkbox-text-primary input[type="checkbox"]:checked + label:after {
	color: #0088cc;
}
/* Radios */
html.dark .radio-primary input[type="radio"]:checked + label:after,
.radio-primary input[type="radio"]:checked + label:after {
	background: #0088cc;
	-webkit-box-shadow: 0px 0px 1px #0088cc;
	box-shadow: 0px 0px 1px #0088cc;
}
/* Switch */
.switch.switch-primary .ios-switch .on-background {
	background: #0088cc;
}
/* Progress Bar */
.progress-bar {
	background-color: #0088cc;
}

.progress .progress-bar-primary {
	background-color: #0088cc;
}
/* Slider */
.slider-primary .ui-slider-range,
.slider-primary .ui-slider-handle {
	background: #0088cc;
}

.slider-gradient.slider-primary .ui-slider-range,
.slider-gradient.slider-primary .ui-slider-handle {
	background-image: -webkit-linear-gradient(left, #00aaff 0, #0088cc 50%, #006699 100%);
	background-image: linear-gradient(left, #00aaff 0, #0088cc 50%, #006699 100%);
}

.slider-gradient.ui-slider-vertical.slider-primary .ui-slider-range,
.slider-gradient.ui-slider-vertical.slider-primary .ui-slider-handle {
	background-image: -webkit-linear-gradient(to right, #00aaff 0, #0088cc 50%, #006699 100%);
	background-image: linear-gradient(to right, #00aaff 0, #0088cc 50%, #006699 100%);
}
/* DatePicker */
.datepicker table {
	width: 100%;
}

.datepicker table thead tr th.prev:hover,
.datepicker table thead tr th.next:hover {
	background: #0088cc;
}

.datepicker table thead tr:first-child th:hover {
	background: #0088cc;
}

.datepicker table tr td span:hover {
	background: #0088cc;
}

.datepicker table tr td.day:hover {
	background: #0088cc;
}

.datepicker table tfoot tr th:hover {
	background: #0088cc;
}
/* DatePicker: Dark */
html.dark .datepicker.datepicker-primary table thead tr th.prev:hover,
.datepicker.datepicker-dark table thead tr th.prev:hover,
html.dark .datepicker.datepicker-primary table thead tr th.next:hover,
.datepicker.datepicker-dark table thead tr th.next:hover {
	background: #0088cc;
}

html.dark .datepicker.datepicker-primary table tbody tr td.day:hover,
.datepicker.datepicker-dark table tbody tr td.day:hover {
	background: #0088cc;
}

html.dark .datepicker.datepicker-primary table tbody tr td.day.active,
.datepicker.datepicker-dark table tbody tr td.day.active {
	background: #0088cc;
}
/* DatePicker: Primary */
.datepicker.datepicker-primary table thead tr:first-child {
	background-color: #0088cc;
}

.datepicker.datepicker-primary table thead tr:first-child th:hover {
	background-color: #006699;
}

.datepicker.datepicker-primary table thead tr:last-child {
	background-color: #0099e6;
}

.datepicker.datepicker-primary table thead tr:last-child th:hover {
	background-color: #0088cc;
}

.datepicker.datepicker-primary table tbody tr td.day:hover {
	background: #0088cc;
}

.datepicker.datepicker-primary table tbody tr td.day.active {
	background: #0088cc;
}
/* Select 2 */
.select2-container-multi .select2-choices .select2-search-choice {
	background: #0088cc;
}
/* Wizard */
.wizard-steps > li.active .badge {
	background-color: #0088cc;
}

.wizard-steps > li.active a,
.wizard-steps > li.active a:hover,
.wizard-steps > li.active a:focus {
	border-top-color: #0088cc;
}

.wizard-tabs ul > li.active .badge {
	background-color: #0088cc;
}

html .wizard-progress .steps-progress .progress-indicator,
html.dark .wizard-progress .steps-progress .progress-indicator {
	background: #0088cc;
}

html .wizard-progress .wizard-steps li.completed a span,
html.dark .wizard-progress .wizard-steps li.completed a span {
	border-color: #0088cc;
	background: #0088cc;
}

html .wizard-progress .wizard-steps li.active a span,
html.dark .wizard-progress .wizard-steps li.active a span {
	color: #0088cc;
	border-color: #0088cc;
}
/* Tables */
.table > thead > tr > td.primary,
.table > tbody > tr > td.primary,
.table > tfoot > tr > td.primary,
.table > thead > tr > th.primary,
.table > tbody > tr > th.primary,
.table > tfoot > tr > th.primary,
.table > thead > tr.primary > td,
.table > tbody > tr.primary > td,
.table > tfoot > tr.primary > td,
.table > thead > tr.primary > th,
.table > tbody > tr.primary > th,
.table > tfoot > tr.primary > th {
	background-color: #0088cc !important;
}
/* Data Tables Loading */
.dataTables_processing {
	background-color: #0088cc;
}
/* Liquid Meter */
.liquid-meter-wrapper .liquid-meter-selector a.active {
	color: #0088cc;
}
