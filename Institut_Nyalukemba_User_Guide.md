# 📚 Institut Nyalukemba - Guide d'Utilisation du Système de Gestion Scolaire

## 📋 Table des Matières

1. [Vue d'Ensemble du Système](#vue-densemble-du-système)
2. [Rôles et Permissions des Utilisateurs](#rôles-et-permissions-des-utilisateurs)
3. [Navigation de l'Interface](#navigation-de-linterface)
4. [Guide de Configuration Post-Connexion](#guide-de-configuration-post-connexion)
5. [Séquence de Configuration Recommandée](#séquence-de-configuration-recommandée)
6. [Documentation des Fonctionnalités](#documentation-des-fonctionnalités)
7. [Dépannage et Bonnes Pratiques](#dépannage-et-bonnes-pratiques)

---

## 🎯 Vue d'Ensemble du Système

### Qu'est-ce que le Système de Gestion Scolaire ?

Le système de gestion scolaire de l'Institut Nyalukemba est une solution ERP (Enterprise Resource Planning) éducative complète conçue pour automatiser et simplifier tous les aspects de la gestion scolaire. Ce système multi-branches permet de gérer efficacement :

- **Gestion Académique** : Classes, matières, emplois du temps, examens
- **Gestion des Utilisateurs** : Étudiants, enseignants, parents, personnel administratif
- **Gestion Financière** : Frais scolaires, paiements, factures, comptabilité
- **Communication** : Messages, notifications, rapports
- **Ressources** : Bibliothèque, auberge, transport
- **Rapports et Analyses** : Statistiques, performances, présence

### Objectifs Principaux

1. **Centralisation** : Toutes les informations scolaires en un seul endroit
2. **Automatisation** : Réduction des tâches manuelles répétitives
3. **Transparence** : Accès en temps réel aux informations pour tous les acteurs
4. **Efficacité** : Optimisation des processus administratifs et pédagogiques
5. **Sécurité** : Protection des données avec contrôle d'accès par rôles

---

## 👥 Rôles et Permissions des Utilisateurs

### 🔑 Super Administrateur
**Accès complet au système - Contrôle total**

**Responsabilités :**
- Configuration initiale du système
- Gestion des branches (si multi-branches)
- Création et gestion des comptes administrateurs
- Configuration des paramètres globaux
- Supervision générale de toutes les activités

**Accès aux modules :**
- ✅ Tous les modules sans restriction
- ✅ Paramètres système
- ✅ Gestion des utilisateurs de tous niveaux
- ✅ Rapports globaux
- ✅ Sauvegarde et maintenance

### 👨‍💼 Administrateur
**Gestion complète d'une branche spécifique**

**Responsabilités :**
- Gestion quotidienne de l'école
- Supervision du personnel enseignant
- Gestion des étudiants et parents
- Configuration des paramètres scolaires
- Génération de rapports

**Accès aux modules :**
- ✅ Gestion des étudiants et parents
- ✅ Gestion du personnel enseignant
- ✅ Configuration scolaire
- ✅ Gestion financière
- ✅ Rapports et statistiques
- ❌ Paramètres système globaux

### 👨‍🏫 Enseignant
**Gestion pédagogique et suivi des étudiants**

**Responsabilités :**
- Gestion des notes et évaluations
- Suivi de la présence des étudiants
- Création et gestion des devoirs
- Communication avec les parents
- Gestion de l'emploi du temps personnel

**Accès aux modules :**
- ✅ Gestion des notes (classes assignées uniquement)
- ✅ Présence des étudiants
- ✅ Devoirs et évaluations
- ✅ Communication (messages)
- ✅ Emploi du temps personnel
- ❌ Gestion financière
- ❌ Configuration système

### 💰 Comptable
**Gestion financière et comptabilité**

**Responsabilités :**
- Gestion des frais scolaires
- Traitement des paiements
- Génération des factures
- Suivi des créances
- Rapports financiers

**Accès aux modules :**
- ✅ Gestion des frais et paiements
- ✅ Facturation
- ✅ Rapports financiers
- ✅ Comptabilité
- ❌ Gestion académique
- ❌ Configuration système

### 📚 Bibliothécaire
**Gestion de la bibliothèque et des ressources**

**Responsabilités :**
- Gestion du catalogue de livres
- Suivi des emprunts et retours
- Gestion des amendes
- Rapports de bibliothèque

**Accès aux modules :**
- ✅ Gestion des livres
- ✅ Emprunts et retours
- ✅ Rapports de bibliothèque
- ❌ Autres modules

### 👨‍🎓 Étudiant
**Accès aux informations personnelles et académiques**

**Accès aux fonctionnalités :**
- ✅ Profil personnel
- ✅ Notes et bulletins
- ✅ Emploi du temps
- ✅ Devoirs
- ✅ Présence
- ✅ Frais et paiements
- ✅ Bibliothèque (emprunts)

### 👨‍👩‍👧‍👦 Parent
**Suivi de l'enfant et communication avec l'école**

**Accès aux fonctionnalités :**
- ✅ Profil de l'enfant
- ✅ Notes et bulletins de l'enfant
- ✅ Présence de l'enfant
- ✅ Devoirs de l'enfant
- ✅ Communication avec les enseignants
- ✅ Paiements des frais

---

## 🧭 Navigation de l'Interface

### Structure du Menu Principal

L'interface est organisée en sections logiques accessibles via le menu latéral gauche :

#### 📊 **Tableau de Bord**
- Vue d'ensemble des statistiques
- Graphiques et indicateurs clés
- Notifications importantes
- Raccourcis vers les fonctions fréquentes

#### 👥 **Gestion des Utilisateurs**
- **Étudiants** : Inscription, profils, recherche
- **Enseignants** : Gestion du personnel enseignant
- **Parents** : Comptes parents et tuteurs
- **Personnel** : Autres employés de l'école

#### 🎓 **Gestion Académique**
- **Classes** : Création et gestion des classes
- **Matières** : Configuration des matières
- **Sections** : Organisation des sections
- **Sessions** : Années scolaires
- **Emploi du Temps** : Planification des cours

#### 📝 **Évaluations et Examens**
- **Examens** : Configuration des examens
- **Notes** : Saisie et gestion des notes
- **Bulletins** : Génération des bulletins
- **Examens en Ligne** : Tests numériques

#### 💰 **Gestion Financière**
- **Frais** : Structure des frais scolaires
- **Paiements** : Traitement des paiements
- **Factures** : Génération et suivi
- **Comptabilité** : Rapports financiers

#### 📚 **Ressources**
- **Bibliothèque** : Gestion des livres
- **Auberge** : Gestion de l'hébergement
- **Transport** : Organisation du transport
- **Inventaire** : Gestion du matériel

#### 💬 **Communication**
- **Messages** : Système de messagerie interne
- **Notifications** : Alertes et annonces
- **SMS/Email** : Communication externe

#### 📊 **Rapports**
- **Rapports Académiques** : Performances des étudiants
- **Rapports Financiers** : Situation financière
- **Rapports de Présence** : Statistiques de présence
- **Rapports Personnalisés** : Rapports sur mesure

#### ⚙️ **Paramètres**
- **École** : Configuration de l'établissement
- **Système** : Paramètres techniques
- **Sauvegardes** : Gestion des sauvegardes
- **Modules** : Activation/désactivation des fonctionnalités

### Navigation Rapide

#### Barre Supérieure
- **Logo de l'école** : Retour au tableau de bord
- **Sélecteur de branche** : (Si multi-branches)
- **Notifications** : Alertes en temps réel
- **Profil utilisateur** : Paramètres personnels et déconnexion

#### Raccourcis Clavier
- `Ctrl + D` : Tableau de bord
- `Ctrl + S` : Étudiants
- `Ctrl + T` : Enseignants
- `Ctrl + F` : Finances
- `Ctrl + R` : Rapports

---

## 🚀 Guide de Configuration Post-Connexion

### ⚠️ IMPORTANT : Ordre de Configuration

La configuration doit suivre un ordre précis car certains éléments dépendent d'autres. **Ne pas respecter cet ordre peut causer des erreurs ou des incohérences dans le système.**

### Pourquoi l'Ordre est Crucial ?

1. **Dépendances** : Certaines fonctionnalités nécessitent que d'autres soient configurées en premier
2. **Intégrité des données** : L'ordre assure la cohérence des informations
3. **Éviter les erreurs** : Prévient les messages d'erreur et les dysfonctionnements
4. **Efficacité** : Évite de refaire certaines configurations

### Conséquences d'un Mauvais Ordre

- **Erreurs de base de données** : Références manquantes
- **Fonctionnalités non disponibles** : Options grisées ou absentes
- **Données incohérentes** : Informations contradictoires
- **Nécessité de reconfiguration** : Perte de temps et d'efforts

---

## 📋 Séquence de Configuration Recommandée

### 🏗️ **PHASE 1 : FONDATIONS (OBLIGATOIRE EN PREMIER)**

#### **Étape 1.1 : Configuration de l'École**
**📍 Menu : Paramètres → École**

**Pourquoi en premier ?**
- Définit l'identité de l'établissement
- Paramètres utilisés dans tous les autres modules
- Base pour la génération des documents officiels

**Configuration requise :**
```
✅ Nom de l'école : Institut Nyalukemba
✅ Adresse complète
✅ Téléphone et email
✅ Logo de l'école (format PNG/JPG, max 2MB)
✅ Devise et symbole monétaire (Franc Congolais - FC)
✅ Fuseau horaire : Africa/Kinshasa
✅ Langue par défaut : Français
✅ Jours de weekend : Samedi, Dimanche
```

**⚠️ Attention :** Ces informations apparaîtront sur tous les documents générés (bulletins, factures, certificats).

#### **Étape 1.2 : Sessions Scolaires**
**📍 Menu : Paramètres → Sessions**

**Pourquoi maintenant ?**
- Toutes les autres données sont liées à une session
- Impossible de créer des classes sans session active
- Base temporelle de tout le système

**Configuration :**
```
✅ Session actuelle : 2024-2025
✅ Date de début : 15 septembre 2024
✅ Date de fin : 15 juillet 2025
✅ Statut : Active
```

**Instructions détaillées :**
1. Cliquez sur "Ajouter une Session"
2. Saisissez le nom de la session (ex: 2024-2025)
3. Définissez les dates de début et fin
4. Marquez comme "Active"
5. Sauvegardez

#### **Étape 1.3 : Matières**
**📍 Menu : Académique → Matières**

**Pourquoi avant les classes ?**
- Les classes ont besoin de matières pour être complètes
- Les enseignants seront assignés aux matières
- Base pour les emplois du temps

**Matières suggérées pour Institut Nyalukemba :**
```
✅ Mathématiques
✅ Français
✅ Anglais
✅ Sciences Naturelles
✅ Histoire-Géographie
✅ Éducation Civique et Morale
✅ Éducation Physique
✅ Arts Plastiques
✅ Musique
✅ Informatique
✅ Lingala (langue locale)
✅ Religion
```

**Instructions :**
1. Menu → Académique → Matières
2. Cliquez "Ajouter Matière"
3. Nom de la matière
4. Code (ex: MATH, FRAN, ANGL)
5. Type : Théorique/Pratique
6. Sauvegardez et répétez

### 🏢 **PHASE 2 : STRUCTURE ORGANISATIONNELLE**

#### **Étape 2.1 : Classes**
**📍 Menu : Académique → Classes**

**Pourquoi maintenant ?**
- Dépend des sessions (créées en Phase 1)
- Nécessaire avant l'inscription des étudiants
- Base pour l'organisation pédagogique

**Classes suggérées :**
```
✅ Maternelle 1 (M1)
✅ Maternelle 2 (M2)
✅ Maternelle 3 (M3)
✅ 1ère Primaire (1P)
✅ 2ème Primaire (2P)
✅ 3ème Primaire (3P)
✅ 4ème Primaire (4P)
✅ 5ème Primaire (5P)
✅ 6ème Primaire (6P)
✅ 1ère Secondaire (1S)
✅ 2ème Secondaire (2S)
✅ 3ème Secondaire (3S)
✅ 4ème Secondaire (4S)
✅ 5ème Secondaire (5S)
✅ 6ème Secondaire (6S)
```

**Instructions détaillées :**
1. Menu → Académique → Classes
2. Cliquez "Ajouter Classe"
3. Nom de la classe (ex: 1ère Primaire)
4. Nom court (ex: 1P)
5. Capacité maximale (ex: 40 étudiants)
6. Sélectionnez la session active
7. Sauvegardez

#### **Étape 2.2 : Sections**
**📍 Menu : Académique → Sections**

**Pourquoi après les classes ?**
- Les sections subdivisent les classes
- Permet de gérer plusieurs groupes par classe
- Nécessaire pour les grandes classes

**Configuration :**
```
✅ Section A (pour chaque classe)
✅ Section B (si nécessaire)
✅ Section C (si nécessaire)
```

**Instructions :**
1. Menu → Académique → Sections
2. Cliquez "Ajouter Section"
3. Nom de la section (A, B, C...)
4. Sélectionnez la classe
5. Capacité de la section
6. Sauvegardez

#### **Étape 2.3 : Départements**
**📍 Menu : Personnel → Départements**

**Pourquoi maintenant ?**
- Organisation du personnel enseignant
- Nécessaire avant l'ajout des enseignants
- Structure hiérarchique

**Départements suggérés :**
```
✅ Direction
✅ Administration
✅ Maternelle
✅ Primaire
✅ Secondaire
✅ Comptabilité
✅ Bibliothèque
✅ Maintenance
```

### 👥 **PHASE 3 : GESTION DES UTILISATEURS**

#### **Étape 3.1 : Personnel Enseignant**
**📍 Menu : Personnel → Enseignants**

**Pourquoi maintenant ?**
- Dépend des départements (créés en Phase 2)
- Nécessaire avant l'assignation aux matières
- Base pour les emplois du temps

**Informations requises par enseignant :**
```
✅ Informations personnelles (nom, prénom, sexe)
✅ Contact (téléphone, email, adresse)
✅ Département d'affectation
✅ Matières enseignées
✅ Qualification/diplômes
✅ Date d'embauche
✅ Salaire (optionnel)
✅ Photo (optionnel)
```

**Instructions :**
1. Menu → Personnel → Enseignants
2. Cliquez "Ajouter Enseignant"
3. Remplissez tous les champs obligatoires
4. Assignez au département approprié
5. Définissez les permissions d'accès
6. Sauvegardez

#### **Étape 3.2 : Assignation Matières-Enseignants**
**📍 Menu : Académique → Assignation des Matières**

**Pourquoi après les enseignants ?**
- Lie les enseignants aux matières qu'ils enseignent
- Nécessaire pour les emplois du temps
- Base pour la saisie des notes

**Instructions :**
1. Menu → Académique → Assignation des Matières
2. Sélectionnez la classe
3. Sélectionnez la matière
4. Assignez l'enseignant
5. Répétez pour toutes les combinaisons
6. Sauvegardez

### 💰 **PHASE 4 : STRUCTURE FINANCIÈRE**

#### **Étape 4.1 : Types de Frais**
**📍 Menu : Finances → Types de Frais**

**Pourquoi maintenant ?**
- Base pour la structure financière
- Nécessaire avant la création des groupes de frais
- Définit les catégories de paiements

**Types de frais suggérés :**
```
✅ Frais de scolarité
✅ Frais d'inscription
✅ Frais d'examen
✅ Frais de bibliothèque
✅ Frais de transport
✅ Frais d'uniforme
✅ Frais de cantine
✅ Frais d'activités extra-scolaires
```

#### **Étape 4.2 : Groupes de Frais**
**📍 Menu : Finances → Groupes de Frais**

**Pourquoi après les types ?**
- Combine plusieurs types de frais
- Permet de créer des packages par classe
- Simplifie la gestion des paiements

**Configuration par classe :**
```
✅ Groupe Maternelle (M1, M2, M3)
✅ Groupe Primaire (1P à 6P)
✅ Groupe Secondaire 1er cycle (1S à 3S)
✅ Groupe Secondaire 2ème cycle (4S à 6S)
```

#### **Étape 4.3 : Allocation des Frais**
**📍 Menu : Finances → Allocation des Frais**

**Pourquoi en dernier de cette phase ?**
- Assigne les groupes de frais aux classes
- Définit les montants par classe
- Active le système de facturation

### 👨‍🎓 **PHASE 5 : INSCRIPTION DES ÉTUDIANTS**

#### **Étape 5.1 : Catégories d'Étudiants**
**📍 Menu : Étudiants → Catégories**

**Pourquoi avant l'inscription ?**
- Permet de classifier les étudiants
- Utile pour les statistiques
- Peut affecter les frais

**Catégories suggérées :**
```
✅ Nouveau
✅ Ancien
✅ Redoublant
✅ Transféré
✅ Boursier
```

#### **Étape 5.2 : Inscription des Étudiants**
**📍 Menu : Étudiants → Ajouter Étudiant**

**Pourquoi maintenant ?**
- Toutes les structures sont en place
- Classes et sections créées
- Frais configurés

**Informations requises :**
```
✅ Informations personnelles
✅ Informations des parents/tuteurs
✅ Classe et section d'affectation
✅ Catégorie d'étudiant
✅ Photo (optionnel)
✅ Documents (certificats, etc.)
```

### 📚 **PHASE 6 : CONFIGURATION ACADÉMIQUE AVANCÉE**

#### **Étape 6.1 : Périodes d'Examen**
**📍 Menu : Examens → Périodes**

**Configuration des périodes :**
```
✅ 1er Trimestre (Sept-Déc)
✅ 2ème Trimestre (Jan-Mars)
✅ 3ème Trimestre (Avril-Juillet)
✅ Examen National (selon calendrier officiel)
```

#### **Étape 6.2 : Types d'Examens**
**📍 Menu : Examens → Types**

**Types suggérés :**
```
✅ Interrogation
✅ Devoir
✅ Composition
✅ Examen Semestriel
✅ Examen National
```

#### **Étape 6.3 : Système de Notation**
**📍 Menu : Examens → Système de Notes**

**Configuration pour le système congolais :**
```
✅ Note maximale : 20
✅ Note de passage : 10
✅ Échelle : A (18-20), B (16-17), C (14-15), D (12-13), E (10-11), F (0-9)
```

### 📊 **PHASE 7 : MODULES OPTIONNELS**

#### **Étape 7.1 : Bibliothèque**
**📍 Menu : Bibliothèque**

**Si vous utilisez la bibliothèque :**
```
✅ Catégories de livres
✅ Ajout des livres
✅ Règles d'emprunt
✅ Amendes
```

#### **Étape 7.2 : Transport**
**📍 Menu : Transport**

**Si vous proposez le transport :**
```
✅ Routes de transport
✅ Véhicules
✅ Arrêts
✅ Frais de transport
```

#### **Étape 7.3 : Auberge**
**📍 Menu : Auberge**

**Si vous avez un internat :**
```
✅ Types de chambres
✅ Chambres
✅ Allocation des lits
✅ Frais d'hébergement
```

---

## 📖 Documentation des Fonctionnalités

### 🎓 **GESTION ACADÉMIQUE**

#### **Classes et Sections**

**Objectif :** Organiser les étudiants en groupes d'apprentissage structurés.

**Fonctionnalités :**
- Création de classes par niveau
- Division en sections pour gérer l'effectif
- Assignation d'enseignants responsables
- Gestion de la capacité maximale

**Prérequis :**
- Sessions scolaires configurées
- Matières créées

**Instructions d'utilisation :**

1. **Créer une classe :**
   - Menu → Académique → Classes
   - Cliquez "Ajouter Classe"
   - Remplissez : Nom, Code, Capacité
   - Sélectionnez la session
   - Sauvegardez

2. **Créer des sections :**
   - Menu → Académique → Sections
   - Sélectionnez la classe
   - Ajoutez les sections (A, B, C...)
   - Définissez la capacité par section

3. **Assigner un enseignant responsable :**
   - Dans la liste des classes
   - Cliquez "Modifier"
   - Sélectionnez l'enseignant responsable
   - Sauvegardez

**Bonnes pratiques :**
- Limitez à 40 étudiants par section maximum
- Nommez les sections de manière cohérente (A, B, C)
- Assignez toujours un enseignant responsable
- Vérifiez la capacité totale de l'école

**Dépannage courant :**
- **Erreur "Session non trouvée"** : Créez d'abord une session active
- **Impossible d'ajouter des étudiants** : Vérifiez que la classe a des sections
- **Enseignant non disponible** : L'enseignant doit être créé en premier

#### **Emploi du Temps**

**Objectif :** Planifier et organiser les cours de manière optimale.

**Fonctionnalités :**
- Création d'emplois du temps par classe
- Assignation automatique des enseignants
- Gestion des conflits d'horaires
- Vue par enseignant et par classe

**Prérequis :**
- Classes et sections créées
- Matières configurées
- Enseignants assignés aux matières
- Périodes de cours définies

**Instructions d'utilisation :**

1. **Configurer les périodes :**
   - Menu → Emploi du Temps → Périodes
   - Définissez les heures de cours
   - Exemple : 7h30-8h15, 8h15-9h00, etc.

2. **Créer l'emploi du temps :**
   - Menu → Emploi du Temps → Par Classe
   - Sélectionnez la classe et section
   - Cliquez sur chaque créneau horaire
   - Sélectionnez la matière et l'enseignant
   - Sauvegardez

3. **Vérifier les conflits :**
   - Le système signale automatiquement les conflits
   - Résolvez avant de sauvegarder
   - Vérifiez la charge horaire des enseignants

**Bonnes pratiques :**
- Respectez la charge horaire officielle par matière
- Évitez les matières difficiles en fin de journée
- Alternez matières théoriques et pratiques
- Prévoyez des pauses appropriées

### 👨‍🎓 **GESTION DES ÉTUDIANTS**

#### **Inscription et Profils**

**Objectif :** Centraliser toutes les informations des étudiants.

**Fonctionnalités :**
- Inscription complète avec documents
- Gestion des profils détaillés
- Historique académique
- Suivi des paiements
- Communication avec les parents

**Prérequis :**
- Classes et sections créées
- Catégories d'étudiants définies
- Structure des frais configurée

**Instructions d'inscription :**

1. **Informations de base :**
   ```
✅ Nom et prénom
   ✅ Date de naissance
   ✅ Lieu de naissance
   ✅ Sexe
   ✅ Nationalité
   ✅ Adresse complète
   ✅ Téléphone
```

2. **Informations académiques :**
   ```
✅ Classe d'affectation
   ✅ Section
   ✅ Numéro d'inscription
   ✅ Catégorie (nouveau/ancien)
   ✅ Date d'admission
```

3. **Informations des parents :**
   ```
✅ Nom du père/tuteur
   ✅ Profession du père
   ✅ Nom de la mère
   ✅ Profession de la mère
   ✅ Contact d'urgence
   ✅ Adresse des parents
```

4. **Documents requis :**
   ```
✅ Acte de naissance
   ✅ Certificat médical
   ✅ Photos d'identité
   ✅ Bulletins précédents
   ✅ Certificat de transfert (si applicable)
```

**Processus d'inscription :**

1. **Préparation :**
   - Vérifiez que toutes les structures sont en place
   - Préparez les documents requis
   - Confirmez la disponibilité de places

2. **Saisie des données :**
   - Menu → Étudiants → Ajouter Étudiant
   - Remplissez tous les onglets
   - Téléchargez les documents
   - Vérifiez les informations

3. **Validation :**
   - Vérifiez l'exactitude des données
   - Confirmez l'assignation de classe
   - Générez le numéro d'inscription
   - Sauvegardez

4. **Post-inscription :**
   - Créez le compte parent (si nécessaire)
   - Générez la facture des frais
   - Imprimez la carte d'étudiant
   - Archivez les documents

**Bonnes pratiques :**
- Vérifiez toujours l'âge par rapport à la classe
- Gardez des copies de tous les documents
- Utilisez un système de numérotation cohérent
- Mettez à jour régulièrement les contacts

### 💰 **GESTION FINANCIÈRE**

#### **Structure des Frais**

**Objectif :** Organiser et gérer tous les aspects financiers de l'école.

**Composants :**
1. **Types de frais** : Catégories de base
2. **Groupes de frais** : Combinaisons par niveau
3. **Allocation** : Assignation aux classes
4. **Collection** : Processus de paiement

**Configuration détaillée :**

**1. Types de Frais :**
```
Frais de Scolarité :
- Montant : Variable par classe
- Fréquence : Trimestrielle
- Obligatoire : Oui

Frais d'Inscription :
- Montant : Fixe
- Fréquence : Annuelle
- Obligatoire : Oui

Frais d'Examen :
- Montant : Par période
- Fréquence : Trimestrielle
- Obligatoire : Oui
```

**2. Groupes de Frais par Niveau :**
```
Groupe Maternelle :
- Frais de scolarité : 50,000 FC/trimestre
- Frais d'inscription : 25,000 FC/an
- Frais d'activités : 15,000 FC/trimestre
- Total : 90,000 FC/trimestre

Groupe Primaire :
- Frais de scolarité : 75,000 FC/trimestre
- Frais d'inscription : 30,000 FC/an
- Frais d'examen : 10,000 FC/trimestre
- Frais de bibliothèque : 5,000 FC/trimestre
- Total : 120,000 FC/trimestre

Groupe Secondaire :
- Frais de scolarité : 100,000 FC/trimestre
- Frais d'inscription : 40,000 FC/an
- Frais d'examen : 15,000 FC/trimestre
- Frais de laboratoire : 10,000 FC/trimestre
- Total : 165,000 FC/trimestre
```

#### **Processus de Paiement**

**Étapes du paiement :**

1. **Génération de facture :**
   - Menu → Finances → Collection des Frais
   - Sélectionnez l'étudiant
   - Choisissez les frais à facturer
   - Générez la facture

2. **Réception du paiement :**
   - Vérifiez le montant
   - Sélectionnez le mode de paiement
   - Enregistrez le paiement
   - Imprimez le reçu

3. **Suivi des impayés :**
   - Menu → Finances → Rapports de Créances
   - Identifiez les retards
   - Envoyez des rappels
   - Appliquez les pénalités si nécessaire

**Modes de paiement acceptés :**
- Espèces
- Chèque
- Virement bancaire
- Mobile Money (Airtel Money, Orange Money)

### 📊 **SYSTÈME D'ÉVALUATION**

#### **Configuration des Examens**

**Types d'évaluations :**

1. **Évaluations continues :**
   - Interrogations (coefficient 1)
   - Devoirs (coefficient 2)
   - Participation (coefficient 1)

2. **Évaluations périodiques :**
   - Compositions trimestrielles (coefficient 3)
   - Examens semestriels (coefficient 4)

3. **Évaluations nationales :**
   - TENAFEP (6ème Primaire)
   - Examen d'État (6ème Secondaire)

**Système de notation :**
```
Échelle sur 20 points :
- A : 18-20 (Très Bien)
- B : 16-17 (Bien)
- C : 14-15 (Assez Bien)
- D : 12-13 (Passable)
- E : 10-11 (Faible)
- F : 0-9 (Échec)

Note de passage : 10/20
Moyenne générale : Moyenne pondérée des coefficients
```

#### **Saisie des Notes**

**Processus :**

1. **Préparation :**
   - Créez la période d'examen
   - Définissez les matières à évaluer
   - Assignez les enseignants

2. **Saisie :**
   - Menu → Examens → Saisie des Notes
   - Sélectionnez : Classe, Matière, Période
   - Saisissez les notes pour chaque étudiant
   - Validez et sauvegardez

3. **Vérification :**
   - Contrôlez la cohérence des notes
   - Vérifiez les calculs de moyennes
   - Corrigez les erreurs éventuelles

4. **Publication :**
   - Générez les bulletins
   - Validez définitivement
   - Distribuez aux étudiants/parents

### 📚 **GESTION DE LA BIBLIOTHÈQUE**

#### **Configuration de la Bibliothèque**

**Étapes de mise en place :**

1. **Catégories de livres :**
   ```
   ✅ Manuels scolaires
   ✅ Littérature française
   ✅ Littérature africaine
   ✅ Sciences et techniques
   ✅ Histoire et géographie
   ✅ Dictionnaires et encyclopédies
   ✅ Bandes dessinées
   ✅ Livres religieux
   ```

2. **Ajout des livres :**
   - Menu → Bibliothèque → Livres
   - Informations requises :
     ```
     ✅ Titre
     ✅ Auteur
     ✅ ISBN
     ✅ Catégorie
     ✅ Éditeur
     ✅ Année de publication
     ✅ Nombre d'exemplaires
     ✅ Emplacement
     ```

3. **Règles d'emprunt :**
   ```
   Étudiants :
   - Durée : 2 semaines
   - Nombre max : 2 livres
   - Renouvellement : 1 fois

   Enseignants :
   - Durée : 1 mois
   - Nombre max : 5 livres
   - Renouvellement : 2 fois
   ```

4. **Système d'amendes :**
   ```
   Retard :
   - 1-7 jours : 500 FC
   - 8-14 jours : 1,000 FC
   - 15+ jours : 2,000 FC

   Perte/Dégradation :
   - Remplacement obligatoire
   - Amende supplémentaire : 5,000 FC
   ```

#### **Gestion des Emprunts**

**Processus d'emprunt :**

1. **Enregistrement :**
   - Menu → Bibliothèque → Emprunts
   - Scannez/saisissez l'ID étudiant
   - Sélectionnez les livres
   - Confirmez l'emprunt

2. **Suivi :**
   - Vérifiez les dates de retour
   - Envoyez des rappels automatiques
   - Gérez les retards

3. **Retour :**
   - Vérifiez l'état du livre
   - Enregistrez le retour
   - Calculez les amendes éventuelles

---

## 🔧 Dépannage et Bonnes Pratiques

### ⚠️ **Problèmes Courants et Solutions**

#### **Problèmes de Configuration**

**1. "Impossible de créer une classe"**
```
Cause : Session scolaire non active
Solution :
1. Menu → Paramètres → Sessions
2. Vérifiez qu'une session est marquée "Active"
3. Si aucune session, créez-en une nouvelle
```

**2. "Enseignant non disponible pour assignation"**
```
Cause : Enseignant non créé ou inactif
Solution :
1. Menu → Personnel → Enseignants
2. Vérifiez que l'enseignant existe
3. Vérifiez le statut "Actif"
4. Créez l'enseignant si nécessaire
```

**3. "Erreur lors de l'inscription d'un étudiant"**
```
Causes possibles :
- Classe/section non créée
- Numéro d'inscription en double
- Champs obligatoires manquants

Solutions :
1. Vérifiez la structure des classes
2. Utilisez un numéro unique
3. Remplissez tous les champs requis
```

#### **Problèmes Financiers**

**1. "Frais non disponibles pour la classe"**
```
Cause : Allocation des frais non configurée
Solution :
1. Menu → Finances → Allocation des Frais
2. Assignez les groupes de frais aux classes
3. Vérifiez les montants
```

**2. "Impossible de générer une facture"**
```
Causes possibles :
- Étudiant non inscrit correctement
- Frais non alloués à la classe
- Période de facturation fermée

Solutions :
1. Vérifiez l'inscription de l'étudiant
2. Configurez l'allocation des frais
3. Ouvrez la période de facturation
```

#### **Problèmes d'Évaluation**

**1. "Notes non sauvegardées"**
```
Causes possibles :
- Période d'examen non créée
- Enseignant non assigné à la matière
- Problème de permissions

Solutions :
1. Créez la période d'examen
2. Vérifiez l'assignation matière-enseignant
3. Vérifiez les permissions utilisateur
```

**2. "Bulletin non généré"**
```
Cause : Notes incomplètes ou non validées
Solution :
1. Vérifiez que toutes les notes sont saisies
2. Validez les notes par période
3. Régénérez le bulletin
```

### ✅ **Bonnes Pratiques**

#### **Sécurité et Sauvegarde**

**1. Sauvegardes régulières :**
```
Fréquence recommandée :
- Quotidienne : Données critiques
- Hebdomadaire : Base de données complète
- Mensuelle : Sauvegarde complète système

Stockage :
- Local : Serveur de l'école
- Externe : Cloud ou disque externe
- Hors site : Copie de sécurité distante
```

**2. Gestion des mots de passe :**
```
Règles :
- Minimum 8 caractères
- Combinaison lettres/chiffres/symboles
- Changement tous les 3 mois
- Pas de partage de comptes

Comptes par défaut :
- Changez immédiatement les mots de passe par défaut
- Désactivez les comptes inutilisés
- Surveillez les connexions suspectes
```

**3. Permissions utilisateurs :**
```
Principe du moindre privilège :
- Donnez uniquement les accès nécessaires
- Révisez régulièrement les permissions
- Désactivez les comptes des anciens employés
- Loggez les actions sensibles
```

#### **Maintenance Préventive**

**1. Vérifications quotidiennes :**
```
✅ Sauvegarde automatique effectuée
✅ Espace disque suffisant
✅ Connexions utilisateurs normales
✅ Pas d'erreurs système
```

**2. Vérifications hebdomadaires :**
```
✅ Cohérence des données
✅ Performance du système
✅ Mises à jour de sécurité
✅ Nettoyage des fichiers temporaires
```

**3. Vérifications mensuelles :**
```
✅ Audit des comptes utilisateurs
✅ Révision des permissions
✅ Test de restauration de sauvegarde
✅ Mise à jour de la documentation
```

#### **Formation du Personnel**

**1. Formation initiale :**
```
Durée : 2 jours minimum
Contenu :
- Navigation de base
- Fonctions spécifiques au rôle
- Procédures de sécurité
- Cas pratiques
```

**2. Formation continue :**
```
Fréquence : Trimestrielle
Contenu :
- Nouvelles fonctionnalités
- Bonnes pratiques
- Résolution de problèmes
- Retours d'expérience
```

**3. Documentation :**
```
Maintenir à jour :
- Guides utilisateur par rôle
- Procédures d'urgence
- Contacts support
- FAQ mise à jour
```

### 📞 **Support et Assistance**

#### **Contacts Support**
```
Support Technique :
Email : <EMAIL>
Téléphone : +243 995 618 678
Heures : Lundi-Vendredi 8h-17h

Support Utilisateur :
Formation et assistance quotidienne
Documentation en ligne
Tutoriels vidéo (si disponibles)
```

#### **Escalade des Problèmes**
```
Niveau 1 : Utilisateur final
- Consultation de la documentation
- Vérification des procédures de base

Niveau 2 : Administrateur système local
- Problèmes de configuration
- Gestion des utilisateurs
- Maintenance courante

Niveau 3 : Support technique externe
- Problèmes techniques complexes
- Mises à jour système
- Récupération de données
```

---

## 📋 **Checklist de Démarrage Rapide**

### ✅ **Configuration Minimale (1ère semaine)**
```
□ Configuration de l'école (nom, adresse, logo)
□ Création de la session scolaire active
□ Ajout des matières principales
□ Création des classes essentielles
□ Ajout de 2-3 enseignants test
□ Configuration des frais de base
□ Inscription de 5-10 étudiants test
□ Test de génération de facture
□ Test de saisie de notes
□ Sauvegarde initiale
```

### ✅ **Déploiement Complet (1 mois)**
```
□ Toutes les classes et sections
□ Tout le personnel enseignant
□ Tous les étudiants
□ Structure financière complète
□ Emplois du temps
□ Système d'évaluation
□ Bibliothèque (si applicable)
□ Formation du personnel
□ Procédures de sauvegarde
□ Documentation utilisateur
```

---

**© 2025 Institut Nyalukemba - Système de Gestion Scolaire**
**Powered by PASS-DRC School Management System**

*Ce guide est un document vivant qui doit être mis à jour régulièrement selon l'évolution du système et les besoins de l'école.*
```
Causes possibles :
- Période d'examen non créée
- Enseignant non assigné à la matière
- Problème de permissions

Solutions :
1. Créez la période d'examen
2. Vérifiez l'assignation matière-enseignant
3. Vérifiez les permissions utilisateur
```

**2. "Bulletin non généré"**
```
Cause : Notes incomplètes ou non validées
Solution :
1. Vérifiez que toutes les notes sont saisies
2. Validez les notes par période
3. Régénérez le bulletin
```

**Fonctionnalités :**
- Création d'emplois du temps par classe
- Assignation automatique des enseignants
- Gestion des conflits d'horaires
- Vue par enseignant et par classe

**Prérequis :**
- Classes et sections créées
- Matières configurées
- Enseignants assignés aux matières
- Périodes de cours définies

**Instructions d'utilisation :**

1. **Configurer les périodes :**
   - Menu → Emploi du Temps → Périodes
   - Définissez les heures de cours
   - Exemple : 7h30-8h15, 8h15-9h00, etc.

2. **Créer l'emploi du temps :**
   - Menu → Emploi du Temps → Par Classe
   - Sélectionnez la classe et section
   - Cliquez sur chaque créneau horaire
   - Sélectionnez la matière et l'enseignant
   - Sauvegardez

3. **Vérifier les conflits :**
   - Le système signale automatiquement les conflits
   - Résolvez avant de sauvegarder
   - Vérifiez la charge horaire des enseignants

**Bonnes pratiques :**
- Respectez la charge horaire officielle par matière
- Évitez les matières difficiles en fin de journée
- Alternez matières théoriques et pratiques
- Prévoyez des pauses appropriées

### 👨‍🎓 **GESTION DES ÉTUDIANTS**

#### **Inscription et Profils**

**Objectif :** Centraliser toutes les informations des étudiants.

**Fonctionnalités :**
- Inscription complète avec documents
- Gestion des profils détaillés
- Historique académique
- Suivi des paiements
- Communication avec les parents

**Prérequis :**
- Classes et sections créées
- Catégories d'étudiants définies
- Structure des frais configurée

**Instructions d'inscription :**

1. **Informations de base :**
   ```
   ✅ Nom et prénom
   ✅ Date de naissance
   ✅ Lieu de naissance
   ✅ Sexe
   ✅ Nationalité
   ✅ Adresse complète
   ✅ Téléphone
   ```

2. **Informations académiques :**
   ```
   ✅ Classe d'affectation
   ✅ Section
   ✅ Numéro d'inscription
   ✅ Catégorie (nouveau/ancien)
   ✅ Date d'admission
   ```

3. **Informations des parents :**
   ```
   ✅ Nom du père/tuteur
   ✅ Profession du père
   ✅ Nom de la mère
   ✅ Profession de la mère
   ✅ Contact d'urgence
   ✅ Adresse des parents
   ```

4. **Documents requis :**
   ```
   ✅ Acte de naissance
   ✅ Certificat médical
   ✅ Photos d'identité
   ✅ Bulletins précédents
   ✅ Certificat de transfert (si applicable)
   ```

**Processus d'inscription :**

1. **Préparation :**
   - Vérifiez que toutes les structures sont en place
   - Préparez les documents requis
   - Confirmez la disponibilité de places

2. **Saisie des données :**
   - Menu → Étudiants → Ajouter Étudiant
   - Remplissez tous les onglets
   - Téléchargez les documents
   - Vérifiez les informations

3. **Validation :**
   - Vérifiez l'exactitude des données
   - Confirmez l'assignation de classe
   - Générez le numéro d'inscription
   - Sauvegardez

4. **Post-inscription :**
   - Créez le compte parent (si nécessaire)
   - Générez la facture des frais
   - Imprimez la carte d'étudiant
   - Archivez les documents

**Bonnes pratiques :**
- Vérifiez toujours l'âge par rapport à la classe
- Gardez des copies de tous les documents
- Utilisez un système de numérotation cohérent
- Mettez à jour régulièrement les contacts

### 💰 **GESTION FINANCIÈRE**

#### **Structure des Frais**

**Objectif :** Organiser et gérer tous les aspects financiers de l'école.

**Composants :**
1. **Types de frais** : Catégories de base
2. **Groupes de frais** : Combinaisons par niveau
3. **Allocation** : Assignation aux classes
4. **Collection** : Processus de paiement

**Configuration détaillée :**

**1. Types de Frais :**
```
Frais de Scolarité :
- Montant : Variable par classe
- Fréquence : Trimestrielle
- Obligatoire : Oui

Frais d'Inscription :
- Montant : Fixe
- Fréquence : Annuelle
- Obligatoire : Oui

Frais d'Examen :
- Montant : Par période
- Fréquence : Trimestrielle
- Obligatoire : Oui
```

**2. Groupes de Frais par Niveau :**
```
Groupe Maternelle :
- Frais de scolarité : 50,000 FC/trimestre
- Frais d'inscription : 25,000 FC/an
- Frais d'activités : 15,000 FC/trimestre
- Total : 90,000 FC/trimestre

Groupe Primaire :
- Frais de scolarité : 75,000 FC/trimestre
- Frais d'inscription : 30,000 FC/an
- Frais d'examen : 10,000 FC/trimestre
- Frais de bibliothèque : 5,000 FC/trimestre
- Total : 120,000 FC/trimestre

Groupe Secondaire :
- Frais de scolarité : 100,000 FC/trimestre
- Frais d'inscription : 40,000 FC/an
- Frais d'examen : 15,000 FC/trimestre
- Frais de laboratoire : 10,000 FC/trimestre
- Total : 165,000 FC/trimestre
```

#### **Processus de Paiement**

**Étapes du paiement :**

1. **Génération de facture :**
   - Menu → Finances → Collection des Frais
   - Sélectionnez l'étudiant
   - Choisissez les frais à facturer
   - Générez la facture

2. **Réception du paiement :**
   - Vérifiez le montant
   - Sélectionnez le mode de paiement
   - Enregistrez le paiement
   - Imprimez le reçu

3. **Suivi des impayés :**
   - Menu → Finances → Rapports de Créances
   - Identifiez les retards
   - Envoyez des rappels
   - Appliquez les pénalités si nécessaire

**Modes de paiement acceptés :**
- Espèces
- Chèque
- Virement bancaire
- Mobile Money (Airtel Money, Orange Money)
