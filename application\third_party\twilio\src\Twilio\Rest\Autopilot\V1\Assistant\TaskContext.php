<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Autopilot
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Autopilot\V1\Assistant;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;
use Twilio\Rest\Autopilot\V1\Assistant\Task\SampleList;
use Twilio\Rest\Autopilot\V1\Assistant\Task\FieldList;
use Twilio\Rest\Autopilot\V1\Assistant\Task\TaskActionsList;
use Twilio\Rest\Autopilot\V1\Assistant\Task\TaskStatisticsList;


/**
 * @property SampleList $samples
 * @property FieldList $fields
 * @property TaskActionsList $taskActions
 * @property TaskStatisticsList $statistics
 * @method \Twilio\Rest\Autopilot\V1\Assistant\Task\SampleContext samples(string $sid)
 * @method \Twilio\Rest\Autopilot\V1\Assistant\Task\TaskActionsContext taskActions()
 * @method \Twilio\Rest\Autopilot\V1\Assistant\Task\TaskStatisticsContext statistics()
 * @method \Twilio\Rest\Autopilot\V1\Assistant\Task\FieldContext fields(string $sid)
 */
class TaskContext extends InstanceContext
    {
    protected $_samples;
    protected $_fields;
    protected $_taskActions;
    protected $_statistics;

    /**
     * Initialize the TaskContext
     *
     * @param Version $version Version that contains the resource
     * @param string $assistantSid The SID of the [Assistant](https://www.twilio.com/docs/autopilot/api/assistant) that is the parent of the new resource.
     * @param string $sid The Twilio-provided string that uniquely identifies the Task resource to delete.
     */
    public function __construct(
        Version $version,
        $assistantSid,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'assistantSid' =>
            $assistantSid,
        'sid' =>
            $sid,
        ];

        $this->uri = '/Assistants/' . \rawurlencode($assistantSid)
        .'/Tasks/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Delete the TaskInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the TaskInstance
     *
     * @return TaskInstance Fetched TaskInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): TaskInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new TaskInstance(
            $this->version,
            $payload,
            $this->solution['assistantSid'],
            $this->solution['sid']
        );
    }


    /**
     * Update the TaskInstance
     *
     * @param array|Options $options Optional Arguments
     * @return TaskInstance Updated TaskInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): TaskInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' =>
                $options['friendlyName'],
            'UniqueName' =>
                $options['uniqueName'],
            'Actions' =>
                Serialize::jsonObject($options['actions']),
            'ActionsUrl' =>
                $options['actionsUrl'],
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new TaskInstance(
            $this->version,
            $payload,
            $this->solution['assistantSid'],
            $this->solution['sid']
        );
    }


    /**
     * Access the samples
     */
    protected function getSamples(): SampleList
    {
        if (!$this->_samples) {
            $this->_samples = new SampleList(
                $this->version,
                $this->solution['assistantSid'],
                $this->solution['sid']
            );
        }

        return $this->_samples;
    }

    /**
     * Access the fields
     */
    protected function getFields(): FieldList
    {
        if (!$this->_fields) {
            $this->_fields = new FieldList(
                $this->version,
                $this->solution['assistantSid'],
                $this->solution['sid']
            );
        }

        return $this->_fields;
    }

    /**
     * Access the taskActions
     */
    protected function getTaskActions(): TaskActionsList
    {
        if (!$this->_taskActions) {
            $this->_taskActions = new TaskActionsList(
                $this->version,
                $this->solution['assistantSid'],
                $this->solution['sid']
            );
        }

        return $this->_taskActions;
    }

    /**
     * Access the statistics
     */
    protected function getStatistics(): TaskStatisticsList
    {
        if (!$this->_statistics) {
            $this->_statistics = new TaskStatisticsList(
                $this->version,
                $this->solution['assistantSid'],
                $this->solution['sid']
            );
        }

        return $this->_statistics;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name): ListResource
    {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Autopilot.V1.TaskContext ' . \implode(' ', $context) . ']';
    }
}
