<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Preview\DeployedDevices\Fleet;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;


class DeviceContext extends InstanceContext
    {
    /**
     * Initialize the DeviceContext
     *
     * @param Version $version Version that contains the resource
     * @param string $fleetSid 
     * @param string $sid Provides a 34 character string that uniquely identifies the requested Device resource.
     */
    public function __construct(
        Version $version,
        $fleetSid,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'fleetSid' =>
            $fleetSid,
        'sid' =>
            $sid,
        ];

        $this->uri = '/Fleets/' . \rawurlencode($fleetSid)
        .'/Devices/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Delete the DeviceInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->version->delete('DELETE', $this->uri);
    }


    /**
     * Fetch the DeviceInstance
     *
     * @return DeviceInstance Fetched DeviceInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): DeviceInstance
    {

        $payload = $this->version->fetch('GET', $this->uri);

        return new DeviceInstance(
            $this->version,
            $payload,
            $this->solution['fleetSid'],
            $this->solution['sid']
        );
    }


    /**
     * Update the DeviceInstance
     *
     * @param array|Options $options Optional Arguments
     * @return DeviceInstance Updated DeviceInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): DeviceInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' =>
                $options['friendlyName'],
            'Identity' =>
                $options['identity'],
            'DeploymentSid' =>
                $options['deploymentSid'],
            'Enabled' =>
                Serialize::booleanToString($options['enabled']),
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new DeviceInstance(
            $this->version,
            $payload,
            $this->solution['fleetSid'],
            $this->solution['sid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Preview.DeployedDevices.DeviceContext ' . \implode(' ', $context) . ']';
    }
}
