<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Preview\Understand\Assistant;

use Twilio\Options;
use Twilio\Values;

abstract class AssistantFallbackActionsOptions
{

    /**
     * @param array $fallbackActions 
     * @return UpdateAssistantFallbackActionsOptions Options builder
     */
    public static function update(
        
        array $fallbackActions = Values::ARRAY_NONE

    ): UpdateAssistantFallbackActionsOptions
    {
        return new UpdateAssistantFallbackActionsOptions(
            $fallbackActions
        );
    }

}


class UpdateAssistantFallbackActionsOptions extends Options
    {
    /**
     * @param array $fallbackActions 
     */
    public function __construct(
        
        array $fallbackActions = Values::ARRAY_NONE

    ) {
        $this->options['fallbackActions'] = $fallbackActions;
    }

    /**
     * 
     *
     * @param array $fallbackActions 
     * @return $this Fluent Builder
     */
    public function setFallbackActions(array $fallbackActions): self
    {
        $this->options['fallbackActions'] = $fallbackActions;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Understand.UpdateAssistantFallbackActionsOptions ' . $options . ']';
    }
}

