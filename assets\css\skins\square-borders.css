.img-rounded,
.social-icons-list a,
.img-thumbnail,
.img-thumbnail img,
code,
pre,
.form-control,
.input-sm,
.form-group-sm .form-control,
.input-lg,
.form-group-lg .form-control,
.btn,
.btn-group-lg > .btn,
.btn-sm,
.btn-group-sm > .btn,
.btn-xs,
.btn-group-xs > .btn,
.dropdown-menu,
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn,
.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn,
.input-group-addon,
.input-group-addon.input-sm,
.input-group-addon.input-lg,
.input-group-rounded input.form-control:first-child,
.input-group-rounded input.form-control:last-child,
.input-search input.form-control:first-child,
.input-search input.form-control:last-child,
.checkbox-custom label::before,
.nav-tabs > li > a,
.nav-tabs.nav-justified > li > a,
.nav-pills > li > a,
.nav-tabs-justified > li > a,
.navbar,
.navbar-toggle,
.navbar-toggle .icon-bar,
.breadcrumb,
.pagination,
.pager li > a,
.pager li > span,
.label,
.badge,
.container .jumbotron,
.container-fluid .jumbotron,
.thumbnail,
.alert,
.progress,
.progress.progress-border-radius,
.progress.progress-border-radius .progress-bar,
.progress-bar,
.progress-bar-tooltip,
.progress-xs.progress-half-rounded,
.progress-xs.progress-half-rounded .progress-bar,
.progress-sm.progress-half-rounded,
.progress-sm.progress-half-rounded .progress-bar,
.progress-md.progress-half-rounded,
.progress-md.progress-half-rounded .progress-bar,
.progress-lg.progress-half-rounded,
.progress-lg.progress-half-rounded .progress-bar,
.progress-xl.progress-half-rounded,
.progress-xl.progress-half-rounded .progress-bar,
.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br,
.panel,
.panel-group .panel,
.panel-heading + .panel-body,
.panel-heading,
.panel-body,
.panel-group .panel-accordion .panel-body,
.panel-footer,
.panel-horizontal .panel-heading + .panel-body,
.panel-horizontal .panel-heading,
.panel-group .panel-accordion .panel-heading,
html .panel .panel-heading-transparent + .panel-body,
.panel-group .panel-accordion .panel-heading a,
.panel-group .panel-accordion.panel-accordion-first .panel-heading,
.well,
.well-lg,
.well-sm,
.modal-content,
.tooltip-inner,
.popover,
.popover-title,
.call-to-action,
.carousel-indicators li,
.pagination > li:first-child > a,
.pagination > li:first-child > span,
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span,
.pagination > li:last-child > a,
.pagination > li:last-child > span,
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span,
.progress .progress-bar,
.input-group-icon input.form-control:first-child,
.input-group-icon input.form-control:last-child,
.input-search input.form-control:first-child,
.input-search input.form-control:last-child {
	border-radius: 0;
}

.sidebar-left .sidebar-header .sidebar-toggle,
.simple-compose-box,
.timeline .tm-title,
ul.simple-card-list li,
.timeline .tm-items > li .tm-box,
.fc .fc-toolbar .fc-button,
.fc-event,
.fc-state-default.fc-corner-right,
html .scroll-to-top,
.style-switcher .style-switcher-open,
.style-switcher .options-links a,
.search-results-list a,
p.drop-caps.secondary:first-child::first-letter,
.tabs,
.nav-tabs li a,
.toggle > label,
.toggle.toggle-simple .toggle > label,
.toggle.toggle-simple .toggle > label:after,
.widget-twitter-profile .profile-quote,
.btn-group-vertical > .btn:first-child:not(:last-child),
.btn-group-vertical > .btn:last-child:not(:first-child),
.btn-group-vertical > .btn:first-child:not(:last-child),
.btn-group-vertical > .btn:last-child:not(:first-child),
.ui-pnotify .notification,
.owl-theme .owl-nav [class*="owl-"],
.dd-handle,
.scrollable .scrollable-slider,
.form-group-vertical .form-control:first-child,
.form-group-vertical .form-control:first-of-type,
.form-group-vertical .form-control:last-child,
.form-group-vertical .form-control:last-of-type,
.form-group-vertical .input-group.input-group-icon:first-child .form-control,
.form-group-vertical .input-group.input-group-icon:first-of-type .form-control,
.form-group-vertical .input-group.input-group-icon:last-child .form-control,
.form-group-vertical .input-group.input-group-icon:last-of-type .form-control,
.select2-container--bootstrap .select2-selection,
.select2-container--bootstrap .select2-dropdown,
.select2-container--bootstrap .select2-search--dropdown .select2-search__field,
.btn-group > .btn-group:nth-child(2) > .multiselect.btn,
.bootstrap-tagsinput,
.colorpicker,
.image-content-center,
.profile-head,
.toggle.bswitch,
.toggle-on.bswitch,
.toggle-off.bswitch,
.toggle.bswitch .toggle-handle,
.userbox.open .dropdown-menu a,
.header .menu-icon-grid > a,
.page-header .page-title-icon,
.invoice header,
html.boxed .inner-wrapper,
html.boxed .header,
html.boxed .content-body {
	border-radius: 0;
}