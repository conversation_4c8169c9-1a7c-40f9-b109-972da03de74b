<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width,initial-scale=1" name="viewport">
	<meta name="keywords" content="">
	<meta name="description" content="<?php echo $global_config['institute_name'] ?>">
	<meta name="author" content="<?php echo $global_config['institute_name'] ?>">
	<title><?php echo translate('login');?></title>
	<link rel="shortcut icon" href="<?php echo base_url('assets/images/favicon.png');?>">

    <!-- Web Fonts  -->
	<link href="<?php echo is_secure('fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');?>" rel="stylesheet">
	<link rel="stylesheet" href="<?php echo base_url('assets/vendor/font-awesome/css/all.min.css'); ?>">
	<script src="<?php echo base_url('assets/vendor/jquery/jquery.js');?>"></script>

	<!-- sweetalert js/css -->
	<link rel="stylesheet" href="<?php echo base_url('assets/vendor/sweetalert/sweetalert-custom.css');?>">
	<script src="<?php echo base_url('assets/vendor/sweetalert/sweetalert.min.js');?>"></script>

	<!-- eLima Modern Login Styles -->
	<style>
		/*
		COLOR PALETTE USED IN THIS DESIGN:

		Background Colors:
		- slate-900: #0f172a
		- slate-800: #1e293b

		Brand Gradient Colors:
		- emerald-500: #10b981
		- emerald-600: #059669
		- teal-500: #14b8a6
		- blue-500: #3b82f6
		- blue-600: #2563eb
		- cyan-500: #06b6d4

		Text Colors:
		- white: #ffffff
		- slate-300: #cbd5e1
		- slate-400: #94a3b8
		- slate-500: #64748b

		Accent Colors:
		- emerald-400: #34d399
		- teal-400: #2dd4bf
		- blue-400: #60a5fa

		Opacity Variations:
		- /20 = 20% opacity
		- /10 = 10% opacity
		- /5 = 5% opacity
		- /30 = 30% opacity

		Border Colors:
		- white/10: rgba(255, 255, 255, 0.1)
		- white/20: rgba(255, 255, 255, 0.2)
		- white/30: rgba(255, 255, 255, 0.3)
		- emerald-500/20: rgba(16, 185, 129, 0.2)
		*/
		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			font-family: 'Inter', sans-serif;
			min-height: 100vh;
			background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
			position: relative;
			overflow-x: hidden;
		}

		/* Gradient Orbs Background */
		.gradient-orb-1 {
			position: absolute;
			top: 0;
			left: 0;
			width: 24rem;
			height: 24rem;
			background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(20, 184, 166, 0.2) 100%);
			border-radius: 50%;
			filter: blur(3rem);
			transform: translate(-50%, -50%);
		}

		.gradient-orb-2 {
			position: absolute;
			bottom: 0;
			right: 0;
			width: 24rem;
			height: 24rem;
			background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.2) 100%);
			border-radius: 50%;
			filter: blur(3rem);
			transform: translate(50%, 50%);
		}

		.main-container {
			position: relative;
			z-index: 10;
			min-height: 100vh;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 1rem;
			padding-bottom: 5rem;
		}

		.content-grid {
			width: 100%;
			max-width: 72rem;
			margin: 0 auto;
			display: grid;
			grid-template-columns: 1fr;
			gap: 2rem;
			align-items: center;
		}

		@media (min-width: 1024px) {
			.content-grid {
				grid-template-columns: 1fr 1fr;
				gap: 3rem;
			}
		}

		/* Left Panel - Welcome Section */
		.welcome-section {
			text-align: center;
			order: 2;
			margin-bottom: 2rem;
			padding: 1.5rem;
		}

		@media (min-width: 1024px) {
			.welcome-section {
				text-align: left;
				order: 1;
				margin-bottom: 0;
				padding: 2rem;
			}
		}

		.security-badge {
			display: inline-flex;
			align-items: center;
			gap: 0.75rem;
			padding: 0.5rem 1rem;
			background: linear-gradient(90deg, rgba(16, 185, 129, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
			backdrop-filter: blur(8px);
			border: 1px solid rgba(16, 185, 129, 0.2);
			border-radius: 9999px;
			margin-bottom: 1.5rem;
		}

		.security-icon {
			width: 1.25rem;
			height: 1.25rem;
			color: #34d399;
		}

		.security-text {
			color: #34d399;
			font-weight: 500;
		}

		.welcome-title {
			font-size: 1.125rem; /* text-lg for mobile */
			font-weight: 700;
			background: linear-gradient(90deg, #34d399 0%, #2dd4bf 50%, #60a5fa 100%);
			-webkit-background-clip: text;
			background-clip: text;
			-webkit-text-fill-color: transparent;
			line-height: 1.4;
			margin-bottom: 1rem;
			max-width: 64rem;
			margin-left: auto;
			margin-right: auto;
		}

		@media (min-width: 640px) {
			.welcome-title {
				font-size: 1.25rem; /* text-xl for small screens */
			}
		}

		@media (min-width: 768px) {
			.welcome-title {
				font-size: 1.5rem; /* text-2xl for medium screens */
			}
		}

		@media (min-width: 1024px) {
			.welcome-title {
				font-size: 1.5rem; /* text-2xl for large screens */
				margin-left: 0;
				margin-right: 0;
			}
		}

		@media (min-width: 1280px) {
			.welcome-title {
				font-size: 1.5rem; /* text-2xl for extra large screens */
			}
		}

		/* eLima brand name styling */
		.elima-brand {
			font-weight: 900; /* Extra bold for brand emphasis */
			background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
			-webkit-background-clip: text;
			background-clip: text;
			-webkit-text-fill-color: transparent;
			text-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
			position: relative;
			display: inline-block;
		}

		/* Add a subtle glow effect to the eLima brand name */
		.elima-brand::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
			opacity: 0.1;
			border-radius: 0.25rem;
			filter: blur(4px);
			z-index: -1;
		}

		.title-underline {
			width: 6rem;
			height: 0.25rem;
			background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%);
			border-radius: 9999px;
			margin: 1rem auto 0;
		}

		@media (min-width: 1024px) {
			.title-underline {
				margin: 1rem 0 0;
			}
		}

		/* Social Media Icons */
		.social-icons {
			display: flex;
			justify-content: center;
			gap: 1rem;
			margin-top: 2rem;
		}

		@media (min-width: 1024px) {
			.social-icons {
				justify-content: flex-start;
			}
		}

		.social-icon {
			position: relative;
			display: block;
			text-decoration: none;
		}

		.social-icon-blur {
			position: absolute;
			inset: 0;
			background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%);
			border-radius: 0.75rem;
			filter: blur(4px);
			opacity: 0;
			transition: opacity 0.3s ease;
		}

		.social-icon-container {
			position: relative;
			width: 3rem;
			height: 3rem;
			background: rgba(255, 255, 255, 0.1);
			backdrop-filter: blur(8px);
			border: 1px solid rgba(255, 255, 255, 0.2);
			border-radius: 0.75rem;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
			cursor: pointer;
		}

		.social-icon:hover .social-icon-blur {
			opacity: 0.75;
		}

		.social-icon:hover .social-icon-container {
			background: rgba(255, 255, 255, 0.2);
		}
		/* Login Form Styles */
		.login-card {
			background: rgba(255, 255, 255, 0.1);
			backdrop-filter: blur(16px);
			border: 1px solid rgba(255, 255, 255, 0.2);
			border-radius: 1rem;
			box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
		}

		.login-form-header {
			text-align: center;
			margin-bottom: 2rem;
		}

		.logo-container {
			position: relative;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-bottom: 2rem; /* Increased margin for better spacing */
			width: 100%;
		}

		.logo-box {
			position: relative;
			/* Remove fixed width and height constraints to allow natural sizing */
			min-width: 12rem; /* Minimum size for smaller screens */
			max-width: 20rem; /* Maximum size to prevent overflow */
			width: auto; /* Allow natural width */
			height: auto; /* Allow natural height */
			background: transparent;
			border-radius: 1rem;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 1rem; /* Increased padding for better proportions */
		}

		.logo-box img {
			width: 15rem; /* Large fixed size for prominence - 240px */
			height: auto; /* Maintain aspect ratio */
			max-width: 100%; /* Ensure responsiveness */
			object-fit: contain;
		}

		/* Responsive adjustments for different screen sizes */
		@media (max-width: 480px) {
			.logo-box img {
				width: 10rem; /* Smaller on mobile - 160px */
			}
		}

		@media (min-width: 481px) and (max-width: 768px) {
			.logo-box img {
				width: 12rem; /* Medium size on tablets - 192px */
			}
		}

		@media (min-width: 769px) {
			.logo-box img {
				width: 18rem; /* Largest size on desktop - 288px */
			}
		}

		.login-subtitle {
			color: #48baef;
			font-size: 0.875rem;
		}

		@media (min-width: 768px) {
			.login-subtitle {
				font-size: 1rem;
			}
		}

		.form-group {
			margin-bottom: 1rem;
		}

		.input-container {
			position: relative;
		}

		.input-icon {
			position: absolute;
			left: 1rem;
			top: 50%;
			transform: translateY(-50%);
			color: #94a3b8;
			font-size: 1.25rem;
			transition: color 0.3s ease;
		}

		.form-input {
			width: 100%;
			height: 3.5rem;
			padding-left: 3rem;
			padding-right: 1rem;
			background: rgba(255, 255, 255, 0.05);
			border: 1px solid rgba(255, 255, 255, 0.2);
			border-radius: 0.75rem;
			color: white;
			font-size: 1rem;
			transition: all 0.3s ease;
		}

		.form-input:focus {
			outline: none;
			border-color: #10b981;
			box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
		}

		.form-input:focus + .input-icon {
			color: #34d399;
		}

		.form-input::placeholder {
			color: #94a3b8;
		}

		.password-toggle {
			position: absolute;
			right: 1rem;
			top: 50%;
			transform: translateY(-50%);
			background: none;
			border: none;
			color: #94a3b8;
			cursor: pointer;
			transition: color 0.3s ease;
		}

		.password-toggle:hover {
			color: white;
		}

		.form-options {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 1.5rem;
		}

		.checkbox-container {
			display: flex;
			align-items: center;
			gap: 0.75rem;
		}

		.checkbox {
			width: 1rem;
			height: 1rem;
			accent-color: #10b981;
			border-radius: 0.25rem;
		}

		.checkbox-label {
			color: #cbd5e1;
			font-size: 0.875rem;
			cursor: pointer;
		}

		.forgot-password {
			color: #34d399;
			font-size: 0.875rem;
			text-decoration: none;
			transition: color 0.3s ease;
		}

		.forgot-password:hover {
			color: #2dd4bf;
		}

		.login-button {
			width: 100%;
			height: 3.5rem;
			background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%);
			border: none;
			border-radius: 0.75rem;
			color: white;
			font-weight: 600;
			font-size: 1rem;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
		}

		.login-button:hover {
			transform: scale(1.02);
			box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.2);
		}

		.support-section {
			margin-top: 2rem;
			padding-top: 1.5rem;
			border-top: 1px solid rgba(255, 255, 255, 0.1);
			text-align: center;
		}

		.support-text {
			color: #94a3b8;
			font-size: 0.875rem;
		}

		.support-link {
			color: #34d399;
			text-decoration: none;
			transition: color 0.3s ease;
		}

		.support-link:hover {
			color: #2dd4bf;
		}

		.error-message {
			color: #ef4444;
			font-size: 0.875rem;
			margin-top: 0.25rem;
			display: block;
		}
	</style>

	<script type="text/javascript">
		var base_url = '<?php echo base_url() ?>';
	</script>
</head>
<body>
	<!-- Gradient Orbs Background -->
	<div class="gradient-orb-1"></div>
	<div class="gradient-orb-2"></div>

	<div class="main-container">
		<div class="content-grid">
			<!-- Left Panel - Welcome Section -->
			<div class="welcome-section">
				<div style="margin-bottom: 2rem;">
					<!-- Security Badge -->
					<div class="security-badge">
						<i class="fas fa-shield-alt security-icon"></i>
						<span class="security-text">Système Sécurisé</span>
					</div>

					<!-- Welcome Title -->
					<div>
						<h1 class="welcome-title">Bienvenu sur <span class="elima-brand">eLima</span>, votre plateforme de gestion scolaire par excellence.</h1>
						<div class="title-underline"></div>
					</div>
				</div>

				<!-- Social Media Icons -->
				<div class="social-icons">
					<!-- Facebook Icon -->
					<a href="https://www.facebook.com/passdrc" target="_blank" class="social-icon">
						<div class="social-icon-blur"></div>
						<div class="social-icon-container">
							<i class="fab fa-facebook-f" style="color: white; font-size: 1.25rem;"></i>
						</div>
					</a>
					<!-- LinkedIn Icon -->
					<a href="https://www.linkedin.com/company/pro-active-safety-solutions-drc/" target="_blank" class="social-icon">
						<div class="social-icon-blur"></div>
						<div class="social-icon-container">
							<i class="fab fa-linkedin-in" style="color: white; font-size: 1.25rem;"></i>
						</div>
					</a>
					<!-- Instagram Icon -->
					<a href="https://www.instagram.com/passdrc/profilecard/?igsh=MWdjanZqOWxjd3kzNw==" target="_blank" class="social-icon">
						<div class="social-icon-blur"></div>
						<div class="social-icon-container">
							<i class="fab fa-instagram" style="color: white; font-size: 1.25rem;"></i>
						</div>
					</a>
				</div>
			</div>

			<!-- Right Panel - Login Form -->
			<div style="order: 1;">
				<div class="login-card">
					<div style="padding: 2rem;">
						<!-- Form Header -->
						<div class="login-form-header">
							<div class="logo-container">
								<div class="logo-box">
									<img src="<?php echo base_url('eLima_logo.svg'); ?>" alt="eLima Logo">
								</div>
							</div>
							<p class="login-subtitle">Connectez-vous à votre compte</p>
						</div>

						<!-- Login Form -->
						<?php echo form_open($this->uri->uri_string()); ?>
							<div style="margin-bottom: 1.5rem;">
								<!-- Username Field -->
								<div class="form-group">
									<div class="input-container">
										<i class="far fa-user input-icon"></i>
										<input
											type="text"
											name="email"
											value="<?php echo set_value('email');?>"
											placeholder="Nom d'utilisateur"
											class="form-input"
										/>
									</div>
									<?php if (form_error('email')): ?>
										<span class="error-message"><?php echo form_error('email'); ?></span>
									<?php endif; ?>
								</div>

								<!-- Password Field -->
								<div class="form-group">
									<div class="input-container">
										<i class="fas fa-lock input-icon"></i>
										<input
											type="password"
											name="password"
											id="password"
											placeholder="Mot de passe"
											class="form-input"
											style="padding-right: 3rem;"
										/>
										<button
											type="button"
											onclick="togglePassword()"
											class="password-toggle"
										>
											<i class="fas fa-eye" id="toggleIcon"></i>
										</button>
									</div>
									<?php if (form_error('password')): ?>
										<span class="error-message"><?php echo form_error('password'); ?></span>
									<?php endif; ?>
								</div>
							</div>

							<!-- Remember Me & Forgot Password -->
							<div class="form-options">
								<div class="checkbox-container">
									<input
										type="checkbox"
										name="remember"
										id="remember"
										class="checkbox"
									/>
									<label for="remember" class="checkbox-label">
										Se souvenir de moi
									</label>
								</div>
								<a href="<?php echo base_url("authentication/forgot"); ?>" class="forgot-password">
									Mot de passe oublié?
								</a>
							</div>

							<!-- Login Button -->
							<button
								type="submit"
								id="btn_submit"
								class="login-button"
							>
								S'identifier
							</button>
						<?php echo form_close();?>

						<!-- Support Link -->
						<div class="support-section">
							<p class="support-text">
								Besoin d'aide?
								<a href="mailto:<EMAIL>" class="support-link">
									Contactez le support
								</a>
							</p>
							<p class="support-text" style="margin-top: 0.5rem;">
								Email: <a href="mailto:<EMAIL>" class="support-link"><EMAIL></a><br>
								Téléphone: <a href="tel:+243970688665" class="support-link">+243970688665</a>
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Footer -->
	<div style="position: fixed; bottom: 0; left: 0; right: 0; padding: 1rem; text-align: center; background: linear-gradient(to top, rgba(15, 23, 42, 0.8) 0%, transparent 100%); backdrop-filter: blur(8px);">
		<p style="color: #64748b; font-size: 0.875rem;">
			© 2025 eLima - powered by
			<span style="background: linear-gradient(90deg, #34d399 0%, #60a5fa 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; font-weight: 500;">
				PASS-DRC
			</span>
		</p>
	</div>

	<!-- JavaScript -->
	<script>
		// Toggle password visibility
		function togglePassword() {
			const passwordField = document.getElementById('password');
			const toggleIcon = document.getElementById('toggleIcon');

			if (passwordField.type === 'password') {
				passwordField.type = 'text';
				toggleIcon.className = 'fas fa-eye-slash';
			} else {
				passwordField.type = 'password';
				toggleIcon.className = 'fas fa-eye';
			}
		}

		// Add focus effects to form inputs
		document.addEventListener('DOMContentLoaded', function() {
			const inputs = document.querySelectorAll('.form-input');
			inputs.forEach(input => {
				const icon = input.parentElement.querySelector('.input-icon');

				input.addEventListener('focus', function() {
					if (icon) {
						icon.style.color = '#34d399';
					}
				});

				input.addEventListener('blur', function() {
					if (icon) {
						icon.style.color = '#94a3b8';
					}
				});
			});
		});
	</script>

		<?php
		$alertclass = "";
		if($this->session->flashdata('alert-message-success')){
			$alertclass = "success";
		} else if ($this->session->flashdata('alert-message-error')){
			$alertclass = "error";
		} else if ($this->session->flashdata('alert-message-info')){
			$alertclass = "info";
		}
		if($alertclass != ''):
			$alert_message = $this->session->flashdata('alert-message-'. $alertclass);
		?>
			<script type="text/javascript">
				swal({
					toast: true,
					position: 'top-end',
					type: '<?php echo $alertclass;?>',
					title: '<?php echo $alert_message;?>',
					confirmButtonClass: 'btn btn-default',
					buttonsStyling: false,
					timer: 8000
				})
			</script>
		<?php endif; ?>
</body>
</html>