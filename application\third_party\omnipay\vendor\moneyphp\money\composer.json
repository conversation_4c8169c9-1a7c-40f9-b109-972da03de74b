{"name": "moneyphp/money", "description": "PHP implementation of Fowler's Money pattern", "license": "MIT", "keywords": ["money", "vo", "value object"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://moneyphp.org", "require": {"php": ">=5.6", "ext-json": "*"}, "require-dev": {"ext-bcmath": "*", "ext-gmp": "*", "ext-intl": "*", "cache/taggable-cache": "^0.4.0", "doctrine/instantiator": "^1.0.5", "florianv/exchanger": "^1.0", "florianv/swap": "^3.0", "friends-of-phpspec/phpspec-code-coverage": "^3.1.1 || ^4.3", "moneyphp/iso-currencies": "^3.2.1", "php-http/message": "^1.4", "php-http/mock-client": "^1.0.0", "phpspec/phpspec": "^3.4.3", "phpunit/phpunit": "^5.7.27 || ^6.5.14 || ^7.5.18 || ^8.5", "psr/cache": "^1.0", "symfony/phpunit-bridge": "^4"}, "suggest": {"ext-bcmath": "Calculate without integer limits", "ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Money\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\Money\\": "tests/", "spec\\Money\\": "spec/"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true}, "sort-packages": true}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "scripts": {"clean": "rm -rf build/ vendor/", "test": ["vendor/bin/phpspec run", "vendor/bin/phpunit -v"], "test-coverage": ["vendor/bin/phpspec run -c phpspec.ci.yml", "vendor/bin/phpunit -v --coverage-text --coverage-clover=build/unit_coverage.xml"], "update-currencies": ["cp vendor/moneyphp/iso-currencies/resources/current.php resources/currency.php", "php resources/generate-money-factory.php"]}}