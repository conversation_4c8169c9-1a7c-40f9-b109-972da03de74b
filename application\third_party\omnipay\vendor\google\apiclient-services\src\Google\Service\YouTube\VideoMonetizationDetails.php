<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_YouTube_VideoMonetizationDetails extends Google_Model
{
  protected $accessType = 'Google_Service_YouTube_AccessPolicy';
  protected $accessDataType = '';

  public function setAccess(Google_Service_YouTube_AccessPolicy $access)
  {
    $this->access = $access;
  }
  public function getAccess()
  {
    return $this->access;
  }
}
