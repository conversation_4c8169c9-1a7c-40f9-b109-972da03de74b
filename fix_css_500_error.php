<?php
/**
 * CSS 500 Error Fix Script
 * This script helps identify and fix the passdrc.css 500 error
 * Upload to your website root and run it
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>CSS 500 Error Fix</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.fix-section { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.success { color: #10b981; font-weight: bold; }
.error { color: #ef4444; font-weight: bold; }
.warning { color: #f59e0b; font-weight: bold; }
.code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
h2 { color: #1f2937; border-bottom: 2px solid #10b981; padding-bottom: 5px; }
</style></head><body>";

echo "<h1>🔧 CSS 500 Error Fix</h1>";

// 1. Check if passdrc.css exists and its status
echo "<div class='fix-section'>";
echo "<h2>1. CSS File Investigation</h2>";

$css_files_to_check = [
    'passdrc.css',
    'assets/css/passdrc.css',
    'assets/passdrc.css',
    'css/passdrc.css',
    'application/views/css/passdrc.css'
];

$found_css = false;
foreach ($css_files_to_check as $css_file) {
    if (file_exists($css_file)) {
        $found_css = true;
        $file_size = filesize($css_file);
        $mod_time = date('Y-m-d H:i:s', filemtime($css_file));
        echo "<div class='success'>✅ Found: {$css_file} (Size: {$file_size} bytes, Modified: {$mod_time})</div>";
        
        // Check if file is readable
        if (is_readable($css_file)) {
            echo "<div class='success'>   → File is readable</div>";
            
            // Check file permissions
            $perms = substr(sprintf('%o', fileperms($css_file)), -4);
            echo "<div>   → Permissions: {$perms}</div>";
            
            // Check first few lines for syntax errors
            $content = file_get_contents($css_file, false, null, 0, 500);
            if (strpos($content, '<?php') !== false) {
                echo "<div class='error'>   → ❌ PROBLEM: CSS file contains PHP code!</div>";
            } else {
                echo "<div class='success'>   → CSS content looks normal</div>";
            }
        } else {
            echo "<div class='error'>   → ❌ File is not readable - permission issue</div>";
        }
    }
}

if (!$found_css) {
    echo "<div class='warning'>⚠️ passdrc.css file not found in common locations</div>";
}

echo "</div>";

// 2. Check dashboard view file for CSS references
echo "<div class='fix-section'>";
echo "<h2>2. Dashboard CSS References</h2>";

$dashboard_file = 'application/views/dashboard/index.php';
if (file_exists($dashboard_file)) {
    $content = file_get_contents($dashboard_file);
    
    // Look for CSS references
    if (preg_match_all('/href=["\']([^"\']*passdrc[^"\']*)["\']/', $content, $matches)) {
        echo "<div>Found CSS references in dashboard:</div>";
        foreach ($matches[1] as $css_ref) {
            echo "<div class='code'>CSS Reference: {$css_ref}</div>";
            
            // Check if this file exists
            $css_path = ltrim($css_ref, '/');
            if (file_exists($css_path)) {
                echo "<div class='success'>   ✅ File exists</div>";
            } else {
                echo "<div class='error'>   ❌ File missing - THIS IS THE PROBLEM!</div>";
            }
        }
    } else {
        echo "<div class='warning'>No passdrc.css references found in dashboard file</div>";
    }
} else {
    echo "<div class='error'>❌ Dashboard file not found</div>";
}

echo "</div>";

// 3. Generate fix solutions
echo "<div class='fix-section'>";
echo "<h2>3. Automatic Fix Solutions</h2>";

echo "<div class='warning'>🔧 Applying automatic fixes...</div>";

// Solution 1: Create missing CSS file if it doesn't exist
$common_css_paths = ['passdrc.css', 'assets/css/passdrc.css'];
$css_created = false;

foreach ($common_css_paths as $css_path) {
    if (!file_exists($css_path)) {
        $css_dir = dirname($css_path);
        if ($css_dir !== '.' && !is_dir($css_dir)) {
            if (mkdir($css_dir, 0755, true)) {
                echo "<div class='success'>✅ Created directory: {$css_dir}</div>";
            }
        }
        
        // Create a minimal CSS file
        $minimal_css = "/* Emergency CSS fix for 500 error */\n";
        $minimal_css .= "/* This file was auto-generated to fix the missing CSS error */\n";
        $minimal_css .= "body { /* minimal styles */ }\n";
        
        if (file_put_contents($css_path, $minimal_css)) {
            echo "<div class='success'>✅ Created missing CSS file: {$css_path}</div>";
            $css_created = true;
            break;
        }
    }
}

if (!$css_created && $found_css) {
    echo "<div class='success'>✅ CSS file already exists, checking permissions...</div>";
    
    // Fix permissions if needed
    foreach ($css_files_to_check as $css_file) {
        if (file_exists($css_file)) {
            $current_perms = fileperms($css_file);
            if (chmod($css_file, 0644)) {
                echo "<div class='success'>✅ Fixed permissions for: {$css_file}</div>";
            }
            break;
        }
    }
}

echo "</div>";

// 4. Test the fix
echo "<div class='fix-section'>";
echo "<h2>4. Testing the Fix</h2>";

echo "<div>🧪 Testing CSS file access...</div>";

// Try to access the CSS file via HTTP
$test_urls = [
    'passdrc.css',
    'assets/css/passdrc.css',
    'assets/passdrc.css'
];

foreach ($test_urls as $test_url) {
    if (file_exists($test_url)) {
        $full_url = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . '/' . $test_url;
        echo "<div class='code'>Testing URL: {$full_url}</div>";
        
        // Use curl to test if available
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $full_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200) {
                echo "<div class='success'>   ✅ CSS file accessible (HTTP 200)</div>";
            } else {
                echo "<div class='error'>   ❌ CSS file returns HTTP {$http_code}</div>";
            }
        }
        break;
    }
}

echo "</div>";

// 5. Final instructions
echo "<div class='fix-section'>";
echo "<h2>5. Final Steps</h2>";

echo "<div class='success'>🎯 <strong>Next Actions:</strong></div>";
echo "<ol>";
echo "<li><strong>Clear your browser cache completely</strong> (Ctrl+Shift+Delete)</li>";
echo "<li><strong>Try your dashboard again</strong> in a new incognito window</li>";
echo "<li><strong>Check browser console</strong> (F12) - the CSS 500 error should be gone</li>";
echo "<li><strong>If charts still don't show:</strong> The CSS fix worked, but there might be another issue</li>";
echo "</ol>";

echo "<div class='warning'>⚠️ <strong>If this doesn't work:</strong></div>";
echo "<ul>";
echo "<li>The CSS file might be referenced with a different name in your dashboard</li>";
echo "<li>There might be multiple CSS files causing 500 errors</li>";
echo "<li>Server configuration might be blocking CSS files</li>";
echo "</ul>";

echo "</div>";

echo "<div style='margin-top: 30px; padding: 20px; background: #e0f2fe; border-radius: 8px;'>";
echo "<h3>🚀 Expected Result:</h3>";
echo "<p>After running this fix:</p>";
echo "<ul>";
echo "<li>The <code>passdrc.css:1 Failed to load resource: the server responded with a status of 500</code> error should disappear</li>";
echo "<li>Your dashboard charts should start rendering properly</li>";
echo "<li>The console should show <code>Re-initializing income vs expense chart...</code> and <code>Re-initializing student quantity chart...</code> without errors</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
