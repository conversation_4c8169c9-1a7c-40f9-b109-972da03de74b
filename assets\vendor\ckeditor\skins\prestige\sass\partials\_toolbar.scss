/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/license
*/

/*
toolbar.css (part of editor.css)
==================================
This files styles the CKEditor toolbar and its buttons. For toolbar combo
styles, check richcombo.css.
The toolbar is rendered as a big container (called toolbox), which contains
smaller "toolbars". Each toolbar represents a group of items that cannot be
separated. The following is the visual representation of the toolbox.
+-- .cke_toolbox ----------------------------------------------------------+
| +-- .cke_toolbar --+ +-- .cke_toolbar --+ ... +-- .cke_toolbar_break --+ |
| |                  | |                  |     |                        | |
| +------------------+ +------------------+     +------------------------+ |
| +-- .cke_toolbar --+ +-- .cke_toolbar --+ ...                            |
| |                  | |                  |                                |
| +------------------+ +------------------+                                |
+--------------------------------------------------------------------------+
The following instead is the visual representation of a single toolbar:
+-- .cke_toolbar ----------------------------------------------------------------+
| +-- .cke_toolbar_start --+ +-- .cke_toolgroup (*) --+ +-- .cke_toolbar_end --+ |
| |                        | |                        | |                      | |
| +------------------------+ +------------------------+ +----------------------+ |
+--------------------------------------------------------------------------------+
(*) .cke_toolgroup is available only when the toolbar items can be grouped
    (buttons). If the items can't be group (combos), this box is not available
	and the items are rendered straight in that place.
This file also styles toolbar buttons, which are rendered inside the above
.cke_toolgroup containers. This is the visual representation of a button:
+-- .cke_button -------------------------------------+
| +-- .cke_button_icon --+ +-- .cke_button_label --+ |
| |                      | |                       | |
| +----------------------+ +-----------------------+ |
+----------------------------------------------------+
Special outer level classes used in this file:
	.cke_hc: Available when the editor is rendered on "High Contrast".
	.cke_rtl: Available when the editor UI is on RTL.
*/


/* The box that holds each toolbar. */
.cke_toolbar
{
	float: left;
}

.cke_rtl .cke_toolbar
{
	float: right;
}


/* The box that holds buttons. */
.cke_toolgroup
{
	border: 0;
	float: left;
	margin: 1px 2px 6px 0;
	/* Helps to set proper position for separators after .cke_button. */
	padding-right: 3px;
}

.cke_rtl .cke_toolgroup
{
	float: right;
	margin: 1px 0 6px 2px;
	/* Helps to set proper position for separators after .cke_button. */
	padding-left: 3px;
	padding-right: 0;
}

.cke_hc .cke_toolgroup
{
	margin-right: 5px;
	margin-bottom: 5px;
}

.cke_hc.cke_rtl .cke_toolgroup
{
	margin-right: 0;
	margin-left: 5px;
}


/* A toolbar button . */
a.cke_button
{
	display: inline-block;
	height: 18px;
	padding: 4px 6px;
	outline: none;
	cursor: default;
	float: left;
	border: 0;
	position: relative;
}

.cke_rtl a.cke_button
{
	float: right;
}

.cke_hc a.cke_button
{
	border: 1px solid black;
	/* Compensate the added border */
	padding: 3px 5px;
	margin: 0 3px 5px 0;
}

.cke_hc.cke_rtl a.cke_button
{
	margin: 0 0 5px 3px;
}


/* This class is applied to the button when it is "active" (pushed).
   This style indicates that the feature associated with the button is active
   i.e. currently writing in bold or when spell checking is enabled. */
a.cke_button_on
{
	background: rgba(0,0,0,0.3);
	border-radius: 3px;
	border: 1px rgba(0,0,0,0.3) solid;
	padding: 3px 5px;
}


/* Button states. */
a.cke_button_off:hover,
a.cke_button_off:focus,
a.cke_button_off:active
{
	background: rgba(0,0,0,0.1);
	border-radius: 3px;
	border: 1px rgba(0,0,0,0.1) solid;
	/* Compensate the border change */
	padding: 3px 5px;
}

.cke_hc a.cke_button_on,
.cke_hc a.cke_button_off:hover,
.cke_hc a.cke_button_off:focus,
.cke_hc a.cke_button_off:active
{
	background: rgba(0,0,0,0.1);
	border-radius: 3px;
	border: 1px rgba(0,0,0,0.1) solid;
	/* Compensate the border change */
	padding: 1px 3px;
}


/* Disabled Button states. */
a.cke_button_disabled:hover,
a.cke_button_disabled:focus,
a.cke_button_disabled:active
{
	border: 0;
	padding: 4px 6px;
	background-color: transparent;
}

a.cke_button_disabled:focus {
	border: 1px rgba(0,0,0,0.1) solid;
	padding: 3px 5px;
}

.cke_hc a.cke_button_disabled:hover,
.cke_hc a.cke_button_disabled:focus,
.cke_hc a.cke_button_disabled:active
{
	border: 1px solid #acacac;
	padding: 3px 5px;
	margin: 0 3px 5px 0;
}

.cke_hc a.cke_button_disabled:focus {
	border: 3px solid #000;
	/* Compensate the border change */
	padding: 1px 3px;
}

.cke_hc.cke_rtl a.cke_button_disabled:hover,
.cke_hc.cke_rtl a.cke_button_disabled:focus,
.cke_hc.cke_rtl a.cke_button_disabled:active
{
	margin: 0 0 5px 3px;
}


/* Disabled button. */
/* This class is applied to the button when the feature associated with the
   button cannot be used (grayed-out), i.e. paste button remains disabled when
   there is nothing in the clipboard to be pasted. */
a.cke_button_disabled .cke_button_icon,
a.cke_button_disabled .cke_button_arrow
{
	opacity: 0.3;
}

.cke_hc a.cke_button_disabled
{
	border-color: #acacac;
}

.cke_hc a.cke_button_disabled .cke_button_icon,
.cke_hc a.cke_button_disabled .cke_button_label
{
	opacity: 0.5;
}


/* Toolbar group separators. */
/* Separator after last button in the group. */
.cke_toolgroup a.cke_button:last-child:after,
.cke_toolgroup a.cke_button.cke_button_disabled:hover:last-child:after
{
	content: "";
	position: absolute;
	height: 18px;
	width: 0;
	border-right: 1px solid rgba(255, 255, 255, 0.1);
	margin-top: 4px;
	top: 0;
	right: -3px;
}

.cke_rtl .cke_toolgroup a.cke_button:last-child:after,
.cke_rtl .cke_toolgroup a.cke_button.cke_button_disabled:hover:last-child:after
{
	border-right: 0;
	right: auto;

	border-left: 1px solid rgba(255, 255, 255, 0.1);
	top: 0;
	left: -3px;
}

.cke_hc .cke_toolgroup a.cke_button:last-child:after,
.cke_hc .cke_toolgroup a.cke_button.cke_button_disabled:last-child:after,
.cke_hc .cke_toolgroup a.cke_button.cke_button_disabled:hover:last-child:after
{
	border-color:#000;
	top: 0;
	right: -7px;
}

.cke_hc.cke_rtl .cke_toolgroup a.cke_button:last-child:after,
.cke_hc.cke_rtl .cke_toolgroup a.cke_button.cke_button_disabled:last-child:after,
.cke_hc.cke_rtl .cke_toolgroup a.cke_button.cke_button_disabled:hover:last-child:after
{
	top: 0;
	right: auto;
	left: -7px;
}

/* Adjust separator position on button hover. */
.cke_toolgroup a.cke_button:hover:last-child:after,
.cke_toolgroup a.cke_button.cke_button_on:last-child:after
{
	top: -1px;
	right: -4px;
}

.cke_rtl .cke_toolgroup a.cke_button:hover:last-child:after,
.cke_rtl .cke_toolgroup a.cke_button.cke_button_on:last-child:after
{
	top: -1px;
	right: auto;
	left: -4px;
}

.cke_hc .cke_toolgroup a.cke_button:hover:last-child:after,
.cke_hc .cke_toolgroup a.cke_button.cke_button_on:last-child:after
{
	top: -2px;
	right: -9px;
}

.cke_hc.cke_rtl .cke_toolgroup a.cke_button:hover:last-child:after,
.cke_hc.cke_rtl .cke_toolgroup a.cke_button.cke_button_on:last-child:after
{
	top: -2px;
	right: auto;
	left: -9px;
}

/* No separator after last button in a group in a row. */
.cke_toolbar.cke_toolbar_last .cke_toolgroup a.cke_button:last-child:after
{
	content: none;
	border: none;
	width: 0;
	height: 0;
}


/* Button inner elements. */
/* The icon which is a visual representation of the button. */
.cke_button_icon
{
	cursor: inherit;
	background-repeat: no-repeat;
	margin-top: 1px;
	width: 16px;
	height: 16px;
	float: left;
	display: inline-block;
}

.cke_rtl .cke_button_icon
{
	float: right;
}

.cke_hc .cke_button_icon
{
	display: none;
}

/* The label of the button that stores the name of the feature. By default,
   labels are invisible. They can be revealed on demand though. */
.cke_button_label
{
	display: none;
	padding-left: 3px;
	margin-top: 1px;
	line-height: 17px;
	vertical-align: middle;
	float: left;
	cursor: default;
	color: #484848;
}

.cke_rtl .cke_button_label
{
	padding-right: 3px;
	padding-left: 0;
	float: right;
}

.cke_hc .cke_button_label
{
	padding: 0;
	display: inline-block;
	font-size: 12px;
}

/* The small arrow available on buttons that can be expanded
   (e.g. the color buttons). */
.cke_button_arrow
{
	/* Arrow in CSS */
	display: inline-block;
	margin: 8px 0 0 1px;
	width: 0;
	height: 0;
	cursor: default;
	vertical-align: top;
	border-left: 3px solid transparent;
	border-right: 3px solid transparent;
	border-top: 3px solid #484848;
}

.cke_rtl .cke_button_arrow
{
	margin-right: 5px;
	margin-left: 0;
}

.cke_hc .cke_button_arrow
{
	font-size: 10px;
	margin: 3px 0 0 3px;
	width: auto;
	border: 0;
}

/* The vertical separator which is used within a single toolbar to split
   buttons into sub-groups. */
.cke_toolbar_separator
{
	float: left;
	background-color: rgba(255, 255, 255, 0.1);
	margin: 4px 2px 0 2px;
	height: 18px;
	width: 1px;
}

.cke_rtl .cke_toolbar_separator
{
	float: right;
}

.cke_hc .cke_toolbar_separator
{
	background-color: #000;
	margin-left: 2px;
	margin-right: 5px;
	margin-bottom: 9px;
}
.cke_hc.cke_rtl .cke_toolbar_separator
{
	margin-left: 5px;
	margin-right: 2px;
}

/* The dummy element that breaks toolbars.
   Once it is placed, the very next toolbar is moved to the new row. */
.cke_toolbar_break
{
	display: block;
	clear: left;
}

.cke_rtl .cke_toolbar_break
{
	clear: right;
}

/* The button, which when clicked hides (collapses) all the toolbars. */
a.cke_toolbox_collapser
{
	width: 12px;
	height: 11px;
	float: right;
	margin: 11px 0 0;
	font-size: 0;
	cursor: default;
	text-align: center;

	border: 1px solid #bcbcbc;
}

.cke_rtl .cke_toolbox_collapser
{
	float: left;
}

.cke_toolbox_collapser:hover
{
	background: #e5e5e5;
}

.cke_toolbox_collapser.cke_toolbox_collapser_min
{
	margin: 0 2px 4px;
}

/* The CSS arrow, which belongs to the toolbar collapser. */
.cke_toolbox_collapser .cke_arrow
{
	display: inline-block;

	/* Pure CSS Arrow */
	height: 0;
	width: 0;
	font-size: 0;
	margin-top: 1px;
	border: 3px solid transparent;
	border-bottom-color: #484848;
}

.cke_toolbox_collapser.cke_toolbox_collapser_min .cke_arrow
{
	margin-top: 4px;
	border-bottom-color: transparent;
	border-top-color: #484848;
}

.cke_hc .cke_toolbox_collapser .cke_arrow
{
	font-size: 8px;
	width: auto;
	border: 0;
	margin-top: 0;
}