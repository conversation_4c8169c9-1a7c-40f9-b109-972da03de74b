<?php
/*
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Appsactivity_PermissionChange extends Google_Collection
{
  protected $collection_key = 'removedPermissions';
  protected $addedPermissionsType = 'Google_Service_Appsactivity_Permission';
  protected $addedPermissionsDataType = 'array';
  protected $removedPermissionsType = 'Google_Service_Appsactivity_Permission';
  protected $removedPermissionsDataType = 'array';

  public function setAddedPermissions($addedPermissions)
  {
    $this->addedPermissions = $addedPermissions;
  }
  public function getAddedPermissions()
  {
    return $this->addedPermissions;
  }
  public function setRemovedPermissions($removedPermissions)
  {
    $this->removedPermissions = $removedPermissions;
  }
  public function getRemovedPermissions()
  {
    return $this->removedPermissions;
  }
}
