# Guzzle 6 HTTP Adapter

[![Latest Version](https://img.shields.io/github/release/php-http/guzzle6-adapter.svg?style=flat-square)](https://github.com/php-http/guzzle6-adapter/releases)
[![Software License](https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat-square)](LICENSE)
[![Build Status](https://img.shields.io/travis/php-http/guzzle6-adapter.svg?style=flat-square)](https://travis-ci.org/php-http/guzzle6-adapter)
[![Code Coverage](https://img.shields.io/scrutinizer/coverage/g/php-http/guzzle6-adapter.svg?style=flat-square)](https://scrutinizer-ci.com/g/php-http/guzzle6-adapter)
[![Quality Score](https://img.shields.io/scrutinizer/g/php-http/guzzle6-adapter.svg?style=flat-square)](https://scrutinizer-ci.com/g/php-http/guzzle6-adapter)
[![Total Downloads](https://img.shields.io/packagist/dt/php-http/guzzle6-adapter.svg?style=flat-square)](https://packagist.org/packages/php-http/guzzle6-adapter)

**Guzzle 6 HTTP Adapter.**

**Note for PHP 8**: This adapter can now be installed with PHP 8, even though Guzzle 6 is not tested on PHP 8.
If you need a PSR-18 client, use Guzzle 7 which natively implements PSR-18.
If you need the HTTPlug interfaces for asynchronous calls or for a system that still requires HTTPlug, use the [guzzle7-adapter](https://github.com/php-http/guzzle7-adapter/) instead of this repository.

## Install

Via Composer

``` bash
$ composer require php-http/guzzle6-adapter
```


## Documentation

Please see the [official documentation](http://docs.php-http.org/en/latest/clients/guzzle6-adapter.html).


## Testing

First launch the http server:

```bash
$ ./vendor/bin/http_test_server > /dev/null 2>&1 &
```

Then the test suite:

``` bash
$ composer test
```


## Contributing

Please see our [contributing guide](http://docs.php-http.org/en/latest/development/contributing.html).


## Security

If you discover any security related issues, please contact us at [<EMAIL>](mailto:<EMAIL>).


## Credits

Thanks to [David de Boer](https://github.com/ddeboer) for implementing this adapter.


## License

The MIT License (MIT). Please see [License File](LICENSE) for more information.
